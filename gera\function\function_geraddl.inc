<?
class DDL_GENERATOR {
	
	var $arGeomDtypes;
	var $schema=null;
	function __construct(){
		global $arGeomDtypes, $lf;
		$this->arGeomDtypes=$arGeomDtypes;
	}
	static function getGenerators() {
		$return = null;
		// retorna um array de generators definidos.
		foreach (get_declared_classes() as $classe) {
			if (!(strpos(strtolower($classe), strtolower("DDL_GENERATOR_")) === false)) {
				$return[] = substr($classe, 14);
			}
		}
		return $return;
	}

	function geraSequencias($entidades) {
		// Itera pelas entidades, gerando sequências conforme apropriado.
		// Veja ::geraSequencia()
		$gerador = "";
		foreach ($entidades as $entidade) {
			$gerador .= $this->geraSequencia($entidade);
		}
		return $gerador;
	}

	function geraEntidades($entidades) {
		// Itera pelas entidades, gerando tabelas conforme apropriado.
		// Veja ::geraEntidade()
		$gerador = "";
		foreach ($entidades as $entidade) {
			$gerador .= $this->geraEntidade($entidade);
		}
		return $gerador;
	}

	function geraEntidadesOnly($entidades) {
		// Esta versão sem constraints
		// Itera pelas entidades, gerando tabelas conforme apropriado.
		// Veja ::geraEntidade()
		$gerador = "";
		foreach ($entidades as $entidade) {
			$gerador .= $this->geraEntidadeSemContraints($entidade);
		}
		return $gerador;
	}


	function getaConstraintsOnly($entidades) {
		// Este gera somente constraints, em forma de ALTER.
		$gerador = "";
		foreach ($entidades as $entidade) {
			$gerador .= $this->geraConstraintsEntidade($entidade);
		}
		return $gerador;
	}

	function geraRelacionamentos($entidades) {
		// Itera pelos relacionamentos, gerando constraints conforme apropriado.
		// Veja ::geraRelacionamento
		$gerador = "";
		foreach ($entidades as $entidade) {
			if (isset($entidade->relats_parent)) {
				foreach ($entidade->relats_parent as $relat) {
					$gerador .= $this->geraRelacionamento($relat);
				}
			}
		}

		return $gerador;
	}

	function geraInit($entidades) {
		// Gera um cabeçalho...
		return "--- DDL gerado pelo GeraClasses ".getGCVersion();
	}

	function init($entidades) {
		global $lf;
		// Rotina principal.
		// Chama as rotinas que geram sequencias, tabelas, e constraints.
		// Retorna o DDL completo. Veja ::geraSequencias(), geraEntidades(), e geraRelacionamentos().
		$this->init = $this->geraInit($entidades);
		$this->seqs = $this->geraSequencias($entidades);
		$this->ents = $this->geraEntidades($entidades);
		// Separando em duas. Uma gera as entidades, sem contraints, a outra gera somente as contraints (PKs, UNIQUEs, etc)
		$this->ents_only = $this->geraEntidadesOnly($entidades);
		$this->cns_only  = $this->getaConstraintsOnly($entidades);

		$this->rels = $this->geraRelacionamentos($entidades);
		return $this->init.$lf.$lf.$this->seqs.$lf.$lf.$this->ents.$lf.$lf.$this->rels.$lf;
	}

	function geraRelacionamento($rel) {
		// Gera um relacionamento em forma de ALTER TABLE.
		// Pega os nomes das tabelas
		global $lf;
		$parent = $rel->relat_parentname;
		$child  = $rel->relat_childname;
		$id_relat = $rel->id_relat;
		$nome_relat = $parent . "_" . $child . "_" . $id_relat;

		// Itera pelos campos do relacionamento, criando um array
		$campos_child = null;
		$campos_parent = null;

		for ($i = 0; $i < count($rel->cols_relat); $i++) {
			$campo_child = $rel->cols_apoio[$i]->nome_fis;
			$campo_parent = $rel->cols_relat[$i]->nome_fis;
			$campos_child[] = $campo_child;
			$campos_parent[] = $campo_parent;
		}
		// Implode o array, juntando os membros com vírgulas.
		if (is_array($campos_child)) { $campos_child = implode(",", $campos_child); } else { echo "Olha só:". $rel->toString(); }
		if (is_array($campos_parent)) { $campos_parent = implode(",", $campos_parent); } else { echo "Olha só:". $rel->toString(); }
		// Retorna bonitinho.
		return
		$this->geraIndexForRelat($campos_child, $nome_relat, $child).
		"-- Relacionamento $id_relat".$lf."".
		"ALTER TABLE ".($this->schema?$this->schema.'.':'')."$child ADD CONSTRAINT $nome_relat ".$lf.
		"\tFOREIGN KEY ($campos_child)".$lf."".
		"\tREFERENCES ".($this->schema?$this->schema.'.':'')."$parent($campos_parent);".$lf.$lf;
	}

	function geraIndexForRelat($campos_child, $id_relat, $child) {
		// Gera um índice para orelacionamento em forma de CREATE INDEX.
		global $lf;
		$nome_relat = $id_relat;

		// Retorna bonitinho.
		return
		"-- Indice $id_relat".$lf."".
		"CREATE INDEX ".$nome_relat."_IDX ON ".($this->schema?$this->schema.'.':'')."".$child." (".$campos_child.");".$lf.$lf;
	}

	function geraSequencia($entidade) {
		// Itera pelos campos de uma entidade; se um deles tem Identity, gera uma sequência.
		// Assume que cada entidade tem no máximo um campo Identity. (OK)
		global $lf;
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			if ($campo->ident_campo) {
				return "CREATE SEQUENCE ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis."_".$campo->nome_fis."_seq;".$lf."". $this->geraComentarioSequencia($entidade->nome_fis."_".$campo->nome_fis."_seq", $entidade->nome_log, $campo->nome_log);
			}
		}
		return null;
	}


	function geraEntidadeSemContraints($entidade) {
		// Gera CREATE TABLES, contendo os campos e tipos a serem gerados (veja ::geraCampoEntidade()).
		// Este geraEntidade gera CREATE TABLES que já especificam constraints
		// PRIMARY KEY (veja ::geraDeclPk()) e UNIQUE (veja ::geraDeclUnique())
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$create = "CREATE TABLE ".(@$this->schema?$this->schema.'.':'').$nome_fis." (".$lf;

		// Itera pelos campos, gerando um array.
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			if (in_array($campo->dt_campo, $this->arGeomDtypes))continue;
			if ($campo->dt_campo=='GEOMETRY' || $campo->dt_campo=='IMAGE/LONG BINARY' || $campo->dt_campo=='NVARCHAR')continue;
			$create_campo[] .= $this->geraCampoEntidade($campo);
		}

		// Itera pelas constraints PK.
		$pks = $entidade->campos_pk;
		$pks_stat = $this->geraDeclPk($pks);

		// Itera pelas constraints UNIQUE.
		$uniques = $entidade->uniques;
		$uniques_stat = $this->geraDeclUnique($uniques);

		// Cola o array de campos com virgulas. Calcula vírgulas apropriadas, conforme
		// a existência de constraints PK, UNIQUE, as duas, ou nenhuma.
		$create .= implode(",".$lf, $create_campo);
		$create .= ");".$lf."";
		$create .= $this->geraParamAfterCreateTable($entidade);

		// Busca como espetar os comentários.
		$create .= $this->geraComentarioTabela($entidade);

		// Retorna bonitinho.
		return $create;
	}


	function geraConstraintsEntidade($entidade) {
		global $lf;
		$s = "-- Constraints para {$entidade->nome_fis} ".$lf."";
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;

		$alter = "ALTER TABLE $nome_fis ";

		// Itera pelas constraints PK.
		$pks = $entidade->campos_pk;
		$pks_stat = $this->geraDeclPk($pks);

		if ($pks_stat) {
			$s .= $alter . " ADD " . $pks_stat. ";".$lf."";
		}

		// Itera pelas constraints UNIQUE. HACK ALERT: os uniques vem implodidos por implode($s, ",".$lf.""), explodimos aqui.
		$uniques = $entidade->uniques;
		$uniques_stat = $this->geraDeclUnique($uniques);

		if ($uniques_stat) {
			$partes = explode(",".$lf."", $uniques_stat);
			foreach ($partes as $parte) {
				$s .= $alter . " ADD ". $parte.";".$lf."";
			}
		}

		$s .= "".$lf."";
		return $s;

	}

	function geraEntidade($entidade) {
		// Gera CREATE TABLES, contendo os campos e tipos a serem gerados (veja ::geraCampoEntidade()).
		// Este geraEntidade gera CREATE TABLES que já especificam constraints
		// PRIMARY KEY (veja ::geraDeclPk()) e UNIQUE (veja ::geraDeclUnique())
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$create = "CREATE TABLE ".(@$this->schema?$this->schema.'.':'').$nome_fis." (".$lf."";

		// Itera pelos campos, gerando um array.
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			if (in_array($campo->dt_campo, $this->arGeomDtypes))continue;
			if ($campo->dt_campo=='GEOMETRY' || $campo->dt_campo=='IMAGE/LONG BINARY' || $campo->dt_campo=='NVARCHAR')continue;
			$create_campo[] .= $this->geraCampoEntidade($campo);
		}

		// Itera pelas constraints PK.
		$pks = $entidade->campos_pk;
		$pks_stat = $this->geraDeclPk($pks);

		// Itera pelas constraints UNIQUE.
		$uniques = $entidade->uniques;
		$uniques_stat = $this->geraDeclUnique($uniques);

		// Cola o array de campos com virgulas. Calcula vírgulas apropriadas, conforme
		// a existência de constraints PK, UNIQUE, as duas, ou nenhuma.
		$create .= implode(",".$lf."", $create_campo). ($pks_stat?",".$lf."":"");
		$create .= $this->geraDeclPk($pks) . "".($uniques_stat?",".$lf."":"");
		$create .= $this->geraDeclUnique($uniques) . "".$lf."";
		$create .= ");".$lf."";
		$create .= $this->geraParamAfterCreateTable($entidade);

		// Busca como espetar os comentários.
		$create .= $this->geraComentarioTabela($entidade);

		// Retorna bonitinho.
		return $create;
	}

	function geraParamAfterCreateTable($entidade) {
		// Gera apêndices dos CREATE TABLES, contendo os campos e tipos que tem de ser criados a posteriori
		// usado, no momento, para colunas GIS
		// No ER-Studio deve haver um mapeamento dos tipo de campo Geometria_ponto, Geometria_linha e Geometria_poligono para NVARCHAR(1), NVARCHAR(2) E NVARCHAR(3), RESPECTIVAMENTE
		global $lf;
		$pos="";
		foreach($entidade->campos as $campo){
			$tam=$campo->dtl_campo;
			$geomType=null;
			switch($campo->dt_campo){
				case 'POINT':
					$geomType=$campo->dt_campo;
					$geomDims=2;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POINT',2);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'POINTM':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POINTM',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'POINTZ':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POINTZ',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTIPOINT':
					$geomType=$campo->dt_campo;
					$geomDims=2;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOINT',2);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTIPOINTM':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOINTM',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTIPOINTZ':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOINTZ',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'LINESTRING':
					$geomType=$campo->dt_campo;
					$geomDims=2;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'LINESTRING',2);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'LINESTRINGM':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'LINESTRINGM',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'LINESTRINGZ':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'LINESTRINGZ',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTILINESTRING':
					$geomType=$campo->dt_campo;
					$geomDims=2;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTILINESTRING',2);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTILINESTRINGM':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTILINESTRINGM',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTILINESTRINGZ':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTILINESTRINGZ',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'POLYGON':
					$geomType=$campo->dt_campo;
					$geomDims=2;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POLYGON',2);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'POLYGONM':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POLYGONM',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'POLYGONZ':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POLYGONZ',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTIPOLYGON':
					$geomType=$campo->dt_campo;
					$geomDims=2;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOLYGON',2);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTIPOLYGONM':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOLYGONM',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'MULTIPOLYGONZ':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOLYGONZ',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'GEOMETRY':
					$geomType=$campo->dt_campo;
					$geomDims=3;
					//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".trtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'GEOMETRY',3);".$lf."";
					//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
					break;
				case 'IMAGE/LONG BINARY':
					$tam='3';
				case 'NVARCHAR':
					switch($tam){
						case '1':
							$geomType='MULTIPOINTM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOINTM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '12':
							$geomType='POINT';
							$geomDims=2;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POINT',2);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '13':
							$geomType='POINTM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POINTM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '14':
							$geomType='POINTZ';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POINTZ',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '112':
							$geomType='MULTIPOINT';
							$geomDims=2;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOINT',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '113':
							$geomType='MULTIPOINTM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOINTM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '114':
							$geomType='MULTIPOINTZ';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOINTZ',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '2':
							$geomType='MULTILINESTRINGM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTILINESTRINGM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '22':
							$geomType='LINESTRING';
							$geomDims=2;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'LINESTRING',2);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '23':
							$geomType='LINESTRINGM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'LINESTRINGM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '24':
							$geomType='LINESTRINGZ';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'LINESTRINGZ',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '122':
							$geomType='MULTILINESTRING';
							$geomDims=2;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTILINESTRING',2);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '123':
							$geomType='MULTILINESTRINGM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTILINESTRINGM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '124':
							$geomType='MULTILINESTRINGZ';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTILINESTRINGZ',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '3':
							$geomType='MULTIPOLYGONM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOLYGONM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '32':
							$geomType='POLYGON';
							$geomDims=2;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POLYGON',2);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '33':
							$geomType='POLYGONM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POLYGONM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '34':
							$geomType='POLYGONZ';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'POLYGONZ',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '132':
							$geomType='MULTIPOLYGON';
							$geomDims=2;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOLYGON',2);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '133':
							$geomType='MULTIPOLYGONM';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOLYGONM',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '134':
							$geomType='MULTIPOLYGONZ';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'MULTIPOLYGONZ',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '100':
							$geomType='GEOMETRY';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'GEOMETRY',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						case '360':
							$geomType='GEOGRAPHY';
							$geomDims=3;
							//$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'GEOGRAPHY',3);".$lf."";
							//$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
							break;
						default:
							$pos.="NVARCHAR (TRADUZIDO PARA GEOMETRIA) COM TAMANHO NÃO ADEQUADO: ".$campo->nome_fis;
							break;
					}
					break;
				default:
					$pos.="";
					break;
			}
			if ($geomType){
				$pos.="select addgeometrycolumn(".(@$this->schema?"'".$this->schema."',":'')."'".strtolower($entidade->nome_fis)."','".strtolower($campo->nome_fis)."',4326,'".$geomType."',".$geomDims.");".$lf."";
				$pos.="create index ".$entidade->nome_fis."_".$campo->nome_fis."_GIDX ON ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." using GIST (".$campo->nome_fis.");".$lf."";
			}

			if ($campo->null_campo==true && in_array($campo->dt_campo, $this->arGeomDtypes)){
				$pos.="ALTER TABLE ".(@$this->schema?$this->schema.'.':'').$entidade->nome_fis." ALTER COLUMN ".$campo->nome_fis." SET NOT NULL;".$lf."";
				//var_dump($pos);exit;
			}
		}
		return $pos;
	}

	function geraComentarioSequencia($seq, $nome_log, $nome_log_campo) {
		global $lf;
		return "COMMENT ON SEQUENCE ".($this->schema?$this->schema.'.':'')."$seq IS 'AUTONUMBER - $nome_log - $nome_log_campo';".$lf."";
	}

	function geraComentarioTabela($entidade) {
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$coment = "COMMENT ON TABLE ".($this->schema?$this->schema.'.':'')."$nome_fis IS '$nome_log';".$lf."";

		// Cria comentários também para os campos!
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			$coment .= $this->geraComentarioCampo($entidade, $campo);
		}
		return $coment;
	}

	function geraComentarioCampo($entidade, $campo, $ultimo=false) {
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$nome_fis_campo = $campo->nome_fis;
		$nome_log_campo = $campo->nome_log;
		$dt_campo = $campo->dt_campo;
		$ident_campo = $campo->ident_campo;
		if ($dt_campo=='GEOMETRY' || $dt_campo=='IMAGE/LONG BINARY' || $dt_campo=='NVARCHAR'){
			$tam=$campo->dtl_campo;
			if ($dt_campo=='GEOMETRY' || $dt_campo=='IMAGE/LONG BINARY') $tam='3';
			switch($tam){
				case '1':
				case '12':
					$dt_campo="POINT (GEOMETRY)";break;
				case '13':
					$dt_campo="POINTM (GEOMETRY)";break;
				case '14':
					$dt_campo="POINTZ (GEOMETRY)";break;
				case '112':
					$dt_campo="MULTIPOINT (GEOMETRY)";break;
				case '113':
					$dt_campo="MULTIPOINTM (GEOMETRY)";break;
				case '114':
					$dt_campo="MULTIPOINTZ (GEOMETRY)";break;
				case '2':
					$dt_campo="MULTILINESTRING (GEOMETRY)";break;
				case '22':
					$dt_campo="LINESTRING (GEOMETRY)";break;
				case '23':
					$dt_campo="LINESTRINGM (GEOMETRY)";break;
				case '24':
					$dt_campo="LINESTRINGZ (GEOMETRY)";break;
				case '122':
					$dt_campo="MULTILINESTRING (GEOMETRY)";break;
				case '123':
					$dt_campo="MULTILINESTRINGM (GEOMETRY)";break;
				case '124':
					$dt_campo="MULTILINESTRINGZ (GEOMETRY)";break;
				case '3':
					$dt_campo="MULTIPOLYGONM (GEOMETRY)";break;
				case '32':
					$dt_campo="POLYGON (GEOMETRY)";break;
				case '33':
					$dt_campo="POLYGONM (GEOMETRY)";break;
				case '34':
					$dt_campo="POLYGONZ (GEOMETRY)";break;
				case '132':
					$dt_campo="MULTIPOLYGON (GEOMETRY)";break;
				case '133':
					$dt_campo="MULTIPOLYGONM (GEOMETRY)";break;
				case '134':
					$dt_campo="MULTIPOLYGONZ (GEOMETRY)";break;
				case '100':
					$dt_campo="GEOMETRY (GEOMETRY)";break;
				case '360':
					$dt_campo="GEOGRAPHY (GEOMETRY)";break;
				default:
					$dt_campo="TIPO GEOMETRY NÃO ESPECIFICADO (".$campo->dtl_campo.")";break;
			}
		}
		return "COMMENT ON COLUMN ".($this->schema?$this->schema.'.':'')."$nome_fis.$nome_fis_campo IS '$nome_log - $nome_log_campo - $dt_campo';".$lf."";
	}

	function geraDeclPk($pks) {
		// Gera a declaração de CONSTRAINT PRIMARY KEY.
		if ($pks) {
			// Tem PK para a Entidade em questão; gera o famoso array de campos, que vai ser implodido.
			$create_campo = null;
			foreach ($pks as $campo) {
				$create_campo[] = $campo->nome_fis;
			}
			if (count($create_campo) < 1) return null;
			sort($create_campo);
			return "\t\t\tPRIMARY KEY (" .implode(",", $create_campo).")";
		} else {
			// Não tem PK para a Entidade... boa sorte... vai entender esses modellers/atrizes...
			return null;
		}
	}

	function geraDeclUnique($uniques) {
		// Gera declaração(s) CONSTRAINT UNIQUE (veja ::geraDeclPk()).
		global $lf;
		$s = null;
		if (!$uniques) return null;
		foreach ($uniques as $unique) {
			$create_campo = null;
			foreach ($unique as $campo) {
				$create_campo[] = $campo->nome_fis;
			}
			if (count($create_campo) < 1) continue;
			sort($create_campo);
			$s[] = "\t\t\tUNIQUE (" .implode(",", $create_campo).")";
		}
		return $s?implode(",".$lf."", $s):null;
	}


	function geraCampoEntidade($campo, $ultimo=false) {
		// Este é o método que faz a tradução dos tipos de dados fictícios do XML
		$nome_campo  = $campo->nome_fis;
		$dt_campo    = $campo->dt_campo;
		$dtl_campo   = $campo->dtl_campo;
		$ident_campo = $campo->ident_campo;
		$null_campo  = $campo->null_campo;
		$def_campo   = $campo->default;
		// echo $nome_campo."\r\n";
		// if ($nome_campo=='STAP1_COD') {
		// 	echo nl2br(print_r($campo, true))."<br/>";exit;
		// }
		// if ($def_campo) die('aqui ');

		switch (strtoupper($dt_campo)) {
			case 'BIT':
				$campo = "BOOLEAN";
				break;
			case 'TEXT':
				$campo = 'TEXT';
				break;
			case 'DATE':
				$campo = "TIMESTAMP";
				break;
			case 'DOUBLE PRECISION':
			case 'FLOAT':
				$campo = "DOUBLE PRECISION";
				break;
			case 'INTEGER':
				$campo = "INTEGER";
				break;
			case 'SMALLINT':
				$campo = "SMALLINT";
				break;
			case 'BIGINT':
				$campo = "BIGINT";
				break;
			case 'VARCHAR':
				$campo = "VARCHAR($dtl_campo)";
				break;
			case 'IMAGE/LONG BINARY':
			case 'GEOMETRY':
			case 'NVARCHAR':
				return '';
			default:
				$campo = "NAO SEI FAZER: $dt_campo";
				break;
		}
		$campo = "\t\t". $nome_campo . " " . $campo;
		$campo .= $null_campo?" NOT NULL":" NULL";
		$campo .= is_null($def_campo)==false?" DEFAULT ".$def_campo:"";
		return "\t".$campo;
	}
}


class DDL_GENERATOR_ORACLE extends DDL_GENERATOR {
	// Para o Oracle, vamos fazer override do método ::geraCampoEntidade(),
	// uma vez que os nomes dos datatypes no Oracle são diferentes.
	// Outra coisa: talvez seja necessário gerar índices, não lembro se é automatica a indexação
	// de campos UNIQUE, PRIMARY KEY e os campos das FOREIGN KEYS no Oracle. (NAO!!! TUDO AUTOMATICO! as of 8.1.7)
	// Aparentemente, este imbecil (eu) não sabe como gerar os comentários para sequencias em Oracle,
	// ou o Oracle não suporta isto.

	function geraComentarioSequencia($seq, $nome_log, $nome_log_campo) {
		return null;
	}

	function geraCampoEntidade($campo, $ultimo=false) {
		$nome_campo =  $campo->nome_fis;
		$dt_campo   =  $campo->dt_campo;
		$dtl_campo   =  $campo->dtl_campo;
		$ident_campo = $campo->ident_campo;
		$null_campo  = $campo->null_campo;

		switch (strtoupper($dt_campo)) {
			case 'BIT':
				$campo = "NUMBER(1,0)";
				break;
			case 'TEXT':
				$campo = 'CLOB';  // ou será LONG? veremos...
				break;
			case 'DATE':
				$campo = "DATE";
				break;
			case 'DOUBLE PRECISION':
			case 'FLOAT':
				$campo = "DOUBLE PRECISION";
				break;
			case 'BIGINT':
			case 'INTEGER':
			case 'SMALLINT':
				$campo = "NUMBER(38,0)";
				break;
			case 'IMAGE/LONG BINARY':
			case 'GEOMETRY':
			case 'NVARCHAR':
				return '';
				break;
			case 'VARCHAR':
				if ($dtl_campo > 4000) {
					// Grita!
					PHP_GENERATOR_ENTIDADE::geraWarning("### ATENCAO ### Campo VARCHAR maior que 4000! ($nome_campo). Mudar para TEXT please.");
					$campo = "VARCHAR2(4000)";
				} else {
					$campo = "VARCHAR2($dtl_campo)";
				}
				break;
			default:
				$campo = "NAO SEI FAZER: $dt_campo";
				break;
		}
		$campo = "\t\t". $nome_campo . " " . $campo;
		$campo .= $null_campo?" NOT NULL":" NULL";
		return "\t".$campo;
	}

	function geraInit($doc) {
		return "--- DDL para Oracle 8i/9i gerado pelo GeraClasses ".getGCVersion();
	}
}


class DDL_GENERATOR_POSTGRESQL extends DDL_GENERATOR {
	// Nada, afinal a base é o PostgreSQL.
	function geraInit($doc) {
		global $lf;
		$h= "--- DDL para PostgreSQL 7.x gerado pelo GeraClasses ".getGCVersion();
		if ($this->schema) {
			$h.= $lf."create schema if not exists ".$this->schema." ;".$lf;
			$h.= $lf."set search_path to ".$this->schema.",public;".$lf;
		}
		return $h;
	}
}


class DDL_GENERATOR_MYSQL extends DDL_GENERATOR {
	// Para o MySQL, vamos fazer override do método ::geraCampoEntidade(),
	// uma vez que os nomes dos datatypes no dito são diferentes.

	// Também, não vamos gerar constraints de foreign keys, por enquanto.
	// Diz que suporta, não estou entendendo...
	// Entendi. Ele suporta a sintaxe, mas as contraints não fazem nada, actually.
	// Update 17/08/03: InnoDB supports foreign keys!
	// Update 18/08/03: Criar indexes nas tabelas para que funcione. Expõe problema com ordenação dos campos. Rewrite do GC iminente

	// Outra coisa é que não tem sequências! AUTO_INCREMENT na hora de gerar campo!
	// E também não tem comentários!

	// Adicionar
	//  TYPE = InnoDB
	// aos Create table.

	function geraComentarioSequencia($seq, $nome_log, $nome_log_campo) {
		return null;
	}

	function geraComentarioTabela($entidade) {
		return null;
	}

	function geraComentarioCampo($entidade, $campo, $ultimo=false) {
		return null;
	}

	function init($entidades) {
		// Rotina principal.
		// Chama as rotinas que geram sequencias, tabelas, e constraints.
		// Retorna o DDL completo. Veja ::geraSequencias(), geraEntidades(), e geraRelacionamentos().
		global $lf;
		$this->init = $this->geraInit($entidades);
		$this->ents = $this->geraEntidades($entidades);
		$this->rels = $this->geraRelacionamentos($entidades);
		return $this->init.$lf.$lf.$this->ents.$lf.$lf.$this->rels.$lf;
	}

	function geraCampoEntidade($campo, $ultimo=false) {
		$nome_campo =  $campo->nome_fis;
		$dt_campo   =  $campo->dt_campo;
		$dtl_campo   =  $campo->dtl_campo;
		$ident_campo = $campo->ident_campo;
		$null_campo  = $campo->null_campo;

		switch (strtoupper($dt_campo)) {
			case 'BIT':
				$campo = "TINYINT "; // raios de MySQL não tem booleano....
				break;
			case 'TEXT':
				$campo = 'LONGTEXT';
				break;
			case 'DATE':
				$campo = "DATETIME";
				break;
			case 'DOUBLE PRECISION':
			case 'FLOAT':
				$campo = "DOUBLE PRECISION";
				break;
			case 'INTEGER':
				$campo = "INTEGER";
				break;
			case 'BIGINT':
				$campo = "BIGINT";
				break;
			case 'IMAGE/LONG BINARY':
			case 'GEOMETRY':
			case 'NVARCHAR':
				return '';
				break;
			case 'VARCHAR':
				if ($dtl_campo > 255) {
					$campo = "LONGTEXT";
				} else {
					$campo = "VARCHAR($dtl_campo)";
				}
				break;
			default:
				$campo = "NAO SEI FAZER: $dt_campo";
				break;
		}
		$campo = "\t\t". $nome_campo . " " . $campo;
		$campo .= $null_campo?" NOT NULL":" NULL";
		$campo .= $ident_campo?" AUTO_INCREMENT":"";
		return "\t".$campo;
	}

	function geraInit($doc) {
		return "--- DDL para MySQL gerado pelo GeraClasses v1.0b";
	}

	function geraParamAfterCreateTable($entidade) {
		//return "";
		return "TYPE = InnoDB";
	}


	function geraIndexForRelat($campos_child, $id_relat, $child) {
		global $lf;
		return
		"-- Índice para o relacionamento $id_relat na tabela child ($child)".$lf."".
		"ALTER TABLE $child ADD INDEX ($campos_child);".$lf."".
		"".$lf."";
	}

}






class DDL_GENERATOR_FIREBIRD extends DDL_GENERATOR {
	/* Generator para o Firebird 1.5 */

	function geraComentarioSequencia($seq, $nome_log, $nome_log_campo) {
		return null;
	}


	function geraComentarioCampo($entidade, $campo, $ultimo=false) {
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$nome_fis_campo = $campo->nome_fis;
		$nome_log_campo = $campo->nome_log;
		$dt_campo = $campo->dt_campo;
		$ident_campo = $campo->ident_campo;
		return "".$lf."COMMIT;".$lf."COMMIT;".$lf."COMMIT;".$lf."update RDB\$RELATION_FIELDS set RDB\$DESCRIPTION = '$nome_log - $nome_log_campo - $dt_campo' where (RDB\$RELATION_NAME = '$nome_fis') and (RDB\$FIELD_NAME = '$nome_fis_campo');".$lf."COMMIT;".$lf."";
	}

	function geraComentarioTabela($entidade) {
		//update RDB$RELATIONS set RDB$DESCRIPTION = ?DESC where RDB$RELATION_NAME='NEW_TABLE']
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$coment = "".$lf."COMMIT;".$lf."COMMIT;".$lf."update RDB\$RELATIONS set RDB\$DESCRIPTION = '$nome_log' where RDB\$RELATION_NAME='$nome_fis';".$lf."COMMIT;".$lf."";

		// Cria comentários também para os campos!
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			$coment .= $this->geraComentarioCampo($entidade, $campo);
		}
		$aff = "select RDB\$DESCRIPTION, RDB\$RELATION_NAME from RDB\$RELATIONS WHERE RDB\$RELATION_NAME = '$nome_fis';";
		$coment .= "".$lf."COMMIT;".$lf."COMMIT;".$lf."".$lf."".$lf."".$lf."";

		return $coment;

		return null;
	}


	function geraSequencia($entidade) {
		// Itera pelos campos de uma entidade; se um deles tem Identity, gera uma sequência.
		// Assume que cada entidade tem no máximo um campo Identity. (OK)
		global $lf;
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			if ($campo->ident_campo) {
				return "CREATE GENERATOR ".$entidade->nome_fis."_".$campo->nome_fis."_seq;".$lf."COMMIT;".$lf."". $this->geraComentarioSequencia($entidade->nome_fis."_".$campo->nome_fis."_seq", $entidade->nome_log, $campo->nome_log);
			}
		}
		return null;
	}


	function geraCampoEntidade($campo, $ultimo=false) {
		$nome_campo =  $campo->nome_fis;
		$dt_campo   =  $campo->dt_campo;
		$dtl_campo   =  $campo->dtl_campo;
		$ident_campo = $campo->ident_campo;
		$null_campo  = $campo->null_campo;

		switch (strtoupper($dt_campo)) {
			case 'BIT':
				$campo = "SMALLINT";
				break;
			case 'TEXT':
				$campo = 'BLOB SUB_TYPE TEXT';  // help us god
				break;
			case 'DATE':
				$campo = "TIMESTAMP";  // ou será date? datetime? time?
				break;
			case 'DOUBLE PRECISION':
			case 'FLOAT':
				$campo = "FLOAT";
				break;
			case 'INTEGER':
				$campo = "INTEGER";
				break;
			case 'BIGINT':
				$campo = "INT64";
				break;
			case 'IMAGE/LONG BINARY':
			case 'GEOMETRY':
			case 'NVARCHAR':
				return '';
				break;
			case 'VARCHAR':
				if ($dtl_campo > 4000) {
					// Grita!
					PHP_GENERATOR_ENTIDADE::geraWarning("### ATENCAO ### Campo VARCHAR maior que 4000! ($nome_campo). Mudar para TEXT please.");
					$campo = "VARCHAR(4000)";
				} else {
					$campo = "VARCHAR($dtl_campo)";
				}
				break;
			default:
				$campo = "NAO SEI FAZER: $dt_campo";
				break;
		}
		$campo = "\t\t". $nome_campo . " " . $campo;
		$campo .= $null_campo?" NOT NULL":" ";
		return "\t".$campo;
	}

	function geraInit($doc) {
		return "--- DDL para Firebird 1.5+ gerado pelo GeraClasses v1.0b";
	}
}


class DDL_GENERATOR_SQLSERVER extends DDL_GENERATOR {
	/* Generator para o MS SQL SERVER (provavelmente 7 e acima - verificar) */

	function geraComentarioSequencia($seq, $nome_log, $nome_log_campo) {
		return null;
	}


	function geraEntidade($entidade) {
		// Gera CREATE TABLES, contendo os campos e tipos a serem gerados (veja ::geraCampoEntidade()).
		// Este geraEntidade gera CREATE TABLES que já especificam constraints
		// PRIMARY KEY (veja ::geraDeclPk()) e UNIQUE (veja ::geraDeclUnique())
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$create = "CREATE TABLE dbo.$nome_fis (".$lf."";

		// Itera pelos campos, gerando um array.
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			if ($campo->dt_campo=='GEOMETRY' || $campo->dt_campo=='IMAGE/LONG BINARY' || $campo->dt_campo=='NVARCHAR')continue;
			$create_campo[] .= $this->geraCampoEntidade($campo);
		}

		// Itera pelas constraints PK.
		$pks = $entidade->campos_pk;
		$pks_stat = $this->geraDeclPk($pks);

		// Itera pelas constraints UNIQUE.
		$uniques = $entidade->uniques;
		$uniques_stat = $this->geraDeclUnique($uniques);

		// Cola o array de campos com virgulas. Calcula vírgulas apropriadas, conforme
		// a existência de constraints PK, UNIQUE, as duas, ou nenhuma.
		$create .= implode(",".$lf."", $create_campo). ($pks_stat?",".$lf."":"");
		$create .= $this->geraDeclPk($pks) . "".($uniques_stat?",".$lf."":"");
		$create .= $this->geraDeclUnique($uniques) . "".$lf."";
		$create .= ");".$lf."";
		$create .= $this->geraParamAfterCreateTable($entidade);

		// Busca como espetar os comentários.
		$create .= $this->geraComentarioTabela($entidade);

		// Retorna bonitinho.
		return $create;
	}

	function geraComentarioCampo($entidade, $campo, $ultimo=false) {
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$nome_fis_campo = $campo->nome_fis;
		$nome_log_campo = $campo->nome_log;
		$dt_campo = $campo->dt_campo;
		$ident_campo = $campo->ident_campo;
		return "exec sp_addextendedproperty N'Comment', N'$nome_log_campo', N'user', N'dbo', N'table', N'$nome_fis', N'column', N'$nome_fis_campo'".$lf."";
	}

	function geraComentarioTabela($entidade) {
		//update RDB$RELATIONS set RDB$DESCRIPTION = ?DESC where RDB$RELATION_NAME='NEW_TABLE'
		global $lf;
		$nome_fis = $entidade->nome_fis;
		$nome_log = $entidade->nome_log;
		$coment = "".$lf."exec sp_addextendedproperty N'Comment', N'$nome_log', N'user', N'dbo', N'table', N'$nome_fis'".$lf."";

		// Cria comentários também para os campos!
		$create_campo = null;
		foreach ($entidade->campos as $campo) {
			$coment .= $this->geraComentarioCampo($entidade, $campo);
		}
		$coment .= "GO".$lf."".$lf."".$lf."";

		return $coment;
	}

	function geraSequencia($entidade) {
		return null;
	}

	function geraCampoEntidade($campo, $ultimo=false) {
		$nome_campo =  $campo->nome_fis;
		$dt_campo   =  $campo->dt_campo;
		$dtl_campo   =  $campo->dtl_campo;
		$ident_campo = $campo->ident_campo;
		$null_campo  = $campo->null_campo;

		switch (strtoupper($dt_campo)) {
			case 'BIT':
				$campo = "BIT";
				break;
			case 'TEXT':
				$campo = 'TEXT';  // help us god
				break;
			case 'DATE':
				$campo = "DATETIME";
				break;
			case 'DOUBLE PRECISION':
			case 'FLOAT':
				$campo = "FLOAT(53)";
				break;
			case 'BIGINT':
			case 'INTEGER':
				$campo = "BIGINT";
				break;
			case 'IMAGE/LONG BINARY':
			case 'GEOMETRY':
			case 'NVARCHAR':
				return '';
				break;
			case 'VARCHAR':
				if ($dtl_campo > 8000) {
					// Grita!
					PHP_GENERATOR_ENTIDADE::geraWarning("### ATENCAO ### Campo VARCHAR maior que 4000! ($nome_campo). Mudar para TEXT please.");
					$campo = "VARCHAR(8000)";
				} else {
					$campo = "VARCHAR($dtl_campo)";
				}
				break;
			default:
				$campo = "NAO SEI FAZER: $dt_campo";
				break;
		}
		$campo = "\t\t". $nome_campo . " " . $campo;
		$campo .= $null_campo?" NOT NULL":" NULL";
		$campo .= $ident_campo?" IDENTITY":"";
		return "\t".$campo;
	}

	function geraInit($doc) {
		return "--- DDL para MS SQL Server 7+ gerado pelo GeraClasses v1.0b";
	}
}


?>
