var ajaxUrl='ajax_cb.php';
function sqlGo(base, sql, obj){
	var sender=obj;
	obj.update('<img src="images/ajax-loader.gif">');
	new Ajax.Request(ajaxUrl,{
		method:'post',
		parameters:'a=sqlGo&t='+bases[base].tipo+'&pe='+bases[base].pers+'&s='+bases[base].serv+'&u='+bases[base].user+'&n='+bases[base].name+'&p='+bases[base].pass+'&o='+bases[base].options+'&sql=' + sql,
		onFailure: function(){setWaiting(false);alert('Desculpe-nos! Não conseguimos executar a alteração.');},
		onSuccess: function(resp){
			if (resp.responseText=='OK'){
				var pai=sender.parentNode;
				pai.innerHTML='Alterado &aacute; '+(base==0?'esquerda':'direita');
			}
		}
	});
	
}
function delay(time) {
	return new Promise(resolve => setTimeout(resolve, time));
}
function doAllComenta(){
	var links=document.getElementsByClassName('comenta');
	for (var i=0; i<links.length; i++){
		links[i].onclick();
		delay(300);
	}
}
function doAllCreate(){
	var links=document.getElementsByClassName('gocreate');
	for (var i=0; i<links.length; i++){
		links[i].onclick();
		delay(300);
	}
}
function doAllRenameIdx(){
	var links=document.getElementsByClassName('goRenameIdx');
	for (var i=0; i<links.length; i++){
		links[i].onclick();
		delay(300);
	}
}
function setIgnoreTables(sender){
	var toIgnore=[];
	var checks=document.getElementsByClassName('ignore_tab');
	for (var i=0; i<checks.length; i++){
		if (checks[i].checked==false) continue;
		toIgnore.push(checks[i].value);
	}
	new Ajax.Request(ajaxUrl,{
		method:'post',
		parameters:'a=ignoreTables&t='+toIgnore.join(','),
		onFailure: function(){setWaiting(false);alert('Não foi possível marcar as tabelas para ignorar.');},
		onSuccess: function(resp){
			if (resp.responseText=='OK'){
				var pai=document.getElementById('linkIgnoreTables');
				pai.innerHTML='Tabelas marcadas para ignorar armazenadas';
			}
		}
	});
}
function setIgnoreSequences(sender){
	var toIgnore=[];
	var checks=document.getElementsByClassName('ignore_seq');
	for (var i=0; i<checks.length; i++){
		if (checks[i].checked==false) continue;
		toIgnore.push(checks[i].value);
	}
	new Ajax.Request(ajaxUrl,{
		method:'post',
		parameters:'a=ignoreSeqs&t='+toIgnore.join(','),
		onFailure: function(){setWaiting(false);alert('Não foi possível marcar as sequências para ignorar.');},
		onSuccess: function(resp){
			if (resp.responseText=='OK'){
				var pai=document.getElementById('linkIgnoreSequences');
				pai.innerHTML='Sequências marcadas para ignorar armazenadas';
			}
		}
	});
}
function setIgnoreColumns(sender){
	var toIgnore=[];
	var checks=document.getElementsByClassName('ignore_col');
	for (var i=0; i<checks.length; i++){
		if (checks[i].checked==false) continue;
		toIgnore.push(checks[i].value);
	}
	new Ajax.Request(ajaxUrl,{
		method:'post',
		parameters:'a=ignoreCols&t='+toIgnore.join(','),
		onFailure: function(){setWaiting(false);alert('Não foi possível marcar as colunas para ignorar.');},
		onSuccess: function(resp){
			if (resp.responseText=='OK'){
				var pai=document.getElementById('linkIgnoreColumns');
				pai.innerHTML='Colunas marcadas para ignorar armazenadas';
			}
		}
	});
}
function showMsg(msg){
	alert(msg);
}