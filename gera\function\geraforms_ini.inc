<?php

/**************************************************************************************************************************
** Faz o processo de gerada das classes, incluindo diretórios, etc.
**************************************************************************************************************************/
function geraFormsInicial($doc, $pathsaida, $options) {
	// set mem limit aqui... ou algo assim....
	set_time_limit(0);
	global $_encoding, $_utf8;
	if (@$options['utf8']) {
		$_encoding='UTF-8';
		$_utf8=true;
	}else{
		$_encoding='ISO-8859-1';
		$_utf8=false;
	}
	$base_total = $pathsaida;
	$base_total = criaDiretorio($base_total."forms\\");
	echo($base_total." ---\n");

	if (true) {
		// Inicializa o documento XML a ser *gerado* (q contem as definições dos métodos gerados).
		global $ndoc, $ndoc_root_element;
		$ndoc = domxml_new_doc("1.0");
		$ndoc_root_element = $ndoc->create_element("Classes");
		$ndoc->append_child($ndoc_root_element);

		$gera = new FORM_GENERATOR_BASE;
		$gera->sufixo=($options['sufixo'])?trim($options['sufixo']):'';
		$gera->setDoc($doc);

		if (@!$options['sufixo']){
			$gera->init($base_total, "codebase/function/","codebase/function/classes/", "codebase/function/classes_readonly/", $options);
		}else{
			$gera->init($base_total, "codebase/function/","codebase/function/classes".$options['sufixo']."/", "codebase/function/classes_readonly".$options['sufixo']."/", $options);
		}

		//echo "XML:\n".$ndoc->dump_mem(true,"utf8")."\nFIM XML\n\n";
		$xx = $ndoc->dump_mem(true,"ISO-8859-1");

		/* Analisar se pode gerar */
		global $arr_nomefunc, $tem_erro_nomefunc;

		if ($tem_erro_nomefunc) {
			FORM_GENERATOR_ENTIDADE::geraWarning("###IMPORTANTE#### Favor ler os warnings. O código gerado pelo GeraClasses não funcionará direito.");
		}

		// Grava OS FORMS propriamente ditos.
		for ($i = 0; $i < count($gera->forms); $i++) {
			$forms = preparaSaidaPHP($gera->forms[$i]);

			$entidade = $gera->entidades[$i];

			gravaArquivo($forms, $base_total.strtolower($entidade->nome_classe_base).".php", false);
		}

	}
}




?>
