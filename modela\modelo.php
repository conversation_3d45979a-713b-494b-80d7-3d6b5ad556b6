<?php
include_once('../codebase/function/func_geramodelos.inc');

$modelFile = $_REQUEST['file'] ?? '../userfiles/gtdm1.json';
// $modelFile='./fullmodelo.js';
$modelContent = trim(file_get_contents($modelFile, true));
// No need to replace 'var tbDefs=' as gtdm1.json is already in pure JSON format
// $modelLimpo=str_replace('var tbDefs=', '', $modelContent);
// if (substr($modelLimpo, -1)==';') $modelLimpo=substr($modelLimpo, 0, -1);
$positionsFile = str_replace('.json', '.pos.json', $modelFile);
$positions=null;
$escuro=false;
if (file_exists($positionsFile)) {
	$positionsContent = trim(file_get_contents($positionsFile, true));
	$positions = json_decode($positionsContent, true);
	if (isset($positions['escuro']) && $positions['escuro']) $escuro=true;
}

include('layout/init.inc');

$model = json_decode($modelContent, true);
// Extract entities from the new JSON structure
$entities = $model['entities'] ?? $model;

$logicos = array();
$fisicos = array();
foreach($entities as $physical => $table) {
    $logicos[$table['logical']] = $physical;
    $fisicos[$physical] = $table['logical'];
}
?>
<link rel="stylesheet" type="text/css" href="style/modelo.css">
<!-- <script src="defmodelo.js"></script> -->
<!-- <script src="fullmodelo.js"></script> -->
<script src="positions.json"></script>
<script>
var modelFile='<?=$modelFile?>';
</script>
<script src="modelo.js"></script>

<div id="conteudo">
	<div id="tooltipModelo" style="display:none;"></div>
	<div id="mapWrapper">
		<div id="modelSideBar">
			<div id="msbHeader">
			
			</div>
			<div id="msbContent">
				<?
				ksort($logicos);
				ksort($fisicos);
				$first=true;
				foreach ($logicos as $logical=>$physical){
					?><div ondblclick="editTable('<?=$physical?>')" onclick="selLocateTable('<?=$physical?>')" class="tablebox logical" id="<?=$physical?>_logical" title="<?=$physical?>"><?=$logical?></div><?
					$first=false;
				}
				$first=true;
				foreach ($fisicos as $physical=>$logical){
					?><div ondblclick="editTable('<?=$physical?>')" onclick="selLocateTable('<?=$physical?>')" class="tablebox physical" id="<?=$physical?>_physical" title="<?=$logical?>"><?=$physical?></div><?
					$first=false;
				}
				?>
			</div>
		</div>
		<div id="maptools">
			<a href="/index.php"><i class="fa fa-home"></i>&nbsp;GeraModelos</a>
			&nbsp;|&nbsp;
			<span class="simulink" onclick="centralizar();"><i class="fa fa-crosshairs"></i>&nbsp;Centralizar</span>
			&nbsp;|&nbsp;
			<span class="simulink" onclick="resetZoom();"><i class="fa fa-search">&nbsp;</i>Reset Zoom</span>
			&nbsp;|&nbsp;
			Lóg/Fís: <span class="logphys simulink" onclick="toggleLogPhys('list');"><i class="fa fa-list"></i></span> &nbsp; <span class="logphys simulink" onclick="toggleLogPhys('model');"><i class="fa fa-object-ungroup"></i></span>
			&nbsp;|&nbsp;
			<span class="simulink" onclick="toggleMinimap();"><i class="fa fa-eye"></i>&nbsp;MiniMap</span>
			&nbsp;|&nbsp;
			<span class="simulink" onclick="toggleClaroEscuro();"><i class="fa fa-adjust"></i>&nbsp;C/E</span>
			&nbsp;|&nbsp;
			<span class="simulink" onclick="saveTablePositions();"><i class="fa fa-object-group"></i>&nbsp;Save Layout</span>
			&nbsp;|&nbsp;
			<span class="simulink" onclick="saveModel(true);"><i class="fa fa-save"></i>&nbsp;Save Model</span>
			&nbsp;|&nbsp;
			<button type="button" class="btn btn-default btn-xs" onclick="newTable('id');"><i class="fa fa-table" title="New Table"></i></button>
			&nbsp;|&nbsp;
			Relationships: 
			<div class="btn-group">
				<button type="button" class="btn btn-default btn-xs" onclick="startRelationshipSelection('id');"><i class="fa fa-caret-square-o-right" title="Identifying"></i></button>
				<button type="button" class="btn btn-default btn-xs" onclick="startRelationshipSelection('nonidmand');"><i class="fa fa-arrow-circle-o-right" title="Non Identifying, mandatory"></i></button>
				<button type="button" class="btn btn-default btn-xs" onclick="startRelationshipSelection('nonidopt');"><i class="fa fa-arrow-right" title="Non Identifying, optional"></i></button>
				<button type="button" class="btn btn-default btn-xs" onclick="startRelationshipSelection('onetoone');"><i class="fa fa-arrows-h" title="One to One"></i></button>
			</div>
			&nbsp;|&nbsp;
			<span class="simulink" onclick="move('left');"><i class="fa fa-arrow-left" title="Mover para a esquerda"></i></span>
			<span class="simulink" onclick="move('up');"><i class="fa fa-arrow-up" title="Mover para cima"></i></span>
			<span class="simulink" onclick="move('right');"><i class="fa fa-arrow-right" title="Mover para a direita"></i></span>
			<span class="simulink" onclick="move('down');"><i class="fa fa-arrow-down" title="Mover para baixo"></i></span>
			<span id="infopos"></span>
			<span id="infozoom"></span>
			<span id="infotable"></span>
			<span id="infostatus"></span>
		</div>
		<div id="modelo-wrapper">
			<div class="mapfull" id="divModelo"></div>
		</div>
		<div id="minimap-wrapper">
			<div id="minimap"></div>
		</div>
	</div>
</div>
