<?php

/**************************************************************************************************************************
** Faz o processo de gerada das classes, incluindo diretórios, etc.
**************************************************************************************************************************/
function geraClassesInicial($doc, $pathsaida, $options) {
	// set mem limit aqui... ou algo assim....
	set_time_limit(0);
	global $_encoding, $_utf8;
	$_utf8=true;
	$_encoding='UTF-8';
	global $arGeomDtypes;
	$arGeomDtypes=array('GEOMETRY','POINT','POINTM','MULTIPOINT','MULTIPOINTM','LINESTRING','LINESTRINGM','MULTILINESTRING','MULTILINESTRINGM','POLYGON','POLYGONM','MULTIPOLYGON','MULTIPOLYGONM','NVARCHAR');
	$base_total = $pathsaida;
	$base_total = criaDiretorio($base_total);

	// [2] Configurando os PATHS relativos aqui. Ao alterar, alterar também a sessão [3].
//die('aqui '.$options['sufixo']);

	// [3] Criação da estrutura de diretórios.
	if (@$options['sufixo'] && $options['sufixo']!='' && !$options['namespace']){
		$base_codebase = criaDiretorio($base_total."codebase".$options['sufixo']."/");
		$base_config = criaDiretorio($base_total."config".$options['sufixo']."/");
		$base_codebase_function = criaDiretorio($base_codebase."function/");
		$base_codebase_install = criaDiretorio($base_codebase."install/");
		$base_codebase_install_ddl = criaDiretorio($base_codebase_install."ddl/");
		$base_codebase_function_classes = criaDiretorio($base_codebase_function."classes".$options['sufixo']."/");
		$base_codebase_function_classesreadonly = criaDiretorio($base_codebase_function."classes_readonly".$options['sufixo']."/");
	}else if (@$options['namespace'] && $options['namespace']!=''){
		$base_codebase = criaDiretorio($base_total."codebase".$options['sufixo']."/");
		$base_config = criaDiretorio($base_total."config".$options['sufixo']."/");
		$base_codebase_function = criaDiretorio($base_codebase."function/");
		$base_codebase_install = criaDiretorio($base_codebase."install/");
		$base_codebase_install_ddl = criaDiretorio($base_codebase_install."ddl/");
		$base_codebase_function_classes = criaDiretorio($base_codebase_function."classes".$options['sufixo']."/");
		$base_codebase_function_classesreadonly = criaDiretorio($base_codebase_function."classes_readonly".$options['sufixo']."/");
	}else{
		$base_docroot = criaDiretorio($base_total."docroot/");
		$base_config = criaDiretorio($base_total."config/");
		$base_codebase = criaDiretorio($base_total."codebase/");
		$base_codebase_install = criaDiretorio($base_codebase."install/");
		$base_codebase_install_ddl = criaDiretorio($base_codebase_install."ddl/");
		$base_codebase_php = criaDiretorio($base_codebase."php/");
		$base_codebase_php_adodb = criaDiretorio($base_codebase_php."adodb/");
		$base_codebase_php_adodb_drivers = criaDiretorio($base_codebase_php_adodb."drivers/");
		$base_codebase_php_adodb_datadict = criaDiretorio($base_codebase_php_adodb."datadict/");
		$base_codebase_function = criaDiretorio($base_codebase."function/");
		$base_codebase_function_classes = criaDiretorio($base_codebase_function."classes/");
		$base_codebase_function_classesreadonly = criaDiretorio($base_codebase_function."classes_readonly/");
	}


	if (true) {
		// Inicializa o documento XML a ser *gerado* (q contem as definições dos métodos gerados).
		global $ndoc, $ndoc_root_element;
		$ndoc = domxml_new_doc("1.0");
		$ndoc_root_element = $ndoc->create_element("Classes");
		$ndoc->append_child($ndoc_root_element);

		$gera = new PHP_GENERATOR_BASE;
		$gera->lf=($options['lf'])?$options['lf']:"\n";
		$gera->schema=($options['schema'])?trim($options['schema']):'';
		$gera->sufixo=($options['sufixo'])?trim($options['sufixo']):'';
		$gera->namespace=($options['namespace'])?trim($options['namespace']):'';
		if ($options['sufclasses']) $gera->sufclasses=true;
		$gera->setDoc($doc);
		if (@$options['sufixo'] && $options['sufixo']!='' && !$options['namespace']){
			$gera->init($base_total, "codebase".$options['sufixo']."/function/","codebase".$options['sufixo']."/function/classes".$options['sufixo']."/", "codebase".$options['sufixo']."/function/classes_readonly".$options['sufixo']."/");
		}else if (@$options['namespace'] && $options['namespace']!=''){
			$gera->init($base_total, "codebase".$options['sufixo']."/function/","codebase".$options['sufixo']."/function/classes".$options['sufixo']."/", "codebase".$options['sufixo']."/function/classes_readonly".$options['sufixo']."/");
		}else{
			$gera->init($base_total, "codebase/function/","codebase/function/classes/", "codebase/function/classes_readonly/");
		}
		//echo "XML:\n".$ndoc->dump_mem(true,"utf8")."\nFIM XML\n\n";
		$xx = $ndoc->dump_mem(true,$_encoding);
		gravaArquivo($xx, $base_codebase."gc".(@$options["sufixo"]?$options["sufixo"]:'').".xml", false);

		/* Analisar se pode gerar */
		global $arr_nomefunc, $tem_erro_nomefunc;

		if ($tem_erro_nomefunc) {
			PHP_GENERATOR_ENTIDADE::geraWarning("###IMPORTANTE#### Favor ler os warnings. O código gerado pelo GeraClasses não funcionará direito.");
		}

		gravaArquivo($gera->base_class, $base_codebase_function."gc_base_class.inc", true);
		gravaArquivo($gera->base_list_class, $base_codebase_function."gc_base_list_class.inc", true);
		if (@$options['sufixo'] && $options['sufixo']!='' && !$options['namespace']){
			gravaArquivo($gera->include_all, $base_codebase_function."factory_static".$options['sufixo'].".inc", false);
			gravaArquivo($gera->classe_factory, $base_codebase_function."factory_dyn".$options['sufixo'].".inc", false);
		}else if (@$options['namespace'] && $options['namespace']!=''){
			gravaArquivo($gera->include_all, $base_codebase_function."factory_static".$options['sufixo'].".inc", false);
			gravaArquivo($gera->classe_factory, $base_codebase_function."factory_dyn".$options['sufixo'].".inc", false);
			gravaArquivo($gera->datapump, $base_codebase_install."datapump_ent.inc", false);
		}else{
			gravaArquivo($gera->index_vazio, $base_docroot."index.php", true);
			// Pára com esse papo de regression q não serve para nada...
			// gravaArquivo($gera->regression, $base_docroot."regression.php", false);
			//if ($options["gera_flash"]) gravaArquivo($gera->flash_register, $base_codebase_flash_flashlib."gc_register.as", false);
			gravaArquivo($gera->include_all, $base_codebase_function."factory_static.inc", false);
			gravaArquivo($gera->classe_factory, $base_codebase_function."factory_dyn.inc", false);
			gravaArquivo($gera->datapump, $base_codebase_install."datapump_ent.inc", false);
			gravaArquivo($gera->initdb, $base_codebase_function."initdb.inc", false);
			//gravaArquivo($gera->httpd, $base_config."httpd.ex.conf", true);
			//gravaArquivo($gera->meuphp, $base_total."meuphp", false);	tornaExecutavel($base_total."meuphp");
			//gravaArquivo($gera->meuphpora, $base_total."meuphpora", false);	tornaExecutavel($base_total."meuphpora");

			//gravaArquivo($gera->rodaregress_oracle, $base_total."regride_oracle", false);	tornaExecutavel($base_total."regride_oracle");
			//gravaArquivo($gera->rodaregress_mysql, $base_total."regride_mysql", false);	tornaExecutavel($base_total."regride_mysql");
			//gravaArquivo($gera->rodaregress_postgresql, $base_total."regride_postgresql", false);	tornaExecutavel($base_total."regride_postgresql");
			//gravaArquivo($gera->rodaregress_postgresql."\n".$gera->rodaregress_oracle."\n".$gera->rodaregress_postgresql, $base_total."regride_all", false); tornaExecutavel($base_total."regride_all");

			gravaArquivo($gera->append, $base_codebase_function."auto_append.inc", true);
			gravaArquivo($gera->prepend, $base_codebase_function."auto_prepend.inc", true);

			gravaArquivo($gera->config_mysql, $base_config."config.inc", true);
			gravaArquivo($gera->config_mysql, $base_config."config.mysql.inc", true);
			gravaArquivo($gera->config_oracle, $base_config."config.oracle.inc", true);
			gravaArquivo($gera->config_postgresql, $base_config."config.postgresql.inc", true);
		}


		// Grava as classes propriamente ditas.
		for ($i = 0; $i < count($gera->bases); $i++) {
			$base = preparaSaidaPHP($gera->bases[$i]);
			$base_list = preparaSaidaPHP($gera->bases_list[$i]);
			$rel = preparaSaidaPHP($gera->rels[$i]);
			$rel_list = preparaSaidaPHP($gera->rels_list[$i]);
			$final = preparaSaidaPHP($gera->finals[$i]);
			$final_list = preparaSaidaPHP($gera->finals_list[$i]);

			$entidade = $gera->entidades[$i];

			$dirbase = criaDiretorio($base_codebase_function_classes ."/");
			$dirbasero = criaDiretorio($base_codebase_function_classesreadonly . "/");

			gravaArquivo($base, $dirbasero.strtolower($entidade->nome_classe_base).".inc", false);
			gravaArquivo($base_list, $dirbasero.strtolower($entidade->nome_classe_base_list).".inc", false);
			gravaArquivo($rel, $dirbasero.strtolower($entidade->nome_classe_rel).".inc", false);
			gravaArquivo($rel_list, $dirbasero.strtolower($entidade->nome_classe_rel_list).".inc", false);
			gravaArquivo($final, $dirbase.strtolower($entidade->nome_classe_final).".inc", true);
			gravaArquivo($final_list, $dirbase.strtolower($entidade->nome_classe_final_list).".inc", true);

		}


		/* Procura 'lixo', ou seja, diretórios dentro de
		   * $base_codebase_function_classes
		   * $base_codebase_function_classesreadonly
		   que não foram gerados. */
		/* Aguarda novo modelo de achatação
		$d = dir($base_codebase_function_classes);
		while (false !== ($entry = $d->read())) {
			$path = $base_codebase_function_classes.$entry;
			if ( ($entry != ".") & ($entry != "..") & ($entry != "CVS") &  is_dir($path)) {
				// Verifica se $entry não é uma das entidades.
				$tem = false;
				for ($i = 0; $i < count($gera->bases); $i++) {
					$entidade = $gera->entidades[$i];
					$nomedir = strtolower($entidade->getNomePHP($entidade->nome_log));
					if (strtolower($nomedir) == strtolower($entry)) {
						$tem = true;
					}
				}
				if (!$tem) {
					PHP_GENERATOR_ENTIDADE::geraWarning("###LIXO#### Diretorio abandonado: classes/$entry !!");
				}
			}
		}
		$d->close();

		$d = dir($base_codebase_function_classesreadonly);
		while (false !== ($entry = $d->read())) {
			$path = $base_codebase_function_classesreadonly.$entry;
			if ( ($entry != ".") & ($entry != "..") & ($entry != "CVS") &  is_dir($path)) {
				// Verifica se $entry não é uma das entidades.
				$tem = false;
				for ($i = 0; $i < count($gera->bases); $i++) {
					$entidade = $gera->entidades[$i];
					$nomedir = strtolower($entidade->getNomePHP($entidade->nome_log));
					if (strtolower($nomedir) == strtolower($entry)) {
						$tem = true;
					}
				}
				if (!$tem) {
					PHP_GENERATOR_ENTIDADE::geraWarning("###LIXO#### Diretorio abandonado: classes_readonly/$entry !!");
				}
			}
		}
		$d->close();
		*/

	}

	if ($options["incorpora_ADODB"]) {
		PHP_GENERATOR_ENTIDADE::geraWarning("(ADODB) Incorporando ADODB e ADODB-Z.");
		// Espetar o ADODB no pacotao, sob php/adodb...
		$aqui = dirname(__FILE__);
		$source_adodb = $aqui . "/../php/adodb/";
		$addado = null;
		$d = dir($source_adodb);
		while (false !== ($entry = $d->read())) {
			if (!(strpos($entry, "inc") === false)) {
				$addado[] = $entry;
			}
		}
		$d->close();
		foreach ($addado as $file) {
			//echo "Copy: ".$source_adodb.$file." to ".$base_codebase_php_adodb.$file."\n";
			copy($source_adodb.$file, $base_codebase_php_adodb.$file);
		}

		// Agora também os "drivers".
		$aqui = dirname(__FILE__);
		$source_adodb = $aqui . "/../php/adodb/drivers/";
		$addado = null;
		$d = dir($source_adodb);
		while (false !== ($entry = $d->read())) {
			if (!(strpos($entry, "inc") === false)) {
				$addado[] = $entry;
			}
		}
		$d->close();
		foreach ($addado as $file) {
			//echo "Copy: ".$source_adodb.$fil." to ".$base_codebase_php_adodb_drivers.$file."\n";
			copy($source_adodb.$file, $base_codebase_php_adodb_drivers.$file);
		}

		// Agora também a parte de datadict.
		$aqui = dirname(__FILE__);
		$source_adodb = $aqui . "/../php/adodb/datadict/";
		$addado = null;
		$d = dir($source_adodb);
		while (false !== ($entry = $d->read())) {
			if (!(strpos($entry, "inc") === false)) {
				$addado[] = $entry;
			}
		}
		$d->close();
		foreach ($addado as $file) {
			copy($source_adodb.$file, $base_codebase_php_adodb_datadict.$file);
		}


		// Espetar extensões ZeuZ ao ADODB diretamente no diretório ADODB. Ai ai.
		$aqui = dirname(__FILE__);
		$source_adodb = $aqui . "/../php/adodbz/";
		$addado = null;
		$d = dir($source_adodb);
		while (false !== ($entry = $d->read())) {
			if (!(strpos($entry, "inc") === false)) {
				$addado[] = $entry;
			}
		}
		$d->close();

		foreach ($addado as $file) {
			// nada de copy, apenas symlink para facilitar a vida!
			copy($source_adodb.$file, $base_codebase_php_adodb_drivers.$file);
			//echo "Copy: ".$source_adodb.$file." to ".$base_codebase_php_adodb_drivers.$file."\n";
			//symlink($source_adodb.$file, $base_codebase_php_adodb_drivers.$file);
		}
	} else {
		PHP_GENERATOR_ENTIDADE::geraWarning("(ADODB) Não incorporando ADODB e ADODB-Z.");
	}

	if (@!$options['sufixo']){
		// Espetar as func_* lá também.
		$aqui = dirname(__FILE__);
		$source_adodb = realpath($aqui . "/../function/")."/";
		$addado = null;
		$d = dir($source_adodb);
		while (false !== ($entry = $d->read())) {
			if (!(strpos($entry, "func_") === false)) {
				$addado[] = $entry;
			}
		}
		$d->close();
		foreach ($addado as $file) {
			if (!file_exists($base_codebase_function.$file)) copy($source_adodb.$file, $base_codebase_function.$file);
		}
	}

	if (true) {
		// Incorporar DDLs de criação do banco!
		foreach ($gera->ddls as $classe => $obj) {
			gravaArquivo($obj["ddl"], $base_codebase_install_ddl.$classe.".sql", false);
			/* Adiciona a gravada pré e pós constraint */
			gravaArquivo($obj["obj"]->init."\n".(isset($obj["obj"]->seqs)?$obj["obj"]->seqs."\n":'').(isset($obj["obj"]->ents)?$obj["obj"]->ents:''), $base_codebase_install_ddl.$classe.".sql.create", false);
			gravaArquivo($obj["obj"]->rels, $base_codebase_install_ddl.$classe.".sql.alter", false);

			/* Agora versão de create separado em só tables e só constraints */
			gravaArquivo($obj["obj"]->init."\n".(isset($obj["obj"]->seqs)?$obj["obj"]->seqs."\n":'').(isset($obj["obj"]->ents_only)?$obj["obj"]->ents_only:''), $base_codebase_install_ddl.$classe.".sql.create.tables", false);
			gravaArquivo((isset($obj["obj"]->cns_only)?$obj["obj"]->cns_only:''), $base_codebase_install_ddl.$classe.".sql.create.constraints", false);
		}
	}

}


function &ndocGetRoot() {
	global $ndoc_root_element;
	return $ndoc_root_element;
}

function &ndocGetDoc() {
	global $ndoc;
	return $ndoc;
}

function ndocEmpilhaClass($nome, &$class) {
	global $array_ndoc_class, $ndoc, $array_ndoc_class_props, $array_ndoc_class_meths;
	$props = $ndoc->create_element("Props");
	$meths = $ndoc->create_element("Meths");
	$class->append_child($props);
	$class->append_child($meths);
	$array_ndoc_class[$nome] =& $class;
	$array_ndoc_class_props[$nome] =& $props;
	$array_ndoc_class_meths[$nome] =& $meths;
}

function &ndocGetClass($nome) {
	global $array_ndoc_class;
	return $array_ndoc_class[$nome]["class"];
}

function &ndocGetClassProps($nome) {
	global $array_ndoc_class_props;
	return $array_ndoc_class_props[$nome];
}

function &ndocGetClassMeths($nome) {
	global $array_ndoc_class_meths;
	return $array_ndoc_class_meths[$nome];
}


function &ndocCreateMeth($nome, $methname=null, $returns=null, $desc=null) {
	global $ndoc;
	$meths =& ndocGetClassMeths($nome);
	$meth = $ndoc->create_element("Meth");
	/* Tira os parenteses */
	$methname = str_replace("()", "", $methname);
	if ($methname) $meth->set_attribute("nome", $methname);
	if ($returns) $meth->set_attribute("returns", $returns);
	if ($desc) $meth->set_attribute("desc", consertaEncoding($desc));
	$meths->append_child($meth);
	return $meth;
}

function &ndocCreateParam(&$meth, $nomeparam, $type, $desc=null) {
	global $ndoc;
	$params = $meth->get_elements_by_tagname("Params");
	if (count($params) == 0) {
		$params = $ndoc->create_element("Params");
		$meth->append_child($params);
	} else {
		$params = $params[0];
	}
	$param = $ndoc->create_element("Param");
	$param->set_attribute("nome", $nomeparam);
	$param->set_attribute("type", $type);
	if ($desc) $param->set_attribute("desc", consertaEncoding($desc));
	$params->append_child($param);
	return $param;
}

function consertaEncoding($x) {
	return removeaccents($x);
}

function removeaccents($string) {
	return strtr($string, "ŠŒŽšœžŸ¥µÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýÿ", "SOZsozYYuAAAAAAACEEEEIIIIDNOOOOOOUUUUYsaaaaaaaceeeeiiiionoooooouuuuyy");
}


?>
