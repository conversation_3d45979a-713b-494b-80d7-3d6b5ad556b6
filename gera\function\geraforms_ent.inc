<?php
/**************************************************************************************************************************
** Base do gerador de entidades; parseia tudo etc. O trabalho sujo ficam paras as classes que extendem essa.
**************************************************************************************************************************/
class FORM_GENERATOR_ENTIDADE {
	var $nome_log;
	var $nome_fis;
	var $nome_classe_base;
	var $nome_classe_rel;
	var $nome_classe_final;
	var $nome_classe_base_list;
	var $nome_classe_rel_list;
	var $nome_classe_final_list;
	var $aster, $astercompleto;
	var $campos, $campos_pk, $campos_nao_pk; // array de campos
	var $parents;
	var $children;
	var $uniques;
	var $lista_funcs;
	var $sufixo='';
	var $namespace='';

	var $flash_nome_classe_final;
	var $flash_nome_classe_base;
	var $flash_nome_classe_final_full;
	var $flash_nome_classe_base_full;

	function __construct($ent, $sufx='', $options=null) {
		if (!$options) $this->options=array(); else $this->options=$options;
		$this->sufixo = $sufx;
		$this->fazNomes($ent);
		$this->populaXMLBase();
		$this->fazCampos($ent);
		$this->fazUniques($ent);
		$this->fazInversions($ent);
	}


	function geraForms(){
		//$this->preparaRelats();
		$cospeDropFunctions=null;
		$cospeDropFunctions=array();
		$ret=$this->geraVars($this->campos_pk, $this->campos_nao_pk);
		$ret.=$this->geraSwitchAction($this->campos_pk, $this->campos_nao_pk);
		$ret.=$this->geraCospeForm($this->campos_pk, $this->campos_nao_pk, $this->geraCamposForm($this->campos_pk, true).$this->geraCamposForm($this->campos_nao_pk, false));
		$ret.=$this->geraCospeLista($this->campos_pk, $this->campos_nao_pk);
		return $ret;
		return
		$this->geraVars($this->campos_pk, $this->campos_nao_pk).
		$this->geraSwitchAction($this->campos_pk, $this->campos_nao_pk).
		$this->geraCospeForm($this->campos_pk, $this->campos_nao_pk, $this->geraCamposForm($this->campos_pk, true).$this->geraCamposForm($this->campos_nao_pk, false)).
		$this->geraCospeLista($this->campos_pk, $this->campos_nao_pk)."";
	}

	function populaXMLBase() {
		$root = &ndocGetRoot();
		$ndoc = &ndocGetDoc();
		$class = $ndoc->create_element("Classe");
		$class->set_attribute("nomefis", $this->nome_fis);
		$class->set_attribute("nomelog", $this->nome_classe_final);
		$root->append_child($class);
		ndocEmpilhaClass($this->nome_classe_final, $class);

		$classl = $ndoc->create_element("Classe");
		$classl->set_attribute("nomefis", $this->nome_fis);
		$classl->set_attribute("nomelog", $this->nome_classe_final_list);
		$root->append_child($classl);
		ndocEmpilhaClass($this->nome_classe_final_list, $classl);

	}

	function achaCampoDataIni() {
		foreach ($this->campos as $cc) {
			if ($cc->getStringTipagem() == "\"Date\"") {
				if ( substr_count(strtoupper($cc->nome_fis), "_DAT_HR_INI") > 0) {
					return $cc;
				} else {
				}
			} else {
			}
		}
		return null;
	}

	function achaCampoDataMod() {
		foreach ($this->campos as $cc) {
			if ($cc->getStringTipagem() == "\"Date\"") {
				if ( substr_count(strtoupper($cc->nome_fis), "_DAT_HR_MOD") > 0) {
					return $cc;
				} else {
				}
			} else {
			}
		}
		return null;
	}

	function geraWarning($texto) {
		$cols = $_ENV["COLUMNS"]?$_ENV["COLUMNS"]:80;

		if (php_sapi_name() == "cli") {
			/* Desabilita essa frescura!
			$tmp = explode("\n", wordwrap($texto, ($cols-5)));
			$i = 0;
			foreach ($tmp as $linha) {
				if ($i==0) {
					echo "**** $linha\n";
				} else {
					echo "     $linha\n";
				}
				$i++;
			}
			echo "\n";
			 */
			echo "**** $texto\n";


		} else {
			echo nl2br("<LI>$texto</LI>");
		}
	}

	function fazNomes($ent) {
		$this->nome_log = strtoupper($ent->get_Attribute("NomeLogico"));
		$this->nome_fis = strtoupper($ent->get_Attribute("NomeFisico"));
		// nome da classe base...
		$this->nome_classe_base = $this->getNomePHP($this->nome_log)."_BASE";
		$this->nome_classe_base_list = $this->getNomePHP($this->nome_log)."_BASE_LIST";
		// nome da classe rel...
		$this->nome_classe_rel = $this->getNomePHP($this->nome_log)."_REL";
		$this->nome_classe_rel_list = $this->getNomePHP($this->nome_log)."_REL_LIST";
		// nome da final...
		$this->nome_classe_final = $this->getNomePHP($this->nome_log);
		$this->nome_classe_final_list = $this->getNomePHP($this->nome_log)."_LIST";
	}

	function fazCampos($ent) {
		// Itera pelos campos, gerando um array de objetos.
		$campos = $ent->get_elements_by_tagname("Campo");
		for ($q=0; $q < count($campos); $q++) {
			$campo = $campos[$q];
			$this->campos[] = new PHP_GENERATOR_CAMPO($campo, $this->nome_fis);
		}

		/* Ordenar $this->campos, utilizando ordenação consistente */
		usort($this->campos, "ordenacaoConsistenteCampos");

		/* alimenta o XML */
		$ndoc = &ndocGetDoc();
		$props = &ndocGetClassProps($this->nome_classe_final);
		foreach ($this->campos as $cam) {
			$prop = $ndoc->create_element("Prop");
			$prop->set_attribute("nomefis", $cam->nome_fis);
			$prop->set_attribute("nomelog", consertaEncoding($cam->nome_log));
			$prop->set_attribute("tipo", consertaEncoding($cam->getTipagemLimpo()));
			if ($cam->null_campo) $prop->set_attribute("NotNull", "true");
			if ($cam->ident_campo) $prop->set_attribute("AutoGen", "true");
			$props->append_child($prop);
		}

		// Busca Primary key. // Pode existir apenas uma primary key, com n (n>0) campos.
		$this->campos_pk = null;
		$this->campos_nao_pk = null;

		if ($pks = $ent->get_Elements_By_TagName("ConstraintPK")) {
			$pk = $pks[0];
			$campos = $pk->get_Elements_By_TagName("Coluna");
			for ($rr = 0; $rr < count($this->campos); $rr++) {
				$cc = &$this->campos[$rr];
				// para cada campo da tabela
				$ehpk = false;
				for ($q=0; $q < count($campos); $q++) {
					// procura se o tal campo é da pk ou não
					$campo = $campos[$q];
					$nomecampopk = $campo->get_Attribute("Nome");
					if (strtoupper($nomecampopk) == strtoupper($cc->nome_fis)) $ehpk = true;
				}
				if ($ehpk) {
					$this->campos_pk[] = &$cc;
				} else {
					$this->campos_nao_pk[] = &$cc;
				}
			}
		}

		/* Ordenar $this->campos_pk e campos_nao_pk, utilizando ordenação consistente */
		if ($this->campos_pk) usort($this->campos_pk, "ordenacaoConsistenteCampos");
		if ($this->campos_nao_pk) usort($this->campos_nao_pk, "ordenacaoConsistenteCampos");
	}

	function fazUniques(&$ent) {
		// Busca constraints de unique. podem existir n uniques, com w (w>0) campos cada.
		$this->uniques = null;
		if ($pks = $ent->get_Elements_By_TagName("ConstraintUnique")) {
			for ($q=0; $q < count($pks); $q++) {
				$pk = $pks[$q];
				$campos = null;
				/* @TODO: reescrever, de modo a iterar pelo $this->campos, e pegar os campos na ordem certa (ordem do $this->campos) */
				$campos = $pk->get_Elements_By_TagName("Coluna");
				$campo_obj=null;
				for ($w=0; $w < count($campos); $w++) {
					$campo = $campos[$w];
					$campo_id = $campo->get_Attribute("CampoID");
					$campo_nome = $campo->get_Attribute("Nome");
					$campo_obj[] =& getCampoEntidadeRef($this, strtoupper($campo_nome)); //DFP
					//$campo_obj[] =& getCampoEntidadeRef(&$this, $campo_nome);
				}
				// Ordenação.
				if ($campo_obj) usort($campo_obj, "ordenacaoConsistenteCampos");
				$this->uniques[] = $campo_obj;
			}
		}
	}

	function fazInversions(&$ent) {
		// Busca constraints de inversion. podem existir n uniques, com w (w>0) campos cada.
		$this->inversions = null;
		if ($pks = $ent->get_Elements_By_TagName("ConstraintInversion")) {
			for ($q=0; $q < count($pks); $q++) {
				$pk = $pks[$q];
				/* @TODO: reescrever, de modo a iterar pelo $this->campos, e pegar os campos na ordem certa (ordem do $this->campos) */
				$campos = $pk->get_Elements_By_TagName("Coluna");
				$campo_obj=null;
				$campo_obj["nome"]=$pk->get_Attribute("Nome");
				for ($w=0; $w < count($campos); $w++) {
					$campo = $campos[$w];
					$campo_id = $campo->get_Attribute("CampoID");
					$campo_nome = $campo->get_Attribute("Nome");
					$campo_obj["campos"][] =& getCampoEntidadeRef($this, strtoupper($campo_nome)); //DFP
					//$campo_obj["campos"][] =& getCampoEntidadeRef(&$this, $campo_nome);
				}
				// Ordenação.
				if ($campo_obj) usort($campo_obj["campos"], "ordenacaoConsistenteCampos");
				$this->inversions[] = $campo_obj;
			}
		}
	}

	function getNomePHP($nome) {
		return str_replace(" ", "_", str_replace("-", "_", $nome));
	}

	function getComoInstanciarOne() {
		//echo("----> getComoInstanciarOne: &FACTORY".$this->sufixo."::" . $this->getNomePHP($this->nome_classe_final)."()");
		return "&FACTORY".$this->sufixo."::" . $this->getNomePHP($this->nome_classe_final)."()";
	}

	function getComoInstanciarList() {
		//echo("----> getComoInstanciarList: &FACTORY".$this->sufixo."::" . $this->getNomePHP($this->nome_classe_final_list)."()");
		return "&FACTORY".$this->sufixo."::" . $this->getNomePHP($this->nome_classe_final_list)."()";
	}

	function padTo($string, $len) {
		return $string . str_repeat(" ",$len - strlen($string));
	}

	function preparaRelats(){
		echo($this->nome_fis."\n");
		if (is_array($this->relats_child)){
			echo("Relacionadas:\n");
			foreach($this->relats_child as $relat){
				echo($relat->relat->nome_classe_final." - ".$relat->apoio->nome_classe_final."\n");
				if (!is_array($relat->cols_apoio)) continue;
				$nom_ext = $relat->frase?"_".str_replace(" ", "_", strtoupper($relat->frase)):"";
				$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext."()";
				$pks = null;
				foreach ($relat->cols_relat as $col) {
					$pks[] = "\$item->".$col->getNome();
					$this->camposFk[$col->getNome()]=$col->getNome();
				}
				$pks = implode($pks, ",");
				$instanciar=$relat->apoio->getComoInstanciarOne();
				$relat->nomeFunc=$nomefunc;
				$relat->paraInstanciar=$instanciar;
				$relat->itempks=$pks;
				$relat->nomeItem="\$item_".$relat->apoio->nome_classe_final.$nom_ext;
				$show="";
				if (is_array($relat->apoio->uniques)){
					$sep="";
					foreach ($relat->apoio->uniques as $unique){
						foreach ($unique as $campoUnique){
							if ($campoUnique->null_campo){
								$nome=$campoUnique->getNome($relat->apoio->nome_fis);
								$show.=$sep.$relat->nomeItem."->".$nome;
								$sep="&nbsp;|&nbsp;";
							}
						}
					}
				}
				if ($show==""){
					if (is_array($relat->apoio->campos_nao_pk)){
						$sep="";
						foreach ($relat->apoio->campos_nao_pk as $naopk){
							if ($naopk->null_campo){
								$nome=$naopk->getNome($relat->apoio->nome_fis);
								$show.=$sep.$relat->nomeItem."->".$nome;
								$sep="&nbsp;|&nbsp;";
							}
						}
					}
				}
				//echo($show."---------\n");
				if ($show=="")$show="\$this->";
				$relat->show=$show;

			}
		}
	}

	function getCospeDrop($campo){
		$nomeCampo=$campo->getNome($campo->nome_fis);
		global $cospeDropFunctions;
		$nomeFunc="cospeDrop_".$nomeCampo;
		$campoCod=null;
		if ($this->campos_pk){
			foreach($this->campos_pk as $pk){
				if ($campoCod) return null;
				$campoCod=$pk->getNome();
			}
		}
		$show=null;
		$sep="";
		if ($this->uniques && is_array($this->uniques)){
			foreach ($this->uniques as $unique){
				foreach($unique as $campoUnique){

					$show.=$sep."<?=\$item->".$campoUnique->getNome()."?>";
					$sep="&nbsp;|&nbsp;";
				}
			}
		}
		if (!$show && $this->campos_nao_pk && is_array($this->campos_nao_pk)){
			foreach ($this->campos_nao_pk as $notPk){
				$show.=$sep."<?=\$item->".$notPk->getNome()."?>";
				$sep="&nbsp;|&nbsp;";
			}
		}
		//echo($show."\r\n");
		if (!$show) return '';
		$dropFunc="
function ".$nomeFunc."(\$val){
	\$lista=".$this->getComoInstanciarList().";
	\$lista->getTodos();
	\$num=\$lista->getQuantos();
	if (\$num > 0){
		if (\$num <= 5) \$size=\" size=<?=\$num?>\"; else \$size=\"\";
		?><select name=\"".$nomeCampo."\"<?=\$size?>><option value=\"\">Selecione</option><?
		while (\$item=\$lista->getNextItem()){
			?><option value=\"<?=\$item->".$campoCod."?>\" <?=(\$val==\$item->".$campoCod."?\" SELECTED\":\"\")?>>".$show."</option><?
		}
		?></select><?
	}else{
		?>Nenhum item cadastrado<?
	}
}
";
		$cospeDropFunctions[]=$dropFunc;
		return "<?=".$nomeFunc."(\$item->".$nomeCampo.")?>";
	}

	function getCampoFormFK($campo){
		if (isset($this->relats_child) && is_array($this->relats_child)){
			foreach($this->relats_child as $relat){
				foreach($relat->cols_apoio as $colRelat){
					if ($campo->nome_fis==$colRelat->nome_fis){
						echo($this->nome_fis." - ".$relat->apoio->nome_fis."\n");
						return $relat->apoio->getCospeDrop($campo);
					}
				}
			}
		}
		return null;
	}

	function showRelat($campo){

	}

	function geraCamposForm($arr, $isPk){
		$lte=false;
		if (isset($this->options['lte'])) $lte=true;
		if (!is_array($arr) ) {
			return null;
		}
		$tab="\t";
		$nl="\r\n";
		$s = "";
		foreach ($arr as $c) {
			$nome=$c->getNome();
			$titulo='$txt_'.$c->getNome($this->nome_fis).' ';
			if (!$t=$this->getCampoFormFK($c)){
				if ($isPk) {
					$t="<?=\$item->".$nome."?><input type=\"hidden\" name=\"".$nome."\" id=\"".$nome."\" value=\"<?=\$item->".$nome."?>\">";
				}else{
					switch ($c->dt_campo){
						case 'BIGINT':
						case 'INTEGER':
						case 'DOUBLE PRECISION':
						case 'FLOAT':
							if ($lte){
								$t="<div class=\"form-group\">
	<label><?=".$titulo."?></label>
	<input class=\"form-control\" type=\"text\" name=\"".$nome."\" id=\"".$nome."\" value=\"<?=@\$item->".$nome."?>\">
</div>
";
							}else{
								//echo "numeric\r\n";
								$t="<input class=\"form-control\" type=\"text\" name=\"".$nome."\" id=\"".$nome."\" value=\"<?=@\$item->".$nome."?>\">";
							}
							break;
						case 'DATE':
							//echo "date\r\n";
							if (strpos(strtoupper($nome), "DAT_HR_INI")!==false){
								$t="<?=@\$dia_".$nome."?>/<?=@\$mes_".$nome."?>/<?=@\$ano_".$nome."?>&nbsp;<?=@\$hora_".$nome."?>";
							}else{
								if ($lte){
									$t="<div class=\"form-group\">
	<label><?=".$titulo."?></label>
	<input class=\"form-control\" type=\"text\" name=\"dia_".$nome."\" id=\"dia_".$nome."\" value=\"<?=@\$dia_".$nome."?>\" size=\"4\" maxlength=\"2\">/<input type=\"text\" name=\"mes_".$nome."\" id=\"mes_".$nome."\" value=\"<?=@\$mes_".$nome."?>\" size=\"4\" maxlength=\"2\">/<input type=\"text\" name=\"ano_".$nome."\" id=\"ano_".$nome."\" value=\"<?=@\$ano_".$nome."?>\" size=\"6\" maxlength=\"4\">&nbsp;(dd/mm/aaaa)&nbsp;<input type=\"text\" name=\"hora_".$nome."\" id=\"hora_".$nome."\" value=\"<?=@\$hora_".$nome."?>\" size=\"10\" maxlength=\"8\">&nbsp;(hh:mm:ss)
</div>";
								}else{
									$t="<input class=\"form-control\" type=\"text\" name=\"dia_".$nome."\" id=\"dia_".$nome."\" value=\"<?=@\$dia_".$nome."?>\" size=\"4\" maxlength=\"2\">/<input type=\"text\" name=\"mes_".$nome."\" id=\"mes_".$nome."\" value=\"<?=@\$mes_".$nome."?>\" size=\"4\" maxlength=\"2\">/<input type=\"text\" name=\"ano_".$nome."\" id=\"ano_".$nome."\" value=\"<?=@\$ano_".$nome."?>\" size=\"6\" maxlength=\"4\">&nbsp;(dd/mm/aaaa)&nbsp;<input type=\"text\" name=\"hora_".$nome."\" id=\"hora_".$nome."\" value=\"<?=@\$hora_".$nome."?>\" size=\"10\" maxlength=\"8\">&nbsp;(hh:mm:ss)";
								}
							}
							break;
						case 'BIT':
							//echo "bit\r\n";
							$t="<input type=\"checkbox\" name=\"".$nome."\" value=\"true\"<?=(\$item->".$nome."==true?\" checked\":\"\")?>>";
							//break;
							if ($c->null_campo){
								if ($lte){
									$t='<div class="form-group">';
									$t.='\t<label class="control-label"><?='.$titulo.'?></label>';
									$t.="<div class=\"radio\"><label><input type=\"radio\" name=\"".$nome."\" value=\"\"<?=(!isset(\$item->".$nome.")?\" checked\":\"\")?>>&nbsp;Nulo</label></div>";
									$t.="<div class=\"radio\"><label><input type=\"radio\" name=\"".$nome."\" value=\"true\"<?=(\$item->".$nome."==true?\" checked\":\"\")?>>&nbsp;Verdadeiro</label></div>";
									$t.="<div class=\"radio\"><label><input type=\"radio\" name=\"".$nome."\" value=\"false\"<?=(\$item->".$nome."==false?\" checked\":\"\")?>>&nbsp;Falso</label></div>";
									$t.='</div>';
								}else{
									$t="<input type=\"radio\" name=\"".$nome."\" value=\"\"<?=(!isset(\$item->".$nome.")?\" checked\":\"\")?>>Nulo&nbsp;";
									$t.="<input type=\"radio\" name=\"".$nome."\" value=\"true\"<?=(\$item->".$nome."==true?\" checked\":\"\")?>>Verdadeiro&nbsp;";
									$t.="<input type=\"radio\" name=\"".$nome."\" value=\"false\"<?=(\$item->".$nome."==false?\" checked\":\"\")?>>Falso";
								}
							}else{
								if ($lte){
									$t='<div class="form-group"><label><?='.$titulo.'?></label>';
									$t.="<div class=\"radio\"><label><input type=\"radio\" name=\"".$nome."\" value=\"true\"<?=(\$item->".$nome."==true?\" checked\":\"\")?>>&nbsp;Verdadeiro</label></div>";
									$t.="<div class=\"radio\"><label><input type=\"radio\" name=\"".$nome."\" value=\"false\"<?=(\$item->".$nome."==false || !isset(\$".$nome.")?\" checked\":\"\")?>>&nbsp;Falso</label></div>";
									$t.='</div>';
								}else{
									$t="<input type=\"radio\" name=\"".$nome."\" value=\"true\"<?=(\$item->".$nome."==true?\" checked\":\"\")?>>Verdadeiro&nbsp;";
									$t.="<input type=\"radio\" name=\"".$nome."\" value=\"false\"<?=(\$item->".$nome."==false || !isset(\$".$nome.")?\" checked\":\"\")?>>Falso";
								}
							}
							break;
						case 'TEXT':
							//echo "text\r\n";
							if ($lte){
								$t=$nl.'<div class="form-group">';
								$t.=$nl.$tab.'<label for="'.$nome.'" class="control-label"><?='.$titulo.'?></label>';
								$t.=$nl.$tab."<textarea cols=\"40\" rows=\"10\" name=\"".$nome."\" id=\"".$nome."\"><?=@\$item->".$nome."?></textarea>";
								$t.=$nl.'</div>';
							}else{
								$t="<textarea cols=\"40\" rows=\"10\" name=\"".$nome."\"><?=@\$item->".$nome."?></textarea>";
							}
							break;
						default:
						case 'VARCHAR':
							//echo "varchar\r\n";
							if ($c->dtl_campo>255){
								if ($lte){
									$t=$nl.'<div class="form-group">';
									$t.=$nl.$tab.'<label for="'.$nome.'" class="control-label"><?='.$titulo.'?></label>';
									$t.=$nl.$tab."<textarea cols=\"40\" rows=\"10\" name=\"".$nome."\" id=\"".$nome."\"><?=@\$item->".$nome."?></textarea>";
									$t.=$nl.'</div>';
								}else{
									$t="<textarea cols=\"40\" rows=\"10\" name=\"".$nome."\"><?=@\$item->".$nome."?></textarea>";
								}
							}else{
								if ($lte){
									$t=$nl."<div class=\"form-group\">
	<label><?=".$titulo."?></label>
	<input class=\"form-control\" type=\"text\" name=\"".$nome."\" id=\"".$nome."\" value=\"<?=@\$item->".$nome."?>\" maxlength=\"".$c->dtl_campo."\">
</div>";
								}else{
									$t="<input type=\"text\" name=\"".$nome."\" id=\"".$nome."\" value=\"<?=@\$item->".$nome."?>\" maxlength=\"".$c->dtl_campo."\">";
								}
							}
							break;
					}
				}
			}else{
				if (is_array($this->relats_child)){
					foreach($this->relats_child as $relat){
						foreach($relat->cols_apoio as $colRelat){
							if ($c->nome_fis==$colRelat->nome_fis){
								//echo "rels\r\n";
								//echo($this->nome_fis." - ".$relat->apoio->nome_fis."\n");
								$titulo='$txt_'.$c->getNome($relat->apoio->nome_fis);
								//return $relat->apoio->getCospeDrop($campo);
							}
						}
					}
				}
			}
		if($isPk){
				$s.= "<?
if(\$item){
?>
";
		}
		if ($lte){
			$s.=$nl.'<div class="col-sm-6">'.$t.$nl.'</div>';
		}else{
				$s.="	<tr>
		<td><?= ".$titulo." ?>:</td>
		<td>".$t."</td>
	</tr>
";
		}
		if($isPk) $s.= "
<?
}
?>
			";
		}
		return $s;
	}

	function geraGetRequest($arr){
		if (!is_array($arr) ) {
			return null;
		}
		$s = "";
		foreach ($arr as $c) {
			$nome=$c->getNome($this->nome_fis);
			switch ($c->dt_campo){
				case 'VARCHAR':
				case 'TEXT':
				case 'BIGINT':
				case 'INTEGER':
				case 'DOUBLE PRECISION':
				case 'FLOAT':
					$s.="\$".$nome." = \$_REQUEST[\"".$nome."\"];\n";
					break;
				case 'DATE':
					$s.="\$dia_".$nome." = \$_REQUEST[\"dia_".$nome."\"];\n".
					"\$mes_".$nome." = \$_REQUEST[\"mes_".$nome."\"];\n".
					"\$ano_".$nome." = \$_REQUEST[\"ano_".$nome."\"];\n".
					"\$hora_".$nome." = \$_REQUEST[\"hora_".$nome."\"];\n";
					$s.="\$".$nome." = \$ano_".$nome.".\"-\".\$mes_".$nome.".\"-\".\$dia_".$nome.".(\$hora_".$nome."?\" \".\$hora_".$nome.":\"\");\n";
					//$s.="\/\/data";
					break;
				case 'BIT':
					$s.="if (\$_REQUEST[\"".$nome."\"] && \$_REQUEST[\"".$nome."\"]!='') \$".$nome." = (\$_REQUEST[\"".$nome."\"]=='true');\n";
					break;
			}
		}
		return $s;
	}

	function geraCospeForm($arPks, $arNotPks, $miolo){
		global $cospeDropFunctions;
		$lte=false;
		if (isset($this->options['lte'])) $lte=true;
		$cl="";
		$sep="";
		if ($arPks){
			foreach ($arPks as $c){
				$cl.=$sep."\$item->".$c->getNome($this->nome_fis);
				$sep=" && ";
			}
		}
		$globs='$tituloForm, $tituloLista';
		foreach ($arPks as $c){
			$globs.=', $txt_'.$c->getNome($this->nome_fis);
		}
		if (isset($arNotPks)){
			foreach ($arNotPks as $c){
				$globs.=', $txt_'.$c->getNome($this->nome_fis);
			}
		}
		$s="function cospeForm(\$item=null){
	global ".$globs.";
	if (\$item){
";
		if ($arPks){
			foreach ($arPks as $c){
				if ($c->dt_campo=='DATE'){
					$nome=$c->getNome($this->nome_fis);
					$s.="
		if (@\$item->".$nome."){
			\$d=adodb_getDate(\$item->".$nome.");
			\$dia_".$nome." = \$d['mday'];
			\$mes_".$nome." = \$d['mon'];
			\$ano_".$nome." = \$d['year'];
			\$hora_".$nome." = \$d['hours'].\":\".\$d['minutes'].\":\".\$d['seconds'];
		}";
				}
			}
		}
		if ($arNotPks){
			foreach ($arNotPks as $c){
				if ($c->dt_campo=='DATE'){
					$nome=$c->getNome($this->nome_fis);
					$s.="
		if (@\$item->".$nome."){
			\$d=adodb_getDate(\$item->".$nome.");
			\$dia_".$nome." = \$d['mday'];
			\$mes_".$nome." = \$d['mon'];
			\$ano_".$nome." = \$d['year'];
			\$hora_".$nome." = \$d['hours'].\":\".\$d['minutes'].\":\".\$d['seconds'];
		}";
				}
			}
		}
		if ($lte){
			$s.='
}
?>
<link rel="stylesheet" type="text/css" href="/scripts/forms/forms.css" />
<script language="JavaScript" type="text/javascript" src="/scripts/forms/forms.js"></script>
<div id="containergeral">
	<div class="containerForm" id="containerForm">
		<div id="divShowHideForm"><a id="lnkShowHide" href="javascript:void(0);" onclick="$(\'#rowform\').toggle();if ($(\'#rowform\').is(\'visible\')){$(this).html(\'Ocultar formulário\')}else{$(this).html(\'Exibir formulário\')} ">Ocultar Formulário</a></div>
		<div class="row" id="rowform">
			<form action="<?=$_SERVER["PHP_SELF"]?>" method="post" name="form'.$this->nome_fis.'" id="form'.$this->nome_fis.'">
			<?=montaHidden()?>
			<input type="hidden" name="action" value="save_go">
			<div class="col-sm-12 tituloForm" id="tituloForm"><?= $tituloForm ?></div>
'.$miolo.'
			<div class="col-sm-12 text-right"><input type="submit" name="submeter" id="submeter" value="Salvar">&nbsp;<?if (@'.$cl.'){?><input type="button" name="deletar" id="deletar" value="Excluir" onclick="if (confirm(\'Tem certeza de que deseja excluir?\')) {document.form'.$this->nome_fis.".action.value='delete_go';document.form".$this->nome_fis.'.submit();}"><?}?></div>
			</form>
		</div>
	</div>
	<div class="containerLista" id="containerLista">
		<?=cospeLista()?>
	</div>
</div>
<?
}
';
		}else{
			$s.='
}
?>

<link rel="stylesheet" type="text/css" href="/scripts/forms/forms.css" />
<script language="JavaScript" type="text/javascript" src="/scripts/forms/forms.js"></script>
<div id="containergeral">
	<div class="containerForm" id="containerForm">
		<div id="divShowHideForm"><a id="lnkShowHide" href="javascript:void(0);" onclick="hideShowForm(\'tabelaForm\',\'lnkShowHide\')">Ocultar Formulário</a></div>
		<table cellspacing="0" class="tabelaForm" id="tabelaForm">
			<form action="<?=$_SERVER["PHP_SELF"]?>" method="post" name="form'.$this->nome_fis.'" id="form'.$this->nome_fis.'">
			<?=montaHidden()?>
			<input type="hidden" name="action" value="save_go">
			<tr>
				<td class="tituloForm" colspan="2" id="tituloForm"><?= $tituloForm ?></td>
			</tr>
'.$miolo.'
			<tr>
				<td>&nbsp;</td>
				<td><input type="submit" name="submeter" id="submeter" value="Salvar">&nbsp;<?if (@'.$cl.'){?><input type="button" name="deletar" id="deletar" value="Excluir" onclick="if (confirm(\'Tem certeza de que deseja excluir?\')) {document.form'.$this->nome_fis.".action.value='delete_go';document.form".$this->nome_fis.'.submit();}"><?}?></td>
			</tr>
			</form>
			<tr>
		</table>
	</div>
	<div class="containerLista" id="containerLista">
		<?=cospeLista()?>
	</div>
</div>
<?
}
';
		}
		if (is_array($cospeDropFunctions)){
			foreach($cospeDropFunctions as $dropFunctions){
				$s.=$dropFunctions."\n";
			}
		}
		$cospeDropFunctions=null;
		$cospeDropFunctions=array();
		return $s;
	}

	function geraVars($arPks, $arNotPks){
		$s="
// EDITAR OS TÍTULOS DOS CAMPOS E TABELAS
\$tituloForm=\"Formulário de cadastro de ".ucwords(strtolower($this->nome_log))."\";
\$tituloLista=\"Registros de ".ucwords(strtolower($this->nome_log))."\";
";
		foreach($arPks as $c){
			$s.="
\$txt_".$c->getNome($this->nome_fis)."=\"".$c->getNome($this->nome_fis)."\";";
		}
		if (isset($arNotPks)){
			foreach($arNotPks as $c){
				$s.="
	\$txt_".$c->getNome($this->nome_fis)."=\"".$c->getNome($this->nome_fis)."\";";
			}
		}
		return $s;
	}

	function geraSwitchAction($arPks, $arNotPks){
		if (!is_array($arPks)) return null;
		$parmLoad="";
		$sep="";
		foreach($arPks as $c){
			$parmLoad.=$sep."\$_REQUEST[\"".$c->getNome($this->nome_fis)."\"]";
			$sep=", ";
		}

		$s='
$action=$_REQUEST["action"];
switch($action){
	case "save_go":
		if (salvar()) header("Location: ".montaHref($_SERVER["PHP_SELF"]));
		break;
	case "insert":
		?><a href="<?=montaHref($_SERVER["PHP_SELF"])?>">voltar</a><br>
<?
		$item='.$this->getComoInstanciarOne().';
		cospeForm($item);
		break;
	case "edit":
		$item='.$this->getComoInstanciarOne().';
		if ($item->loadByPk('.$parmLoad.')){
			cospeForm($item);
		}else{
			echo("Não foi possível carregar o objeto");
		}
		break;
	default:
		cospeForm();
		break;
}

function salvar(){
	if (';
$sep="";
foreach($arPks as $c){
	$s.=$sep.'$_REQUEST["'.$c->getNome($this->nome_fis).'"]';
	$sep=" && ";
}
$s.='){
		$item='.$this->getComoInstanciarOne().';
		if ($item->loadByPk(';
$sep="";
foreach($arPks as $c){
	$s.=$sep.'$_REQUEST["'.$c->getNome($this->nome_fis).'"]';
	$sep=", ";
}
$s.=")){
";
if ($arNotPks){
	foreach($arNotPks as $c){
		$s.='			$item->'.$c->getNome($this->nome_fis).' = $_REQUEST["'.$c->getNome($this->nome_fis).'"];
';
	}
}
$s.='			return $item->update();
		}else{
			return false;
		}
	}else{
		$item='.$this->getComoInstanciarOne().';
';
if ($arNotPks){
	foreach($arNotPks as $c){
		$s.='		$item->'.$c->getNome($this->nome_fis).' = $_REQUEST["'.$c->getNome($this->nome_fis).'"];
';
	}
}
$s.='		return $item->insert();
	}
}
';
		return $s;
	}

	function geraCospeLista($arPks, $arNotPks){
		$cols=array();
		$qs="";
		$ordDefaultPk='';
		$ordDefaultUk='';
		$chave='';
		$sepChave='';
		if ($arPks){
			foreach($arPks as $c){
				if ($ordDefaultPk=='') $ordDefaultPk=$c->getNome($this->nome_fis);
				$qs.="&".$c->getNome($this->nome_fis)."=<?=\$item->".$c->getNome($this->nome_fis)."?>";
				$chave.=$sepChave."\$item->".$c->getNome($this->nome_fis);
				$sepChave='."|".';
			}
		}
		if (isset($this->unique) && is_array($this->unique)){
			foreach ($this->unique as $unique){
				foreach($unique as $campoUnique){
					$cols[]=$c;
					if ($ordDefaultUk=='') $ordDefaultUk=$c->getNome($this->nome_fis);
				}
			}
		}
		if (count($cols)==0){
			if ($arNotPks){
				foreach($arNotPks as $c){
					if ($c->null_campo) {
						$cols[]=$c;
					}
				}
			}
		}
		$cols=array_merge($arPks, $cols);
		$numCols=sizeOf($cols);
		$globs='$tituloForm, $tituloLista';
		foreach ($arPks as $c){
			$globs.=', $txt_'.$c->getNome($this->nome_fis);
		}
		if (isset($arNotPks)){
			foreach ($arNotPks as $c){
				$globs.=', $txt_'.$c->getNome($this->nome_fis);
			}
		}
$s='
function cospeLista(){
	global '.$globs.';
	$lista='.$this->getComoInstanciarList().';
	if (!$ord=$_REQUEST["ord"]) $ord="'.($ordDefaultUk==''?$ordDefaultPk:$ordDefaultUk).'";
	if (!$pag=$_REQUEST["pag"]) $pag=1;
	if (!$desc=$_REQUEST["desc"]) $desc="";
	$porPag=50;
	$qs="&ord=".$ord."&desc=".$desc;
	$lista->getTodos();
	if ($lista->getQuantos()>0){
		$arItens=array();
		while ($item=$lista->getNextItem()){
			$chave='.$chave.';
			$arItens[strtolower($item->$ord).$chave]=$item;
		}
		if ($_REQUEST["desc"]) krsort($arItens); else ksort($arItens);
		$ndx=getIndice($arItens, $ord, $porPag, $pag, $qs);
		$cols='.(sizeOf($cols)+2).';
?>
<table id="tbListaForms" cellspacing="0" class="tabelaLista">
	<tr>
		<td class="tituloLista" id="tituloLista" colspan="<?=$cols?>"><?= $tituloLista ?></td>
	</tr>
	<? if($ndx){?>
	<tr>
		<td class="trIndice" colspan="<?=$cols?>"><?=$ndx?$ndx:"&nbsp;"?></td>
	</tr>
	<? } ?>
	<tr>
';
		foreach($cols as $c){
			$s.='		<td nowrap class="cabcoluna"><?= $txt_'.$c->getNome($this->nome_fis).' ?> <?if ($ord!="'.$c->getNome($this->nome_fis).'" || ($ord=="'.$c->getNome($this->nome_fis).'" && $desc=="true")){?><a href="<?=montaHref($_SERVER["PHP_SELF"])."&ord='.$c->getNome($this->nome_fis).'"?>"><img src="/images/icn_ord_desc.gif" alt="" border="0"></a><?}?>&nbsp;<?if ($ord!="'.$c->getNome($this->nome_fis).'" || ($ord=="'.$c->getNome($this->nome_fis).'" && $desc!="true")){?><a href="<?=montaHref($_SERVER["PHP_SELF"])."&ord='.$c->getNome($this->nome_fis).'&desc=true"?>"><img src="/images/icn_ord_asc.gif" alt="" border="0"></a><?}?></td>
';
		}
$s.='		<td nowrap class="cabcoluna">Editar</td>
		<td nowrap class="cabcoluna">Del.</td>
	</tr>
<?
		$cont=0;
		foreach ($arItens as $item){
			$pagAtual=floor($cont/$porPag)+1;
			$cont++;
			if ($pagAtual<$pag) continue;
			if ($pagAtual>$pag) break;
?>
	<tr>
';
		foreach($cols as $c){
			$s.='		<td><?=$item->'.$c->getNome($this->nome_fis).'?$item->'.$c->getNome($this->nome_fis).':"&nbsp;"?></td>
';
		}
$s.='		<td align="center"><a href="<?=montaHref($_SERVER["PHP_SELF"], true)?>&action=edit'.$qs.'"><img src="/images/adm/ico_edit.gif" alt="" border="0"></a></td>
		<td align="center"><a href="javascript:void(0);" onclick="alert(\'Implemente a funcionalidade de apagar o registro\');"><img src="/images/adm/ico_delete.gif" alt="" border="0"></a></td>
	</tr>
<?
		}
?>
	<? if($ndx){?>
	<tr>
		<td class="trIndice" colspan="<?=$cols?>"><?=$ndx?$ndx:"&nbsp;"?></td>
	</tr>
	<? } ?>
</table><?
	}else{
	?><table id="tbListaForms" cellspacing="0" class="tabelaLista"><tr><td>Nenhum item cadastrado</td></tr></table><?
	}
}
';
		return $s;
	}

}
?>
