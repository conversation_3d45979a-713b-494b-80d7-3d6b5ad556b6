<?php

?>
<style>
#mapWrapper{
	width:100%;
	height: 96%;
	position:relative;
}
#divModelo{
	position:relative;
	width: 100%;
	height: 100%;
	overflow: auto;
	background-color: #f5f5f5;
}
#maptools{
	position:absolute;
	top:0;
	left:0;
	z-index:10000;
	background-color: #ffffff;
	width: 100%;
}
#info{
	border:solid 1px lightgray;
}
.mapfull{
	width:100%;
	height: 96%;
}
.sttable{
	border: dotted 1px gray;
	padding: 3px;
	background-color: #ffffff;
	z-index: 1000;
	display:inline-block;
	position: absolute;
}
.tbName{
	font-weight: bold;
	font-size: 1.2em;
}
.stcolumn{
	font-size: 0.8em;
	background-color: #ffffff;
}
.simulink {
	cursor: pointer;
	color: #0066cc;
}
.simulink:hover {
	text-decoration: underline;
}
</style>
<!-- <script src="defmodelo.js"></script> -->
<script src="fullmodelo.js"></script>
<script src="positions.json"></script>
<div id="mapWrapper">
	<div class="mapfull" id="divModelo"></div>
	<div id="maptools">
		<span class="simulink" onclick="centralizar();"><i class="fa fa-crosshairs">&nbsp;</i>Centralizar</span>
		&nbsp;|&nbsp;
		<span class="simulink" onclick="resetZoom();"><i class="fa fa-search">&nbsp;</i>Reset Zoom</span>
		&nbsp;|&nbsp;
		<span id="info"></span>
	</div>
</div>
<script>
var modelContainer;

$(window).on('load',function() {
	resize();
	modelContainer = document.getElementById('divModelo');
	
	// Add mouse move event to show coordinates
	$('#divModelo').on('mousemove', function(e){
		var x = e.pageX - $(this).offset().left;
		var y = e.pageY - $(this).offset().top;
		$('#info').html('X: ' + Math.round(x) + ' Y: ' + Math.round(y));
	});
	
	// Add enhanced mouse wheel functionality
	$('#divModelo').on('wheel', handleScrollEvent);
	
	drawTables();
	$('.sttable').each(function() {
		makeDraggable(this);
	});
	console.log("Drawing FK lines on load");
	drawFkLines();
	
	// Restore viewport settings if available
	restoreViewportSettings();
	
	$(window).on('resize', resize);
});

function resize(){
	var pos=$('#divModelo').position();
	var mwt=$('#mapWrapper').offset().top;
	var posf=$('#boxfoot').position();

	var mt=$('#maptools').outerHeight();
	var mch=$('#mapWrapper').outerHeight();
	$('#divModelo').css('height',(Math.ceil(posf.top)-mwt)+'px');
}

function loadTables(){
	// we are not loading tables from the server at this moment
	$.ajax({
		url:'ajax_modelo.php',
		data:{m:'getTables'},
		dataType:'json',
		success: function(response){
			tbDefs=response;
			drawTables();
		}
	});
}

function drawTables(){
	for(var tb in tbDefs){
		drawTable(tb, tbDefs[tb]);
	}
}

function drawTable(tb, defs){
	var tbDiv = document.createElement('div');
	tbDiv.className = 'sttable';
	// if (tb=='TB2_TELE') debugger;
	// Check if position exists in tbPositions (from positions.js)
	if (typeof tbPositions !== 'undefined' && tbPositions[tb]) {
		tbDiv.style.left = tbPositions[tb][0] + 'px';
		tbDiv.style.top = tbPositions[tb][1] + 'px';
		
		// Update the position in tbDefs to match
		defs.position = [...tbPositions[tb]];
	} else {
		// Use position from tbDefs if no saved position exists
		tbDiv.style.left = defs.position[0] + 'px';
		tbDiv.style.top = defs.position[1] + 'px';
	}
	
	tbDiv.id = tb;
	
	var colDiv = document.createElement('div');
	colDiv.className = 'tbName';
	colDiv.id = tb + '_name';
	colDiv.title = defs.logical;
	colDiv.innerHTML = tb;
	tbDiv.appendChild(colDiv);
	
	for (var col in defs.columns){
		var cDef = defs.columns[col];
		var colDiv = document.createElement('div');
		colDiv.className = 'stcolumn';
		colDiv.id = tb + '_' + col;
		colDiv.innerHTML = col;
		colDiv.title = cDef.logical + '\n' + cDef.type + '\n' + 
			(cDef?.size ? '(' + cDef.size + ')\n' : '') + 
			(cDef?.nullable ? 'nullable\n' : '') + 
			(cDef?.pk ? 'pk\n' : '') + 
			(cDef?.sequence ? 'sequence' : '');
		tbDiv.appendChild(colDiv);
	}
	
	modelContainer.appendChild(tbDiv);
	
	// Make the table draggable
	makeDraggable(tbDiv);
}

function centralizar(){
	// Center the view on the tables
	var container = $('#divModelo');
	var contentWidth = container[0].scrollWidth * currentScale;
	var contentHeight = container[0].scrollHeight * currentScale;
	var containerWidth = container.width();
	var containerHeight = container.height();
	
	container.scrollLeft((contentWidth - containerWidth) / 2);
	container.scrollTop((contentHeight - containerHeight) / 2);
}

// Function to draw FK relationship lines
function drawFkLines() {
	// First, remove all existing FK lines and indicators
	$('.fk-line').remove();
	$('.phrase-indicator, .inverse-phrase-indicator').remove();
	
	// Then redraw all FK lines
	for(var sourceTable in tbDefs) {
		var tableDef = tbDefs[sourceTable];
		for(var sourceCol in tableDef.columns) {
			var colDef = tableDef.columns[sourceCol];
			if(colDef.fk) {
				// console.log("Drawing FK line from", sourceTable, sourceCol, "to", colDef.fk.table, colDef.fk.column);
				drawFkLine(sourceTable, sourceCol, colDef.fk.table, colDef.fk.column);
			}
		}
	}
}

// Function to draw a single FK relationship line
function drawFkLine(sourceTable, sourceCol, targetTable, targetCol) {
	// Get source and target elements
	var sourceElem = document.getElementById(sourceTable + '_' + sourceCol);
	var targetElem = document.getElementById(targetTable + '_' + targetCol);
	
	if (!sourceElem || !targetElem) {
		console.log("Cannot find elements for", sourceTable, sourceCol, targetTable, targetCol);
		return;
	}
	
	// Get column definition to check for phrase attributes
	var colDef = tbDefs[sourceTable].columns[sourceCol];
	var hasPhrase = colDef.phrase !== undefined;
	var hasInverse = colDef.inverse !== undefined;
	
	// Get positions
	var sourceRect = sourceElem.getBoundingClientRect();
	var targetRect = targetElem.getBoundingClientRect();
	var containerRect = modelContainer.getBoundingClientRect();
	
	// Get parent table elements
	var sourceTableElem = document.getElementById(sourceTable);
	var targetTableElem = document.getElementById(targetTable);
	
	if (!sourceTableElem || !targetTableElem) return;
	
	var sourceTableRect = sourceTableElem.getBoundingClientRect();
	var targetTableRect = targetTableElem.getBoundingClientRect();
	
	// Calculate center points
	var sourceCenterX = sourceRect.left + sourceRect.width/2 - containerRect.left + modelContainer.scrollLeft;
	var sourceCenterY = sourceRect.top + sourceRect.height/2 - containerRect.top + modelContainer.scrollTop;
	var targetCenterX = targetRect.left + targetRect.width/2 - containerRect.left + modelContainer.scrollLeft;
	var targetCenterY = targetRect.top + targetRect.height/2 - containerRect.top + modelContainer.scrollTop;
	
	// Calculate the angle between centers
	var angle = Math.atan2(targetCenterY - sourceCenterY, targetCenterX - sourceCenterX);
	
	// Calculate source table edges
	var sourceTableLeft = sourceTableRect.left - containerRect.left + modelContainer.scrollLeft;
	var sourceTableRight = sourceTableLeft + sourceTableRect.width;
	var sourceTableTop = sourceTableRect.top - containerRect.top + modelContainer.scrollTop;
	var sourceTableBottom = sourceTableTop + sourceTableRect.height;
	
	// Calculate target table edges
	var targetTableLeft = targetTableRect.left - containerRect.left + modelContainer.scrollLeft;
	var targetTableRight = targetTableLeft + targetTableRect.width;
	var targetTableTop = targetTableRect.top - containerRect.top + modelContainer.scrollTop;
	var targetTableBottom = targetTableTop + targetTableRect.height;
	
	// Find intersection points with table edges
	var x1, y1, x2, y2;
	
	// Source intersection
	if (Math.abs(Math.cos(angle)) > Math.abs(Math.sin(angle))) {
		// Mostly horizontal movement
		if (Math.cos(angle) > 0) {
			// Moving right
			x1 = sourceTableRight;
			y1 = sourceCenterY;
		} else {
			// Moving left
			x1 = sourceTableLeft;
			y1 = sourceCenterY;
		}
	} else {
		// Mostly vertical movement
		if (Math.sin(angle) > 0) {
			// Moving down
			x1 = sourceCenterX;
			y1 = sourceTableBottom;
		} else {
			// Moving up
			x1 = sourceCenterX;
			y1 = sourceTableTop;
		}
	}
	
	// Target intersection
	if (Math.abs(Math.cos(angle)) > Math.abs(Math.sin(angle))) {
		// Mostly horizontal movement
		if (Math.cos(angle) > 0) {
			// Coming from left
			x2 = targetTableLeft;
			y2 = targetCenterY;
		} else {
			// Coming from right
			x2 = targetTableRight;
			y2 = targetCenterY;
		}
	} else {
		// Mostly vertical movement
		if (Math.sin(angle) > 0) {
			// Coming from top
			x2 = targetCenterX;
			y2 = targetTableTop;
		} else {
			// Coming from bottom
			x2 = targetCenterX;
			y2 = targetTableBottom;
		}
	}
	
	// Recalculate angle based on intersection points
	angle = Math.atan2(y2 - y1, x2 - x1);
	
	// Create SVG element
	var svgNS = "http://www.w3.org/2000/svg";
	var svg = document.createElementNS(svgNS, "svg");
	svg.setAttribute("class", "fk-line");
	svg.style.position = "absolute";
	svg.style.top = "0";
	svg.style.left = "0";
	svg.style.width = "100%";
	svg.style.height = "100%";
	svg.style.pointerEvents = "none";
	svg.style.zIndex = "1";
	
	// Create line
	var line = document.createElementNS(svgNS, "line");
	line.setAttribute("x1", x1);
	line.setAttribute("y1", y1);
	line.setAttribute("x2", x2);
	line.setAttribute("y2", y2);
	line.setAttribute("stroke", "#000000");
	line.setAttribute("stroke-width", Math.max(1, currentScale * 0.8)); // Scale line width with zoom
	
	// Add line to SVG
	svg.appendChild(line);
	
	// Scale arrowhead size with zoom level
	var baseArrowSize = 6;
	var arrowSize = baseArrowSize * currentScale;
	
	// Create arrowhead as a polygon
	var arrowPoints = [
		[x2, y2],
		[x2 - arrowSize * Math.cos(angle - Math.PI/6), y2 - arrowSize * Math.sin(angle - Math.PI/6)],
		[x2 - arrowSize * Math.cos(angle + Math.PI/6), y2 - arrowSize * Math.sin(angle + Math.PI/6)]
	].map(point => point[0] + ',' + point[1]).join(' ');
	
	var arrowhead = document.createElementNS(svgNS, "polygon");
	arrowhead.setAttribute("points", arrowPoints);
	arrowhead.setAttribute("fill", "#000000");
	
	// Add arrowhead to SVG
	svg.appendChild(arrowhead);
	
	// Add SVG to container
	modelContainer.appendChild(svg);
	
	// Add phrase indicators if present
	if (hasPhrase || hasInverse) {
		// Calculate midpoints for phrase indicators
		var startX = x1 + (x2 - x1) * 0.25; // 25% along the line
		var startY = y1 + (y2 - y1) * 0.25;
		var endX = x1 + (x2 - x1) * 0.75; // 75% along the line
		var endY = y1 + (y2 - y1) * 0.75;
		
		// Scale indicator size with zoom level
		var indicatorSize = Math.max(10, 10 * currentScale);
		var indicatorPadding = Math.max(2, 2 * currentScale);
		var indicatorFontSize = Math.max(10, 10 * currentScale);
		
		// Add phrase indicator at start if present
		if (hasPhrase) {
			var phraseDiv = document.createElement('div');
			phraseDiv.className = 'phrase-indicator';
			phraseDiv.innerHTML = 'P';
			phraseDiv.title = colDef.phrase;
			phraseDiv.style.position = 'absolute';
			phraseDiv.style.left = startX + 'px';
			phraseDiv.style.top = startY + 'px';
			phraseDiv.style.backgroundColor = '#fff';
			phraseDiv.style.border = '1px solid #007bff';
			phraseDiv.style.borderRadius = '50%';
			phraseDiv.style.padding = indicatorPadding + 'px ' + (indicatorPadding * 2) + 'px';
			phraseDiv.style.fontSize = indicatorFontSize + 'px';
			phraseDiv.style.color = '#007bff';
			phraseDiv.style.fontWeight = 'bold';
			phraseDiv.style.zIndex = '2';
			phraseDiv.style.cursor = 'help';
			phraseDiv.style.transform = 'translate(-50%, -50%)'; // Center on point
			modelContainer.appendChild(phraseDiv);
		}
		
		// Add inverse phrase indicator at end if present
		if (hasInverse) {
			var inverseDiv = document.createElement('div');
			inverseDiv.className = 'inverse-phrase-indicator';
			inverseDiv.innerHTML = 'IP';
			inverseDiv.title = colDef.inverse;
			inverseDiv.style.position = 'absolute';
			inverseDiv.style.left = endX + 'px';
			inverseDiv.style.top = endY + 'px';
			inverseDiv.style.backgroundColor = '#fff';
			inverseDiv.style.border = '1px solid #007bff';
			inverseDiv.style.borderRadius = '50%';
			inverseDiv.style.padding = indicatorPadding + 'px ' + (indicatorPadding * 2) + 'px';
			inverseDiv.style.fontSize = indicatorFontSize + 'px';
			inverseDiv.style.color = '#007bff';
			inverseDiv.style.fontWeight = 'bold';
			inverseDiv.style.zIndex = '2';
			inverseDiv.style.cursor = 'help';
			inverseDiv.style.transform = 'translate(-50%, -50%)'; // Center on point
			modelContainer.appendChild(inverseDiv);
		}
	}
}

// Update lines when tables are dragged
function updateFkLines() {
	// Just call drawFkLines which now handles cleanup
	drawFkLines();
}

// Extend makeDraggable to update FK lines
function makeDraggable(element) {
	var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
	
	// Add cursor style to indicate draggable
	element.style.cursor = 'move';
	
	// Get the header element to use as drag handle
	var header = element.querySelector('.tbName');
	if (header) {
		header.onmousedown = dragMouseDown;
	} else {
		element.onmousedown = dragMouseDown;
	}

	function dragMouseDown(e) {
		e = e || window.event;
		e.preventDefault();
		// Get the mouse cursor position at startup
		pos3 = e.clientX;
		pos4 = e.clientY;
		document.onmouseup = closeDragElement;
		// Call a function whenever the cursor moves
		document.onmousemove = elementDrag;
	}

	function elementDrag(e) {
		e = e || window.event;
		e.preventDefault();
		// Calculate the new cursor position
		pos1 = pos3 - e.clientX;
		pos2 = pos4 - e.clientY;
		pos3 = e.clientX;
		pos4 = e.clientY;
		
		// Set the element's new position
		var newTop = (element.offsetTop - pos2);
		var newLeft = (element.offsetLeft - pos1);
		element.style.top = newTop + "px";
		element.style.left = newLeft + "px";
		
		// Update the info div with position
		$('#info').html('Table: ' + element.id + ' - Position: [' + newLeft + ', ' + newTop + ']');
		
		// Update FK lines
		updateFkLines();
	}

	function closeDragElement() {
		// Stop moving when mouse button is released
		document.onmouseup = null;
		document.onmousemove = null;
		
		// Update the table definition with new position
		if (tbDefs[element.id]) {
			// If we're at a non-default zoom, store the scaled position
			if (currentScale !== 1) {
				var scaledLeft = Math.round(parseInt(element.style.left) / currentScale);
				var scaledTop = Math.round(parseInt(element.style.top) / currentScale);
				tbDefs[element.id].position = [scaledLeft, scaledTop];
			} else {
				tbDefs[element.id].position = [parseInt(element.style.left), parseInt(element.style.top)];
			}
		}
		
		// Final update of FK lines
		updateFkLines();
	}
}

// Current scale of the model
var currentScale = 1;
var minScale = 0.5;
var maxScale = 2;
var scaleStep = 0.1;
var originalPositions = {}; // Store original positions of tables

// New function to handle scroll events with different modifiers
function handleScrollEvent(e) {
	e.preventDefault();
	
	var delta = e.originalEvent.deltaY;
	
	// Ctrl + wheel = zoom
	if (e.ctrlKey) {
		handleZoom(e);
	} 
	// Shift + wheel = horizontal scroll
	else if (e.shiftKey) {
		$('#divModelo').scrollLeft($('#divModelo').scrollLeft() + delta);
	} 
	// No modifier = vertical scroll
	else {
		$('#divModelo').scrollTop($('#divModelo').scrollTop() + delta);
	}
		
	// Redraw FK lines to account for new scale
	drawFkLines();
	
	return false;
}

// Keep the existing handleZoom function but modify it to work with the new event handler
function handleZoom(e) {
	// Get mouse position relative to the container
	var mouseX = e.pageX - $('#divModelo').offset().left;
	var mouseY = e.pageY - $('#divModelo').offset().top;
	
	// Determine zoom direction
	var delta = e.originalEvent.deltaY < 0 ? 1 : -1;
	var newScale = currentScale + (delta * scaleStep);
	
	// Limit scale to min/max values
	newScale = Math.max(minScale, Math.min(maxScale, newScale));
	
	// Only proceed if scale changed
	if (newScale !== currentScale) {
		// Store original positions if not already stored
		if (Object.keys(originalPositions).length === 0) {
			storeOriginalPositions();
		}
		
		// Apply the new scale to all tables
		applyZoomToTables(newScale);
		
		// Update current scale
		currentScale = newScale;
		
		// Update info display with current zoom level
		$('#info').html('Zoom: ' + Math.round(currentScale * 100) + '%');
		
		// Redraw FK lines to account for new scale
		drawFkLines();
	}
}

// Store original positions of all tables
function storeOriginalPositions() {
	$('.sttable').each(function() {
		var id = $(this).attr('id');
		originalPositions[id] = {
			left: parseInt($(this).css('left')),
			top: parseInt($(this).css('top'))
		};
		
		// Also store in tbDefs if not already there
		if (!tbDefs[id].originalPosition) {
			tbDefs[id].originalPosition = [...tbDefs[id].position];
		}
	});
}

// Apply zoom scale to all tables
function applyZoomToTables(scale) {
	$('.sttable').each(function() {
		var id = $(this).attr('id');
		var origPos = originalPositions[id];
		
		if (origPos) {
			// Calculate new position based on scale
			var newLeft = Math.round(origPos.left * scale);
			var newTop = Math.round(origPos.top * scale);
			
			// Apply new position
			$(this).css({
				left: newLeft + 'px',
				top: newTop + 'px',
				// Scale the table and its contents
				transform: 'scale(' + scale + ')',
				transformOrigin: '0 0'
			});
			
			// Update position in tbDefs
			tbDefs[id].position = [newLeft, newTop];
		}
	});
}

// Reset zoom to default scale
function resetZoom() {
	currentScale = 1;
	
	// Reset all table positions to original
	$('.sttable').each(function() {
		var id = $(this).attr('id');
		
		// Use original position from tbDefs if available
		if (tbDefs[id].originalPosition) {
			var origLeft = tbDefs[id].originalPosition[0];
			var origTop = tbDefs[id].originalPosition[1];
			
			$(this).css({
				left: origLeft + 'px',
				top: origTop + 'px',
				transform: 'scale(1)',
				transformOrigin: '0 0'
			});
			
			// Update current position in tbDefs
			tbDefs[id].position = [...tbDefs[id].originalPosition];
		}
	});
	
	// Redraw FK lines
	drawFkLines();
	
	// Update info display
	$('#info').html('Zoom: 100%');
}

function saveTablePositions() {
    var positions = {};
    
    // Collect positions of all tables
    $('.sttable').each(function() {
        var tableId = $(this).attr('id');
        var left = parseInt($(this).css('left'));
        var top = parseInt($(this).css('top'));
        
        positions[tableId] = [left, top];
        
        // Also update the tbDefs object
        if (tbDefs[tableId]) {
            tbDefs[tableId].position = [left, top];
        }
    });
    
    // Add viewport information
    positions.viewport = {
        zoom: currentScale,
        scrollTop: $('#divModelo').scrollTop(),
        scrollLeft: $('#divModelo').scrollLeft()
    };
    
    // Convert to JSON
    var positionsJson = JSON.stringify(positions);
    
    // Send to server
    $.ajax({
        url: 'ajax_modelo.php',
        type: 'POST',
        data: {
            m: 'savePositions',
            positions: positionsJson
        },
        success: function(response) {
            // eval(response); // Execute the response (likely an alert)
            console.log(response);
        },
        error: function() {
            alert('Error saving positions');
        }
    });
}

// Add a save button to the toolbar
$(document).ready(function() {
    $('#maptools').append('&nbsp;|&nbsp;<span class="simulink" onclick="saveTablePositions();"><i class="fa fa-save">&nbsp;</i>Save Positions</span>');
});

// Update centralizar function to account for scaling
function centralizar() {
	// Center the view on the tables
	var container = $('#divModelo');
	
	// Find the bounds of all tables
	var minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
	
	$('.sttable').each(function() {
		var left = parseInt($(this).css('left'));
		var top = parseInt($(this).css('top'));
		var width = $(this).outerWidth();
		var height = $(this).outerHeight();
		
		minX = Math.min(minX, left);
		minY = Math.min(minY, top);
		maxX = Math.max(maxX, left + width);
		maxY = Math.max(maxY, top + height);
	});
	
	// Calculate center point
	var centerX = (minX + maxX) / 2;
	var centerY = (minY + maxY) / 2;
	
	// Calculate container center
	var containerWidth = container.width();
	var containerHeight = container.height();
	
	// Set scroll position to center the content
	container.scrollLeft(centerX - containerWidth / 2);
	container.scrollTop(centerY - containerHeight / 2);
}

// Update lines when tables are dragged
function updateFkLines() {
	// Just call drawFkLines which now handles cleanup
	drawFkLines();
}

// Extend makeDraggable to update FK lines
function makeDraggable(element) {
	var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
	
	// Add cursor style to indicate draggable
	element.style.cursor = 'move';
	
	// Get the header element to use as drag handle
	var header = element.querySelector('.tbName');
	if (header) {
		header.onmousedown = dragMouseDown;
	} else {
		element.onmousedown = dragMouseDown;
	}

	function dragMouseDown(e) {
		e = e || window.event;
		e.preventDefault();
		// Get the mouse cursor position at startup
		pos3 = e.clientX;
		pos4 = e.clientY;
		document.onmouseup = closeDragElement;
		// Call a function whenever the cursor moves
		document.onmousemove = elementDrag;
	}

	function elementDrag(e) {
		e = e || window.event;
		e.preventDefault();
		// Calculate the new cursor position
		pos1 = pos3 - e.clientX;
		pos2 = pos4 - e.clientY;
		pos3 = e.clientX;
		pos4 = e.clientY;
		
		// Set the element's new position
		var newTop = (element.offsetTop - pos2);
		var newLeft = (element.offsetLeft - pos1);
		element.style.top = newTop + "px";
		element.style.left = newLeft + "px";
		
		// Update the info div with position
		$('#info').html('Table: ' + element.id + ' - Position: [' + newLeft + ', ' + newTop + ']');
		
		// Update FK lines
		updateFkLines();
	}

	function closeDragElement() {
		// Stop moving when mouse button is released
		document.onmouseup = null;
		document.onmousemove = null;
		
		// Update the table definition with new position
		if (tbDefs[element.id]) {
			// If we're at a non-default zoom, store the scaled position
			if (currentScale !== 1) {
				var scaledLeft = Math.round(parseInt(element.style.left) / currentScale);
				var scaledTop = Math.round(parseInt(element.style.top) / currentScale);
				tbDefs[element.id].position = [scaledLeft, scaledTop];
			} else {
				tbDefs[element.id].position = [parseInt(element.style.left), parseInt(element.style.top)];
			}
		}
		
		// Final update of FK lines
		updateFkLines();
	}
}

// Function to restore viewport settings
function restoreViewportSettings() {
    if (typeof tbPositions !== 'undefined' && tbPositions.viewport) {
        // Restore zoom level if available
        if (tbPositions.viewport.zoom && tbPositions.viewport.zoom !== currentScale) {
            // Store original positions if not already stored
            if (Object.keys(originalPositions).length === 0) {
                storeOriginalPositions();
            }
            
            // Apply the saved zoom level
            var savedScale = tbPositions.viewport.zoom;
            applyZoomToTables(savedScale);
            currentScale = savedScale;
            
            // Update info display with current zoom level
            $('#info').html('Zoom: ' + Math.round(currentScale * 100) + '%');
            
            // Redraw FK lines to account for scale
            drawFkLines();
        }
        
        // Restore scroll positions after a short delay to ensure DOM is ready
        setTimeout(function() {
            if (tbPositions.viewport.scrollTop) {
                $('#divModelo').scrollTop(tbPositions.viewport.scrollTop);
            }
            
            if (tbPositions.viewport.scrollLeft) {
                $('#divModelo').scrollLeft(tbPositions.viewport.scrollLeft);
            }
        }, 100);
    }
}

</script>
