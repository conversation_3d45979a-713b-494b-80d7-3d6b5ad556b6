<?
$eu = dirname(__FILE__);
include("$eu/../function/function.inc");
$arq = realpath($argv[1]);

if (!is_readable($arq)) {
	explica();
	$filename = $argv[1]?$argv[1]:"<arquivo de origem nao especificado!>";
	die("Não encontrou $filename!"); // Não encontrou o arquivo de origem
}

$projname = $argv[2];
if (!$projname) {
	explica();
	die("Favor especificar nome do projeto como segundo parâmetro! \n");
}


if (strpos($projname, DIRECTORY_SEPARATOR) === false) {
	$pathsaida = criaDiretorio(getBaseSaida() .DIRECTORY_SEPARATOR.$projname . DIRECTORY_SEPARATOR);
	$pathsaida = realpath($pathsaida).DIRECTORY_SEPARATOR;
	$saida = $pathsaida;
} else {
	$pathsaida = realpath($projname).DIRECTORY_SEPARATOR;
	if ($pathsaida == DIRECTORY_SEPARATOR) die("Must exist, second parameter.");
	$saida = $pathsaida;
}

$path = realpath($saida);
if (!$path) {
	die("Path de saída não encontrado.");
} else {
	$path = $path.DIRECTORY_SEPARATOR;
}
$sufixo=trim(getCmdLine("sufixo", false));
$namespace=trim(getCmdLine("namespace", false));
$schema=trim(getCmdLine("schema", false));
$usaCrlf=trim(getCmdLine("crlf", false));
$utf8=true;
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN' && $usaCrlf) $lf="\r\n"; else $lf="\n";
$lf="\r\n";
//var_dump($utf8);exit;
global $sufclasses;
$sufclasses=trim(getCmdLine("sufclasses", false));
$options["namespace"] = $namespace;
$options["schema"] = $schema;
$options["sufixo"] = (!$sufixo && $namespace?'_'.$namespace:$sufixo);
$options["incorpora_ADODB"] = getCmdLine("adodb", false);
$options["gera_flash"] = getCmdLine("flash", false);
$options["sufclasses"] = getCmdLine("sufclasses", false);
$options["utf8"] = ($utf8==true || $utf8=='true');
$options["lf"] = $lf;
$options["fromddl"] = getCmdLine("from-ddl", false);
$options['format'] = getCmdLine('format', false);
$options["saida"] = getCmdLine("output", true);
$options["fromjson"] = getCmdLine("from-json", false);
$options["tojson"] = getCmdLine("to-json", false);
global $lf;

echo "\nArquivo: $arq\n";
echo "Path Saída: $path\n";
echo "Options: ".print_r($options, true);
echo "Argv: ".print_r($argv,true);

// Check if we're generating from DDL file
if ($options["fromjson"]) {
    // Get JSON file path
    $jsonFile = isset($argv[1]) ? $argv[1] : "";
    if (!file_exists($jsonFile)) {
        die("JSON file not found: $jsonFile");
    }
    
    // Generate XML from JSON
    $xmlDoc = generateXmlFromJSON($jsonFile);
    
    if ($options['saida']) {
        $xmlDoc->dump_file($options['saida'], false, true);
        die('XML gerado.');
    }
    
    // Generate classes from the XML
    geraClassesInicial($xmlDoc, $path, $options);
} else if ($options["fromddl"]) {
	// die("Generating from DDL file.");
    
    // Get DDL file path
    $ddlFile = isset($argv[1]) ? $argv[1] : "";
    if (!file_exists($ddlFile)) {
        die("DDL file not found: $ddlFile");
    }
	// die('formato: |'.$options['format'].'|');
	if ($options['format']=='json') {
		$ddlContent = file_get_contents($ddlFile);
		$json=generateJSONSchemaFromDDL($ddlContent);
		if ($options['saida']){
			file_put_contents($options['saida'], $json);
			die('JSON gerado.');
		}
		die($json);
	}elseif($options['format']=='cjson'){
		$ddlContent = file_get_contents($ddlFile);
		$json=generateJSONSchemaFromDDL($ddlContent);
		if ($options['saida']){
			file_put_contents($options['saida'], $json);
			die('JSON gerado.');
		}
		die($json);
	}else{
		// Generate XML from DDL and then classes
		$xmlDoc = generateXmlFromDDL($ddlFile);
		if ($options['saida']){
			$xmlDoc->dump_file($options['saida'], false, true);
			die('XML gerado.');
		}
	}
		
    geraClassesInicial($xmlDoc, $path, $options);
} else {
	if ($options["tojson"]) {
		// Generate JSON from XML
		$json = generateJsonFromXml($arq);
		if ($options['saida']){
			file_put_contents($options['saida'], $json);
			die('JSON gerado.');
		}
		die($json);
	}
	// exit;
	if (!$doc = domxml_open_file($arq)) {
		die("Error while parsing the document\n");
	}
	echo("Generating from XML file.\r\n");
    // Original XML-based generation
    geraClassesInicial($doc, $path, $options);
}

echo "Gerado ok.\n\n";

function initProgresso($max) {
	
}

function incrProg($val) {

}

function explica() {
	echo "
	Uso:
		php4 -q geraphp.php <nomexml> <nomeprojeto> [options]
	";
}

/**
 * Generates XML document from DDL file
 * @param string $ddlFile Path to DDL file
 * @return object DOMDocument compatible with GeraClasses
 */
function generateXmlFromDDL($ddlFile) {
    echo "Generating XML from DDL file: $ddlFile\n";
    
    // Read DDL file
    $ddlContent = file_get_contents($ddlFile);
    
    // Create new XML document
    $doc = domxml_new_doc("1.0");
    $root = $doc->create_element("Modelo");
    $doc->append_child($root);
    
    // Add Entidades element
    $entidades = $doc->create_element("Entidades");
    $root->append_child($entidades);
    
    // Add Relacionamentos element
    $relacionamentos = $doc->create_element("Relacionamentos");
    $root->append_child($relacionamentos);
    
    // Parse DDL and populate XML
    parseDDLToXML($ddlContent, $doc, $entidades, $relacionamentos);
    return $doc;
}

/**
 * Parses DDL content and populates XML document
 * @param string $ddlContent DDL file content
 * @param object $doc XML document
 * @param object $entidades Entidades XML node
 * @param object $relacionamentos Relacionamentos XML node
 */
function parseDDLToXML($ddlContent, $doc, $entidades, $relacionamentos) {
    // Extract comments from the full DDL before cleaning
    $comments = extractComments($ddlContent);
    
    // Extract geometry columns from the full DDL
    $geometryColumns = extractGeometryColumns($ddlContent);
    
    // Convert geometry column table names to uppercase for consistent lookup
    $geometryColumnsUpper = [];
    foreach ($geometryColumns as $tableName => $columns) {
        $geometryColumnsUpper[strtoupper($tableName)] = $columns;
    }
    $geometryColumns = $geometryColumnsUpper;
    
    // Normalize line endings and remove comments
    $cleanDDL = normalizeAndCleanDDL($ddlContent);
    
    // Extract CREATE TABLE statements
    $tables = extractCreateTableStatements($cleanDDL);
    
    // Sort tables by name
    ksort($tables);
    
    // Extract ALTER TABLE statements for constraints and foreign keys
    $alterStatements = extractAlterTableStatements($ddlContent);
    
    // Extract sequences from DDL
    $sequences = extractSequences($ddlContent);
    
    // Process each table
    $entidadesMap = []; // Map to store entidade elements by table name
    foreach ($tables as $tableName => $tableDefinition) {
        // Extract schema and table name if present
        $tableParts = explode('.', $tableName);
        $tableNameOnly = count($tableParts) > 1 ? $tableParts[1] : $tableName;
        
        // Process table definition
        $entidade = processTableDefinition($doc, $entidades, $tableName, $tableDefinition, $comments, $alterStatements, $sequences);
        $entidadesMap[strtoupper($tableNameOnly)] = $entidade;
        
        // Process geometry columns if they exist for this table
        if (isset($geometryColumns[$tableNameOnly])) {
            $campos = $entidade->get_elements_by_tagname("Campos")[0];
            $currentCount = (int)$campos->get_attribute("quantos");
            
            foreach ($geometryColumns[$tableNameOnly] as $columnName => $geomInfo) {
                // Check if column already exists (might be defined in CREATE TABLE)
                $existingColumns = $campos->get_elements_by_tagname("Campo");
                $columnExists = false;
                
                foreach ($existingColumns as $existingColumn) {
                    if ($existingColumn->get_attribute("NomeFisico") === $columnName) {
                        // Update existing column with geometry info
                        $existingColumn->set_attribute("DataType", mapGeometryTypeToGeraClasses($geomInfo['type']));
                        $existingColumn->set_attribute("SRID", $geomInfo['srid']);
                        $existingColumn->set_attribute("GeomDimension", $geomInfo['dimension']);
                        $columnExists = true;
                        break;
                    }
                }
                
                // If column doesn't exist, add it
                if (!$columnExists) {
                    // Add new geometry column
                    $campo = $doc->create_element("Campo");
                    $logicalName = !empty($geomInfo['comment']) ? $geomInfo['comment'] : $columnName;
                    
                    $campo->set_attribute("NomeLogico", $logicalName);
                    $campo->set_attribute("NomeFisico", $columnName);
                    $campo->set_attribute("DataType", mapGeometryTypeToGeraClasses($geomInfo['type']));
                    $campo->set_attribute("SRID", $geomInfo['srid']);
                    $campo->set_attribute("GeomDimension", $geomInfo['dimension']);
                    $campo->set_attribute("id", "A" . (20000 + rand(1000, 9999)));
                    
                    // Add campo to campos
                    $campos->append_child($campo);
                    $currentCount++;
                }
            }
            
            // Update the count attribute
            $campos->set_attribute("quantos", $currentCount);
        }
    }
    
    // Add Parents element to each entidade based on foreign key relationships
    addParentsElement($doc, $entidadesMap, $alterStatements);
    
    // Process foreign key relationships
    processForeignKeyRelationships($doc, $relacionamentos, $alterStatements, $tables);
}

/**
 * Normalizes line endings and removes comments from DDL
 * @param string $ddlContent Original DDL content
 * @return string Cleaned DDL content
 */
function normalizeAndCleanDDL($ddlContent) {
    // Normalize line endings
    $ddlContent = str_replace(["\r\n", "\r"], "\n", $ddlContent);
    
    // Remove single-line comments
    $ddlContent = preg_replace('/--.*$/m', '', $ddlContent);
    
    // Remove multi-line comments
    $ddlContent = preg_replace('/\/\*.*?\*\//s', '', $ddlContent);
    
    return $ddlContent;
}

/**
 * Extracts CREATE TABLE statements from DDL
 * @param string $ddlContent Cleaned DDL content
 * @return array Associative array of table names and their definitions
 */
function extractCreateTableStatements($ddlContent) {
    $tables = [];
    
    // First, find all CREATE TABLE statements
    if (preg_match_all('/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(?:`|")?(\w+(?:\.\w+)?)(?:`|")?\s*\(/is', $ddlContent, $matches, PREG_OFFSET_CAPTURE)) {
        foreach ($matches[0] as $index => $match) {
            $startPos = $match[1];
            $tableName = $matches[1][$index][0];
            
            // Find the position after the opening parenthesis
            $openParenPos = strpos($ddlContent, '(', $startPos);
            if ($openParenPos === false) continue;
            
            // Start parsing from the character after the opening parenthesis
            $pos = $openParenPos + 1;
            $level = 1; // We're already inside one level of parentheses
            $endPos = null;
            
            // Parse through the content to find the matching closing parenthesis
            while ($pos < strlen($ddlContent)) {
                $char = $ddlContent[$pos];
                
                if ($char === '(') {
                    $level++;
                } elseif ($char === ')') {
                    $level--;
                    if ($level === 0) {
                        $endPos = $pos;
                        break;
                    }
                }
                
                $pos++;
            }
            
            if ($endPos !== null) {
                // Extract the table definition (content between parentheses)
                $tableDefinition = substr($ddlContent, $openParenPos + 1, $endPos - $openParenPos - 1);
                $tables[$tableName] = $tableDefinition;
            }
        }
    }
    
    if (empty($tables)) {
        die('Failed to extract CREATE TABLE statements');
    }
    
    return $tables;
}

/**
 * Extracts ALTER TABLE statements from DDL
 * @param string $ddlContent Cleaned DDL content
 * @return array Array of ALTER TABLE statements
 */
function extractAlterTableStatements($ddlContent) {
    $alterStatements = [];
    
    // Match ALTER TABLE statements with FOREIGN KEY - PostgreSQL format
    $fkPattern = '/ALTER\s+TABLE(?:\s+ONLY)?\s+(?:"?([^"\s\.]+)"?\.)?(?:"?([^"\s\.]+)"?)\s+ADD\s+CONSTRAINT\s+(?:"?([^"\s]+)"?)\s+FOREIGN\s+KEY\s+\(([^)]+)\)\s+REFERENCES\s+(?:"?([^"\s\.]+)"?\.)?(?:"?([^"\s\.]+)"?)\s*\(([^)]+)\)(?:\s+(?:ON\s+DELETE\s+(\w+(?:\s+\w+)?)))?(?:\s+(?:ON\s+UPDATE\s+(\w+(?:\s+\w+)?)))?/is';
    
    if (preg_match_all($fkPattern, $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            // Extract table schema and name
            $tableSchema = !empty($match[1]) ? $match[1] : '';
            $tableName = $match[2];
            
            // Extract constraint name
            $constraintName = $match[3];
            
            // Extract source columns
            $sourceColumns = preg_replace('/[`"\s]/', '', $match[4]);
            
            // Extract referenced schema and table
            $refTableSchema = !empty($match[5]) ? $match[5] : '';
            $refTableName = $match[6];
            
            // Extract referenced columns
            $refColumns = preg_replace('/[`"\s]/', '', $match[7]);
            
            // Extract ON DELETE and ON UPDATE actions
            $onDelete = isset($match[8]) ? $match[8] : '';
            $onUpdate = isset($match[9]) ? $match[9] : '';
            
            $alterStatements[] = [
                'table' => strtoupper($tableName),
                'schema' => strtoupper($tableSchema),
                'full_table' => strtoupper($tableSchema ? "$tableSchema.$tableName" : $tableName),
                'constraint' => strtoupper($constraintName),
                'fk_name' => strtoupper($constraintName),
                'columns' => $sourceColumns,
                'ref_table' => strtoupper($refTableName),
                'ref_schema' => strtoupper($refTableSchema),
                'full_ref_table' => strtoupper($refTableSchema ? "$refTableSchema.$refTableName" : $refTableName),
                'ref_columns' => $refColumns,
                'on_delete' => $onDelete,
                'on_update' => $onUpdate
            ];
        }
    }
    
    // Match ALTER TABLE statements with PRIMARY KEY
    $pkPattern = '/ALTER\s+TABLE(?:\s+ONLY)?\s+(?:"?([^"\s\.]+)"?\.)?(?:"?([^"\s\.]+)"?)\s+ADD\s+CONSTRAINT\s+(?:"?([^"\s]+)"?)\s+PRIMARY\s+KEY\s*\(([^)]+)\)/is';
    if (preg_match_all($pkPattern, $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            // Extract table schema and name
            $tableSchema = !empty($match[1]) ? $match[1] : '';
            $tableName = $match[2];
            
            // Extract constraint name
            $constraintName = $match[3];
            
            // Extract columns
            $columns = preg_replace('/[`"\s]/', '', $match[4]);
            
            $alterStatements[] = [
                'table' => strtoupper($tableName),
                'schema' => strtoupper($tableSchema),
                'full_table' => strtoupper($tableSchema ? "$tableSchema.$tableName" : $tableName),
                'constraint' => strtoupper($constraintName),
                'type' => 'PRIMARY KEY',
                'columns' => explode(',', $columns)
            ];
        }
    }
    
    // Match ALTER TABLE statements with UNIQUE
    $uniquePattern = '/ALTER\s+TABLE(?:\s+ONLY)?\s+(?:"?([^"\s\.]+)"?\.)?(?:"?([^"\s\.]+)"?)\s+ADD\s+CONSTRAINT\s+(?:"?([^"\s]+)"?)\s+UNIQUE\s*\(([^)]+)\)/is';
    if (preg_match_all($uniquePattern, $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            // Extract table schema and name
            $tableSchema = !empty($match[1]) ? $match[1] : '';
            $tableName = $match[2];
            
            // Extract constraint name
            $constraintName = $match[3];
            
            // Extract columns
            $columns = preg_replace('/[`"\s]/', '', $match[4]);
            
            $alterStatements[] = [
                'table' => strtoupper($tableName),
                'schema' => strtoupper($tableSchema),
                'full_table' => strtoupper($tableSchema ? "$tableSchema.$tableName" : $tableName),
                'constraint' => strtoupper($constraintName),
                'type' => 'UNIQUE',
                'columns' => explode(',', $columns)
            ];
        }
    }
    
    // Match CREATE UNIQUE INDEX statements
    $uniqueIndexPattern = '/CREATE\s+UNIQUE\s+INDEX\s+(?:"?([^"\s]+)"?)\s+ON\s+(?:"?([^"\s\.]+)"?\.)?(?:"?([^"\s\.]+)"?)\s*(?:USING\s+\w+\s*)?\(([^)]+)\)/is';
    if (preg_match_all($uniqueIndexPattern, $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            // Extract index name
            $indexName = $match[1];
            
            // Extract table schema and name
            $tableSchema = !empty($match[2]) ? $match[2] : '';
            $tableName = $match[3];
            
            // Extract columns
            $columns = preg_replace('/[`"\s]/', '', $match[4]);
            
            // Remove any ASC/DESC directives from column names
            $columns = preg_replace('/\s+(?:ASC|DESC)(?:\s+|$)/i', '', $columns);
            $columnsArray = explode(',', $columns);
            
            $alterStatements[] = [
                'table' => strtoupper($tableName),
                'schema' => strtoupper($tableSchema),
                'full_table' => strtoupper($tableSchema ? "$tableSchema.$tableName" : $tableName),
                'constraint' => strtoupper($indexName),
                'type' => 'UNIQUE',
                'columns' => $columnsArray,
                'source' => 'INDEX'  // Mark this as coming from an INDEX statement
            ];
        }
    }
    
    return $alterStatements;
}

/**
 * Extracts comments from DDL content
 * @param string $ddlContent Full DDL content
 * @return array Associative array of table and column comments
 */
function extractComments($ddlContent) {
    $comments = [
        'tables' => [],
        'columns' => []
    ];
    
    // Extract table comments
    if (preg_match_all('/COMMENT\s+ON\s+TABLE\s+(?:[\w\.]+\.)?(\w+)\s+IS\s+[\'"]([^\'"]*)[\'"];/i', $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $tableName = $match[1];
            $comment = $match[2];
            $comments['tables'][$tableName] = $comment;
        }
    }
    
    // Extract column comments
    if (preg_match_all('/COMMENT\s+ON\s+COLUMN\s+(?:[\w\.]+\.)?(\w+)\.(\w+)\s+IS\s+[\'"]([^\'"]*)[\'"];/i', $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $tableName = $match[1];
            $columnName = $match[2];
            $comment = $match[3];
            
            if (!isset($comments['columns'][$tableName])) {
                $comments['columns'][$tableName] = [];
            }
            
            $comments['columns'][$tableName][$columnName] = $comment;
        }
    }
    
    return $comments;
}

/**
 * Extracts geometry column definitions from DDL content
 * @param string $ddlContent Full DDL content
 * @return array Associative array of geometry columns by table
 */
function extractGeometryColumns($ddlContent) {
    $geometryColumns = [];
    
    // Pattern to match addgeometrycolumn statements
    // addgeometrycolumn('schema','table','column',srid,'type',dimension)
    $pattern = "/select\s+addgeometrycolumn\s*\(\s*'([^']+)'\s*,\s*'([^']+)'\s*,\s*'([^']+)'\s*,\s*(\d+)\s*,\s*'([^']+)'\s*,\s*(\d+)\s*\)/i";
    
    if (preg_match_all($pattern, $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $schema = $match[1];
            $tableName = strtoupper($match[2]);
            $columnName = strtoupper($match[3]);
            $srid = $match[4];
            $geomType = $match[5];
            $dimension = $match[6];
            
            if (!isset($geometryColumns[$tableName])) {
                $geometryColumns[$tableName] = [];
            }
            
            $geometryColumns[$tableName][$columnName] = [
                'schema' => $schema,
                'srid' => $srid,
                'type' => $geomType,
                'dimension' => $dimension
            ];
        }
    }
    
    return $geometryColumns;
}

/**
 * Maps PostGIS geometry types to GeraClasses data types
 * @param string $geomType PostGIS geometry type
 * @return string GeraClasses data type
 */
function mapGeometryTypeToGeraClasses($geomType) {
    $geomTypeMap = [
        'POINT' => 'POINT',
        'POINTZ' => 'POINTZ',
        'POINTM' => 'POINTM',
        'MULTIPOINT' => 'MULTIPOINT',
        'MULTIPOINTZ' => 'MULTIPOINTZ',
        'MULTIPOINTM' => 'MULTIPOINTM',
        'LINESTRING' => 'LINESTRING',
        'LINESTRINGZ' => 'LINESTRINGZ',
        'LINESTRINGM' => 'LINESTRINGM',
        'MULTILINESTRING' => 'MULTILINESTRING',
        'MULTILINESTRINGZ' => 'MULTILINESTRINGZ',
        'MULTILINESTRINGM' => 'MULTILINESTRINGM',
        'POLYGON' => 'POLYGON',
        'POLYGONZ' => 'POLYGONZ',
        'POLYGONM' => 'POLYGONM',
        'MULTIPOLYGON' => 'MULTIPOLYGON',
        'MULTIPOLYGONZ' => 'MULTIPOLYGONZ',
        'MULTIPOLYGONM' => 'MULTIPOLYGONM',
        'GEOMETRY' => 'GEOMETRY',
        'GEOMETRYCOLLECTION' => 'GEOMETRY',
        'GEOGRAPHY' => 'GEOGRAPHY'
    ];
    
    return $geomTypeMap[$geomType] ?? 'GEOMETRY';
}

/**
 * Process table definition and create Entidade element
 * @param object $doc XML document
 * @param object $entidades Entidades XML node
 * @param string $tableName Table name
 * @param string $tableDefinition Table definition
 * @param array $comments Comments extracted from DDL
 * @param array $alterStatements ALTER TABLE statements
 * @param array $sequences Sequences extracted from DDL
 * @return object Created Entidade element
 */
function processTableDefinition($doc, $entidades, $tableName, $tableDefinition, $comments, $alterStatements, $sequences = []) {
    // Extract schema and table name if present
    $tableParts = explode('.', $tableName);
    $tableSchema = count($tableParts) > 1 ? $tableParts[0] : '';
    $tableNameOnly = count($tableParts) > 1 ? $tableParts[1] : $tableName;
    
    // Create Entidade element
    $entidade = $doc->create_element("Entidade");
    
    // Extract table comment
    $tableComment = '';
    if (preg_match('/COMMENT\s*=\s*[\'"]([^\'"]*)[\'"]/', $tableDefinition, $commentMatch)) {
        $tableComment = $commentMatch[1];
    }
    
    // If no comment found in table definition, check external comments
    if (empty($tableComment) && isset($comments['tables'][$tableNameOnly])) {
        $tableComment = $comments['tables'][$tableNameOnly];
    }
    
    // Set attributes
    $nomeLogicoTabela = !empty($tableComment) ? $tableComment : strtoupper($tableNameOnly);
    $entidade->set_attribute("NomeLogico", $nomeLogicoTabela);
    $entidade->set_attribute("NomeFisico", strtoupper($tableNameOnly));
    $entidade->set_attribute("id", "E" . (5000 + rand(1000, 9999)));
    $entidade->set_attribute("owner", strtoupper($tableSchema));
    
    // Add Entidade to Entidades
    $entidades->append_child($entidade);
    
    // Create Campos element
    $campos = $doc->create_element("Campos");
    $entidade->append_child($campos);
    
    // Parse columns
    $columns = parseTableColumns($tableDefinition);
    $campos->set_attribute("quantos", count($columns));
    
    // Process each column
    foreach ($columns as $columnName => $columnDef) {
        $campo = $doc->create_element("Campo");

		// Map SQL data type to format in gt.dm1.xml
        $dataType = mapSQLTypeToGeraClasses($columnDef['type']);
        
        // Check for column comment
        $columnComment = $columnDef['comment'] ?? '';
        
        // If no comment found in column definition, check external comments
        if (empty($columnComment) && isset($comments['columns'][$tableNameOnly][$columnName])) {
            $columnComment = $comments['columns'][$tableNameOnly][$columnName];
        }
        
        // Use comment as logical name if available, otherwise generate from physical name
        // Strip table name and data type from comment if they exist
        if (!empty($columnComment)) {
            // Remove any instances of table name from comment
            $columnComment = str_ireplace(strtoupper($tableNameOnly), '', $columnComment);
            $columnComment = str_ireplace(strtolower($tableNameOnly), '', $columnComment);
            
            // Remove any instances of table logical name from comment
            $columnComment = str_ireplace(strtoupper($nomeLogicoTabela), '', $columnComment);
            $columnComment = str_ireplace(strtolower($nomeLogicoTabela), '', $columnComment);
            
            // Remove any instances of column name from comment
            $columnComment = str_ireplace(strtoupper($columnName), '', $columnComment);
            $columnComment = str_ireplace(strtolower($columnName), '', $columnComment);
            
            // Remove any instances of data type from comment
            $dataTypePattern = strtoupper($dataType);
            $columnComment = str_ireplace($dataTypePattern, '', $columnComment);
            
            // Clean up any resulting double spaces, dashes, or underscores
            $columnComment = preg_replace('/\s+/', ' ', $columnComment);
            $columnComment = preg_replace('/[-_\s]{2,}/', ' ', $columnComment);
            $columnComment = trim($columnComment, " -_");
            
            $logicalName = !empty($columnComment) ? $columnComment : strtoupper($columnName);
        } else {
            $logicalName = strtoupper($columnName);
        }
        
        // Set attributes similar to gt.dm1.xml
        $campo->set_attribute("NomeLogico", $logicalName);
        $campo->set_attribute("NomeFisico", strtoupper($columnName));
        
        // Set nullable attribute
        if (!$columnDef['nullable']) {
            $campo->set_attribute("NotNull", "True");
        }
        
        $campo->set_attribute("DataType", $dataType);
        
        // Add DataLength attribute for VARCHAR types
        if (preg_match('/varchar|character\s+varying/i', $columnDef['type']) && 
            preg_match('/\((\d+)\)/', $columnDef['type'], $matches)) {
            $campo->set_attribute("DataLength", $matches[1]);
        }
        
        // Generate a unique ID for the field
        $campo->set_attribute("id", "A" . (20000 + rand(1000, 9999)));
        
        // Set auto increment attribute - check for sequence with pattern tablename_columnname_seq
        $sequenceName = strtoupper($tableNameOnly) . '_' . strtoupper($columnName) . '_SEQ';
        if ($columnDef['auto_increment'] || in_array($sequenceName, array_map('strtoupper', $sequences))) {
            $campo->set_attribute("Identity", "True");
        }
        
        // Add campo to campos
        $campos->append_child($campo);
    }
    
    // Process constraints
    $constraints = extractConstraints($tableDefinition, $tableNameOnly, $alterStatements);
    if (strtoupper($tableNameOnly)=='TB0_LGVL') {
		// echo print_r($constraints, true);exit;
	}
    // Add Constraints element if there are constraints
    if (!empty($constraints['pk']) || !empty($constraints['uniques'])) {
        $constraintsElement = $doc->create_element("Constraints");
        $entidade->append_child($constraintsElement);
        
        $constraintCount = 0;
        
        // Add primary key constraint
        if (!empty($constraints['pk'])) {
            $constraintCount++;
            $pkConstraint = $doc->create_element("ConstraintPK");
            $pkConstraint->set_attribute("Tipo", "PK");
            $pkConstraint->set_attribute("Nome", strtoupper($tableNameOnly)."_PK");
            $constraintsElement->append_child($pkConstraint);
            
            foreach ($constraints['pk'] as $pkColumn) {
                $coluna = $doc->create_element("Coluna");
                $coluna->set_attribute("Nome", strtoupper($pkColumn));
                $pkConstraint->append_child($coluna);
            }
        }
        
        // Add unique constraints
        if (!empty($constraints['uniques'])) {
            foreach ($constraints['uniques'] as $unique) {
                $constraintCount++;
                $uniqueConstraint = $doc->create_element("ConstraintUnique");
                $uniqueConstraint->set_attribute("Nome", $unique['name']);
                $uniqueConstraint->set_attribute("Tipo", "Unique");
                
                // Add source attribute if this came from an INDEX statement
                if (isset($unique['source']) && $unique['source'] === 'INDEX') {
                    $uniqueConstraint->set_attribute("Source", "INDEX");
                }
                
                $constraintsElement->append_child($uniqueConstraint);
                
                foreach ($unique['columns'] as $uniqueColumn) {
                    $coluna = $doc->create_element("Coluna");
                    $coluna->set_attribute("Nome", strtoupper($uniqueColumn));
                    $uniqueConstraint->append_child($coluna);
                }
            }
        }
        
        $constraintsElement->set_attribute("quantos", $constraintCount);
    }
    
    // Add Children element for foreign keys
    $foreignKeys = extractForeignKeys($alterStatements, $tableNameOnly);
    if (!empty($foreignKeys)) {
        $childrenElement = $doc->create_element("Children");
        $entidade->append_child($childrenElement);
        
        foreach ($foreignKeys as $fk) {
			// if ($debug) {echo print_r($fk, true);exit;}
            $relatElement = $doc->create_element("Relat");
            $relatElement->set_attribute("ParentName", strtoupper($fk['ref_table']));
            $relatElement->set_attribute("ChildName", strtoupper($tableNameOnly));
            $relatElement->set_attribute("Cardinality", "0");
            $relatElement->set_attribute("Tipo", "1");
            $childrenElement->append_child($relatElement);
            
            // Process each column pair in the foreign key
            $childColumns = explode(',', preg_replace('/[`"\s]/', '', $fk['columns']));
            $parentColumns = explode(',', preg_replace('/[`"\s]/', '', $fk['ref_columns']));
            
            for ($i = 0; $i < count($childColumns); $i++) {
                $colunaRelElement = $doc->create_element("ColunaRelParent");
                $colunaRelElement->set_attribute("ParentName", strtoupper($parentColumns[$i]));
                $colunaRelElement->set_attribute("ChildName", strtoupper($childColumns[$i]));
                $relatElement->append_child($colunaRelElement);
            }
        }
    }
    
    return $entidade;
}

/**
 * Parses table columns from table definition
 * @param string $tableDefinition Table definition
 * @return array Associative array of column definitions
 */
function parseTableColumns($tableDefinition) {
    $columns = [];
    // Split definition into lines
    $lines = preg_split('/,\s*(?=(?:[^\'"]|\'[^\']*\'|"[^"]*")*$)/', $tableDefinition);
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        // Skip PRIMARY KEY, UNIQUE, etc. constraints
        if (preg_match('/^(?:PRIMARY\s+KEY|UNIQUE(?:\s+KEY)?|KEY|INDEX|CONSTRAINT|FOREIGN\s+KEY)/i', $line)) {
            continue;
        }
        
        // Match column name and data type with improved regex
        if (preg_match('/^\s*(?:`|")?(\w+)(?:`|")?\s+(\w+(?:\s+\w+)*(?:\([^)]+\))?)(?:\s+(.*)|$)/i', $line, $matches)) {
            $columnName = $matches[1];
            $columnType = trim($matches[2]);
            $columnAttrs = isset($matches[3]) ? trim($matches[3]) : '';
            
            // Ensure NULL/NOT NULL is not part of the data type
            if (preg_match('/^(.*?)\s+(NULL|NOT\s+NULL)$/i', $columnType, $typeMatches)) {
                $columnType = trim($typeMatches[1]);
                $columnAttrs = trim($typeMatches[2] . ' ' . $columnAttrs);
            }
            
            // Check for COMMENT clause
            $comment = '';
            if (preg_match('/COMMENT\s+[\'"]([^\'"]*)[\'"]/', $columnAttrs, $commentMatch)) {
                $comment = $commentMatch[1];
            }
            
            $columns[$columnName] = [
                'type' => $columnType,
                'nullable' => !preg_match('/\bNOT\s+NULL\b/i', $columnAttrs) && 
                              (preg_match('/\bNULL\b/i', $columnAttrs) || empty($columnAttrs)),
                'auto_increment' => preg_match('/\bAUTO_INCREMENT\b/i', $columnAttrs),
                'comment' => $comment
            ];
        }
    }
    
    return $columns;
}

/**
 * Extracts primary key and unique constraints from table definition and ALTER TABLE statements
 * @param string $tableDefinition Table definition
 * @param string $tableNameOnly Table name without schema
 * @param array $alterStatements Array of ALTER TABLE statements
 * @return array Associative array with 'pk' and 'uniques' keys
 */
function extractConstraints($tableDefinition, $tableNameOnly, $alterStatements = []) {
    $constraints = [
        'pk' => [],
        'uniques' => []
    ];
    
    // Extract primary key from table definition
    if (preg_match('/PRIMARY\s+KEY\s*\(([^)]+)\)/i', $tableDefinition, $matches)) {
        $pkColumns = $matches[1];
        $pkColumns = preg_replace('/[`"\s]/', '', $pkColumns);
        $constraints['pk'] = explode(',', $pkColumns);
    }
    
    // Extract unique constraints from table definition
    preg_match_all('/UNIQUE(?:\s+(?:KEY|INDEX|CONSTRAINT)\s+(?:`|")?(\w+)(?:`|")?)?\s*\(([^)]+)\)/i', $tableDefinition, $matches, PREG_SET_ORDER);
    
    foreach ($matches as $match) {
        $uniqueColumns = preg_replace('/[`"\s]/', '', $match[2]);
        $columnsArray = explode(',', $uniqueColumns);
        
        // Generate a name in the format TABLE_COLUMNS_UK
        $columnsForName = implode('_', $columnsArray);
        $uniqueName = strtoupper($tableNameOnly . '_' . $columnsForName . '_UK');
        
        // If a name was provided in the SQL, use it instead
        if (!empty($match[1])) {
            $uniqueName = strtoupper($match[1]);
        }
        
        $constraints['uniques'][] = [
            'name' => $uniqueName,
            'columns' => array_map('strtoupper', $columnsArray)
        ];
    }
    
    // Check ALTER TABLE statements for additional constraints
    foreach ($alterStatements as $alter) {
        // Skip if not for this table
        if (strtoupper($alter['table']) !== strtoupper($tableNameOnly)) {
            continue;
        }
        
        // Check for PRIMARY KEY constraints
        if (isset($alter['type']) && $alter['type'] === 'PRIMARY KEY' && empty($constraints['pk'])) {
            $constraints['pk'] = $alter['columns'];
        }
        
        // Check for UNIQUE constraints (from both ALTER TABLE and CREATE UNIQUE INDEX)
        if (isset($alter['type']) && $alter['type'] === 'UNIQUE') {
            $uniqueName = !empty($alter['constraint']) ? strtoupper($alter['constraint']) : 
                          strtoupper($tableNameOnly . '_' . implode('_', $alter['columns']) . '_UK');
            
            $uniqueConstraint = [
                'name' => $uniqueName,
                'columns' => array_map('strtoupper', $alter['columns'])
            ];
            
            // Add source information if this came from an INDEX statement
            if (isset($alter['source']) && $alter['source'] === 'INDEX') {
                $uniqueConstraint['source'] = 'INDEX';
            }
            
            $constraints['uniques'][] = $uniqueConstraint;
        }
    }
    
    return $constraints;
}

/**
 * Maps SQL data type to GeraClasses format
 * @param string $sqlType SQL data type
 * @return string GeraClasses data type
 */
function mapSQLTypeToGeraClasses($sqlType) {
    $sqlType = strtolower($sqlType);
	$debug=false;
    if ($sqlType === 'timestamp without time zone'){
		$debug=true;
	}
    // Extract base type without length/precision
    if (preg_match('/^(\w+(?:\s+\w+(?:\s+\w+)*)?)/', $sqlType, $matches)) {
        $baseType = $matches[1];
        if ($debug)
			{
				// echo 'sqlType: |'.$sqlType.'|'.$baseType.'|';exit;
				//exit;
			}
        // Map common SQL types to GeraClasses types
        switch (strtolower($baseType)) {
            case 'int':
            case 'integer':
            case 'int4':
                return 'INTEGER';
                
            case 'bigint':
            case 'int8':
                return 'BIGINT';
                
            case 'smallint':
            case 'int2':
                return 'SMALLINT';
                
            case 'decimal':
            case 'numeric':
                return 'DOUBLE PRECISION';
                
            case 'real':
            case 'float4':
            case 'float':
            case 'double':
            case 'double precision':
                return 'FLOAT';
                
            case 'char':
            case 'character':
            case 'varchar':
            case 'character varying':
                return 'VARCHAR';
                
            case 'text':
            case 'tinytext':
            case 'mediumtext':
            case 'longtext':
                return 'TEXT';
                
            case 'date':
                return 'DATE';
                
            case 'time':
                return 'DATE';
                
			case 'timestamp without time zone':
			case 'timestamp':
            case 'datetime':
                return 'DATE';
                
            case 'boolean':
            case 'bool':
                return 'BIT';
                
            case 'bytea':
            case 'blob':
            case 'binary':
                return 'IMAGE/LONG BINARY';
                
            case 'json':
            case 'jsonb':
                return 'TEXT';
                
            case 'point':
            case 'linestring':
            case 'polygon':
            case 'geometry':
                return 'GEOMETRY';
                
            default:
                return 'VARCHAR';
        }
    }
    if ($debug)
		{echo 'mapSQLTypeToGeraClasses sqlType: |'.$sqlType.'|';exit;}
    return 'VARCHAR';
}

/**
 * Processes foreign key relationships and adds to XML
 * @param object $doc XML document
 * @param object $relacionamentos Relacionamentos XML node
 * @param array $alterStatements ALTER TABLE statements
 * @param array $tables Table definitions
 */
function processForeignKeyRelationships($doc, $relacionamentos, $alterStatements, $tables) {
    $relationId = 1;
    
    foreach ($alterStatements as $fk) {
        // Skip if this is not a foreign key statement
        if (!isset($fk['ref_table'])) {
            continue;
        }
        
        // Create Relacionamento element
        $relacionamento = $doc->create_element("Relacionamento");
        $relacionamento->set_attribute("id", "R" . $relationId++);
        
        // Use constraint name if available, otherwise generate a name
        $fkName = isset($fk['fk_name']) ? $fk['fk_name'] : 
                 (isset($fk['constraint']) && !empty($fk['constraint']) ? $fk['constraint'] : 
                 'FK_' . $fk['table'] . '_' . $fk['ref_table']);
                 
        $relacionamento->set_attribute("nome_fis", $fkName);
        $relacionamento->set_attribute("nome_log", "FK_" . $fk['table'] . "_" . $fk['ref_table']);
        
        // Set parent table
        $relacionamento->set_attribute("parent", $fk['ref_table']);
        
        // Set child table
        $relacionamento->set_attribute("child", $fk['table']);
        
        // Set cardinality (always 1:N for foreign keys)
        $relacionamento->set_attribute("card_parent", "1");
        $relacionamento->set_attribute("card_child", "n");
        
        // Set on delete/update actions
        if (isset($fk['on_delete']) && !empty($fk['on_delete'])) {
            $relacionamento->set_attribute("on_delete", strtolower($fk['on_delete']));
        }
        
        if (isset($fk['on_update']) && !empty($fk['on_update'])) {
            $relacionamento->set_attribute("on_update", strtolower($fk['on_update']));
        }
        
        // Add campos
        $childColumns = explode(',', preg_replace('/[`"\s]/', '', $fk['columns']));
        $parentColumns = explode(',', preg_replace('/[`"\s]/', '', $fk['ref_columns']));
        
        for ($i = 0; $i < count($childColumns); $i++) {
            $campo = $doc->create_element("Campo");
            $campo->set_attribute("child", $childColumns[$i]);
            $campo->set_attribute("parent", $parentColumns[$i]);
            $relacionamento->append_child($campo);
        }
        
        // Add relacionamento to relacionamentos
        $relacionamentos->append_child($relacionamento);
    }
}

/**
 * Main function to convert DDL to XML
 * @param string $ddlContent DDL content
 * @return string XML content
 */
function convertDDLToXML($ddlContent) {
    // Normalize and clean DDL
    $cleanDDL = normalizeAndCleanDDL($ddlContent);
    
    // Extract comments from the full DDL
    $comments = extractComments($ddlContent);
    
    // Extract CREATE TABLE statements
    $tables = extractCreateTableStatements($cleanDDL);
    
    // Create XML document
    $doc = new_dom_document("1.0");
    $root = $doc->create_element("GeraClasses");
    $doc->append_child($root);
    
    // Create Entidades element
    $entidades = $doc->create_element("Entidades");
    $entidades->set_attribute("quantos", count($tables));
    $root->append_child($entidades);
    
    // Process each table
    foreach ($tables as $tableName => $tableDefinition) {
        processTableDefinition($doc, $entidades, $tableName, $tableDefinition, $comments);
    }
    
    // Return XML as string
    return $doc->dump_mem(true);
}

/**
 * Generates JSON Schema from DDL
 * @param string $ddlContent DDL content
 * @return string JSON Schema
 */
function generateJSONSchemaFromDDL($ddlContent) {
    // Normalize and clean DDL
    $cleanDDL = normalizeAndCleanDDL($ddlContent);
    
    // Extract comments from the full DDL
    $comments = extractComments($ddlContent);
    
    // Extract CREATE TABLE statements
    $tables = extractCreateTableStatements($cleanDDL);
    
    // Extract ALTER TABLE statements
    $alterStatements = extractAlterTableStatements($cleanDDL);
    
    // Build JSON Schema
    $schema = [
        '$schema' => 'http://json-schema.org/draft-07/schema#',
        'title' => 'Database Schema',
        'description' => 'JSON Schema representation of database structure',
        'type' => 'object',
        'definitions' => []
    ];
    
    // Process each table
    foreach ($tables as $tableName => $tableDefinition) {
        // Extract schema and table name if present
        $tableParts = explode('.', $tableName);
        $tableSchema = count($tableParts) > 1 ? $tableParts[0] : '';
        $tableNameOnly = count($tableParts) > 1 ? $tableParts[1] : $tableName;
        
        // Extract table comment
        $tableComment = '';
        if (preg_match('/COMMENT\s*=\s*[\'"]([^\'"]*)[\'"]/', $tableDefinition, $commentMatch)) {
            $tableComment = $commentMatch[1];
        }
        
        // If no comment found in table definition, check external comments
        if (empty($tableComment) && isset($comments['tables'][$tableNameOnly])) {
            $tableComment = $comments['tables'][$tableNameOnly];
        }
        
        // Create table schema
        $tableSchema = [
            'type' => 'object',
            'title' => strtoupper($tableNameOnly),
            'description' => !empty($tableComment) ? $tableComment : strtoupper($tableNameOnly),
            'properties' => [],
            'required' => []
        ];
        
        // Parse columns
        $columns = parseTableColumns($tableDefinition);
        
        // Extract constraints
        $constraints = extractConstraints($tableDefinition, $tableNameOnly, $alterStatements);
        
        // Add columns to properties
        foreach ($columns as $columnName => $columnDef) {
            // Check for column comment
            $columnComment = $columnDef['comment'] ?? '';
            
            // If no comment found in column definition, check external comments
            if (empty($columnComment) && isset($comments['columns'][$tableNameOnly][$columnName])) {
                $columnComment = $comments['columns'][$tableNameOnly][$columnName];
            }
            
            // Map SQL type to JSON Schema type
            $jsonType = mapSQLTypeToJSONSchemaType($columnDef['type']);
            
            // Create property
            $property = [
                'type' => $jsonType['type'],
                'description' => !empty($columnComment) ? $columnComment : strtoupper($columnName)
            ];
            
            // Add format if applicable
            if (!empty($jsonType['format'])) {
                $property['format'] = $jsonType['format'];
            }
            
            // Add to properties
            $tableSchema['properties'][strtoupper($columnName)] = $property;
            
            // Add to required if not nullable
            if (!$columnDef['nullable']) {
                $tableSchema['required'][] = strtoupper($columnName);
            }
        }
        
        // Add primary key information
        if (!empty($constraints['pk'])) {
            $tableSchema['primaryKey'] = array_map('strtoupper', $constraints['pk']);
        }
        
        // Add unique constraints
        if (!empty($constraints['uniques'])) {
            $tableSchema['uniqueKeys'] = [];
            foreach ($constraints['uniques'] as $unique) {
                $tableSchema['uniqueKeys'][$unique['name']] = array_map('strtoupper', $unique['columns']);
            }
        }
        
        // Add to definitions
        $schema['definitions'][strtoupper($tableNameOnly)] = $tableSchema;
    }
    
    // Add foreign key relationships
    $relationships = [];
    foreach ($alterStatements as $alter) {
        if (isset($alter['ref_table'])) {
            $relationships[] = [
                'name' => !empty($alter['constraint']) ? $alter['constraint'] : 'FK_' . $alter['table'] . '_' . $alter['ref_table'],
                'source' => [
                    'table' => $alter['table'],
                    'schema' => $alter['schema'],
                    'columns' => explode(',', $alter['columns'])
                ],
                'target' => [
                    'table' => $alter['ref_table'],
                    'schema' => $alter['ref_schema'],
                    'columns' => explode(',', $alter['ref_columns'])
                ],
                'onDelete' => $alter['on_delete'],
                'onUpdate' => $alter['on_update']
            ];
        }
    }
    
    if (!empty($relationships)) {
        $schema['relationships'] = $relationships;
    }
    
    return json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
}

/**
 * Maps SQL data type to JSON Schema type
 * @param string $sqlType SQL data type
 * @return array JSON Schema type and format
 */
function mapSQLTypeToJSONSchemaType($sqlType) {
    $sqlType = strtolower($sqlType);
    
    // Extract base type without length/precision
    if (preg_match('/^(\w+)/', $sqlType, $matches)) {
        $baseType = $matches[1];
        
        // Map common SQL types to JSON Schema types
        switch ($baseType) {
            case 'int':
            case 'integer':
            case 'int4':
            case 'tinyint':
            case 'smallint':
            case 'mediumint':
            case 'bigint':
                return ['type' => 'integer'];
                
            case 'decimal':
            case 'numeric':
            case 'float':
            case 'double':
                return ['type' => 'number'];
                
            case 'char':
            case 'varchar':
            case 'tinytext':
            case 'text':
            case 'mediumtext':
            case 'longtext':
                return ['type' => 'string'];
                
            case 'date':
                return ['type' => 'string', 'format' => 'date'];
                
            case 'datetime':
            case 'timestamp':
                return ['type' => 'string', 'format' => 'date-time'];
                
            case 'time':
                return ['type' => 'string', 'format' => 'time'];
                
            case 'boolean':
            case 'bool':
                return ['type' => 'boolean'];
                
            case 'json':
            case 'jsonb':
                return ['type' => 'object'];
                
            case 'array':
                return ['type' => 'array'];
                
            case 'point':
            case 'linestring':
            case 'polygon':
            case 'geometry':
                return ['type' => 'object', 'format' => 'geojson'];
                
            default:
                return ['type' => 'string'];
        }
    }
    
    return ['type' => 'string'];
}

/**
 * Generates compact JSON representation from DDL
 * @param string $ddlContent DDL content
 * @return string Compact JSON
 */
function generateCompactJSONFromDDL($ddlContent) {
    // Normalize and clean DDL
    $cleanDDL = normalizeAndCleanDDL($ddlContent);
    
    // Extract comments from the full DDL
    $comments = extractComments($ddlContent);
    
    // Extract CREATE TABLE statements
    $tables = extractCreateTableStatements($cleanDDL);
    
    // Extract ALTER TABLE statements
    $alterStatements = extractAlterTableStatements($cleanDDL);
    
    $result = [
        'tables' => [],
        'relationships' => []
    ];
    
    // Process each table
    foreach ($tables as $tableName => $tableDefinition) {
        // Extract schema and table name if present
        $tableParts = explode('.', $tableName);
        $tableSchema = count($tableParts) > 1 ? $tableParts[0] : '';
        $tableNameOnly = count($tableParts) > 1 ? $tableParts[1] : $tableName;
        
        // Extract table comment
        $tableComment = '';
        if (preg_match('/COMMENT\s*=\s*[\'"]([^\'"]*)[\'"]/', $tableDefinition, $commentMatch)) {
            $tableComment = $commentMatch[1];
        }
        
        // If no comment found in table definition, check external comments
        if (empty($tableComment) && isset($comments['tables'][$tableNameOnly])) {
            $tableComment = $comments['tables'][$tableNameOnly];
        }
        
        // Parse columns
        $columns = parseTableColumns($tableDefinition);
        
        // Extract constraints
        $constraints = extractConstraints($tableDefinition, $tableNameOnly, $alterStatements);
        
        $tableData = [
            'name' => strtoupper($tableNameOnly),
            'schema' => strtoupper($tableSchema),
            'description' => $tableComment,
            'columns' => [],
            'primaryKey' => !empty($constraints['pk']) ? array_map('strtoupper', $constraints['pk']) : [],
            'uniqueKeys' => []
        ];
        
        // Add columns
        foreach ($columns as $columnName => $columnDef) {
            // Check for column comment
            $columnComment = $columnDef['comment'] ?? '';
            
            // If no comment found in column definition, check external comments
            if (empty($columnComment) && isset($comments['columns'][$tableNameOnly][$columnName])) {
                $columnComment = $comments['columns'][$tableNameOnly][$columnName];
            }
            
            $tableData['columns'][] = [
                'name' => strtoupper($columnName),
                'type' => $columnDef['type'],
                'nullable' => $columnDef['nullable'],
                'autoIncrement' => $columnDef['auto_increment'],
                'description' => $columnComment
            ];
        }
        
        // Add unique constraints
        if (!empty($constraints['uniques'])) {
            foreach ($constraints['uniques'] as $unique) {
                $tableData['uniqueKeys'][] = [
                    'name' => $unique['name'],
                    'columns' => array_map('strtoupper', $unique['columns'])
                ];
            }
        }
        
        $result['tables'][] = $tableData;
    }
    
    // Add relationships
    foreach ($alterStatements as $alter) {
        if (isset($alter['ref_table'])) {
            $result['relationships'][] = [
                'name' => !empty($alter['constraint']) ? $alter['constraint'] : 'FK_' . $alter['table'] . '_' . $alter['ref_table'],
                'sourceTable' => $alter['table'],
                'sourceSchema' => $alter['schema'],
                'sourceColumns' => explode(',', strtoupper($alter['columns'])),
                'targetTable' => $alter['ref_table'],
                'targetSchema' => $alter['ref_schema'],
                'targetColumns' => explode(',', strtoupper($alter['ref_columns'])),
                'onDelete' => $alter['on_delete'],
                'onUpdate' => $alter['on_update']
            ];
        }
    }
    
    return json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
}

/**
 * Extracts sequence definitions from DDL content
 * @param string $ddlContent Full DDL content
 * @return array Array of sequence names
 */
function extractSequences($ddlContent) {
    $sequences = [];
    
    // Pattern to match CREATE SEQUENCE statements
    $pattern = "/CREATE\s+SEQUENCE\s+(?:[\w\.]+\.)?([^\s;]+)/i";
    
    if (preg_match_all($pattern, $ddlContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $sequenceName = $match[1];
            // Remove quotes if present
            $sequenceName = trim($sequenceName, '"\'`');
            $sequences[] = $sequenceName;
        }
    }
    
    return $sequences;
}

/**
 * Extract foreign keys for a specific table from ALTER TABLE statements
 * @param array $alterStatements Array of ALTER TABLE statements
 * @param string $tableName Table name
 * @return array Array of foreign key definitions
 */
function extractForeignKeys($alterStatements, $tableName) {
    $foreignKeys = [];
    foreach ($alterStatements as $alter) {
        // Skip if not for this table or not a foreign key
        if (strtoupper($alter['table']) !== strtoupper($tableName) || 
            !isset($alter['ref_table'])) {
            continue;
        }
        
        $foreignKeys[] = $alter;
    }
    return $foreignKeys;
}

/**
 * Adds Parents element to entidades based on foreign key relationships
 * @param object $doc XML document
 * @param array $entidadesMap Map of entidade elements by table name
 * @param array $alterStatements ALTER TABLE statements
 */
function addParentsElement($doc, $entidadesMap, $alterStatements) {
    foreach ($alterStatements as $fk) {
        // Skip if this is not a foreign key statement
        if (!isset($fk['ref_table'])) {
            continue;
        }
        
        $childTableName = strtoupper($fk['table']);
        $parentTableName = strtoupper($fk['ref_table']);
        
        // Skip if we don't have both tables in our map
        if (!isset($entidadesMap[$childTableName]) || !isset($entidadesMap[$parentTableName])) {
            continue;
        }
        
        $parentEntidade = $entidadesMap[$parentTableName];
        
        // Check if Parents element already exists
        $parentsElements = $parentEntidade->get_elements_by_tagname("Parents");
        if (count($parentsElements) > 0) {
            $parentsElement = $parentsElements[0];
        } else {
            // Create Parents element
            $parentsElement = $doc->create_element("Parents");
            $parentEntidade->append_child($parentsElement);
        }
        
        // Create Child element
        $relatElement = $doc->create_element("Relat");
        $relatElement->set_attribute("ParentName", $parentTableName);
        $relatElement->set_attribute("ChildName", $childTableName);
        $relatElement->set_attribute("Cardinality", 0);
        $relatElement->set_attribute("Tipo", 1);
        $relatElement->set_attribute("FKName", isset($fk['constraint']) ? $fk['constraint'] : 'FK_' . $childTableName . '_' . $parentTableName);
        
        // Add column mappings
        $childColumns = explode(',', preg_replace('/[`"\s]/', '', $fk['columns']));
        $parentColumns = explode(',', preg_replace('/[`"\s]/', '', $fk['ref_columns']));
        
        for ($i = 0; $i < count($childColumns); $i++) {
            $columnMapping = $doc->create_element("ColunaRelParent");
            $columnMapping->set_attribute("ChildName", strtoupper($childColumns[$i]));
            $columnMapping->set_attribute("ParentName", strtoupper($parentColumns[$i]));
            $relatElement->append_child($columnMapping);
        }
        
        $parentsElement->append_child($relatElement);
    }
}
