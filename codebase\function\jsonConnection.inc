<?php
class JSON_CONNECTION{
	private $jsonFile;
	private $json;
	public $configDbType='json';
	public $configDbSchema='public';
	public $configDbName='json';
	public $schema='public';
	public $databaseType='json';
	public function __construct($jsonFile){
		$this->jsonFile=$jsonFile;
		$json=trim(file_get_contents($jsonFile, true));
		$jsonLimpo=str_replace('var tbDefs=', '', $json);
		if (substr($jsonLimpo, -1)==';') $jsonLimpo=substr($jsonLimpo, 0, -1);
		$this->json=json_decode($jsonLimpo, true);
	}
	function getArrayTabelas($somenteTb=true, $ajax=false, $soPublic=true, $soSchema='public'){
		$tabs=[];
		foreach ($this->json['entities'] as $physical=>$defs){
			$logical=$defs['logical'];
			$tab=(object)'';
			$tab->from='json';
			$tab->schema=$this->schema;
			$tab->name=strtolower(($this->schema!='public'?$this->schema.'.':'').$physical);
			$tab->comment=$logical;
			$cols=[];
			foreach($defs['columns'] as $colphys=>$colDef){
				$col=$this->getColumnFromDef($physical, $logical, $colphys, $colDef);
				// $col->name=strtolower($colphys);
				// $col->type=$this->mapaTipo($colDef['type']);
				// $col->not_null=!$colDef['nullable'];
				// $col->primary_key=$colDef['pk'];
				// $col->comment=$logical.' - '.$colDef['logical'].' - '.$col->type;
				// $col->max_length=$colDef['size']??null;
				// $col->has_default=(!empty($colDef['default']));
				// $col->default_value=$colDef['default']??null;
				$cols[$col->name]=$col;
			}
			$tab->cols=$cols;
			$tabs[$tab->name]=$tab;
		}
		return $tabs;
	}
	function getArraySeqs(){
		$seqs=[];
		foreach ($this->json['entities'] as $physical=>$defs){
			foreach($defs['columns'] as $colphys=>$colDef){
				if ($colDef['sequence']){
					$seqs[]=strtolower($physical).'_'.strtolower($colphys).'_seq';
				}
			}
		}
		return $seqs;
	}
	function MetaForeignKeys($table, $owner=false, $upper=false){
		$debug=false;
		$table=strtoupper($table);
		$tabDef=$this->json['entities'][$table];
		$rels=$this->json['relationships']??[];
		if (!$tabDef) return [];
		$fks=[];
		foreach($tabDef['columns'] as $colphys=>$colDef){
			if (isset($colDef['fkrefs'])){
				if (count($colDef['fkrefs'])>0){
					foreach($colDef['fkrefs'] as $fkref){
						if ($debug) echo "fkref ".$fkref."<br/>";
						if (isset($rels[$fkref])){
							// echo nl2br(print_r($rels[$fkref], true))."<br/>";
							$cols=[];
							foreach($rels[$fkref]['columns'] as $col){
								$cols[$col['parent']]=$col['child'];
							}
							$fks[$fkref]=[$rels[$fkref]['sourceTable'].'='.implode(',', array_keys($cols)).'='.implode(',', $cols)];
						}
					}
				}else{
					if ($debug) echo "fkrefs vazia<br/>";
				}
			}else{
				if ($debug) echo "Não tem fkrefs em ".$colphys."<br/>";
			}
		}
		return $fks;
	}
	function MetaIndexes($table){
		global $geraIndicesNasFks, $geraIndicesNasGeoms;
		if (!isset($geraIndicesNasFks)) $geraIndicesNasFks=true;
		if (!isset($geraIndicesNasGeoms)) $geraIndicesNasGeoms=true;
		// echo '<br/>'.nl2br(print_r($this->json, true))."<br/>";exit;
		$tabDef=$this->json['entities'][$table];
		if (empty($tabDef)) $tabDef=$this->json['entities'][strtoupper($table)];
		if (empty($tabDef)) $tabDef=$this->json['entities'][strtolower($table)];
		if (empty($tabDef)) return [];
		// echo '<br/>'.$table.' = '.nl2br(print_r($tabDef, true))."<br/>";exit;
		$indexes=[];
		if (isset($tabDef['indexes'])) {
			// quando houver índices na definição da tabela, incluir aqui
			foreach($tabDef['indexes'] as $idx){
				$indexes[]=$idx['name'];
			}
		}
		if (isset($tabDef['constraints'])) {
			foreach($tabDef['constraints'] as $cname=>$ct){
				if ($ct['type']=='unique'){
					$idxDef='CREATE UNIQUE INDEX '.strtolower($cname).' ON '.($this->schema!='public'?$this->schema.'.':'').strtolower($table).' USING btree ('.strtolower(implode(',', $ct['columns'])).')';
					$indexes[$cname]=$idxDef;
				}
			}
		}
		if ($geraIndicesNasFks) {
			$fks=$this->MetaForeignKeys($table);
			foreach($fks as $fkname=>$fkdef){
				// echo nl2br(print_r($fkdef, true))."<br/>";exit;
				$cols=[];
				foreach($fkdef as $colFkdef){
					$partes=explode('=', $colFkdef);
					$colsFrom=explode(',',$partes[1]);
					$colsTo=explode(',',$partes[2]);
				}
				$idxDef='CREATE INDEX '.strtolower($fkname).' ON '.($this->schema!='public'?$this->schema.'.':'').strtolower($table).' USING btree ('.strtolower(implode(',', $colsTo)).')';
				$indexes[$fkname]=$idxDef;
			}
		}
		if ($geraIndicesNasGeoms) {
			// echo nl2br(print_r($tabDef, true))."<br/>";
			foreach($tabDef['columns'] as $colphys=>$colDef){
				// echo '<br/>'. $colphys.' = '.nl2br(print_r($colDef, true))."<br/>";
				if (in_array(strtoupper($colDef['type']), ['GEOMETRY', 'POINT', 'POINTZ', 'MULTIPOINT', 'MULTIPOINTZ', 'LINESTRING', 'LINESTRINGZ', 'MULTILINESTRING', 'MULTILINESTRINGZ', 'POLYGON', 'POLYGONZ', 'MULTIPOLYGON', 'MULTIPOLYGONZ'])){
					$idxName=strtolower($table.'_'.$colphys.'_gidx');
					$idxDef='CREATE INDEX '.$idxName.' ON '.($this->schema!='public'?$this->schema.'.':'').$table.' USING gist ('.strtolower($colphys).')';
					$indexes[$idxName]=$idxDef;
					// echo $idxName.' = '.$idxDef."<br/>";exit;
				}
			}
		}
		return $indexes;
	}
	function mapaTipo($tipo, $length){
		$tipo=strtoupper($tipo);
		if (in_array($tipo, ['SMALLINT'])) return ['int2',2];
		if (in_array($tipo, ['INT', 'INTEGER'])) return ['int4',4];
		if (in_array($tipo, ['VARCHAR', 'CHAR'])) return ['varchar', $length];
		if (in_array($tipo, ['TEXT', 'NTEXT'])) return ['text', -1];
		if (in_array($tipo, ['DATE', 'DATETIME'])) return ['timestamp', 8];
		if (in_array($tipo, ['BOOL', 'BOOLEAN','BIT'])) return ['bool', 1];
		if (in_array($tipo, ['FLOAT', 'DOUBLE PRECISION', 'DOUBLE', 'DECIMAL'])) return ['float8', 8];
		$tiposGeom=[
			'GEOMETRY'=>'Geometry',
			'POINT'=>'Point',
			'POINTZ'=>'PointZ',
			'MULTIPOINT'=>'MultiPoint',
			'MULTIPOINTZ'=>'MultiPointZ',
			'LINESTRING'=>'Linestring',
			'LINESTRINGZ'=>'LineStringZ',
			'MULTILINESTRING'=>'MultiLineString',
			'MULTILINESTRINGZ'=>'MultiLineStringZ',
			'POLYGON'=>'Polygon',
			'POLYGONZ'=>'PolygonZ',
			'MULTIPOLYGON'=>'MultiPolygon',
			'MULTIPOLYGONZ'=>'MultiPolygonZ'
		];
		// if (in_array($tipo, $tiposGeom)) {
		if (isset($tiposGeom[$tipo])) {
			// echo 'geometry('.$tiposGeom[$tipo].',4326)';exit;
			return ['geometry', 'geometry('.$tiposGeom[$tipo].',4326)', $tiposGeom[$tipo]];
		}
		return [$tipo, $length];
	}
	function getColumnFromDef($physical, $logical, $colphys, $colDef){
		$tipo=$this->mapaTipo($colDef['type'], $colDef['size']??null);
		$col=(object)'';
		$col->name=strtolower($colphys);
		$col->type=$tipo[0];
		$col->not_null=!$colDef['nullable'];
		$col->primary_key=$colDef['pk'];
		$col->comment=$logical.' - '.$colDef['logical'].' - '.$colDef['type'];
		$col->max_length=$tipo[1];
		$col->has_default=(!empty($colDef['default']));
		$col->default_value=$colDef['default']??null;
		if ($tipo[0]=='geometry') {
			$col->formated=$tipo[1];
			// echo $col->type."=================<br/>";
			// echo nl2br(print_r($col, true))."<br/>";exit;
		}
		return $col;
	}

}
?>
