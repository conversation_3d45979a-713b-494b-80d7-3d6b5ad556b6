<?php

?>
<style>
#mapWrapper{
	width:100%;
	height: 96%;
	position:relative;
}
#divModelo{
	position:relative;
}
#maptools{
	position:absolute;
	top:0;
	left:0;
	z-index:10000;
	background-color: #ffffff;
	width: 100%;
}
#info{
	border:solid 1px lightgray;
}
.mapfull{
	width:100%;
	height: 96%;
}
.sttable{
	border: dotted 1px gray;
	padding: 3px;
	background-color: #ffffff33;
	z-index: 1000;
	display:inline-block;
}
.tbName{
	font-weight: bold;
	font-size: 1.2em;
}
.stcolumn{
	font-size: 0.8em;
	background-color: #ffffff;
}
</style>
<script src="<?=$layoutURL?>/scripts/leaflet/leaflet.js"></script>
<script src="defmodelo.js"></script>
<div id="mapWrapper">
	<div class="mapfull" id="divModelo"></div>
	<div id="maptools">
		<span class="simulink" onclick="centralizar();"><i class="fa fa-crosshairs">&nbsp;</i>Centralizar</span>
		&nbsp;|&nbsp;
		<span id="info"></span>
	</div>
</div>
<script>
var mapa;
$(window).on('load',function() {
	resize();
	var mapBounds=new L.latLngBounds([[900,0], [0,900]]);
	var mapOptions={
		maxZoom: 20,
		minZoom: 0,
		crs: L.CRS.Simple,
		// maxBounds:mapBounds
	};
	mapa = L.map('divModelo', mapOptions);
	mapa.setView([100,100], 0);
	mapa.on('mousemove', function(e){
		$('#info').html('Zooml: '+mapa.getZoom()+' X: '+e.latlng.lng.toFixed(0)+' Y: '+e.latlng.lat.toFixed(0));
	});
	drawTables();
	$('.sttable').each(function() {
		makeDraggable(this);
	});
	console.log("Drawing FK lines on load");
	drawFkLines();
	$(window).on('resize', resize);
});
function resize(){
	var pos=$('#divModelo').position();
	var mwt=$('#mapWrapper').offset().top;
	var posf=$('#boxfoot').position();

	var mt=$('#maptools').outerHeight();
	var mch=$('#mapWrapper').outerHeight();
	$('#divModelo').css('height',(Math.ceil(posf.top)-mwt)+'px');
	//showAlert(mch);
}
function loadTables(){
	// we are not loading tables from the server at this moment
	$.ajax({
		url:'ajax_modelo.php',
		data:{m:'getTables'},
		dataType:'json',
		success: function(response){
			tbDefs=response;
			drawTables();
		}
	});
}
function drawTables(){
	for(var tb in tbDefs){
		drawTable(tb, tbDefs[tb]);
	}
}

function drawTable(tb, defs){
	var tbDiv=L.DomUtil.create('div', 'sttable', mapa._container);
	// L.DomUtil.setPosition(tbDiv, defs.position);
	tbDiv.style.left=defs.position[0]+'px';
	tbDiv.style.top=defs.position[1]+'px';
	tbDiv.id=tb;
	tbDiv.style.position='absolute';
	var colDiv=L.DomUtil.create('div', 'tbName', tbDiv);
	colDiv.id=tb+'_name';
	colDiv.style.position='relative';
	colDiv.title=defs.logical;
	colDiv.innerHTML=tb;
	for (var col in defs.columns){
		var cDef=defs.columns[col];
		var colDiv=L.DomUtil.create('div', 'stcolumn', tbDiv);
		colDiv.id=tb+'_'+col;
		colDiv.style.position='relative';
		colDiv.innerHTML=col;
		colDiv.title=cDef.logical+'\n'+cDef.type+'\n'+(cDef?.size?'('+cDef.size+')\n':'')+(cDef?.nullable?'nullable\n':'')+(cDef?.pk?'pk\n':'')+(cDef?.sequence?'sequence':'');
	}
	
	// Make the table draggable
	makeDraggable(tbDiv);
}

// Function to draw FK relationship lines
function drawFkLines() {
    // First, remove all existing FK lines and indicators
    $('.fk-line').remove();
    $('.phrase-indicator, .inverse-phrase-indicator').remove();
    
    // Then redraw all FK lines
    for(var sourceTable in tbDefs) {
        var tableDef = tbDefs[sourceTable];
        for(var sourceCol in tableDef.columns) {
            var colDef = tableDef.columns[sourceCol];
            if(colDef.fk) {
                console.log("Drawing FK line from", sourceTable, sourceCol, "to", colDef.fk.table, colDef.fk.column);
                drawFkLine(sourceTable, sourceCol, colDef.fk.table, colDef.fk.column);
            }
        }
    }
}

// Function to draw a single FK relationship line
function drawFkLine(sourceTable, sourceCol, targetTable, targetCol) {
    // Get source and target elements
    var sourceElem = document.getElementById(sourceTable + '_' + sourceCol);
    var targetElem = document.getElementById(targetTable + '_' + targetCol);
    
    if (!sourceElem || !targetElem) {
        console.log("Cannot find elements for", sourceTable, sourceCol, targetTable, targetCol);
        return;
    }
    
    // Get column definition to check for phrase attributes
    var colDef = tbDefs[sourceTable].columns[sourceCol];
    var hasPhrase = colDef.phrase !== undefined;
    var hasInverse = colDef.inverse !== undefined;
    
    // Get positions
    var sourceRect = sourceElem.getBoundingClientRect();
    var targetRect = targetElem.getBoundingClientRect();
    
    // Get parent table elements
    var sourceTableElem = document.getElementById(sourceTable);
    var targetTableElem = document.getElementById(targetTable);
    
    if (!sourceTableElem || !targetTableElem) return;
    
    var sourceTableRect = sourceTableElem.getBoundingClientRect();
    var targetTableRect = targetTableElem.getBoundingClientRect();
    
    // Determine the centers of each table
    var sourceCenterX = sourceTableRect.left + sourceTableRect.width / 2;
    var sourceCenterY = sourceTableRect.top + sourceTableRect.height / 2;
    var targetCenterX = targetTableRect.left + targetTableRect.width / 2;
    var targetCenterY = targetTableRect.top + targetTableRect.height / 2;
    
    // Calculate the angle between the tables
    var angle = Math.atan2(targetCenterY - sourceCenterY, targetCenterX - sourceCenterX);
    
    // Determine which sides to connect based on the angle
    var x1, y1, x2, y2;
    
    // Source point
    if (Math.abs(angle) < Math.PI/4) { // Right side
        x1 = sourceTableRect.right;
        y1 = sourceRect.top + sourceRect.height/2;
    } else if (Math.abs(angle) > 3*Math.PI/4) { // Left side
        x1 = sourceTableRect.left;
        y1 = sourceRect.top + sourceRect.height/2;
    } else if (angle > 0) { // Bottom side
        x1 = sourceRect.left + sourceRect.width/2;
        y1 = sourceTableRect.bottom;
    } else { // Top side
        x1 = sourceRect.left + sourceRect.width/2;
        y1 = sourceTableRect.top;
    }
    
    // Target point - opposite angle
    if (Math.abs(angle) < Math.PI/4) { // Left side (opposite of right)
        x2 = targetTableRect.left;
        y2 = targetRect.top + targetRect.height/2;
    } else if (Math.abs(angle) > 3*Math.PI/4) { // Right side (opposite of left)
        x2 = targetTableRect.right;
        y2 = targetRect.top + targetRect.height/2;
    } else if (angle > 0) { // Top side (opposite of bottom)
        x2 = targetRect.left + targetRect.width/2;
        y2 = targetTableRect.top;
    } else { // Bottom side (opposite of top)
        x2 = targetRect.left + targetRect.width/2;
        y2 = targetTableRect.bottom;
    }
    
    // Create SVG line
    var svgNS = "http://www.w3.org/2000/svg";
    var svg = document.createElementNS(svgNS, "svg");
    svg.setAttribute("class", "fk-line");
    svg.style.position = "absolute";
    svg.style.top = "0";
    svg.style.left = "0";
    svg.style.width = "100%";
    svg.style.height = "100%";
    svg.style.pointerEvents = "none";
    svg.style.zIndex = "1";
    
    // Create line
    var line = document.createElementNS(svgNS, "line");
    
    // Create marker definition
    var marker = document.createElementNS(svgNS, "marker");
    var markerId = "arrowhead-" + sourceTable + "-" + sourceCol;
    marker.setAttribute("id", markerId);
    marker.setAttribute("markerWidth", "10");
    marker.setAttribute("markerHeight", "7");
    marker.setAttribute("refX", "10");
    marker.setAttribute("refY", "3.5");
    marker.setAttribute("orient", "auto");
    
    var polygon = document.createElementNS(svgNS, "polygon");
    polygon.setAttribute("points", "0 0, 10 3.5, 0 7");
    polygon.setAttribute("fill", "#000000");
    
    marker.appendChild(polygon);
    
    // Add defs section to SVG
    var defs = document.createElementNS(svgNS, "defs");
    defs.appendChild(marker);
    
    // Set line endpoints
    line.setAttribute("x1", x1);
    line.setAttribute("y1", y1);
    line.setAttribute("x2", x2);
    line.setAttribute("y2", y2);
    line.setAttribute("stroke", "#000000");
    line.setAttribute("stroke-width", "1");
    line.setAttribute("marker-end", "url(#" + markerId + ")");
    
    // Add elements to SVG
    svg.appendChild(defs);
    svg.appendChild(line);
    
    // Add SVG to document
    document.body.appendChild(svg);
    
    // Add phrase indicators if present
    if (hasPhrase || hasInverse) {
        // Calculate midpoints for phrase indicators
        var startX = x1 + (x2 - x1) * 0.25; // 25% along the line
        var startY = y1 + (y2 - y1) * 0.25;
        var endX = x1 + (x2 - x1) * 0.75; // 75% along the line
        var endY = y1 + (y2 - y1) * 0.75;
        
        // Add phrase indicator at start if present
        if (hasPhrase) {
            var phraseDiv = document.createElement('div');
            phraseDiv.className = 'phrase-indicator';
            phraseDiv.innerHTML = 'P';
            phraseDiv.title = colDef.phrase;
            phraseDiv.style.position = 'absolute';
            phraseDiv.style.left = startX + 'px';
            phraseDiv.style.top = startY + 'px';
            phraseDiv.style.backgroundColor = '#fff';
            phraseDiv.style.border = '1px solid #007bff';
            phraseDiv.style.borderRadius = '50%';
            phraseDiv.style.padding = '2px 4px';
            phraseDiv.style.fontSize = '10px';
            phraseDiv.style.color = '#007bff';
            phraseDiv.style.fontWeight = 'bold';
            phraseDiv.style.zIndex = '2';
            phraseDiv.style.cursor = 'help';
            document.body.appendChild(phraseDiv);
        }
        
        // Add inverse phrase indicator at end if present
        if (hasInverse) {
            var inverseDiv = document.createElement('div');
            inverseDiv.className = 'inverse-phrase-indicator';
            inverseDiv.innerHTML = 'IP';
            inverseDiv.title = colDef.inverse;
            inverseDiv.style.position = 'absolute';
            inverseDiv.style.left = endX + 'px';
            inverseDiv.style.top = endY + 'px';
            inverseDiv.style.backgroundColor = '#fff';
            inverseDiv.style.border = '1px solid #007bff';
            inverseDiv.style.borderRadius = '50%';
            inverseDiv.style.padding = '2px 4px';
            inverseDiv.style.fontSize = '10px';
            inverseDiv.style.color = '#007bff';
            inverseDiv.style.fontWeight = 'bold';
            inverseDiv.style.zIndex = '2';
            inverseDiv.style.cursor = 'help';
            document.body.appendChild(inverseDiv);
        }
    }
}

// Update lines when tables are dragged
function updateFkLines() {
    // Just call drawFkLines which now handles cleanup
    drawFkLines();
}

// Extend makeDraggable to update FK lines
function makeDraggable(element) {
	var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
	
	// Add cursor style to indicate draggable
	element.style.cursor = 'move';
	
	// Get the header element to use as drag handle
	var header = element.querySelector('.tbName');
	if (header) {
		header.onmousedown = dragMouseDown;
	} else {
		element.onmousedown = dragMouseDown;
	}

	function dragMouseDown(e) {
		e = e || window.event;
		e.preventDefault();
		// Get the mouse cursor position at startup
		pos3 = e.clientX;
		pos4 = e.clientY;
		document.onmouseup = closeDragElement;
		// Call a function whenever the cursor moves
		document.onmousemove = elementDrag;
	}

	function elementDrag(e) {
		e = e || window.event;
		e.preventDefault();
		// Calculate the new cursor position
		pos1 = pos3 - e.clientX;
		pos2 = pos4 - e.clientY;
		pos3 = e.clientX;
		pos4 = e.clientY;
		
		// Set the element's new position
		var newTop = (element.offsetTop - pos2);
		var newLeft = (element.offsetLeft - pos1);
		element.style.top = newTop + "px";
		element.style.left = newLeft + "px";
		
		// Update the info div with position
		$('#info').html('Table: ' + element.id + ' - Position: [' + newLeft + ', ' + newTop + ']');
		
		// Update FK lines
		updateFkLines();
	}

	function closeDragElement() {
		// Stop moving when mouse button is released
		document.onmouseup = null;
		document.onmousemove = null;
		
		// Update the table definition with new position
		if (tbDefs[element.id]) {
			tbDefs[element.id].position = [parseInt(element.style.left), parseInt(element.style.top)];
		}
		
		// Final update of FK lines
		updateFkLines();
	}
}


</script>
