O GeraClasses propriamente dito é um aplicativo web que reúne todas as funcionalidades
previamente descritas. Isto é: dado um modelo de dados, gera um esqueleto de site
completo, incluindo DDLs de criação para todos os bancos suportados pelo GeraDDL, classes
geradas pelo GeraPHP, exemplos de arquivos de configuração, scripts de shell, e testes de
regressão. Uma árvore de diretórios típica seria:

<ul>
	<li><TT>./docroot</TT><BR>Diretório que deverá se tornar o <em>DocumentRoot</em> do Apache para o site.</li><br>
	<li><TT>./config</TT><BR>Configurações do Apache e do próprio site.</li><br>
	<li><TT>./logs</TT><BR>Arquivos de log, do Apache e do site.</li><br>
	<li><TT>./codebase</TT><BR>Base de código do site; pronta para entrar em controle CVS.</li><br>
	<li><TT>./codebase/function/classes/</TT><BR>Classes geradas pelo GeraClasses.</li><br>
	<li><TT>./codebase/php</TT><BR>Bibliotecas utilizadas: ADOdb, PHPLOT, P4C, etc.</li><br>
	<li><TT>./codebase/install/ddl</TT><BR>DDLs, para todos os bancos suportados pelo GeraDDL.</li><br>
	<li><em>etc</em></li>
</ul>
