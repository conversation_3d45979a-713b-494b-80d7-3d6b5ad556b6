<?include("function/function.inc");?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
	<title>Vazio...</title>
	<link rel="stylesheet" type="text/css" href="style/style.css">
<SCRIPT language="JavaScript1.2">
function getPathSaida(campo) {
	document.getElementById("pathsaida").value = "<?=str_replace("\\", "/", getBaseSaida())?>/" + campo.value + "/";
}
</SCRIPT>
</head>
<body bgcolor="whitesmoke"><br>
<br>
<TABLE border="0" cellpadding="2" cellspacing="0">
	<form enctype="multipart/form-data" method="post" action="go.php">
		<input type="hidden" name="MAX_FILE_SIZE" value="10000000">
		<TR valign="middle">
			<td>Arquivo XML:
			</td>
			<td><input name="userfile" type="file" size="80">
			</td>
			<td>
			<?=cospeHintHelp("Arquivo XML gerado pelo EmbarcXML. Upload.")?>
			</td>
		</TR>
		<TR>
			<td>Nome do Projeto:
			</td>
			<td><input  type="Text" size="20" id="projname" name="projname" value="nome_do_projeto" onchange="getPathSaida(this)" onkeypress="getPathSaida(this)" onkeyup="getPathSaida(this)">
			</td>
			<td>
			<?=cospeHintHelp("Nome do projeto; será utilizado para compor o path (veja abaixo).")?>
			</td>
		</TR>
		<TR>
			<td>Path final:
			</td>
			<td><input type="Text" size="90" name="pathsaida" id="pathsaida" disabled>
			</td>
			<td>
			<?=cospeHintHelp("Path, na máquina onde está rodando o GeraClasses, onde serão colocados os resultados (classes, etc) para o XML especificado acima. Levar em consideração que esse diretório deve poder ser escrito pelo usuário que roda o Apache (nobody/nobody).")?>
			</td>
		</TR>
		<?
		if (getLinkSamba()) {
		?>
		<TR>
			<td>Acessível via:
			</td>
			<td><a target="_blank" href="<?=getLinkSamba()?>"><?=getLinkSamba()?></a>
			</td>
			<td>
			<?=cospeHintHelp("Good. Link para o Samba. We like.")?>
			</td>
		</TR>
		<?
		}
		?>
		
		<!--
		<TR>
			<td>Opções:
			</td>
			<td>
			<input type="Checkbox" name="targztoo" value="true" checked>Também me envie um .tar.gz com os resultados&nbsp;<?=cospeHintHelp("Dowload de um .tar.gz com as classes geradas. Feito para levar facilmente para outra máquina.")?><br>
			</td>
		</TR>
		-->
		<Tr>
			<td colspan="2" align="right"><input type="submit" style="font-weight: bold;" value="GeraClasses!">
			</td>
		</TR>
	</FORM>
</TABLE>
<SCRIPT language="JavaScript1.2">
getPathSaida(document.all.projname)
</SCRIPT>
</body>
</html>
