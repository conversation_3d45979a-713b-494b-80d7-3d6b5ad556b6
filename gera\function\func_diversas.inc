<?php
/* Essa função retorna o valor de parâmetro especificado na linha de comando, ou true se o parâmetro foi especificado
   sem valor, ou false se não especificado... */
function getCmdLine($which, $default=null) {
	if (php_sapi_name()!=="cli") return null;
	global $argv, $argc; $total = "";
	if (count($argv) < 2) return null;
	for ($i=1; $i < count($argv); $i++) {
		$total[] = $argv[$i];
	}
	$total = implode($total, " ");
	$bynaco = explode("--",$total);
	foreach ($bynaco as $naco) {
		if ($naco) {
			if (strtoupper(substr($naco, 0, strlen($which) )) == strtoupper($which)) {
				$present = true;
				$a = explode("=", $naco);
				if (count($a) > 1) {
					return $a[1];
				} else {
					return $default;
				}
			}
		}
	}
	return $default;
}

?>