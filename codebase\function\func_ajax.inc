<?
function cospeJSAjax($urlAjax='/ajax.php'){
	global $_functionJSAjaxCuspido, $_configProducao;
	if ($_functionJSAjaxCuspido) return;
?>
<script language="JavaScript1.2">
var _xmlHttp=getXMLHTTP();
var poolxh=new Array();
var urlAjax='<?=$urlAjax?>';
var basePostAjax='NO_LAYOUT=true';
function getXMLHTTPfromPool(){
	var qtos=poolxh.length;
	for (var i=0; i<qtos; i++){
		if (poolxh[i]!=null && poolxh[i].readyState==0) return i;
	}
	poolxh[qtos]=getXMLHTTP();
	return qtos;
}
function getXMLHTTP(){
	var A=null;
	try{
		A=new ActiveXObject("Msxml2.XMLHTTP")
	}catch(e){
		try{
			A=new ActiveXObject("Microsoft.XMLHTTP")
		} catch(oc){
			A=null
		}
	}
	if(!A && typeof XMLHttpRequest != "undefined") A=new XMLHttpRequest();
	return A
}
function getResposta(oque, qs, func){
	isResp=false;
	wait0=true;
	callCnetMulti(oque, qs, null, func);
}
function evalResposta(oque, qs, dest){
	if (null!=dest) callCnetMulti(oque, qs, dest, null);
}
function callCnetMulti(oque, qs, dest, func){
	var qual=getXMLHTTPfromPool();
	var showErro=<?=(@$_configProducao==true?'false':'true')?>;
	if(poolxh[qual]){
		var oDest = '';
		if (null!=dest && dest!='') oDest = '&dest=' + dest;
		//alert(urlAjax+"&ajax="+oque+oDest+"&"+qs);
		//poolxh[qual].open("GET", urlAjax+"&ajax="+oque+oDest+"&"+qs,true);
		poolxh[qual].open("POST", urlAjax,true);
		poolxh[qual].setRequestHeader('Content-Type','application/x-www-form-urlencoded');
		//alert(qs);
		//alert('fez open');
		// Esta função só será executada quando houver uma resposta completa
		if (oDest!=''){
			poolxh[qual].onreadystatechange=function() {
				if(poolxh[qual].readyState==4 && poolxh[qual].responseText) {
					if (false && showErro && dest=='toolbarMapa') {
						diverro=document.getElementById('msgErro');
						if (diverro!=null) {
							diverro.style.display='';
							if (showInfo!=null) showInfo(false, '');
							diverro.innerHTML=poolxh[qual].responseText.toString();
						}
						//alert(poolxh[qual].responseText.toString());
					}
					//alert(dest);
					//alert(poolxh[qual].responseText.toString());
					//prompt('Resposta: ', poolxh[qual].responseText.toString());
						//eval(poolxh[qual].responseText);
					try{
						eval(poolxh[qual].responseText);
					}catch (erro){
						if (showErro){
							if (typeof(showInfo)!='undefined' && showInfo!=null) showInfo(false, '');
							var odiv = document.createElement('div');
							odiv.style.position='absolute';
							odiv.style.left='10px';
							odiv.style.top='10px';
							odiv.style.width='800px';
							odiv.style.height='600px';
							odiv.style.zIndex='5000000';
							odiv.id='mensagemErro';
							odiv.style.border='solid 1px black';
							odiv.style.overflow='auto';
							odiv.style.padding='2px';
							odiv.style.backgroundColor='white';
							var resposta=poolxh[qual].responseText.toString();
							var re=new RegExp('<', 'g');
							resposta=resposta.replace(re, '&lt;');
							re=new RegExp('\r\n', 'g');
							resposta=resposta.replace(re, '<br>');
							odiv.innerHTML='<div id="fechaErro" style="position:absolute;right:10px; top:10px;padding:2px;border: solid 1px;"><a href="#" onClick="javascript:document.body.removeChild(document.getElementById(\'mensagemErro\'));">Fechar</a></div>'+erro.message+"<br>"+resposta;
							document.body.appendChild(odiv);
							alert(erro.toString());
						}
					}
				}
			}
			;
		}else if (func==null){
			poolxh[qual].onreadystatechange=function() {
				if(poolxh[qual].readyState==4 && poolxh[qual].responseText) {
					//alert(poolxh[qual].responseText.toString());
					//eval(poolxh[qual].responseText);
					try{
						eval(poolxh[qual].responseText);
					}catch (erro){
						if (showErro){
							if (typeof(showInfo)!='undefined' && showInfo!=null) showInfo(false, '');
							var odiv = document.createElement('div');
							odiv.style.position='absolute';
							odiv.style.left='10px';
							odiv.style.top='10px';
							odiv.style.width='800px';
							odiv.style.height='600px';
							odiv.style.zIndex='5000000';
							odiv.id='mensagemErro';
							odiv.style.border='solid 1px black';
							odiv.style.overflow='auto';
							odiv.style.padding='2px';
							odiv.style.backgroundColor='white';
							var resposta=poolxh[qual].responseText.toString();
							var re=new RegExp('<', 'g');
							resposta=resposta.replace(re, '&lt;');
							re=new RegExp('\r\n', 'g');
							resposta=resposta.replace(re, '<br>');
							odiv.innerHTML='<div id="fechaErro" style="position:absolute;right:10px; top:10px;padding:2px;border: solid 1px;"><a href="#" onClick="javascript:document.body.removeChild(document.getElementById(\'mensagemErro\'));">Fechar</a></div>'+erro.message+"<br>"+resposta;
							document.body.appendChild(odiv);
							alert(erro.toString());
						}
					}
				}
			}
			;
		}else{
			respAjax='';
			poolxh[qual].onreadystatechange=function() {
				if(poolxh[qual].readyState==4 && poolxh[qual].responseText) {
					//alert(poolxh[qual].responseText.toString());
					eval(func+'(\''+ poolxh[qual].responseText+'\');');
				}
			}
			;
		}
		// DON'T TRY TO TALK WHEN WE'RE LOCAL...
		// Comment out when running from a local file...
		poolxh[qual].send(basePostAjax+"&ajax="+oque+oDest+"&"+qs)
		//resp2=prompt('data:', basePostAjax+"&ajax="+oque+oDest+"&"+qs);
	}
}
function callCnet(oque, qs, dest, func){
	if(_xmlHttp&&_xmlHttp.readyState!=0){
		_xmlHttp.abort();
	}
	_xmlHttp=getXMLHTTP();
	if(_xmlHttp){
		var oDest = '';
		if (null!=dest && dest!='') oDest = '&dest=' + dest;
		//alert(urlAjax+"&ajax="+oque+oDest+"&"+qs);
		_xmlHttp.open("GET", urlAjax+"&ajax="+oque+oDest+"&"+qs,true);
		// Esta função só será executada quando houver uma resposta completa
		if (oDest!=''){
			_xmlHttp.onreadystatechange=function() {
				if(_xmlHttp.readyState==4&&_xmlHttp.responseText) {
					//alert(_xmlHttp.responseText.toString());
					eval(_xmlHttp.responseText);
				}
			}
			;
		}else if (func==null){
			_xmlHttp.onreadystatechange=function() {

				if(_xmlHttp.readyState==4&&_xmlHttp.responseText) {
					//alert(_xmlHttp.responseText.toString());
					eval(_xmlHttp.responseText);
				}
			}
			;
		}else{
			respAjax='';
			_xmlHttp.onreadystatechange=function() {
				if(_xmlHttp.readyState==4&&_xmlHttp.responseText) {
					//alert(_xmlHttp.responseText.toString());
					eval(func+'(\''+ _xmlHttp.responseText+'\');');
				}
			}
			;
		}
		// DON'T TRY TO TALK WHEN WE'RE LOCAL...
		// Comment out when running from a local file...
		_xmlHttp.send(null)
	}
}
</script>
<?
}
function cospeJSAjaxMulti($urlAjax='/ajax.php'){
	global $_functionJSAjaxCuspido, $acao, $_configSiteDevel;
	if ($_functionJSAjaxCuspido) return;
?>
<script language="JavaScript">
var trapErrors=<?=((@$_configSiteDevel)?'true':'false')?>;
//trapErrors=true;
var seCount=0;
var lastAccess=new Date();
var poolxh=new Array();
var poolxhprops=new Array();
var poolIncParser=new Array();
var urlAjax='<?=$urlAjax?>';
var basePostAjax='_byPass=true&NO_LAYOUT=true';
basePostAjax='';
function getXMLHTTPfromPool(){
	var qtos=poolxh.length;
	for (var i=0; i<qtos; i++){
		if (poolxh[i]!=null && poolxh[i].readyState==0) return i;
		if (poolxh[i]!=null && poolxh[i].readyState==4) {
			if (typeof poolxhprops[i].parsing == 'undefined' || poolxhprops[i].parsing==false){
				poolxh[i] = getXMLHTTP();
				poolxhprops[i] = new XhtControl(poolxh[i]);
			}
			return i;
		}
	}
	try{
		poolxh[qtos]=getXMLHTTP();
	}catch(e){

	}
	poolxhprops[qtos] = new XhtControl(poolxh[qtos]);
	return qtos;
}
function getXMLHTTP(){
	var A=null;
	if (typeof ActiveXObject != 'undefined'){
		try{
			A=new ActiveXObject("Msxml2.XMLHTTP")
		}catch(e){
			try{
				A=new ActiveXObject("Microsoft.XMLHTTP")
			} catch(oc){
				A=null
			}
		}
	}
	if (!A) A = new XMLHttpRequest();
	return A;
}
function XhtControl(obj){
	this.xhr=obj;
	this.parsed=0;
	this.parsing=false;
	this.partialResponse='';
	this.received=0;
}
XhtControl.prototype.consomeResposta=function(txt){
	//GLog.write(txt);
	consomeEval(txt);
};
function getAjax(oque, qs, url){
	callCnetMulti(oque, qs, null, null, null, url);
}
function getAjaxFluido(oque, qs, url){
	callCnetMulti(oque, qs, null, null, true, url);
}
function getResposta(oque, qs, func, url){
	callCnetMulti(oque, qs, null, func, null, url);
}
function evalResposta(oque, qs, dest, url){
	if (null!=dest) callCnetMulti(oque, qs, dest, null, null, url);
}
function setLastAccess(){
	lastAccess=new Date();
}
function callCnetMulti(oque, qs, dest, func, parseIncomplete, url){
	if (typeof url=='undefined' || url==null) url=urlAjax;
	if (typeof parseIncomplete=='undefined' || parseIncomplete==null) parseIncomplete=false;
	if (typeof showLog=='undefined') showLog=false;
	if (showLog) GLog.write(oque);
	if (typeof showErro=='undefined') showErro=false;
	var qual=getXMLHTTPfromPool();
	var agora=new Date();
	if (typeof sessionTimeout == 'undefined') sessionTimeout=10;
	if (agora-lastAccess>sessionTimeout*60000){
		if (self.readCookieVal){
			get=readGet();
			c=readCookieVal('CNETSERVERLOGACAO');
			ca=readCookieVal('acao3_cod0');
			if (ca==null) ca='';
			a=(typeof get['acao3_cod0'] == 'undefined' ? ca : get['acao3_cod0']);
			s=readCookieVal('SSID');
			qs+='&_t=true&_s='+s+'&_a='+a+'&_c='+c;
		}
	}
	if (parseIncomplete) qs+='&PI=1';
	if (typeof showErro == 'undefined') showErro=false;
	if(poolxh[qual]){
		var oDest = '';
		if (typeof dest != 'undefined' && null!=dest && dest!='') oDest = '&dest=' + dest;
		if (typeof func == 'undefined') func = null;
		//alert(url+"&ajax="+oque+oDest+"&"+qs);
		if (true && showErro){
			poolxh[qual].open("GET", url+"?ajax="+oque+oDest+"&"+qs,true);
		}else{
			poolxh[qual].open("POST", url,true);
			poolxh[qual].setRequestHeader('Content-Type','application/x-www-form-urlencoded');
		}
		//if (parseIncomplete)
		//poolxh[qual].setRequestHeader('Accept-Encoding','identity');
		//alert(qs);
		//alert('fez open');
		// Esta função só será executada quando houver uma resposta completa
		if (oDest!=''){
			poolxh[qual].onreadystatechange=function() {
				if(typeof poolxh != 'undefined' && poolxh[qual]!=null && poolxh[qual].readyState==4 && poolxh[qual].responseText) {
					if (trapErrors){
						try{
							eval(poolxh[qual].responseText);
						}catch (erro){
							if (showErro){
								cospeErro(erro, poolxh[qual].responseText.toString());
							}else{
								alert('Houve um erro ao recuperar a resposta do servidor. Tente novamente, por favor.');
								try{
									aguarde(false, '');
								}catch(ex){

								}
							}
						}
					}else{
						eval(poolxh[qual].responseText);
					}
				}
			}
			;
		}else if (func==null){
			eval('var orsc=function(){if(typeof poolxh != "undefined" && poolxh['+qual+']!=null){if (parseIncomplete){if (poolxh['+qual+'].readyState==3) {startParseIncompleteResponse('+qual+');poolxh['+qual+'].onreadystatechange=function() {};}}else{if (poolxh['+qual+'].readyState==4 && poolxh['+qual+'].responseText) {if (trapErrors){try{eval(poolxh['+qual+'].responseText);}catch (erro){if (showErro){cospeErro(erro, poolxh['+qual+'].responseText.toString());}}}else{eval(poolxh['+qual+'].responseText);}}}}};');
			var poolNdx=qual;
			var orsc=function(){
				if(typeof poolxh != "undefined" && poolxh[poolNdx]!=null){
					if (parseIncomplete){
						if (poolxh[poolNdx].readyState==3) {
							startParseIncompleteResponse(poolNdx);
							poolxh[poolNdx].onreadystatechange=function() {};
						}
					}else{
						if (poolxh[poolNdx].readyState==4 && poolxh[poolNdx].responseText) {
							if (trapErrors){
								try{
									eval(poolxh[poolNdx].responseText);
								}catch (erro){
									if (showErro){
										cospeErro(erro, poolxh[poolNdx].responseText.toString());
									}
								}
							}else{
								eval(poolxh[poolNdx].responseText);
							}
						}
					}
				}
			};
			//alert(orsc);
			poolxh[qual].onreadystatechange=orsc;
		}else{
			poolxh[qual].onreadystatechange=function() {
				if(typeof poolxh != 'undefined' && poolxh[qual]!=null && poolxh[qual].readyState==4 && poolxh[qual].responseText) {
					if (trapErrors){
						try{
							eval(poolxh[qual].responseText);
						}catch (erro){
							if (showErro){
								cospeErro(erro, poolxh[qual].responseText.toString());
							}else{
								alert('Houve um erro ao recuperar a resposta do servidor. Tente novamente, por favor.');
								try{
									aguarde(false, '');
								}catch(ex){

								}
							}
						}
					}else{
						eval(poolxh[qual].responseText);
					}
				}
			}
			;
		}
		poolxh[qual].send((basePostAjax==''?'':'&')+"ajax="+oque+oDest+"&"+qs)
	}
}
function startParseIncompleteResponse(qual){
	var m=qual;
	var pir=function(){parseIncompleteResponse(m);};
	poolIncParser[qual]=setInterval(pir, 10, qual);
}
function parseIncompleteResponse(qual){
	if (typeof poolxhprops[qual].parsed == 'undefined') poolxhprops[qual].parsed=0;
	var rtl=-1;
	try{
		rtl=poolxh[qual].responseText.length;
	}catch(erro){
		return;
	}
	//GLog.write('no parseIncompleteResponse ' + poolxh[qual].readyState + '/' + poolxhprops[qual].parsing + '/' +  rtl + '/' + poolxhprops[qual].parsed);
	if (poolxh[qual].readyState==3){
		if (poolxhprops[qual].parsing) return;
		try{
			poolxhprops[qual].received=poolxh[qual].responseText.length;
		}catch(erro){
			GLog.write(erro);
			return;
		}
		poolxhprops[qual].parsing=true;
		var toParse=poolxh[qual].responseText.substr(poolxhprops[qual].parsed);
		var pos=toParse.lastIndexOf('#_#');
		if (pos>=0){
			var posParsed=poolxhprops[qual].parsed+pos;
			toParse=toParse.substr(0,pos);
			poolxhprops[qual].consomeResposta(toParse);
			poolxhprops[qual].parsed=parseInt(posParsed);
		}
		poolxhprops[qual].parsing=false;
	}else{
		clearInterval(poolIncParser[qual]);
		if (poolxhprops[qual].parsed!=poolxh[qual].responseText.length){
			var toParse=poolxh[qual].responseText.substr(poolxhprops[qual].parsed);
			poolxhprops[qual].consomeResposta(toParse);
		}
	}
}
function consomeEval(txt){
	var re = /#_#/gi;
	if (txt=='') return;
	//GLog.write('antes: ' + txt);
	txt=txt.replace(re, '');
	//GLog.write('depois: ' + txt);
	if (trapErrors){
		try{
			eval(txt);
		}catch (erro){
			if (showErro){
				cospeErro(erro, txt);
			}
		}
	}else{
		eval(txt);
	}
}
function cospeErro(erro, resposta){
	if (typeof showInfo != 'undefined' && showInfo!=null) showInfo(false, '');
	var odiv = document.createElement('div');
	odiv.style.position='absolute';
	odiv.style.left='10px';
	odiv.style.top='10px';
	odiv.style.width='800px';
	odiv.style.height='600px';
	odiv.style.zIndex='5000000';
	odiv.id='mensagemErro'+seCount;
	odiv.style.border='solid 1px black';
	odiv.style.overflow='auto';
	odiv.style.padding='2px';
	odiv.style.backgroundColor='#ffffcc';
	var re=new RegExp('<', 'g');
	//resposta=resposta.replace(re, '&lt;');
	re=new RegExp('\r\n', 'g');
	//resposta=resposta.replace(re, '<br>');
	var divFecha = document.createElement('div');
	divFecha.id='fechaErro'+seCount;
	divFecha.style.position='absolute';
	divFecha.style.right='10px';
	divFecha.style.top='10px';
	divFecha.style.padding='2px';
	divFecha.style.border='solid 1px';
	divFecha.innerHTML='<a style="cursor:pointer;" onClick="javascript:document.body.removeChild(document.getElementById(\'mensagemErro'+seCount+'\'));">Fechar</a>';
	odiv.appendChild(divFecha);
	var divMsgErro = document.createElement('div');
	var msgErro='';
	if (erro!=null) msgErro=erro.message;
	divMsgErro.innerHTML=msgErro+"<br>"+resposta;
	odiv.appendChild(divMsgErro);
	document.body.appendChild(odiv);
	seCount++;
	//alert(erro.toString());
}
</script>
<?
}
?>
