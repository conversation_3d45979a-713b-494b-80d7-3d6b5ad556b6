
<?// Este porra tem que saber acertar o include_path sozinho.
$eu = dirname(__FILE__);

if (PHP_VERSION>='5') require("$eu/../function/domxmlphp4to5.inc");
require("$eu/../function/function_geraddl.inc");
require("$eu/../function/function_geraphp.inc");
require("$eu/../function/function_geraforms.inc");
require("$eu/../function/function_hiermenu.inc");
require("$eu/../function/function_javadoc.inc");

require("$eu/../function/geraphp_ent.inc");
require("$eu/../function/geraphp_campo.inc");
require("$eu/../function/geraphp_relat.inc");
//require("$eu/../function/geraphp_base.inc");
//require("$eu/../function/geraphp_base_list.inc");
require("$eu/../function/geraphp_final.inc");
require("$eu/../function/geraphp_ini.inc");
//require("$eu/../function/geraphp_rel.inc");

/* Forms */
require("$eu/../function/geraforms_ini.inc");
require("$eu/../function/geraforms_ent.inc");
require("$eu/../function/geraforms_relat.inc");

// precisa parsear o geraclasses.ini.xml.
$pathinixml = dirname(__FILE__)."/../geraclasses.ini.xml";
$inixml = realpath($pathinixml);
if (!file_exists($inixml)) die("Não existe: $pathinixml\n");
if (!is_readable($inixml)) die("Não legível (mas existe): $pathinixml\n");

if (!$ini = domxml_open_file("$inixml")) {
	die("Erro parseando XML ($inixml)! de configuração!");
}
include_once('../codebase/config.inc');
function getBaseSaida() {
	global $_pathSaida;
	return realpath($_pathSaida['PathSaida']);
}

function getLinkSamba() {
	global $_pathSaida;
	return $_pathSaida['SambaLink'];
}

function geraClassesConfigAutor() {
	return "Ricardo Pardini (<EMAIL>)";
}

function geraClassesConfigSoEntidade() {
	return getCmdLine("entidade");
	return null;
}

/* Essa função retorna o valor de parâmetro especificado na linha de comando, ou true se o parâmetro foi especificado
   sem valor, ou false se não especificado... */
function getCmdLine($which, $default=null) {
	if (php_sapi_name()!=="cli" && strpos(php_sapi_name(), "cgi")===false) return null;
	global $argv, $argc;
	if (empty($total)) $total=array();
	//echo nl2br(print_r($argv, true))."<br/>";exit;
	if (count($argv) < 2) return null;
	for ($i=1; $i < count($argv); $i++) {
		$total[] = $argv[$i];
	}
	$total = implode(" ", $total);
	$bynaco = explode("--",$total);
	foreach ($bynaco as $naco) {
		if ($naco) {
			if (strtoupper(substr($naco, 0, strlen($which) )) == strtoupper($which)) {
				$present = true;
				$a = explode("=", $naco);
				if (count($a) > 1) {
					return trim($a[1]);
				} else {
					return $default;
				}
			}
		}
	}
	return $default;
}

function getGCVersion() {
	return "4.0 CdV";
}

// importa variaveis do help!
$basehelp = $eu."/../help/menuhelp/";
$d = dir($basehelp);
while (false !== ($entry = $d->read())) {
	if (!(strpos(strtoupper($entry), strtoupper(".html")) === false)) {
		$varname = substr($entry, 0, strlen($entry) - 5);
		$file = $basehelp.$varname.".html";
		$value = leArquivo($file);
		$value = str_replace("\n", " ", $value);
		$value = str_replace("\r", "", $value);
		$$varname = $value;
	}
}
$d->close();



/**************************************************************************************************************************
** Funções para tratamento com arquivos.
**************************************************************************************************************************/
function leArquivo($arq) {
	$fd = fopen($arq,"r");
	$cont = fread($fd,filesize($arq));
	fclose($fd);
	return $cont;
}

function existe($arq) {
	return file_exists($arq);
}

function criaDiretorio($base) {
	if (!file_exists($base)) {
		if (!mkdir($base, 0777)) {
			die("Erro criando diretório $base !");
		}
	}
	return $base;
}

function preparaSaidaPHP($classe) {
	global $lf;
	$head = "<?php".$lf.$lf;
	$tail = "?>";
	return $head.$classe.$tail;
}

function gravaArquivo($conteudo, $nome, $donotover=true) {
	global $listaArqs;
	$listaArqs[] = ($donotover?"KEEP":"OVERWRITE"). ": " . $nome;
	if ($donotover) {
		if (existe($nome)) {
			//echo "Não gravando $nome, pois existe e dito que não é para overwrite!\n";
		} else {
			if (! grava($nome, $conteudo)) {
				die("Erro ao gravar arquivo $nome");
			} else {
				return true;
			}
		}
	} else {
		if (! grava($nome, $conteudo)) {
			die("Erro ao gravar arquivo $nome");
		} else {
			return true;
		}
	}
}

function grava($arq, $cr) {
	global $_utf8;
	if (!$fp = fopen ($arq, "wb")) return false;
	if ($_utf8){
		fputs($fp, $cr);
	}else{
		fputs($fp, utf8_decode($cr));
	}
	//Estava usando o comando abaixo, gravando o UTF-8 com BOM
	//fputs($fp, ($_utf8?"\xEF\xBB\xBF":'').$cr);
	fclose ($fp);
	@chmod ($arq, octdec(660));
	return true;
}

function tornaExecutavel($arq) {
	@chmod ($arq, octdec(777));
	return true;
}


function p_arr($arr) {
	foreach($arr as $k=>$v)
		$out[] = "\t$k => $v";
	return implode("\n", $out);
}

function object_info($obj) {
	$out[] = "Class: ".get_class($obj);
	foreach(get_object_vars($obj) as $var=>$val)
		if (is_array($val))
			$out[] = "property: $var (array)\n".p_arr($val);
		else
			$out[] = "property: $var = $val";
	foreach(get_class_methods($obj) as $method)
		$out[] = "method: $method";
	return implode("\n", $out);
}


function nomeniza($algo) {
	return UCwords(strtolower(str_replace("-", "_", str_replace(" ", "_", $algo))));
}


$arr_nomefunc = null;
$tem_erro_nomefunc = false;
function testaJaExiste($entidade, $numefunc, $arq, $linha, $frase) {
	global $arr_nomefunc, $tem_erro_nomefunc;
	if (!isset($arr_nomefunc[$entidade])) return;
	if (is_array($arr_nomefunc[$entidade])) {
		if (in_array($numefunc, $arr_nomefunc[$entidade])) {
			$tem_erro_nomefunc = true;
			PHP_GENERATOR_ENTIDADE::geraWarning("$frase (at $arq linha $linha).");
			return true; /* Always false... */
		} else {
			$arr_nomefunc[$entidade][] = $numefunc;
			return false;
		}
	} else {
		$arr_nomefunc[$entidade][] = $numefunc;
		return false;
	}
}


/**
 * Generates XML document from JSON file
 * @param string $jsonFile Path to JSON file
 * @return object DOMDocument compatible with GeraClasses
 */
function generateXmlFromJSON($jsonFile) {
	echo "Generating XML from JSON file: $jsonFile\n";
	
	// Read JSON file
	$jsonContent = file_get_contents($jsonFile);
	$obj = json_decode($jsonContent, true);
	$modelData = $obj['entities'] ?? $obj;
	$relationshipData = $obj['relationships'] ?? [];
	
	if (json_last_error() !== JSON_ERROR_NONE) {
		die("Error parsing JSON file: " . json_last_error_msg());
	}
	
	// Create new XML document
	$doc = domxml_new_doc("1.0");
	$root = $doc->create_element("Modelo");
	$doc->append_child($root);
	
	// Add Entidades element
	$entidades = $doc->create_element("Entidades");
	$entidades->set_attribute("quantos", count($modelData));
	$root->append_child($entidades);
	
	// Add Relacionamentos element
	$relacionamentos = $doc->create_element("Relacionamentos");
	$root->append_child($relacionamentos);
	
	// Process each table in the JSON
	$entidadeId = 1000;
	foreach ($modelData as $tableName => $tableData) {
		// Create Entidade element
		$entidade = $doc->create_element("Entidade");
		$entidade->set_attribute("NomeFisico", $tableName);
		$entidade->set_attribute("NomeLogico", $tableData['logical']);
		$entidade->set_attribute("id", "E" . $entidadeId++);
		$entidades->append_child($entidade);
		
		// Add Campos element
		$campos = $doc->create_element("Campos");
		$campos->set_attribute("quantos", count($tableData['columns']));
		$entidade->append_child($campos);
		
		// Process each column
		$campoId = 10000;
		foreach ($tableData['columns'] as $columnName => $columnData) {
			$campo = $doc->create_element("Campo");
			$campo->set_attribute("NomeFisico", $columnName);
			$campo->set_attribute("NomeLogico", isset($columnData['logical']) ? $columnData['logical'] : $columnName);
			$campo->set_attribute("DataType", $columnData['type']);
			
			if (isset($columnData['sequence']) && $columnData['sequence']) {
				$campo->set_attribute("Identity", "true");
			}
			
			if (isset($columnData['nullable']) && !$columnData['nullable']) {
				$campo->set_attribute("NotNull", "true");
			}
			
			if (isset($columnData['size'])) {
				$campo->set_attribute("DataLength", $columnData['size']);
			}
			
			if (isset($columnData['srid'])) {
				$campo->set_attribute("SRID", $columnData['srid']);
			}
			
			if (isset($columnData['dimension'])) {
				$campo->set_attribute("GeomDimension", $columnData['dimension']);
			}
			
			if (isset($columnData['default'])) {
				$campo->set_attribute("default", $columnData['default']);
			}
			
			$campo->set_attribute("id", "A" . $campoId++);
			$campos->append_child($campo);
		}
	}
	
	// Process relationships (foreign keys)
	$relId = 30000;

	// First, process relationships from column FK data (legacy format)
	foreach ($modelData as $tableName => $tableData) {
		foreach ($tableData['columns'] as $columnName => $columnData) {
			if (isset($columnData['fk'])) {
				$relacionamento = $doc->create_element("Relacionamento");
				$relacionamento->set_attribute("id", "R" . $relId++);

				// Set parent and child tables
				$relacionamento->set_attribute("ParentName", $columnData['fk']['table']);
				$relacionamento->set_attribute("ChildName", $tableName);

				// Create ColunaRel element for the column relationship
				$colunaRel = $doc->create_element("ColunaRel");
				$colunaRel->set_attribute("ParentName", $columnData['fk']['column']);
				$colunaRel->set_attribute("ChildName", $columnName);
				$relacionamento->append_child($colunaRel);

				// Set phrases if available
				if (isset($columnData['phrase'])) {
					$relacionamento->set_attribute("Frase", $columnData['phrase']);
				}

				if (isset($columnData['inverse'])) {
					$relacionamento->set_attribute("FraseInversa", $columnData['inverse']);
				}

				$relacionamentos->append_child($relacionamento);
			}
		}
	}

	// Process relationships from $relationshipData variable
	foreach ($relationshipData as $relationshipInfo) {
		$relacionamento = $doc->create_element("Relacionamento");
		$relacionamento->set_attribute("id", "R" . $relId++);
		
		// Set parent and child tables
		$relacionamento->set_attribute("ParentName", $relationshipInfo['sourceTable']);
		$relacionamento->set_attribute("ChildName", $relationshipInfo['targetTable']);
		
		// Handle multiple column relationships
		if (isset($relationshipInfo['columns']) && is_array($relationshipInfo['columns'])) {
			// Add all column relationships as ColunaRel elements
			foreach ($relationshipInfo['columns'] as $columnPair) {
				$colunaRel = $doc->create_element("ColunaRel");
				$colunaRel->set_attribute("ParentName", $columnPair['parent']);
				$colunaRel->set_attribute("ChildName", $columnPair['child']);
				$relacionamento->append_child($colunaRel);
			}
		}
		
		// Set additional attributes if available
		if (isset($relationshipInfo['cardinality'])) {
			$relacionamento->set_attribute("Cardinality", $relationshipInfo['cardinality']);
		}
		
		if (isset($relationshipInfo['reltype'])) {
			$relacionamento->set_attribute("Tipo", $relationshipInfo['reltype']);
		}
		
		// Set phrases if available
		if (isset($relationshipInfo['phrase'])) {
			$relacionamento->set_attribute("Frase", $relationshipInfo['phrase']);
		}
		
		if (isset($relationshipInfo['inverse'])) {
			$relacionamento->set_attribute("FraseInversa", $relationshipInfo['inverse']);
		}
		
		$relacionamentos->append_child($relacionamento);
	}

	// Add Constraints to each Entidade
	$entidades = $root->get_elements_by_tagname("Entidade");
	foreach ($entidades as $entidade) {
		$tableName = $entidade->get_attribute("NomeFisico");
		$tableData = $modelData[$tableName] ?? null;
		
		if (!$tableData) continue;
		
		// Create Constraints element
		$constraints = $doc->create_element("Constraints");
		$constraintCount = 0;
		
		// Find primary key columns
		$pkColumns = [];
		foreach ($tableData['columns'] as $columnName => $columnData) {
			if (isset($columnData['pk']) && $columnData['pk']) {
				$pkColumns[] = $columnName;
			}
		}
		
		// Add ConstraintPK if there are primary key columns
		if (!empty($pkColumns)) {
			$constraintPK = $doc->create_element("ConstraintPK");
			$constraintPK->set_attribute("Tipo", "PK");
			
			// Generate PK name - use existing pattern or create one
			$pkName = "PK_" . $tableName;
			$constraintPK->set_attribute("Nome", $pkName);
			
			// Add Coluna elements for each PK column
			$campoId = 10000; // Starting ID for campo references
			foreach ($pkColumns as $columnName) {
				$coluna = $doc->create_element("Coluna");
				$coluna->set_attribute("Nome", $columnName);
				$coluna->set_attribute("id", "IC" . $campoId);
				$coluna->set_attribute("CampoID", "A" . $campoId);
				$constraintPK->append_child($coluna);
				$campoId++;
			}
			
			$constraints->append_child($constraintPK);
			$constraintCount++;
		}
		
		// Add ConstraintUnique elements if constraints are defined in table data
		if (isset($tableData['constraints'])) {
			foreach ($tableData['constraints'] as $constraintName => $constraintData) {
				if ($constraintData['type'] === 'unique') {
					$constraintUnique = $doc->create_element("ConstraintUnique");
					$constraintUnique->set_attribute("Tipo", "Unique");
					$constraintUnique->set_attribute("Nome", $constraintName);
					
					// Add Coluna elements for each unique constraint column
					$campoId = 20000; // Starting ID for unique constraint campo references
					foreach ($constraintData['columns'] as $columnName) {
						$coluna = $doc->create_element("Coluna");
						$coluna->set_attribute("Nome", $columnName);
						$coluna->set_attribute("id", "IC" . $campoId);
						$coluna->set_attribute("CampoID", "A" . $campoId);
						$constraintUnique->append_child($coluna);
						$campoId++;
					}
					
					$constraints->append_child($constraintUnique);
					$constraintCount++;
				}
			}
		}
		
		// Only add Constraints element if there are constraints
		if ($constraintCount > 0) {
			$constraints->set_attribute("quantos", $constraintCount);
			$entidade->append_child($constraints);
		}
	}

	// Add Children elements to each Entidade that is a child in relationships
	$entidades = $root->get_elements_by_tagname("Entidade");
	$relacionamentos = $root->get_elements_by_tagname("Relacionamento");

	// Create a map of entity names to their IDs
	$entityNameToId = [];
	foreach ($entidades as $entidade) {
		$tableName = $entidade->get_attribute("NomeFisico");
		$entityId = $entidade->get_attribute("id");
		$entityNameToId[$tableName] = $entityId;
	}

	// Group relationships by child entity (entities that have foreign keys)
	$childRelationships = [];
	foreach ($relacionamentos as $relacionamento) {
		$parentTable = $relacionamento->get_attribute("EntidadePai");
		$childTable = $relacionamento->get_attribute("EntidadeFilho");
		$relationshipId = $relacionamento->get_attribute("id");
		
		if (!isset($childRelationships[$childTable])) {
			$childRelationships[$childTable] = [];
		}

		$childRelationships[$childTable][] = [
			'relationshipId' => $relationshipId,
			'parentTable' => $parentTable,
			'childTable' => $childTable,
			'relacionamento' => $relacionamento
		];
	}

	// Add Children elements to child entities (entities with foreign keys)
	foreach ($entidades as $entidade) {
		$tableName = $entidade->get_attribute("NomeFisico");
		
		if (isset($childRelationships[$tableName])) {
			$children = $doc->create_element("Children");
			
			foreach ($childRelationships[$tableName] as $relInfo) {
				$relat = $doc->create_element("Relat");
				$relacionamento = $relInfo['relacionamento'];
				
				// Set basic attributes
				$relat->set_attribute("id", $relInfo['relationshipId']);
				$relat->set_attribute("Parent", $entityNameToId[$relInfo['parentTable']] ?? 'E0');
				$relat->set_attribute("Child", $entityNameToId[$relInfo['childTable']] ?? 'E0');
				$relat->set_attribute("ParentName", $relInfo['parentTable']);
				$relat->set_attribute("ChildName", $relInfo['childTable']);
				
				// Copy additional attributes from Relacionamento
				if ($relacionamento->has_attribute("Cardinality")) {
					$relat->set_attribute("Cardinality", $relacionamento->get_attribute("Cardinality"));
				} else {
					$relat->set_attribute("Cardinality", "0");
				}
				
				if ($relacionamento->has_attribute("Tipo")) {
					$relat->set_attribute("Tipo", $relacionamento->get_attribute("Tipo"));
				} else {
					$relat->set_attribute("Tipo", "1");
				}
				
				if ($relacionamento->has_attribute("Frase")) {
					$relat->set_attribute("Frase", $relacionamento->get_attribute("Frase"));
				}
				
				if ($relacionamento->has_attribute("FraseInversa")) {
					$relat->set_attribute("FraseInversa", $relacionamento->get_attribute("FraseInversa"));
				}
				
				// Add ColunaRel elements
				$colunaRels = $relacionamento->get_elements_by_tagname("ColunaRel");
				if ($colunaRels && count($colunaRels) > 0) {
					// Use existing ColunaRel elements
					foreach ($colunaRels as $colunaRel) {
						$newColunaRel = $doc->create_element("ColunaRel");
						$newColunaRel->set_attribute("Parent", $colunaRel->get_attribute("ParentName"));
						$newColunaRel->set_attribute("Child", $colunaRel->get_attribute("ChildName"));
						$newColunaRel->set_attribute("ParentName", $colunaRel->get_attribute("ParentName"));
						$newColunaRel->set_attribute("ChildName", $colunaRel->get_attribute("ChildName"));
						$relat->append_child($newColunaRel);
					}
				} else {
					// Create ColunaRel from basic relationship attributes
					if ($relacionamento->has_attribute("CampoPai") && $relacionamento->has_attribute("CampoFilho")) {
						$colunaRel = $doc->create_element("ColunaRel");
						$colunaRel->set_attribute("Parent", "A10000"); // Default campo ID
						$colunaRel->set_attribute("Child", "A20000"); // Default campo ID
						$colunaRel->set_attribute("ParentName", $relacionamento->get_attribute("CampoPai"));
						$colunaRel->set_attribute("ChildName", $relacionamento->get_attribute("CampoFilho"));
						$relat->append_child($colunaRel);
					}
				}
				
				$children->append_child($relat);
			}
			
			$entidade->append_child($children);
		}
	}

	// Add Parents elements to each Entidade that is a parent in relationships
	$entidades = $root->get_elements_by_tagname("Entidade");
	$relacionamentos = $root->get_elements_by_tagname("Relacionamento");

	// Create a map of entity names to their IDs
	$entityNameToId = [];
	foreach ($entidades as $entidade) {
		$tableName = $entidade->get_attribute("NomeFisico");
		$entityId = $entidade->get_attribute("id");
		$entityNameToId[$tableName] = $entityId;
	}

	// Group relationships by parent entity (entities that are referenced by foreign keys)
	$parentRelationships = [];
	foreach ($relacionamentos as $relacionamento) {
		$parentTable = $relacionamento->get_attribute("EntidadePai");
		$childTable = $relacionamento->get_attribute("EntidadeFilho");
		$relationshipId = $relacionamento->get_attribute("id");
		
		if (!isset($parentRelationships[$parentTable])) {
			$parentRelationships[$parentTable] = [];
		}
		
		$parentRelationships[$parentTable][] = [
			'relationshipId' => $relationshipId,
			'parentTable' => $parentTable,
			'childTable' => $childTable,
			'relacionamento' => $relacionamento
		];
	}

	// Add Parents elements to parent entities (entities that are referenced by foreign keys)
	foreach ($entidades as $entidade) {
		$tableName = $entidade->get_attribute("NomeFisico");
		
		if (isset($parentRelationships[$tableName])) {
			$parents = $doc->create_element("Parents");
			
			foreach ($parentRelationships[$tableName] as $relInfo) {
				$relat = $doc->create_element("Relat");
				$relacionamento = $relInfo['relacionamento'];
				
				// Set basic attributes
				$relat->set_attribute("id", $relInfo['relationshipId']);
				$relat->set_attribute("Parent", $entityNameToId[$relInfo['parentTable']] ?? 'E0');
				$relat->set_attribute("Child", $entityNameToId[$relInfo['childTable']] ?? 'E0');
				$relat->set_attribute("ParentName", $relInfo['parentTable']);
				$relat->set_attribute("ChildName", $relInfo['childTable']);
				
				// Copy additional attributes from Relacionamento
				if ($relacionamento->has_attribute("Cardinality")) {
					$relat->set_attribute("Cardinality", $relacionamento->get_attribute("Cardinality"));
				} else {
					$relat->set_attribute("Cardinality", "0");
				}
				
				if ($relacionamento->has_attribute("Tipo")) {
					$relat->set_attribute("Tipo", $relacionamento->get_attribute("Tipo"));
				} else {
					$relat->set_attribute("Tipo", "1");
				}
				
				if ($relacionamento->has_attribute("Frase")) {
					$relat->set_attribute("Frase", $relacionamento->get_attribute("Frase"));
				}
				
				if ($relacionamento->has_attribute("FraseInversa")) {
					$relat->set_attribute("FraseInversa", $relacionamento->get_attribute("FraseInversa"));
				}
				
				// Add ColunaRel elements
				$colunaRels = $relacionamento->get_elements_by_tagname("ColunaRel");
				if ($colunaRels && count($colunaRels) > 0) {
					// Use existing ColunaRel elements
					foreach ($colunaRels as $colunaRel) {
						$newColunaRel = $doc->create_element("ColunaRel");
						$newColunaRel->set_attribute("Parent", $colunaRel->get_attribute("ParentName"));
						$newColunaRel->set_attribute("Child", $colunaRel->get_attribute("ChildName"));
						$newColunaRel->set_attribute("ParentName", $colunaRel->get_attribute("ParentName"));
						$newColunaRel->set_attribute("ChildName", $colunaRel->get_attribute("ChildName"));
						$relat->append_child($newColunaRel);
					}
				} else {
					// Create ColunaRel from basic relationship attributes
					if ($relacionamento->has_attribute("CampoPai") && $relacionamento->has_attribute("CampoFilho")) {
						$colunaRel = $doc->create_element("ColunaRel");
						$colunaRel->set_attribute("Parent", "A10000"); // Default campo ID
						$colunaRel->set_attribute("Child", "A20000"); // Default campo ID
						$colunaRel->set_attribute("ParentName", $relacionamento->get_attribute("CampoPai"));
						$colunaRel->set_attribute("ChildName", $relacionamento->get_attribute("CampoFilho"));
						$relat->append_child($colunaRel);
					}
				}
				
				$parents->append_child($relat);
			}
			
			$entidade->append_child($parents);
		}
	}

	return $doc;
}

/**
 * Generates JSON from XML file in the format outlined in modelReference.json
 * @param string $xmlFile Path to XML file
 * @return string JSON representation of the model
 */
function generateJsonFromXml($xmlFile) {
	echo "Generating JSON from XML file: $xmlFile\n";
	
	// Load the XML file
	if (!$doc = domxml_open_file($xmlFile)) {
		die("Error parsing XML file: $xmlFile");
	}
	
	// Initialize output structure
	$output = [
		"entities" => [],
		"relationships" => []
	];
	
	// Process entities
	$entidades = $doc->get_elements_by_tagname("Entidade");
	foreach ($entidades as $entidade) {
		$tableName = $entidade->get_attribute("NomeFisico");
		$tableLogical = $entidade->get_attribute("NomeLogico");
		
		// Initialize table structure
		$output["entities"][$tableName] = [
			"logical" => $tableLogical,
			"columns" => [],
			"constraints" => [],
			"indexes" => [],
			"position" => [300, 9700] // Default position
		];
		
		// First, get primary key columns from ConstraintPK
		$pkColumns = [];
		$constraints = $entidade->get_elements_by_tagname("Constraints");
		if ($constraints && count($constraints) > 0) {
			$pkConstraints = $constraints[0]->get_elements_by_tagname("ConstraintPK");
			if ($pkConstraints && count($pkConstraints) > 0) {
				$pkColunas = $pkConstraints[0]->get_elements_by_tagname("Coluna");
				foreach ($pkColunas as $pkColuna) {
					$pkColumns[] = $pkColuna->get_attribute("Nome");
				}
			}
			
			// Process unique constraints
			$uniqueConstraints = $constraints[0]->get_elements_by_tagname("ConstraintUnique");
			foreach ($uniqueConstraints as $uniqueConstraint) {
				$uniqueColumns = [];
				
				// Get columns for this unique constraint
				$uniqueColunas = $uniqueConstraint->get_elements_by_tagname("Coluna");
				foreach ($uniqueColunas as $uniqueColuna) {
					$uniqueColumns[] = $uniqueColuna->get_attribute("Nome");
				}
				
				// Skip if no columns found
				if (empty($uniqueColumns)) {
					continue;
				}
				
				// Sort columns to ensure consistent naming
				sort($uniqueColumns);
				
				// Build constraint name according to format: [TABLE]_[ordered list of columns separated by underscore]_UK
				$constraintName = $tableName . "_" . implode("_", $uniqueColumns) . "_UK";
				
				// Use existing name if provided
				if ($uniqueConstraint->has_attribute("Nome") && !empty($uniqueConstraint->get_attribute("Nome"))) {
					$constraintName = $uniqueConstraint->get_attribute("Nome");
				}
				
				// Add unique constraint to table definition
				$output["entities"][$tableName]["constraints"][$constraintName] = [
					"type" => "unique",
					"columns" => $uniqueColumns
				];
			}
		}
		
		// Process columns
		$campos = $entidade->get_elements_by_tagname("Campo");
		foreach ($campos as $campo) {
			$columnName = $campo->get_attribute("NomeFisico");
			$columnLogical = $campo->get_attribute("NomeLogico");
			$dataType = $campo->get_attribute("DataType");
			$notNull = strtolower($campo->get_attribute("NotNull") ?? 'False');
			$dtl = convertType($dataType, $campo->get_attribute("DataLength"));
			$dataType = $dtl[0];
			$size = $dtl[1];

			// Build column definition
			$columnDef = [
				"logical" => $columnLogical,
				"physical" => $columnName,
				"type" => $dataType,
				"pk" => in_array($columnName, $pkColumns),
				"nullable" => !($notNull === "true")
			];
			if (false && $columnName=='TCTB1_COD'){
				echo $notNull."<br/>";
				echo (($campo->get_attribute("NotNull") === "True")?'true':'false')."<br/>";
				exit;
			}
			// die('aqui');

			// Add optional attributes
			if ($size) {
				$columnDef["size"] = $size;
			}
			
			// Check for Identity attribute to set sequence
			// if ($campo->has_attribute("Identity")) die('campo: '.($campo->get_attribute("Identity") === "True"?'true':'false'));
			if ($campo->has_attribute("Identity") && $campo->get_attribute("Identity") === "True") {
				$columnDef["sequence"] = true;
			}
			
			if ($campo->has_attribute("SRID")) {
				$columnDef["srid"] = $campo->get_attribute("SRID");
			}
			
			if ($campo->has_attribute("GeomDimension")) {
				$columnDef["dimension"] = $campo->get_attribute("GeomDimension");
			}
			
			// Add column to table
			$output["entities"][$tableName]["columns"][$columnName] = $columnDef;
		}
	}
	
	// Process relationships
	$relacionamentos = $doc->get_elements_by_tagname("Relacionamento");
	foreach ($relacionamentos as $relacionamento) {
		$relId = $relacionamento->get_attribute("id");
		$parentTable = $relacionamento->get_attribute("ParentName");
		$childTable = $relacionamento->get_attribute("ChildName");
		
		// Get all ColunaRel elements for this relationship
		$colunaRels = $relacionamento->get_elements_by_tagname("ColunaRel");
		$columnPairs = [];
		$childColumns = [];
		
		// Process each ColunaRel to build column pairs
		foreach ($colunaRels as $colunaRel) {
			$parentColumn = $colunaRel->get_attribute("ParentName");
			$childColumn = $colunaRel->get_attribute("ChildName");
			
			$columnPairs[] = [
				"parent" => $parentColumn,
				"child" => $childColumn
			];
			
			$childColumns[] = $childColumn;
		}
		
		// If no ColunaRel elements found, use the attributes directly from Relacionamento
		if (empty($columnPairs) && $relacionamento->has_attribute("CampoPai") && $relacionamento->has_attribute("CampoFilho")) {
			$parentColumn = $relacionamento->get_attribute("CampoPai");
			$childColumn = $relacionamento->get_attribute("CampoFilho");
			
			$columnPairs[] = [
				"parent" => $parentColumn,
				"child" => $childColumn
			];
			
			$childColumns[] = $childColumn;
		}
		
		// Sort child columns to ensure consistent FK naming
		sort($childColumns);
		
		// Create FK name following the format: [sourceTable]_[targetTable]_[ordered list of target columns separated by underscore]_FK
		$fkName = "{$parentTable}_{$childTable}_" . implode("_", $childColumns) . "_FK";
		
		// Add FK reference to the relationship section
		$output["relationships"][$fkName] = [
			"sourceTable" => $parentTable,
			"targetTable" => $childTable,
			"columns" => $columnPairs,
			"cardinality" => $relacionamento->get_attribute("Cardinality"),
			"reltype" => $relacionamento->get_attribute("Tipo")
		];
		
		// Add phrases if available
		if ($relacionamento->has_attribute("Frase")) {
			$output["relationships"][$fkName]["phrase"] = $relacionamento->get_attribute("Frase");
		}
		
		if ($relacionamento->has_attribute("FraseInversa")) {
			$output["relationships"][$fkName]["inverse"] = $relacionamento->get_attribute("FraseInversa");
		}
		
		// Add FK reference to the child table
		if (isset($output["entities"][$childTable])) {
			// Initialize parents array if it doesn't exist
			if (!isset($output["entities"][$childTable]["parents"])) {
				$output["entities"][$childTable]["parents"] = [];
			}
			
			// Add the FK name to the target table's parents array
			if (!in_array($fkName, $output["entities"][$childTable]["parents"])) {
				$output["entities"][$childTable]["parents"][] = $fkName;
			}
			
			// Update the columns with FK information
			foreach ($columnPairs as $pair) {
				$childColumn = $pair["child"];
				if (isset($output["entities"][$childTable]["columns"][$childColumn])) {
					// Initialize fkrefs array for the column if it doesn't exist
					if (!isset($output["entities"][$childTable]["columns"][$childColumn]["fkrefs"])) {
						$output["entities"][$childTable]["columns"][$childColumn]["fkrefs"] = [];
					}
					
					// Add the FK name to the column's fkrefs array
					if (!in_array($fkName, $output["entities"][$childTable]["columns"][$childColumn]["fkrefs"])) {
						$output["entities"][$childTable]["columns"][$childColumn]["fkrefs"][] = $fkName;
					}
					
					// Add fk information to the column
					// if (!isset($output["entities"][$childTable]["columns"][$childColumn]["fk"])) {
					//	 $output["entities"][$childTable]["columns"][$childColumn]["fk"] = [
					//		 "table" => $parentTable,
					//		 "column" => $pair["parent"]
					//	 ];
					// }
				}
			}
		}
		// Add FK reference to the parent table
		if (isset($output["entities"][$parentTable])) {
			// Initialize children array if it doesn't exist
			if (!isset($output["entities"][$parentTable]["children"])) {
				$output["entities"][$parentTable]["children"] = [];
			}
			
			// Add the FK name to the source table's children array
			if (!in_array($fkName, $output["entities"][$parentTable]["children"])) {
				$output["entities"][$parentTable]["children"][] = $fkName;
			}
		}
	}
	
	// Convert to JSON
	return json_encode($output, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
function convertType($type, $length){
	if (strtoupper($type)!='NVARCHAR') return [$type, $length];
	switch($length){
		case '1':
			$newType='MULTIPOINTM';
			break;
		case '12':
			$newType='POINT';
			break;
		case '13':
			$newType='POINTM';
			break;
		case '14':
			$newType='POINTZ';
			break;
		case '112':
			$newType='MULTIPOINT';
			break;
		case '113':
			$newType='MULTIPOINTM';
			break;
		case '114':
			$newType='MULTIPOINTZ';
			break;
		case '2':
			$newType='MULTILINESTRINGM';
			break;
		case '22':
			$newType='LINESTRING';
			break;
		case '23':
			$newType='LINESTRINGM';
			break;
		case '24':
			$newType='LINESTRINGZ';
			break;
		case '122':
			$newType='MULTILINESTRING';
			break;
		case '123':
			$newType='MULTILINESTRINGM';
			break;
		case '124':
			$newType='MULTILINESTRINGZ';
			break;
		case '3':
			$newType='MULTIPOLYGONM';
			break;
		case '32':
			$newType='POLYGON';
			break;
		case '33':
			$newType='POLYGONM';
			break;
		case '34':
			$newType='POLYGONZ';
			break;
		case '132':
			$newType='MULTIPOLYGON';
			break;
		case '133':
			$newType='MULTIPOLYGONM';
			break;
		case '134':
			$newType='MULTIPOLYGONZ';
			break;
		case '100':
			$newType='GEOMETRY';
			break;
		case '360':
			$newType='GEOGRAPHY';
			break;
		default:
			$newType = 'ALGO'.$type;
			break;
	}
	return [$newType, null];
}
