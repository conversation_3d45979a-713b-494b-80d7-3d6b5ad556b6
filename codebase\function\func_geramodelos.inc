<?php
ini_set('short_open_tag', '1');
$incpath=get_include_path();
// echo $incpath.'<br/>';
set_include_path('.'.PATH_SEPARATOR.realpath(dirname(__FILE__).'/../../').DIRECTORY_SEPARATOR);

error_reporting(E_ALL ^ E_NOTICE ^ E_DEPRECATED);
$old_error_handler = set_error_handler("meuErrorHandler");
// echo get_include_path().'<br/>';
include_once('codebase/config.inc');
function getDb(){
	return null;
}
function cleanPost(){
	cleanArrayXss($_POST);
}
function cleanGet(){
	cleanArrayXss($_GET);
}
function cleanRequest(){
	cleanArrayXss($_REQUEST);
}
function cleanArrayXss(&$a, $maxLength=null){
	array_walk($a, 'cleanVarXss', $maxLength);
}
function cleanWalkXss(&$item, $key){
	$item=cleanVarXss($item, $key);
}
function cleanVarXss(&$val, $key=null, $maxLength=null){
	if (is_null($val)) return null;
	if ($val=='') return '';
	if (is_array($val)) return cleanArrayXss($val, $maxLength);
	if ($maxLength && strlen($val)>$maxLength) return '';
	$val=str_replace(array("<",">"), array("&lt;","&gt;"), $val);
	$val=str_ireplace(array('onmouse','onchange','onclick'), '', $val);
	$val=preg_replace('/eval\s*\((.*)\)/i','', $val);
	$val=preg_replace('/["\'][\s]*((?i)javascript):(.*)["\']/i', '', $val);
	$val=preg_replace('/((?i)script)/i', '', $val);
	return $val;
}
function montaHidden(){
	
}
function getmicrotime() {
	$temparray=explode(" ",microtime());
	$returntime=$temparray[0]+$temparray[1];
	return $returntime;
}
function emCommandLine(){
	$sapi = php_sapi_name();
	switch(strtolower($sapi)){
		case 'cgi':
		case 'cgi-fcgi':
			if (!$_SERVER["SERVER_SOFTWARE"]) return true;
			if ($_SERVER["SERVER_SOFTWARE"]=='') return true;
		case 'apache':
			return false;
		case 'cli':
			return true;
	}
}
function meuErrorHandler($errno, $errmsg, $filename, $linenum, $vars=null) {
	global $db, $_configEmailError, $_configEmailWebmaster, $_configEmailContato, $_configCustomErrorShow, $_configCustomErrorMail, $DEBUG_DB, $_configNomeSite, $_configSiteDevel, $_numNotices, $_sizeNotices, $_configProducao, $_omiteStackVars, $_configShowStackVars, $acao, $_configDestEmailsDev;
	if ($errno == E_NOTICE && substr($errmsg, 0, 17) == "Undefined index: ") return;
	if ($errno == E_ERROR){
		if ($db){
			while ($db->transCnt>0){
				$db->RollbackTrans();
			}
		}
	}
	$replevel = error_reporting();
	if ($replevel==0) return;
	$errortype = array (1=>"Error", 2=>"Warning", 4=>"Parsing Error", 8=>"Notice", 16=>"Core Error", 32=>"Core Warning", 64=>"Compile Error", 128=>"Compile Warning", 256=>"User Error", 512=>"User Warning", 1024=>"User Notice", 2048=>"Strict", 4096=>"Recoverable", 8192=>"Deprecated", 16384=>"User deprecated", 30719=>"All");
	//definição do padrão de exibição e email
	$_configSiteDevel=true;
	if (@$_configSiteDevel){
		if (@!$_configCustomErrorShow) $_configCustomErrorShow = (E_ALL ^ E_DEPRECATED);
		if (@!$_configCustomErrorMail) $_configCustomErrorMail = (E_ALL ^ E_NOTICE ^ E_DEPRECATED);
	}else{
		if (@!$_configCustomErrorShow) $_configCustomErrorShow = (E_ALL ^ E_NOTICE ^ E_DEPRECATED);
		if (@!$_configCustomErrorMail) $_configCustomErrorMail = (E_ALL ^ E_NOTICE ^ E_DEPRECATED);
	}
	$cospe=(($errno & $_configCustomErrorShow) == $errno && $errno!=E_STRICT);
	if (@$_configProducao===true) $cospe=false; /// colocar exceções para usuários selecionados
	if (false){
		echo (int)$_configCustomErrorShow."<br>";
		echo (($errno & $_configCustomErrorShow))."<br>";
		echo (( $errno!=E_STRICT))."<br>";
		echo (int)($cospe)."<br>";
		echo $errno." - ".$errmsg."<br>";
		exit;
	}
	$mandaMail=(($errno & $_configCustomErrorMail) == $errno);
	$mandaMail=false;
	if (@$_sizeNotices && $_sizeNotices>1000) return;
	$omite=false;
	$pesscod=null;
	if (!isset($acao)){
		if (!class_exists('FACTORY')){
			$pesscod=null;
		}else{
			$acao=&FACTORY::ACAO_IMEDIATA();
			if (!headers_sent()) $pesscod=$acao->getPessCookie();
		}
	}else{
		if (isset($acao->pessoa)){
			$pesscod=$acao->pessoa->pess2_cod;
		}
	}
	$xtraInfo=null;
	if (function_exists('xtraInfoErrorHandler')) $xtraInfo=xtraInfoErrorHandler();
	$titStackVars='';
	if ($pesscod && isset($_configShowStackVars) && is_array($_configShowStackVars) && in_array($pesscod, $_configShowStackVars)) {
		$_omiteStackVars=false;
		$titStackVars='SOMENTE GESTORES DEV';
	}
	if (@$_omiteStackVars===true) {
		$omite=true;
		$_omiteStackVars=false;
	}
	if ($cospe || $mandaMail) { // 
		if ($errno == E_NOTICE){
			if (@!$_numNotices)$_numNotices=1; else $_numNotices++;
			if ($_numNotices>20) return;
		}
		//$varTrace=wddx_serialize_value($vars,"Variables");
		$stack=getFuncStack();
		$errTxt='';
		$errHtml='';
		if ($xtraInfo){
			$errTxt=$xtraInfo."\r\n";
			$errHtml=nl2br($xtraInfo).'<br/>';;
		}
		$errTxt.="Erro: (".$errortype[$errno].") ".$errno." - ".$errmsg." em ".$filename." (".$linenum.")\r\n";
		$errHtml.='<b>Erro:</b> ('.$errortype[$errno].') '.$errno.' - '.$errmsg.' em <b>'.$filename.' ('.$linenum.')</b>';
		$errTxt.="Path: ".$_SERVER['PHP_SELF'].(@$_SERVER['QUERY_STRING'] && $_SERVER['QUERY_STRING']!=''?'?'.$_SERVER['QUERY_STRING']:'')."\r\n";
		$errHtml.="Path: ".$_SERVER['PHP_SELF'].(@$_SERVER['QUERY_STRING'] && $_SERVER['QUERY_STRING']!=''?'?'.$_SERVER['QUERY_STRING']:'')."<br>";
		$cospeTxt=$errTxt;
		$cospeHtml=$errHtml;
		$varTrace='';
		$varTraceHtml='';
		if (@$vars){
			cospeVars($vars, $varTrace, $varTraceHtml);
			if ($varTrace!=''){
				$varTrace="\r\n\r\n----VARIÁVEIS----:".$varTrace;
				$varTraceHtml='<br><br>----VARIÁVEIS----:'.$varTraceHtml;
			}
		}
		$showTrace=true;
		$errTxt.=cospeFuncStack($stack, false);
		$errHtml.=cospeFuncStack($stack);
		if ($omite==false) {
			$cospeTxt.="
".$titStackVars."
".cospeFuncStack($stack, false);
			$cospeHtml.='<br/>'.$titStackVars.'<br/>'.cospeFuncStack($stack);
		}
		if ($DEBUG_DB | $showTrace) {
			$errHtml.=$varTraceHtml;
			if ($omite==false) {
				$cospeTxt.=$varTrace;
				$cospeHtml.=$varTraceHtml;
			}else{
				$cospeTxt."
";
				$cospeHtml.='<br/>';
			}
		}
		$cospeHtml='<div style="Color:gray">'.$cospeHtml.'</div>';
		if ($cospe) {
			if (emCommandLine()){
				echo($cospeTxt);
			}else{
				echo $cospeHtml;
			}
		}
		//echo ((int)$cospe)." - ".$errno." - ".$errmsg."<br>";
		//exit;
		if (@!$_sizeNotices) $_sizeNotices=0;
		$_sizeNotices+=strlen($errTxt) + strlen($varTrace);
		if ($mandaMail) {
			$dest=$_configEmailError?$_configEmailError:($_configEmailWebmaster?$_configEmailWebmaster:$_configEmailContato);
			if (empty($_configDestEmailsDev)==false) $dest=$_configDestEmailsDev;
			$iniFrom=ini_get('sendmail_from');
			$from=($iniFrom?$iniFrom:$_configEmailWebmaster);
			// save to the error log, and e-mail me if there is a critical user error
			//error_log($err, 3, "/usr/local/php4/error.log");
			//echo("Ia enviar email...<br>");
			$headers = 'From: '.$from . "\r\n" .
				'X-Mailer: PHP/' . phpversion();
			//$headers = null;
			$foi = emailRapido($dest, '[Zica no site '.$_configNomeSite.']', $errTxt.'\r\n'.$varTrace);
			/*
			global $_configSendMailViaJobQueue;
			if (!isset($_configSendMailViaJobQueue)) $_configSendMailViaJobQueue=false;
			if ($_configSendMailViaJobQueue){
				$foi = emailRapido($dest, '[Zica no site '.$_configNomeSite.']', $errTxt.'\r\n'.$varTrace);
			}else{
				$foi = mail($dest, '[Zica no site '.$_configNomeSite.']', $errTxt.'\r\n'.$varTrace, $headers);
			}
			*/
		}
	}
}
function getFuncStack(){
	if (function_exists("xdebug_get_function_stack")) {
		return xdebug_get_function_stack();
	} else {
		$stack=debug_backtrace();
		return limpaStack($stack);
	}
}
function limpaStack($stack){
	foreach($stack as $k=>$item){
		unset($stack[$k]['args']);
		unset($stack[$k]['object']);
		unset($stack[$k]['type']);
	}
	$stack=array_reverse($stack);
	return $stack;
}
function cospeFuncStack($stack=null, $emHtml=true){
	if (!$stack) $stack=getFuncStack();
	$txt='';
	$html='';
	if (sizeOf($stack)>0){
		$txt.="\r\nCall stack:";
		$html.='<br>Call stack:<UL>';
		foreach ($stack as $call){
			if (empty($call['class'])) $cl=null; else $cl=$call['class'];
			$txt.="\r\n-->".(@$cl?($cl."::"):"").(@$call['function']).(@$call['file']?" ".$call['file']:'').(@$call['line']?" (".$call['line'].")":'');
			$html.='<li>'.
				(@$cl?($cl.'::'):'').(@$call['function']).
				(@$call['file']?'&nbsp;<TT>'.$call['file'].'</TT>':'').
				(@$call['line']?'&nbsp;('.$call['line'].')':'').
				'</li>';
		}
		$html.='</UL>';
	}
	if ($emHtml) return $html; else return $txt;
}
function cospeStack(){
	$stack=array();
	$errHtml='';
	$stack=getFuncStack();
	if (sizeOf($stack)>0){
		$errHtml.='<br>Call stack:<UL>';
		foreach ($stack as $call){
			$errHtml.='<li>'.
				(@$call['class']?($call['class'].'::'):'').($call['function']).
				'&nbsp;<TT>'.$call['file'].'</TT>'.
				'&nbsp;('.$call['line'].')'.
				'</li>';
		}
		$errHtml=$errHtml.'</UL>';
	}
	echo $errHtml."<br>";
}
function cospeVars($vars, &$varTrace, &$varTraceHtml, $nivel=null, $limit=false){
	if (@!$nivel) $nivel=1;
	if ($nivel>10) return;
	$destaque='';
	for ($i=0; $i<$nivel; $i++){
		$destaque.="-=";
	}
	$destaque.="> ";
	$arignore=array('globals',
		'_env',
		'db',
		'_month_table_normal',
		'_month_table_leaf',
		'translate_from',
		'translate_to',
		'statuserroenvio',
		'ss_log_filename',
		'sapi',
		'_commandline',
		'_bypass',
		'_semprepend',
		'no_layout',
		'no_gz',
		'global_before',
		'ss_log_levels',
		'ss_log_level',
		'passareto',
		'passacache',
		'_ignoretimeout',
		'beanisolado',
		'construseek',
		'debug_stack',
		'debug_db',
		'debug_logacao',
		'no_cookie',
		'_edicaoempreview',
		'_useinnovastudioeditor',
		'_masterarquivo',
		'_masterarquivos',
		'_oracle',
		'_mssql',
		'_timeout',
		'roledefault',
		'roleadm',
		'mydire',
		'mypai',
		'myareas',
		'__s__mapasitearraylinear',
		'sqlcompleto',
		'_skipchecaipbrowser',
		'_descartamarcaquery',
		'__singletonmapa',
		'str',
		'password',
		'pwd',
		'usr',
		'user'
	);
	//print_r(error_get_last());exit;
	$arRGPC=array('_request','_get','_post','_cookie');
	$rgon=ini_get('register_globals');
	if ($rgon=='1') $arignore=array_merge($arignore, $arRGPC);
	$cont=0;
	foreach ($vars as $key=>$value){
		if (in_array(strtolower($key),$arignore)) continue;
		if (in_array($key, array_keys($_ENV))) continue;
		if (in_array($key, array_keys($_SERVER))) continue;
		if (substr($key,0,7)=='_config') continue;
		if (substr($key,0,3)=='db_') continue;
		if (substr($key,0,5)=='ADODB') continue;
		$cont++;
		if ($limit && $cont>$limit) {
			$varTrace.="\r\n(limitado em ".$limit." itens)";
			$varTraceHtml.='<br>(limitado em '.$limit.' itens)';
			break;
		}

		$forwarda=false;
		$fwLimit=3;
		if ($value===true){
			$txtValue="true";
			$htmlValue='true';
		}else if ($value===false){
			$txtValue="false";
			$htmlValue='false';
		}else if ($value==null){
			$txtValue="null";
			$htmlValue='null';
		}else if (is_numeric($value)){
			$txtValue=$value;
			$htmlValue=$value;
		}else if (is_array($value)){
			$txtValue="ARRAY (".sizeof($value)."):";
			$htmlValue='ARRAY('.sizeof($value).'):';
			if (count($value)==0){
				$txtValue.="vazio";
				$htmlValue.='vazio';
			}else{
				$forwarda=true;
				if (in_array(strtolower($key),$arRGPC)) $fwLimit=false;
				//echo $key.((int)$fwLimit).'======<br/>';
			}
		}else if (is_object($value)){
			//die('aqui cospeVars '.$key);
			//if ($key=='aqui') {echo '<br/><br/><br/>===== '.$key.((int)method_exists($value, 'varTrace'));exit;}
			//if ($key=='aqui') {echo '<br/><br/><br/>===== '.$key.get_class($value);exit;}
			if (method_exists($value, 'varTrace')) {
				$txtValue=$value->varTrace();
			} else {
				$txtValue=print_r($value, true);
			}
			if (method_exists($value, 'varTraceHtml')) {
				$htmlValue=$value->varTraceHtml();
			} else {
				$htmlValue=nl2br(print_r($value, true));
			}
		}else{
			$txtValue="\"".$value."\"";
			$htmlValue='&quot;'.$value.'&quot;';
		}
		$varTrace.="\r\n".$destaque.$key."=".$txtValue;
		$varTraceHtml.='<br>'.$destaque.$key.'='.$htmlValue;
		if ($forwarda) cospeVars($value, $varTrace, $varTraceHtml, $nivel+1, $fwLimit);
		if (strlen($varTrace)>4000) {
			$varTrace.="\r\n\r\n---------------------- EXCEDE 4.000 CARACTERES -------------------------";
			$varTraceHtml.='<br><br>---------------------- EXCEDE 4.000 CARACTERES -------------------------';
			return;
			break;
		}
	}
}
function uniformizaPath($path){
	$path = str_replace(['\\', '/'], DIRECTORY_SEPARATOR, $path);
	while(strpos($path, DIRECTORY_SEPARATOR.DIRECTORY_SEPARATOR) !== false) $path = str_replace(DIRECTORY_SEPARATOR.DIRECTORY_SEPARATOR, DIRECTORY_SEPARATOR, $path);
	if (substr($path, -1)==DIRECTORY_SEPARATOR) $path=substr($path, 0, -1);
	return $path;
}
function getModelFilesList($extension='json', $excludeXtraExt=true){
	global $_pathModelos, $_pathModelosPlus;
	$debug=false;
	// if ($extension=='xml') $debug=true;
	$files = [];
	$dirs = [];
	if (!empty($_pathModelos)) {
		$dirs[] = realpath($_pathModelos);
	}
	// $dirs[] = $_pathModelos;
	foreach ($_pathModelosPlus as $path) {
		if (substr($path, 0, 1)=='.') $path=realpath($path);
		$dirs[] = $path;
	}
	foreach ($dirs as $dir) {
		if ($debug) echo $dir."<br/>";
		if ($handle = opendir($dir)) {
			while (false !== ($entry = readdir($handle))) {
				if ($entry == "." || $entry == "..") continue;
				$semExt=substr($entry, 0, strlen($entry)-strlen($extension)-1);
				$temPonto=(strpos($semExt , '.')!==false);
				if ($debug) echo $entry.' - '.$semExt.' - '.$extension.' - '.strtolower(pathinfo($entry, PATHINFO_EXTENSION)).'<br/>';
				if (strtolower(pathinfo($entry, PATHINFO_EXTENSION)) == strtolower($extension) && ($excludeXtraExt===false || ($excludeXtraExt && !$temPonto))) {
					$files[$dir.DIRECTORY_SEPARATOR.$entry] = ['file' => $entry, 'dir'=>$dir];
				}
			}
			closedir($handle);
		}
	}
	return $files;
}
function getCandidatosDirGera(){
	global $_pathModelos, $_pathModelosPlus;
	$dirs = [];
	// if (!empty($_pathModelos)) {
	// 	$dirs[] = realpath($_pathModelos);
	// }
	foreach ($_pathModelosPlus as $path) {
		if (substr($path, 0, 1)=='.') $path=realpath($path);
		$path = uniformizaPath($path);
		$ultimo=basename($path);
		// echo $ultimo."<br/>";
		if ($ultimo=='modelo') $path=dirname($path).DIRECTORY_SEPARATOR;
		$dirs[] = $path;
	}
	return $dirs;

}
?>