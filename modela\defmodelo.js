var tbDefs={
	"TB2_USER":
		{
			"logical":"USUARIO",
			"columns":{
			"USER2_COD":{
				"logical":"Código",
				"physical":"USER2_COD",
				"type":"int",
				"pk":true,
				"sequence":true,
				"nullable":false
			},
			"USER2_COD_CMS":{
				"logical":"Código CMS",
				"physical":"USER2_COD_CMS",
				"type":"int",
				"pk":false,
				"sequence":false,
				"nullable":false
			},
			"USER2_NOM":{
				"logical":"Nome",
				"physical":"USER2_NOM",
				"type":"varchar",
				"size":200,
				"pk":false,
				"sequence":false,
				"nullable":false
			}
		},"position":[680,280]
	},
	"TB2_GRPM":
		{
			"logical":"GRUPO DE PERMISSOES",
			"columns":{
			"GRPM2_COD":{
				"logical":"Código",
				"physical":"GRPM2_COD",
				"type":"int",
				"pk":true,
				"sequence":true,
				"nullable":false
			},
			"GRPM2_COD_TXT":{
				"logical":"Código Texto",
				"physical":"GRPM2_COD_TXT",
				"type":"varchar",
				"size":20,
				"pk":false,
				"sequence":false,
				"nullable":false
			},
			"GRPM2_DAT_HR_INI":{
				"logical":"Data Hora Início",
				"physical":"GRPM2_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"GRPM2_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"GRPM2_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"GRPM2_DES":{
				"logical":"Descrição",
				"physical":"GRPM2_DES",
				"type":"text",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"GRPM2_NOM":{
				"logical":"Nome do Grupo",
				"physical":"GRPM2_NOM",
				"type":"varchar",
				"size":200,
				"pk":false,
				"sequence":false,
				"nullable":false
			}
		},"position":[550,40]
	},
	"TB2_GPUS":{
		"logical":"GRUPO DE PERMISSOES DO USUARIO",
		"columns":{
			"GRPM2_COD":{
				"logical":"Código do Grupo",
				"physical":"GRPM2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_GRPM",column:"GRPM2_COD"}
			},
			"USER2_COD":{
				"logical":"Código do Usuário",
				"physical":"USER2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_USER",column:"USER2_COD"}
			},
			"GPUS2_DAT_HR_INI":{
				"logical":"Data Hora Início",
				"physical":"GPUS2_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"GPUS2_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"GPUS2_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			}
		},"position":[800,60]
	},
	"TB2_PERM":
		{
			"logical":"PERMISSAO",
			"columns":{
			"PERM2_COD":{
				"logical":"Código",
				"physical":"PERM2_COD",
				"type":"int",
				"pk":true,
				"sequence":true,
				"nullable":false
			},
			"PERM2_COD_TXT":{
				"logical":"Código Texto",
				"physical":"PERM2_COD_TXT",
				"type":"varchar",
				"size":50,
				"pk":false,
				"sequence":false,
				"nullable":false
			},
			"PERM2_DAT_HR_INI":{
				"logical":"Data Hora Início",
				"physical":"PERM2_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"PERM2_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"PERM2_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"PERM2_DES":{
				"logical":"Descrição",
				"physical":"PERM2_DES",
				"type":"text",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"PERM2_NOM":{
				"logical":"Nome",
				"physical":"PERM2_NOM",
				"type":"varchar",
				"size":100,
				"pk":false,
				"sequence":false,
				"nullable":false
			}
		},"position":[100,100]
	},
	"TB2_PEUS":{
		"logical":"PERMISSAO DO USUARIO",
		"columns":{
			"PERM2_COD":{
				"logical":"Código Permissão",
				"physical":"PERM2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_PERM",column:"PERM2_COD"}
			},
			"PEUS2_DAT_HR_INI":{
				"logical":"Data Hora Início",
				"physical":"PEUS2_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":false
			},
			"PEUS2_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"PEUS2_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"USER2_COD":{
				"logical":"Código Usuário",
				"physical":"USER2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_USER",column:"USER2_COD"}
			},
			"USER2_COD_ATRIB":{
				"logical":"Código Usuário Atribuidor",
				"physical":"USER2_COD_ATRIB",
				"type":"int",
				"pk":false,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_USER",column:"USER2_COD"},
				"phrase":"atribuiu",
				"inverse":"atribuida por"
			}
		},"position":[340,260]
	},
	"TB2_PEGR":{
		"logical":"PERMISSAO DO GRUPO",
		"columns":{
			"GRPM2_COD":{
				"logical":"Código do Grupo",
				"physical":"GRPM2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_GRPM",column:"GRPM2_COD"}
			},
			"PERM2_COD":{
				"logical":"Código da Permissão",
				"physical":"PERM2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_PERM",column:"PERM2_COD"}
			},
			"PEGR2_DAT_HR_INI":{
				"logical":"Data Hora Início",
				"physical":"PEGR2_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"PEGR2_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"PEGR2_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			}
		},"position":[320,40]
	},
	"TB3_CAMA":{
		"logical":"CAMADA DE INTERFACE",
		"columns":{
			"CAMA3_COD":{
				"logical":"Código da Camada",
				"physical":"CAMA3_COD",
				"type":"int",
				"pk":true,
				"sequence":true,
				"nullable":false
			},
			"CAMA3_NOM":{
				"logical":"Nome da Camada",
				"physical":"CAMA3_NOM",
				"type":"varchar",
				"size":200,
				"pk":false,
				"sequence":false,
				"nullable":false
			},
			"CAMA3_DES":{
				"logical":"Descrição da Camada",
				"physical":"CAMA3_DES",
				"type":"text",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"CAMA3_COD_TXT":{
				"logical":"Código Texto",
				"physical":"CAMA3_COD_TXT",
				"type":"varchar",
				"size":50,
				"pk":false,
				"sequence":false,
				"nullable":false
			},
			"CAMA3_STA":{
				"logical":"Status da Camada",
				"physical":"CAMA3_STA",
				"type":"char",
				"size":1,
				"pk":false,
				"sequence":false,
				"nullable":false
			},
			"CAMA3_DAT_HR_INI":{
				"logical":"Data Hora Criação",
				"physical":"CAMA3_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"CAMA3_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"CAMA3_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			}
		},"position":[600,300]
	},
	"TB2_PGCA":{
		"logical":"PERMISSAO GRUPO CAMADA",
		"columns":{
			"GRPM2_COD":{
				"logical":"Código do Grupo",
				"physical":"GRPM2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_GRPM",column:"GRPM2_COD"}
			},
			"CAMA3_COD":{
				"logical":"Código da Camada",
				"physical":"CAMA3_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB3_CAMA",column:"CAMA3_COD"}
			},
			"PERM2_COD":{
				"logical":"Código da Permissão",
				"physical":"PERM2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_PERM",column:"PERM2_COD"}
			},
			"PGCA2_DAT_HR_INI":{
				"logical":"Data Hora Início",
				"physical":"PGCA2_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"PGCA2_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"PGCA2_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			}
		},"position":[400,200]
	},
	"TB2_PUCA":{
		"logical":"PERMISSAO USUARIO CAMADA",
		"columns":{
			"USER2_COD":{
				"logical":"Código do Usuário",
				"physical":"USER2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_USER",column:"USER2_COD"}
			},
			"CAMA3_COD":{
				"logical":"Código da Camada",
				"physical":"CAMA3_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB3_CAMA",column:"CAMA3_COD"}
			},
			"PERM2_COD":{
				"logical":"Código da Permissão",
				"physical":"PERM2_COD",
				"type":"int",
				"pk":true,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_PERM",column:"PERM2_COD"}
			},
			"PUCA2_DAT_HR_INI":{
				"logical":"Data Hora Início",
				"physical":"PUCA2_DAT_HR_INI",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"PUCA2_DAT_HR_MOD":{
				"logical":"Data Hora Modificação",
				"physical":"PUCA2_DAT_HR_MOD",
				"type":"datetime",
				"pk":false,
				"sequence":false,
				"nullable":true
			},
			"USER2_COD_ATRIB":{
				"logical":"Código Usuário Atribuidor",
				"physical":"USER2_COD_ATRIB",
				"type":"int",
				"pk":false,
				"sequence":false,
				"nullable":false,
				"fk":{table:"TB2_USER",column:"USER2_COD"},
				"phrase":"atribuiu",
				"inverse":"atribuida por"
			}
		},"position":[700,200]
	}
}
