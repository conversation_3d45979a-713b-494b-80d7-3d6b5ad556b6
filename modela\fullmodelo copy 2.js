var tbDefs={"TB1_CTRT":{"logical":"CONTRATO","columns":{"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CTRT1_COD_TXT":{"logical":"CODIGO CONTRATO","physical":"CTRT1_COD_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"CTRT1_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"CTRT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_DAT_HR_FIM":{"logical":"DATA DA CONCLUSAO","physical":"CTRT1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_COD_ERP":{"logical":"CHAVE  ERP","physical":"CTRT1_COD_ERP","type":"text","pk":false,"sequence":false,"nullable":false},"CTRT1_COD_DOCS":{"logical":"CODIGO GODOCS","physical":"CTRT1_COD_DOCS","type":"text","pk":false,"sequence":false,"nullable":true},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"STCT1_COD":{"logical":"COD STATUS CONTRATO","physical":"STCT1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STCT","column":"STCT1_COD"}},"CTRT1_DAT_ENC":{"logical":"DATA DE FINAL DE VALIDADE","physical":"CTRT1_DAT_ENC","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_INDISP":{"logical":"INDICADOR DE CONTRATO INDISPONIVEL","physical":"CTRT1_INDISP","type":"bit","pk":false,"sequence":false,"nullable":true},"CTRT1_ERP_JSON":{"logical":"ULTIMO VALOR ERP","physical":"CTRT1_ERP_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_DAT_ERP":{"logical":"DATA ATUALIZACAO ERP","physical":"CTRT1_DAT_ERP","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_ORIG":{"logical":"ORIGEM DO DADO","physical":"CTRT1_ORIG","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"CTRT1_DAT_PRIO_ERP":{"logical":"DATA ATUALIZACAO PROPRIETARIOS ERP","physical":"CTRT1_DAT_PRIO_ERP","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_ADITIVO":{"logical":"STATUS ADITIVO","physical":"CTRT1_ADITIVO","type":"integer","pk":false,"sequence":false,"nullable":true},"CTRT1_TIPO":{"logical":"STATUS TIPO","physical":"CTRT1_TIPO","type":"integer","pk":false,"sequence":false,"nullable":true},"CTRT1_APTO":{"logical":"STATUS APTO LEILAO","physical":"CTRT1_APTO","type":"integer","pk":false,"sequence":false,"nullable":true},"CTRT1_REV":{"logical":"CONTRATO REVISADO","physical":"CTRT1_REV","type":"bit","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_ANO":{"logical":"VALOR ANO CORRENTE","physical":"CTRT1_VAL_ANO","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_ACUM":{"logical":"VALOR ACUMULADO","physical":"CTRT1_VAL_ACUM","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_CTRT":{"logical":"VALOR CONTRATO","physical":"CTRT1_VAL_CTRT","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_EXTRA":{"logical":"VALOR EXTRA","physical":"CTRT1_VAL_EXTRA","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_DAT_REG":{"logical":"DATA DE REGISTRO","physical":"CTRT1_DAT_REG","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_ATRASO":{"logical":"VALOR ATRASO","physical":"CTRT1_VAL_ATRASO","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_TIPO_ERP":{"logical":"TIPO CONTRATO NO ERP","physical":"CTRT1_TIPO_ERP","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_PRIO_CACHE":{"logical":"CACHE PROPRIETARIOS","physical":"CTRT1_PRIO_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_COD_SUC":{"logical":"ID DO SUCESSOR","physical":"CTRT1_COD_SUC","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"},"phrase":"FOI SUCEDIDO POR","inverse":"EH SUCESSOR DO"},"CTRT1_DAT_CSCT":{"logical":"DATA ATUALIZACAO CESSIONARIOS ERP","physical":"CTRT1_DAT_CSCT","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_CESS_CACHE":{"logical":"CACHE CESSIONARIOS","physical":"CTRT1_CESS_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_PROX":{"logical":"VALOR PROXIMO PAGAMENTO","physical":"CTRT1_VAL_PROX","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_DAT_PROX":{"logical":"DATA PROXIMO PAGAMENTO","physical":"CTRT1_DAT_PROX","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_DIST_AUT_DAT":{"logical":"INDICADOR DISTRATO AUTORIZADO","physical":"CTRT1_DIST_AUT_DAT","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD_AUTDIST":{"logical":"CODIGO USUARIO AUTORIZOU DISTRATO","physical":"USER2_COD_AUTDIST","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CTRT1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"CTRT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_COD_BEMA":{"logical":"CHAVE BEMATECH","physical":"CTRT1_COD_BEMA","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_COD_SAP":{"logical":"CHAVE SAP","physical":"CTRT1_COD_SAP","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_ARQ_CACHE":{"logical":"CACHE ARQUIVOS","physical":"CTRT1_ARQ_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_GD_ID":{"logical":"GDRIVE ID","physical":"CTRT1_GD_ID","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD_CRIA":{"logical":"CODIGO USUARIO CRIADOR","physical":"USER2_COD_CRIA","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"CRIOU","inverse":"CRIADO POR"},"TCTB1_COD":{"logical":"CODIGO TIPO CONTRATO BIG","physical":"TCTB1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_TCTB","column":"TCTB1_COD"}},"STSE1_COD":{"logical":"CODIGO STATUS SERVIDAO","physical":"STSE1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STSE","column":"STSE1_COD"}},"CTRT1_STATUS_CTRT":{"logical":"CODIGO STATUS CONTRATO BIG","physical":"CTRT1_STATUS_CTRT","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STCTB","column":"STCTB1_COD"}},"CTRT1_VAL_BIG":{"logical":"VALOR CONTRATO BIG","physical":"CTRT1_VAL_BIG","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_ACUM_BIG":{"logical":"VALOR ACUMULADO BIG","physical":"CTRT1_VAL_ACUM_BIG","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_VAL_LAUDO":{"logical":"VALOR LAUDO","physical":"CTRT1_VAL_LAUDO","type":"double precision","pk":false,"sequence":false,"nullable":true},"SECO1_COD":{"logical":"ID SETOR CONTRATO","physical":"SECO1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_SECO","column":"SECO1_COD"}},"STIN1_COD":{"logical":"CODIGO STATUS INDENIZACAO","physical":"STIN1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STIN","column":"STIN1_COD"}},"CTRT1_VAL_CONTRA":{"logical":"VALOR CONTRA PROPOSTA","physical":"CTRT1_VAL_CONTRA","type":"double precision","pk":false,"sequence":false,"nullable":true},"CTRT1_DAT_CONTRA":{"logical":"DATA CONTRA PROPOSTA","physical":"CTRT1_DAT_CONTRA","type":"date","pk":false,"sequence":false,"nullable":true},"CTRT1_DAT_AVERB":{"logical":"DATA AVERBACAO","physical":"CTRT1_DAT_AVERB","type":"date","pk":false,"sequence":false,"nullable":true},"STBE1_COD":{"logical":"CODIGO STATUS BENFEITORIA","physical":"STBE1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STBE","column":"STBE1_COD"}}},"position":[100,100]},"TB1_PROP":{"logical":"PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PROP1_NOM":{"logical":"NOME","physical":"PROP1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"PROP1_GEOM":{"logical":"GEOMETRIA","physical":"PROP1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":134},"PROP1_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"PROP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_METOBT":{"logical":"METODO DE OBTENCAO","physical":"PROP1_METOBT","type":"text","pk":false,"sequence":false,"nullable":true},"STPG1_COD":{"logical":"COD STATUS PROPRIEDADE GLEBA","physical":"STPG1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STPG","column":"STPG1_COD"},"phrase":"EH O STATUS DA","inverse":"TEM O"},"PROP1_MATR":{"logical":"MATRICULA","physical":"PROP1_MATR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PROP1_AREA_DOC":{"logical":"AREA DOCUMENTAL","physical":"PROP1_AREA_DOC","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_CCIR":{"logical":"CERTIFICADO CADASTRO IMOVEL RURAL","physical":"PROP1_CCIR","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_NIRF":{"logical":"IDENTIFICACAO RECEITA FEDERAL ITR","physical":"PROP1_NIRF","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_COMA":{"logical":"COMARCA","physical":"PROP1_COMA","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_COD":{"logical":"ID DO CARTORIO","physical":"CART1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CART","column":"CART1_COD"}},"PROP1_CONC":{"logical":"INDICADOR DE INTERESSE DE CONCORRENTE","physical":"PROP1_CONC","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_OBS_CONC":{"logical":"OBSERVACAO CONCORRENTE","physical":"PROP1_OBS_CONC","type":"text","pk":false,"sequence":false,"nullable":true},"ESCR1_COD":{"logical":"ID DO ESCRITORIO","physical":"ESCR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_ESCR","column":"ESCR1_COD"}},"PROP1_CART":{"logical":"CARTORIO","physical":"PROP1_CART","type":"text","pk":false,"sequence":false,"nullable":true},"COMA1_COD":{"logical":"ID COMARCA","physical":"COMA1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMA","column":"COMA1_COD"}},"PROP1_DAT_GEOM":{"logical":"DATA DA GEOMETRIA","physical":"PROP1_DAT_GEOM","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_IND_GEO":{"logical":"INDICADOR DE GEORREFERENCIAMENTO","physical":"PROP1_IND_GEO","type":"bit","pk":false,"sequence":false,"nullable":true},"DESE1_COD":{"logical":"ID DO DESENVOLVEDOR","physical":"DESE1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_DESE","column":"DESE1_COD"}},"STPG1_COD_ERP":{"logical":"ID DO STATUS NO ERP","physical":"STPG1_COD_ERP","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STPG","column":"STPG1_COD"},"phrase":"EH O STATUS NO ERP DA","inverse":"TEM NO ERP O"},"PROP1_ATI":{"logical":"INDICADOR DE PROPRIEDADE ATIVA","physical":"PROP1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_INDISP":{"logical":"INDICADOR DE PROPRIEDADE INDISPONIVEL NO ERP","physical":"PROP1_INDISP","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_DAT_ERP":{"logical":"DATA DA ULTIMA VERIFICACAO NO ERP","physical":"PROP1_DAT_ERP","type":"date","pk":false,"sequence":false,"nullable":true},"MUNI1_COD":{"logical":"CODIGO IBGE MUNICIPIO","physical":"MUNI1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_MUNI","column":"MUNI1_COD"}},"PROP1_COD_ERP":{"logical":"ID PROPRIEDADE NO ERP","physical":"PROP1_COD_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PROP1_DAT_PRIO_ERP":{"logical":"DATA DA ULTIMA VERIFICACAO DE PROPRIETARIOS NO ERP","physical":"PROP1_DAT_PRIO_ERP","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PROP1_CTRT_CACHE":{"logical":"CACHE CONTRATOS","physical":"PROP1_CTRT_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_CONQUISTA":{"logical":"STATUS CONQUISTA","physical":"PROP1_CONQUISTA","type":"integer","pk":false,"sequence":false,"nullable":true},"PROP1_PRIO_STATUS":{"logical":"STATUS PROPRIETARIO","physical":"PROP1_PRIO_STATUS","type":"integer","pk":false,"sequence":false,"nullable":true},"PROP1_MATR_PARC":{"logical":"INDICADOR DE MATRICULA PARCIAL","physical":"PROP1_MATR_PARC","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_CCIR_DESAT":{"logical":"INDICADOR DE CCIR DESATUALIZADO","physical":"PROP1_CCIR_DESAT","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_NIRF_DESAT":{"logical":"INDICADOR DE ITR DESATUALIZADO","physical":"PROP1_NIRF_DESAT","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_CCIR_ERP":{"logical":"CERTIFCADO CADASTRO IMOVEL RURAL NO ERP","physical":"PROP1_CCIR_ERP","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_NIRF_ERP":{"logical":"IDENTIFICACAO RECEITA FEDERAL ITR NO ERP","physical":"PROP1_NIRF_ERP","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_MATR_ERP":{"logical":"MATRICULA NO ERP","physical":"PROP1_MATR_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PROP1_NOM_ERP":{"logical":"NOME ERP","physical":"PROP1_NOM_ERP","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_PRIO_CACHE":{"logical":"CACHE PROPRIETARIOS","physical":"PROP1_PRIO_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_AREA_CONSID":{"logical":"AREA CONSIDERADA","physical":"PROP1_AREA_CONSID","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_COD_ORIG":{"logical":"ID DA PROPRIEDADE ORIGINADORA","physical":"PROP1_COD_ORIG","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"},"phrase":"DESMEMBRADA NESTA","inverse":"DESMEMBRADAS DESTA"},"PROP1_COD_REMEMB":{"logical":"ID DA PROPRIEDADE REMEMBRADA","physical":"PROP1_COD_REMEMB","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"},"phrase":"REMEMBRADA DESTA","inverse":"REMEMBRARAM NESTA"},"PROP1_DAT_HR_REMEMB":{"logical":"DATA REMEMBRAMENTO","physical":"PROP1_DAT_HR_REMEMB","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_DAT_HR_DESMEMB":{"logical":"DATA DESMEMBRAMENTO","physical":"PROP1_DAT_HR_DESMEMB","type":"date","pk":false,"sequence":false,"nullable":true},"CART1_COD_ERP":{"logical":"ID DO CARTORIO ERP","physical":"CART1_COD_ERP","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CART","column":"CART1_COD"},"phrase":"DO ERP NA","inverse":"REGISTRADA NO ERP NO"},"PROP1_COD_CAR":{"logical":"CODIGO NO CAR","physical":"PROP1_COD_CAR","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_COD_SIGEF":{"logical":"CODIGO NO SIGEF","physical":"PROP1_COD_SIGEF","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_COD_SNCI":{"logical":"CODIGO SNCI","physical":"PROP1_COD_SNCI","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PROP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_CMPMATR":{"logical":"COMPLEMENTO MATRICULA","physical":"PROP1_CMPMATR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PROP1_CMPMATR_ERP":{"logical":"COMPLEMENTO MATRICULA ERP","physical":"PROP1_CMPMATR_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PROP1_CCIR_DAT":{"logical":"DATA VALIDADE CCIR","physical":"PROP1_CCIR_DAT","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_CCIR_DAT_ERP":{"logical":"DATA VALIDADE CCIR ERP","physical":"PROP1_CCIR_DAT_ERP","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_NIRF_DAT":{"logical":"DATA VALIDADE ITR","physical":"PROP1_NIRF_DAT","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_NIRF_DAT_ERP":{"logical":"DATA VALIDADE ITR ERP","physical":"PROP1_NIRF_DAT_ERP","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_CIT_DAT":{"logical":"EMISSAO CERTIDAO INTEIRO TEOR","physical":"PROP1_CIT_DAT","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_COD_BEMATECH":{"logical":"ID DA PROPRIEDADE NO BEMATECH","physical":"PROP1_COD_BEMATECH","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PROP1_ARQ_CACHE":{"logical":"CACHE ARQUIVOS","physical":"PROP1_ARQ_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_GD_ID":{"logical":"GDRIVE ID","physical":"PROP1_GD_ID","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_AUT_GEO":{"logical":"GEO AUTORIZADO","physical":"PROP1_AUT_GEO","type":"integer","pk":false,"sequence":false,"nullable":true},"PROP1_AREA_MATR":{"logical":"AREA MATRICULA BIG","physical":"PROP1_AREA_MATR","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_AREA_CCIR":{"logical":"AREA DOCUMENTAL CCIR","physical":"PROP1_AREA_CCIR","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_AREA_ITR":{"logical":"AREA DOCUMENTAL ITR","physical":"PROP1_AREA_ITR","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_SITU_CACHE":{"logical":"CACHE SITUACAO","physical":"PROP1_SITU_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_GEO_STATUS":{"logical":"CODIGO STATUS GEO","physical":"PROP1_GEO_STATUS","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STGEO","column":"STGEO1_COD"}},"STAP1_COD":{"logical":"CODIGO STATUS PASSAGEM","physical":"STAP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STAP","column":"STAP1_COD"}},"PROP1_DOMINIO":{"logical":"CODIGO STATUS DOMINIO","physical":"PROP1_DOMINIO","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STDOM","column":"STDOM1_COD"}},"PROP1_ATIFUND":{"logical":"INDICADOR ATIVO FUNDIARIO","physical":"PROP1_ATIFUND","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_DAT_RTT":{"logical":"DATA READY TO TRANSFER","physical":"PROP1_DAT_RTT","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_CONQ_PROV":{"logical":"INDICADOR CONQUISTA PROVISORIA","physical":"PROP1_CONQ_PROV","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_AREA_TOTAL_SAP":{"logical":"AREA TOTAL SAP","physical":"PROP1_AREA_TOTAL_SAP","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_AREA_EFET_SAP":{"logical":"AREA EFETIVA SAP","physical":"PROP1_AREA_EFET_SAP","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_AREA_DOC_SAP":{"logical":"AREA DOCUMENTAL SAP","physical":"PROP1_AREA_DOC_SAP","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_TC_CP":{"logical":"TIPO TERMO COMPROMISSO CONQUISTA PROVISORIA","physical":"PROP1_TC_CP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PROP1_REPR_CAR":{"logical":"INDICADOR REPRESENTANTE CADASTRADO","physical":"PROP1_REPR_CAR","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_ST_CLGEO":{"logical":"STATUS CHECKLIST GEO","physical":"PROP1_ST_CLGEO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":30},"PROP1_ST_SIGEF":{"logical":"STATUS CERTIFICACAO SIGEF","physical":"PROP1_ST_SIGEF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PROP1_PROT_SIGEF":{"logical":"PROTOCOLOS SIGEF","physical":"PROP1_PROT_SIGEF","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_NEWGEOM_GEO":{"logical":"ALERTA NOVA GEOMETRIA GEO","physical":"PROP1_NEWGEOM_GEO","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[1230,1306]},"TB1_GLEP":{"logical":"GLEBA DE PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"GLEP1_GEOM":{"logical":"GEOMETRIA","physical":"GLEP1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"GLEP1_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"GLEP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STPG1_COD":{"logical":"COD STATUS PROPRIEDADE GLEBA","physical":"STPG1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STPG","column":"STPG1_COD"}},"GLEP1_AREA":{"logical":"AREA DA GLEBA","physical":"GLEP1_AREA","type":"double precision","pk":false,"sequence":false,"nullable":true},"GLEP1_COD":{"logical":"ID DA GLEBA","physical":"GLEP1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"GLEP1_NOM":{"logical":"NOME DA GEOMETRIA","physical":"GLEP1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"GLEP1_DES":{"logical":"DESCRICAO DA GLEBA","physical":"GLEP1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"GLEP1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"GLEP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,100]},"TB1_HGPR":{"logical":"HISTORICO GEOMETRIA DA PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"HGPR1_COD":{"logical":"ID DO HISTORICO","physical":"HGPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HGPR1_DAT_HR_INI":{"logical":"DATA DA OBTENCAO","physical":"HGPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HGPR1_GEOM":{"logical":"GEOMETRIA","physical":"HGPR1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":134},"HGPR1_DAT_HR_SUBS":{"logical":"DATA DA SUBSTITUICAO","physical":"HGPR1_DAT_HR_SUBS","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HGPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HGPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,100]},"TB2_TELE":{"logical":"TELEFONE","columns":{"TELE2_COD":{"logical":"CODIGO DO TELEFONE","physical":"TELE2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TELE2_NUM":{"logical":"TELEFONE","physical":"TELE2_NUM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"TELE2_DDI":{"logical":"DDI","physical":"TELE2_DDI","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":4},"TELE2_DDD":{"logical":"DDD","physical":"TELE2_DDD","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":4},"TELE2_DESC":{"logical":"DESCRICAO","physical":"TELE2_DESC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":75},"TELE2_WAPP":{"logical":"WHATSAPP","physical":"TELE2_WAPP","type":"bit","pk":false,"sequence":false,"nullable":true},"TELE2_DAT_ALO":{"logical":"DATA DO ULTIMO ALO","physical":"TELE2_DAT_ALO","type":"date","pk":false,"sequence":false,"nullable":true},"TELE2_INV":{"logical":"INDICADOR DE TELEFONE INVALIDO","physical":"TELE2_INV","type":"bit","pk":false,"sequence":false,"nullable":true},"TELE2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TELE2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TELE2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TELE2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[1977,1759]},"TB1_PROJ":{"logical":"PROJETO","columns":{"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PROJ1_NOM":{"logical":"NOME","physical":"PROJ1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"PROJ1_ATI":{"logical":"ATIVO","physical":"PROJ1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"PROJ1_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"PROJ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PROJ1_DAT_HR_FIM":{"logical":"DATA DA CONCLUSAO","physical":"PROJ1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"ESCR1_COD":{"logical":"ID DO ESCRITORIO","physical":"ESCR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_ESCR","column":"ESCR1_COD"}},"USER2_COD_ENG1":{"logical":"CODIGO USUARIO ENGENHEIRO 1","physical":"USER2_COD_ENG1","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ENGENHEIROUM","inverse":"DOENGENHEIROUM"},"PROJ1_PREF":{"logical":"PREFIXO","physical":"PROJ1_PREF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PROJ1_GEOM_INT":{"logical":"POLIGONAL DE INTERESSE","physical":"PROJ1_GEOM_INT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"PROJ1_GEOM":{"logical":"GEOMETRIA DO LIMITE","physical":"PROJ1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PROJ1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PROJ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"STPJ1_COD":{"logical":"CODIGO DO STATUS","physical":"STPJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STPJ","column":"STPJ1_COD"}},"USER2_COD_ENG2":{"logical":"CODIGO USUARIO ENGENHEIRO 2","physical":"USER2_COD_ENG2","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ENGENHEIRODOIS","inverse":"DOENGENHEIRODOIS"},"DESE1_COD":{"logical":"ID DO DESENVOLVEDOR","physical":"DESE1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_DESE","column":"DESE1_COD"}},"USER2_COD_AMB":{"logical":"CODIGO USUARIO AMBIENTAL","physical":"USER2_COD_AMB","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"AMBIENTAL","inverse":"DOAMBIENTAL"},"USER2_COD_JUR":{"logical":"CODIGO USUARIO JURIDICO","physical":"USER2_COD_JUR","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"JURIDICO","inverse":"DOJURIDICO"},"PROJ1_TIPO":{"logical":"TIPO DE PROJETO","physical":"PROJ1_TIPO","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD_RESP":{"logical":"CODIGO USUARIO RESPONSAVEL","physical":"USER2_COD_RESP","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"RESPONSAVEL PELO","inverse":"TEM RESPONSAVEL"},"TIPR1_COD":{"logical":"CODIGO TIPO","physical":"TIPR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_TIPR","column":"TIPR1_COD"}},"PRIO1_COD_CESS":{"logical":"ID DO CESSIONARIO","physical":"PRIO1_COD_CESS","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PROJ1_ABR":{"logical":"ABREVIATURA","physical":"PROJ1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":15},"USER2_COD_ENG_NEG":{"logical":"ENGENHEIRO NEGOCIOS","physical":"USER2_COD_ENG_NEG","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ENGENHEIRONEGOCIO","inverse":"DOENGENHEIRONEGOCIO"},"USER2_COD_AMB_GEO":{"logical":"CODIGO USUARIO RESPONSAVEL AMBIENTAL GEO","physical":"USER2_COD_AMB_GEO","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"RESPONSAVELAMBGEO","inverse":"DORESPONSAVELAMBGEO"},"USER2_COD_GEORREF":{"logical":"CODIGO RESPONSAVEL GEORREFERENCIAMENTO","physical":"USER2_COD_GEORREF","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"RESPONSAVELGEORREFERENCIAMENTO","inverse":"DORESPONSAVELGEORREFERENCIAMENTO"},"USER2_COD_GOVFUND":{"logical":"ESPECIALISTA GOVERNANCA FUNDIARIA","physical":"USER2_COD_GOVFUND","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"GOVERNANCAFUNDIARIADO","inverse":"TEMGOVERNANCAFUNDIARIAPOR"},"USER2_COD_COORDFUND":{"logical":"COORDENADOR FUNDIARIO","physical":"USER2_COD_COORDFUND","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"COORDENADORFUNDIARIODO","inverse":"TEMCOORDENADORFUNDIARIPOR"},"PROJ1_GD_ID":{"logical":"GDRIVE ID","physical":"PROJ1_GD_ID","type":"text","pk":false,"sequence":false,"nullable":true},"COMPL1_COD_UG":{"logical":"COMPLEXO UG","physical":"COMPL1_COD_UG","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"},"phrase":"UG","inverse":"SOU UG"},"COMPL1_COD_BOP":{"logical":"COMPLEXO BOP","physical":"COMPL1_COD_BOP","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"},"phrase":"BOP","inverse":"SOU BOP"},"COMPL1_COD_XTRA":{"logical":"COMPLEXO EXTRA","physical":"COMPL1_COD_XTRA","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"},"phrase":"EXTRA","inverse":"SOU EXTRA"},"COMPL1_COD_LI":{"logical":"CODIGO DO COMPLEXO LICENCA INSTALACAO","physical":"COMPL1_COD_LI","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"},"phrase":"LI","inverse":"SOU LI"},"COMPL1_COD_LP":{"logical":"CODIGO DO COMPLEXO LICENCA PREVIA","physical":"COMPL1_COD_LP","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"},"phrase":"LP","inverse":"SOU LP"}},"position":[1724,412]},"TB1_PRPR":{"logical":"PROPRIEDADE DO PROJETO","columns":{"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PRPR1_DAT_HR_INI":{"logical":"DATA DA ENTRADA","physical":"PRPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PRPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PRPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,300]},"TB1_HGGL":{"logical":"HISTORICO DA GEOMETRIA DA GLEBA","columns":{"HGGL1_COD":{"logical":"ID DO HISTORICO DA AREA","physical":"HGGL1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HGGL1_GEOM":{"logical":"GEOMETRIA","physical":"HGGL1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"HGGL1_DAT_HR_INI":{"logical":"DATA DA OBENTCAO","physical":"HGGL1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"HGGL1_DAT_HR_SUBS":{"logical":"DATA DA SUBSTITUICAO","physical":"HGGL1_DAT_HR_SUBS","type":"date","pk":false,"sequence":false,"nullable":false},"GLEP1_COD":{"logical":"ID DA GLEBA","physical":"GLEP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_GLEP","column":"GLEP1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HGGL1_NOM":{"logical":"NOME DA GLEBA","physical":"HGGL1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"HGGL1_DES":{"logical":"DESCRICAO DA GLEBA","physical":"HGGL1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"HGGL1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HGGL1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,300]},"TB1_PRIO":{"logical":"PROPRIETARIO","columns":{"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PRIO1_NOM":{"logical":"NOME OU RAZAO","physical":"PRIO1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"}},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"}},"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_PFIS","column":"PFIS2_COD"}},"PRIO1_MAE":{"logical":"NOME DA MAE","physical":"PRIO1_MAE","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_DOC":{"logical":"DOCUMENTO","physical":"PRIO1_DOC","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_COD_ERP":{"logical":"ID NO ERP","physical":"PRIO1_COD_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PRIO1_RG":{"logical":"RG","physical":"PRIO1_RG","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_EMRG":{"logical":"EMISSOR RG","physical":"PRIO1_EMRG","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"PRIO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PRIO1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PRIO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PRIO1_DP":{"logical":"DOCUMENTOS PESSOAIS","physical":"PRIO1_DP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"PRIO1_ARQ_CACHE":{"logical":"CACHE ARQUIVOS","physical":"PRIO1_ARQ_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_GD_ID":{"logical":"GDRIVE ID","physical":"PRIO1_GD_ID","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_TELES":{"logical":"TELEFONES","physical":"PRIO1_TELES","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_TELES_OBS":{"logical":"OBSERVACAO TELEFONES","physical":"PRIO1_TELES_OBS","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_EMAILS":{"logical":"EMAILS","physical":"PRIO1_EMAILS","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_EMAILS_OBS":{"logical":"OBSERVACAO EMAILS","physical":"PRIO1_EMAILS_OBS","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_ENDES":{"logical":"ENDERECOS","physical":"PRIO1_ENDES","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_ENDES_OBS":{"logical":"OBSERVACAO ENDERECOS","physical":"PRIO1_ENDES_OBS","type":"text","pk":false,"sequence":false,"nullable":true},"PRIO1_CESS":{"logical":"EH CESSIONARIO","physical":"PRIO1_CESS","type":"bit","pk":false,"sequence":false,"nullable":true},"PRIO1_COD_ERP_CESS":{"logical":"CHAVE ERP COMO CESSIONARIO","physical":"PRIO1_COD_ERP_CESS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PRIO1_COD_ERP_DIV":{"logical":"DIVISAO OU FILIAL CESSIONARIO","physical":"PRIO1_COD_ERP_DIV","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"ENDE2_COD":{"logical":"CODIGO DO ENDERECO","physical":"ENDE2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_ENDE","column":"ENDE2_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"},"phrase":"EH MORADIA DO","inverse":"MORA NA"},"PRIO1_DAT_RG":{"logical":"DATA DE EMISSAO RG","physical":"PRIO1_DAT_RG","type":"date","pk":false,"sequence":false,"nullable":true},"PRIO1_SEX":{"logical":"SEXO","physical":"PRIO1_SEX","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PRIO1_EST_CIVIL":{"logical":"ESTADO CIVIL","physical":"PRIO1_EST_CIVIL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PRIO1_NACION":{"logical":"NACIONALIDADE","physical":"PRIO1_NACION","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PRIO1_PROF":{"logical":"PROFISSAO","physical":"PRIO1_PROF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PRIO1_BANCO_NUM":{"logical":"NUMERO BANCO","physical":"PRIO1_BANCO_NUM","type":"integer","pk":false,"sequence":false,"nullable":true},"PRIO1_BANCO":{"logical":"BANCO","physical":"PRIO1_BANCO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PRIO1_BANCO_AG":{"logical":"AGENCIA","physical":"PRIO1_BANCO_AG","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PRIO1_TIPO_CONTA":{"logical":"TIPO CONTA","physical":"PRIO1_TIPO_CONTA","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PRIO1_CONTA":{"logical":"CONTA","physical":"PRIO1_CONTA","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PRIO1_NOM_FANT":{"logical":"NOME FANTASIA","physical":"PRIO1_NOM_FANT","type":"text","pk":false,"sequence":false,"nullable":true},"BANC2_COD":{"logical":"CODIGO","physical":"BANC2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_BANC","column":"BANC2_COD"}},"PRIO1_TI":{"logical":"TERMO DE INVENTARIANTE","physical":"PRIO1_TI","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"PRIO1_LOGIN_CAR":{"logical":"LOGIN CAR","physical":"PRIO1_LOGIN_CAR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PRIO1_PW_CAR":{"logical":"SENHA CAR","physical":"PRIO1_PW_CAR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PRIO1_LOGIN_CEFIR":{"logical":"LOGIN CEFIR","physical":"PRIO1_LOGIN_CEFIR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PRIO1_PW_CEFIR":{"logical":"SENHA CEFIR","physical":"PRIO1_PW_CEFIR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200}},"position":[700,300]},"TB1_HSGL":{"logical":"HISTORICO STATUS GLEBA","columns":{"HSGL1_COD":{"logical":"ID HISTORICO STATUS","physical":"HSGL1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HSGL1_DAT_HR_INI":{"logical":"DATA INICIO","physical":"HSGL1_DAT_HR_INI","type":"integer","pk":false,"sequence":false,"nullable":false},"HSGL1_DAT_HR_FIM":{"logical":"DATA FINAL","physical":"HSGL1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":false},"STPG1_COD":{"logical":"COD STATUS PROPRIEDADE GLEBA","physical":"STPG1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STPG","column":"STPG1_COD"}},"GLEP1_COD":{"logical":"ID DA GLEBA","physical":"GLEP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_GLEP","column":"GLEP1_COD"}},"HSGL1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HSGL1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[1085,395]},"TB1_HSPR":{"logical":"HISTORICO STATUS PROPRIEDADE","columns":{"HSPR1_COD":{"logical":"ID HISTORICO STATUS PROPR","physical":"HSPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HSPR1_DAT_HR_INI":{"logical":"DATA INICIO","physical":"HSPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HSPR1_DAT_HR_FIM":{"logical":"DATA FINAL","physical":"HSPR1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"STPG1_COD":{"logical":"COD STATUS PROPRIEDADE GLEBA","physical":"STPG1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STPG","column":"STPG1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HSPR1_COMENT":{"logical":"COMENTARIO DA MUDANCA DE STATUS","physical":"HSPR1_COMENT","type":"text","pk":false,"sequence":false,"nullable":true},"HSPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HSPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,500]},"TB1_STPG":{"logical":"STATUS POSSIVEL PROPRIEDADE GLEBA","columns":{"STPG1_COD":{"logical":"COD STATUS PROPRIEDADE GLEBA","physical":"STPG1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STPG1_NOM":{"logical":"STATUS","physical":"STPG1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"STPG1_DES":{"logical":"DESCRICAO","physical":"STPG1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STPG1_COD_SGT2":{"logical":"CODIGO SGT2","physical":"STPG1_COD_SGT2","type":"integer","pk":false,"sequence":false,"nullable":true},"STPG1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STPG1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STPG1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STPG1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,500]},"TB1_PPRO":{"logical":"PROPRIETARIO DA PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PPRO1_COD_TIPO_ERP":{"logical":"ID TIPO PROPRIETARIO ERP","physical":"PPRO1_COD_TIPO_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PPRO1_TIPO_ERP":{"logical":"TIPO PROPRIETARIO ERP","physical":"PPRO1_TIPO_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PPRO1_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"PPRO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PPRO1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PPRO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PPRO1_DP":{"logical":"DOCUMENTOS PESSOAIS","physical":"PPRO1_DP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8}},"position":[500,500]},"TB1_ESCR":{"logical":"ESCRITORIO","columns":{"ESCR1_COD":{"logical":"ID DO ESCRITORIO","physical":"ESCR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ESCR1_NOM":{"logical":"NOME DO ESCRITORIO","physical":"ESCR1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ESCR1_DES":{"logical":"DESCRICAO","physical":"ESCR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD_RESP":{"logical":"CODIGO USUARIO RESPONSAVEL","physical":"USER2_COD_RESP","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"EH RESPONSAVEL PELO","inverse":"TEM COMO RESPONSAVEL O"},"ESCR1_GEOM":{"logical":"POLIGONAL","physical":"ESCR1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"ESCR1_BB":{"logical":"BOUNDING BOX","physical":"ESCR1_BB","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"ESCR1_ATI":{"logical":"ATIVO","physical":"ESCR1_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"ESCR1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"ESCR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD_GERE":{"logical":"CODIGO USUARIO GERENTE","physical":"USER2_COD_GERE","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"EH GERENTE","inverse":"TEM COMO GERENTE"},"ESCR1_ABR":{"logical":"ABREVIATURA","physical":"ESCR1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":15},"ESCR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ESCR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,500]},"TB1_PARQ":{"logical":"PARQUE","columns":{"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PARQ1_NOM":{"logical":"NOME DO PARQUE","physical":"PARQ1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PARQ1_DES":{"logical":"DESCRICAO","physical":"PARQ1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"ESCR1_COD":{"logical":"ID DO ESCRITORIO","physical":"ESCR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_ESCR","column":"ESCR1_COD"}},"COMPL1_COD":{"logical":"CODIGO DO COMPLEXO","physical":"COMPL1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"}},"PARQ1_GEOM":{"logical":"GEOMETRIA DO LIMITE","physical":"PARQ1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PARQ1_BB":{"logical":"BOUNDING BOX","physical":"PARQ1_BB","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PARQ1_GEOM_LIC":{"logical":"GEOMETRIA LICENCIAMENTO","physical":"PARQ1_GEOM_LIC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"PARQ1_GEOM_EPE":{"logical":"GEOMETRIA EPE","physical":"PARQ1_GEOM_EPE","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"PARQ1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PARQ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PRIO1_COD_CESS":{"logical":"PROPRIETARIO CESSIONARIO","physical":"PRIO1_COD_CESS","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PARQ1_DAT_CLIE":{"logical":"PRAZO CLIENTE","physical":"PARQ1_DAT_CLIE","type":"date","pk":false,"sequence":false,"nullable":true},"PARQ1_DAT_REG":{"logical":"PRAZO REGULARIZACAO","physical":"PARQ1_DAT_REG","type":"date","pk":false,"sequence":false,"nullable":true},"PARQ1_DAT_CONQ":{"logical":"PRAZO CONQUISTA","physical":"PARQ1_DAT_CONQ","type":"date","pk":false,"sequence":false,"nullable":true},"PARQ1_ABR":{"logical":"ABREVIATURA","physical":"PARQ1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":15},"PARQ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PARQ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PARQ1_GD_ID":{"logical":"GDRIVE ID","physical":"PARQ1_GD_ID","type":"text","pk":false,"sequence":false,"nullable":true},"PARQ1_GEOM_ADA":{"logical":"GEOMETRIA ADA","physical":"PARQ1_GEOM_ADA","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132}},"position":[2161,715]},"TB1_PRPQ":{"logical":"PROPRIEDADE DO PARQUE","columns":{"PRPQ1_DAT_HR_INI":{"logical":"DATA DA ENTRADA","physical":"PRPQ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PRPQ1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PRPQ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,700]},"TB2_PUES":{"logical":"PERMISSAO USUARIO ESCRITORIO","columns":{"PUES2_EXIB":{"logical":"EXIBIR","physical":"PUES2_EXIB","type":"bit","pk":false,"sequence":false,"nullable":false},"PUES2_EDIT":{"logical":"EDITAR","physical":"PUES2_EDIT","type":"bit","pk":false,"sequence":false,"nullable":false},"PUES2_ADMIN":{"logical":"ADMINISTRAR","physical":"PUES2_ADMIN","type":"bit","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"ESCR1_COD":{"logical":"ID DO ESCRITORIO","physical":"ESCR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_ESCR","column":"ESCR1_COD"}},"PUES2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PUES2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PUES2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PUES2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,700]},"TB2_PUPR":{"logical":"PERMISSAO USUARIO PROJETO","columns":{"PUPR2_EXIB":{"logical":"EXIBIR","physical":"PUPR2_EXIB","type":"bit","pk":false,"sequence":false,"nullable":false},"PUPR2_EDIT":{"logical":"EDITAR","physical":"PUPR2_EDIT","type":"bit","pk":false,"sequence":false,"nullable":false},"PUPR2_ADMIN":{"logical":"ADMINISTRAR","physical":"PUPR2_ADMIN","type":"bit","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"PUPR2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PUPR2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PUPR2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PUPR2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,700]},"TB2_PUPQ":{"logical":"PERMISSAO USUARIO PARQUE","columns":{"PUPQ2_EXIB":{"logical":"EXIBIR","physical":"PUPQ2_EXIB","type":"bit","pk":false,"sequence":false,"nullable":false},"PUPQ2_EDIT":{"logical":"EDITAR","physical":"PUPQ2_EDIT","type":"bit","pk":false,"sequence":false,"nullable":false},"PUPQ2_ADMIN":{"logical":"ADMINISTRAR","physical":"PUPQ2_ADMIN","type":"bit","pk":false,"sequence":false,"nullable":false},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PUPQ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PUPQ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PUPQ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PUPQ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,700]},"TB4_DOCU":{"logical":"DOCUMENTO","columns":{"DOCU4_COD":{"logical":"ID DO DOCUMENTO","physical":"DOCU4_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"DOCU4_TIT":{"logical":"TITULO DO DOCUMENTO","physical":"DOCU4_TIT","type":"text","pk":false,"sequence":false,"nullable":false},"DOCU4_DES":{"logical":"DESCRICAO","physical":"DOCU4_DES","type":"text","pk":false,"sequence":false,"nullable":true},"DOCU4_COD_REM":{"logical":"CODIGO REMOTO","physical":"DOCU4_COD_REM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":150},"DOCU4_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"DOCU4_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"DOCU4_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"DOCU4_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,700]},"TB4_DOCO":{"logical":"DOCUMENTO DO CONTRATO","columns":{"DOCU4_COD":{"logical":"ID DO DOCUMENTO","physical":"DOCU4_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB4_DOCU","column":"DOCU4_COD"}},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"DOCO4_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"DOCO4_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"DOCO4_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"DOCO4_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,900]},"TB4_DOPR":{"logical":"DOCUMENTO DA PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"DOCU4_COD":{"logical":"ID DO DOCUMENTO","physical":"DOCU4_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB4_DOCU","column":"DOCU4_COD"}},"DOPR4_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"DOPR4_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"DOPR4_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"DOPR4_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,900]},"TB1_AERG":{"logical":"AEROGERADOR","columns":{"AERG1_COD":{"logical":"ID DO AEROGERADOR","physical":"AERG1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"AERG1_NOM":{"logical":"NOME","physical":"AERG1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"AERG1_DES":{"logical":"DESCRICAO","physical":"AERG1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"AERG1_MAQ":{"logical":"MAQUINA","physical":"AERG1_MAQ","type":"text","pk":false,"sequence":false,"nullable":true},"GLEP1_COD":{"logical":"ID DA GLEBA","physical":"GLEP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_GLEP","column":"GLEP1_COD"}},"AERG1_GEOM":{"logical":"LOCALIZACAO","physical":"AERG1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":12},"AERG1_POT":{"logical":"POTENCIA MW","physical":"AERG1_POT","type":"double precision","pk":false,"sequence":false,"nullable":true},"AERG1_ALT":{"logical":"ALTURA TOTAL","physical":"AERG1_ALT","type":"double precision","pk":false,"sequence":false,"nullable":true},"AERG1_TORRE":{"logical":"ALTURA TORRE","physical":"AERG1_TORRE","type":"double precision","pk":false,"sequence":false,"nullable":true},"AERG1_DIAM":{"logical":"DIAMETRO ROTOR","physical":"AERG1_DIAM","type":"double precision","pk":false,"sequence":false,"nullable":true},"AERG1_COTA":{"logical":"COTA","physical":"AERG1_COTA","type":"double precision","pk":false,"sequence":false,"nullable":true},"AERG1_FUSO":{"logical":"FUSO","physical":"AERG1_FUSO","type":"double precision","pk":false,"sequence":false,"nullable":true},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"STAG1_COD":{"logical":"CODIGO STATUS","physical":"STAG1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STAG","column":"STAG1_COD"}},"VLOA1_COD":{"logical":"CODIGO DA VERSAO","physical":"VLOA1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_VLOA","column":"VLOA1_COD"}},"AERG1_FC":{"logical":"FATOR DE CAPACIDADE","physical":"AERG1_FC","type":"double precision","pk":false,"sequence":false,"nullable":true},"AERG1_LAYO":{"logical":"LAYOUT","physical":"AERG1_LAYO","type":"text","pk":false,"sequence":false,"nullable":true},"AERG1_TIPO_LAYO":{"logical":"TIPO DE LAYOUT","physical":"AERG1_TIPO_LAYO","type":"text","pk":false,"sequence":false,"nullable":true},"AERG1_WM":{"logical":"WIND MODEL","physical":"AERG1_WM","type":"integer","pk":false,"sequence":false,"nullable":true},"AERG1_CENA":{"logical":"CENARIO","physical":"AERG1_CENA","type":"integer","pk":false,"sequence":false,"nullable":true},"AERG1_PROJ":{"logical":"PROJETO","physical":"AERG1_PROJ","type":"text","pk":false,"sequence":false,"nullable":true},"AERG1_NET_YIELD":{"logical":"NET YIELD","physical":"AERG1_NET_YIELD","type":"double precision","pk":false,"sequence":false,"nullable":true},"AERG1_COMP_NAME":{"logical":"NOME DO COMPLEXO ENGENHARIA","physical":"AERG1_COMP_NAME","type":"text","pk":false,"sequence":false,"nullable":true},"AERG1_COMP_COD":{"logical":"CODIGO DO COMPLEXO ENGENHARIA","physical":"AERG1_COMP_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"AERG1_SITE_ID":{"logical":"SITE ID","physical":"AERG1_SITE_ID","type":"integer","pk":false,"sequence":false,"nullable":true},"AERG1_PORTFOLIO":{"logical":"EH PORTFOLIO","physical":"AERG1_PORTFOLIO","type":"bit","pk":false,"sequence":false,"nullable":true},"AERG1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"AERG1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"AERG1_DAT_HR_MOD":{"logical":"DATA DE MODIFICACAO","physical":"AERG1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"AERG1_PARK_COD":{"logical":"CODIGO PARQUE ENGENHARIA","physical":"AERG1_PARK_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"AERG1_PARK_NAME":{"logical":"NOME PARQUE ENGENHARIA","physical":"AERG1_PARK_NAME","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"AERG1_PROJ_COD":{"logical":"CODIGO PROJETO ENGENHARIA","physical":"AERG1_PROJ_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"AERG1_LAYOUT_CHEIO":{"logical":"LAYOUT CHEIO","physical":"AERG1_LAYOUT_CHEIO","type":"bit","pk":false,"sequence":false,"nullable":true},"AERG1_PORTFOLIO_ID":{"logical":"PORTFOLIO ID","physical":"AERG1_PORTFOLIO_ID","type":"integer","pk":false,"sequence":false,"nullable":true}},"position":[500,900]},"TB1_CART":{"logical":"CARTORIO","columns":{"CART1_COD":{"logical":"ID DO CARTORIO","physical":"CART1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CART1_NOM":{"logical":"NOME DO CARTORIO","physical":"CART1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"CART1_MUNI":{"logical":"MUNICIPIO DO CARTORIO","physical":"CART1_MUNI","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"CART1_UF":{"logical":"UF DO CARTORIO","physical":"CART1_UF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":30},"MUNI1_COD":{"logical":"CODIGO IBGE MUNICIPIO","physical":"MUNI1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_MUNI","column":"MUNI1_COD"}},"COMA1_COD":{"logical":"ID COMARCA","physical":"COMA1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMA","column":"COMA1_COD"}},"CART1_CNS":{"logical":"CNS DO CARTORIO","physical":"CART1_CNS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":15},"CART1_CNPJ":{"logical":"CNPJ","physical":"CART1_CNPJ","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":30},"CART1_FANT":{"logical":"FANTASIA","physical":"CART1_FANT","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_ENDE":{"logical":"ENDERECO","physical":"CART1_ENDE","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_BAI":{"logical":"BAIRRO","physical":"CART1_BAI","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_CEP":{"logical":"CEP","physical":"CART1_CEP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"CART1_SITE":{"logical":"SITE","physical":"CART1_SITE","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_EMAIL":{"logical":"EMAIL","physical":"CART1_EMAIL","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_TEL":{"logical":"TELEFONE","physical":"CART1_TEL","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_ABRAN":{"logical":"AREA ABRANGENCIA","physical":"CART1_ABRAN","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_ATRIB":{"logical":"ATRIBUICOES","physical":"CART1_ATRIB","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_ENTR":{"logical":"ENTRANCIA","physical":"CART1_ENTR","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_NOM_CNS":{"logical":"NOME VIA CNS","physical":"CART1_NOM_CNS","type":"text","pk":false,"sequence":false,"nullable":true},"CART1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CART1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CART1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CART1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,900]},"TB1_STCT":{"logical":"STATUS POSSIVEL CONTRATO","columns":{"STCT1_COD":{"logical":"COD STATUS CONTRATO","physical":"STCT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STCT1_NOM":{"logical":"STATUS","physical":"STCT1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"STCT1_DES":{"logical":"DESCRICAO","physical":"STCT1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STCT1_COD_ERP":{"logical":"CODIGO ERP","physical":"STCT1_COD_ERP","type":"text","pk":false,"sequence":false,"nullable":true},"STPG1_COD":{"logical":"COD STATUS PROPRIEDADE GLEBA","physical":"STPG1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STPG","column":"STPG1_COD"}},"STCT1_STATUS_BIG":{"logical":"STATUS CONTRATO BIG","physical":"STCT1_STATUS_BIG","type":"integer","pk":false,"sequence":false,"nullable":true},"STCT1_GEO_BIG":{"logical":"STATUS GEO BIG","physical":"STCT1_GEO_BIG","type":"integer","pk":false,"sequence":false,"nullable":true},"STCT1_NOM_SAP":{"logical":"NOME STATUS SAP","physical":"STCT1_NOM_SAP","type":"text","pk":false,"sequence":false,"nullable":true},"STCT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"STCT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,900]},"TB1_HSCT":{"logical":"HISTORICO STATUS CONTRATO","columns":{"HSCT1_COD":{"logical":"ID HISTORICO","physical":"HSCT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HSCT1_DAT_HR_INI":{"logical":"DATA INICIO","physical":"HSCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HSCT1_DAT_HR_FIM":{"logical":"DATA FINAL","physical":"HSCT1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":false},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"STCT1_COD":{"logical":"COD STATUS CONTRATO","physical":"STCT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STCT","column":"STCT1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HSCT1_COMENT":{"logical":"COMENTARIO MUDANCA STATUS","physical":"HSCT1_COMENT","type":"text","pk":false,"sequence":false,"nullable":true},"HSCT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HSCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,1100]},"TB1_VCTO":{"logical":"VERSAO DO CONTRATO","columns":{"VCTO1_COD":{"logical":"ID DA VERSAO","physical":"VCTO1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"VCTO1_COD_ERP":{"logical":"CODIGO ERP","physical":"VCTO1_COD_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"VCTO1_GODOCS":{"logical":"CODIGO GODOCS","physical":"VCTO1_GODOCS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"VCTO1_COD_CVER":{"logical":"CODIGO CVER","physical":"VCTO1_COD_CVER","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"VCTO1_DES":{"logical":"DESCRICAO","physical":"VCTO1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"VCTO1_DAT_HR":{"logical":"DATA DA VERSAO","physical":"VCTO1_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":true},"VCTO1_MOTIVO":{"logical":"MOTIVO","physical":"VCTO1_MOTIVO","type":"text","pk":false,"sequence":false,"nullable":true},"STCT1_COD":{"logical":"COD STATUS CONTRATO","physical":"STCT1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STCT","column":"STCT1_COD"}},"VCTO1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VCTO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,1100]},"TB1_PPRH":{"logical":"PROPRIETARIO DA PROPRIEDADE HISTORICO","columns":{"PPRH1_DAT_HR":{"logical":"DATA DA ALTERACAO","physical":"PPRH1_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PPRH1_COD":{"logical":"CODIGO DO HISTORICO","physical":"PPRH1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PPRH1_COD_TIPO_ERP":{"logical":"ID TIPO PROPRIETARIO ERP","physical":"PPRH1_COD_TIPO_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PPRH1_TIPO_ERP":{"logical":"TIPO PROPRIETARIO ERP","physical":"PPRH1_TIPO_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PPRH1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PPRH1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,1100]},"TB2_CFUS":{"logical":"CONFIGURACAO DO USUARIO","columns":{"CFUS2_COD":{"logical":"CODIGO DA CONFIGURACAO","physical":"CFUS2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CFUS2_NOM":{"logical":"NOME DA CONFIGURACAO","physical":"CFUS2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"CFUS2_JSON":{"logical":"JSON","physical":"CFUS2_JSON","type":"text","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CFUS2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CFUS2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CFUS2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CFUS2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,1100]},"TB1_COMA":{"logical":"COMARCA","columns":{"COMA1_COD":{"logical":"ID COMARCA","physical":"COMA1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COMA1_NOM":{"logical":"NOME","physical":"COMA1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"MUNI1_COD":{"logical":"CODIGO IBGE MUNICIPIO","physical":"MUNI1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_MUNI","column":"MUNI1_COD"}},"COMA1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"COMA1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"COMA1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"COMA1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,1100]},"TB1_GPCT":{"logical":"GLEBA DE PROPRIEDADE NO CONTRATO","columns":{"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"GLEP1_COD":{"logical":"ID DA GLEBA","physical":"GLEP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_GLEP","column":"GLEP1_COD"}},"GPCT1_AREA_CONT":{"logical":"AREA DA GLEBA NO CONTRATO","physical":"GPCT1_AREA_CONT","type":"double precision","pk":false,"sequence":false,"nullable":true},"GPCT1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"GPCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"GPCT1_AREA_PARC_SAP":{"logical":"AREA PARCIAL SAP","physical":"GPCT1_AREA_PARC_SAP","type":"double precision","pk":false,"sequence":false,"nullable":true},"GPCT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GPCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false}},"position":[1639,1787]},"TB1_DESE":{"logical":"DESENVOLVEDOR","columns":{"DESE1_COD":{"logical":"ID DO DESENVOLVEDOR","physical":"DESE1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"DESE1_NOM":{"logical":"NOME DO DESENVOLVEDOR","physical":"DESE1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"ESCR1_COD":{"logical":"ID DO ESCRITORIO","physical":"ESCR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_ESCR","column":"ESCR1_COD"}},"DESE1_ATI":{"logical":"ATIVO","physical":"DESE1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"DESE1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"DESE1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"DESE1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"DESE1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,1300]},"TB3_GRCM":{"logical":"GRUPO DE CAMADAS","columns":{"GRCM3_COD":{"logical":"CODIGO DO GRUPO","physical":"GRCM3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"GRCM3_NOM":{"logical":"NOME DO GRUPO","physical":"GRCM3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"GRCM3_DES":{"logical":"DESCRICAO","physical":"GRCM3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"GRCM3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GRCM3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"GRCM3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"GRCM3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,1300]},"TB3_CMPT":{"logical":"CAMADA MULTIPOINT","columns":{"CMPT3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMPT3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CMPT3_NOM":{"logical":"NOME DA CAMADA","physical":"CMPT3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"CMPT3_DES":{"logical":"DESCRICAO","physical":"CMPT3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"GRCM3_COD":{"logical":"CODIGO DO GRUPO","physical":"GRCM3_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB3_GRCM","column":"GRCM3_COD"}},"CMPT3_COD_TXT":{"logical":"CODIGO TEXTO CAMADA","physical":"CMPT3_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"CMPT3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CMPT3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CMPT3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CMPT3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,1300]},"TB3_PTCM":{"logical":"MULTIPOINT DA CAMADA","columns":{"PTCM3_COD":{"logical":"CODIGO DO MULTIPOINT","physical":"PTCM3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PTCM3_NOM":{"logical":"TITULO DA GEOMETRIA","physical":"PTCM3_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"PTCM3_GEOM":{"logical":"GEOMETRIA","physical":"PTCM3_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":14},"CMPT3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMPT3_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB3_CMPT","column":"CMPT3_COD"}},"PTCM3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PTCM3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PTCM3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PTCM3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,1300]},"TB3_ATPT":{"logical":"ATRIBUTO DO MULTIPOINT","columns":{"ATPT3_COD":{"logical":"CODIGO DO ATRIBUTO","physical":"ATPT3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATPT3_NOM":{"logical":"NOME DO ATRIBUTO","physical":"ATPT3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"CMPT3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMPT3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CMPT","column":"CMPT3_COD"}},"ATPT3_BUSCA":{"logical":"INDICADOR DE ATRIBUTO BUSCAVEL","physical":"ATPT3_BUSCA","type":"bit","pk":false,"sequence":false,"nullable":true},"ATPT3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATPT3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATPT3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATPT3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,1500]},"TB3_VAPT":{"logical":"VALOR DO ATRIBUTO NO MULTIPOINT","columns":{"ATPT3_COD":{"logical":"CODIGO DO ATRIBUTO","physical":"ATPT3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_ATPT","column":"ATPT3_COD"}},"PTCM3_COD":{"logical":"CODIGO DO MULTIPOINT","physical":"PTCM3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_PTCM","column":"PTCM3_COD"}},"VAPT3_VAL_TXT":{"logical":"VALOR TEXTO","physical":"VAPT3_VAL_TXT","type":"text","pk":false,"sequence":false,"nullable":true},"VAPT3_VAL_NUM":{"logical":"VALOR NUMERICO","physical":"VAPT3_VAL_NUM","type":"double precision","pk":false,"sequence":false,"nullable":true},"VAPT3_VAL_DAT":{"logical":"VALOR DATA","physical":"VAPT3_VAL_DAT","type":"date","pk":false,"sequence":false,"nullable":true},"VAPT3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VAPT3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"VAPT3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VAPT3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,1500]},"TB3_CMLN":{"logical":"CAMADA MULTILINE","columns":{"CMLN3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMLN3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CMLN3_NOM":{"logical":"NOME DA CAMADA","physical":"CMLN3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"CMLN3_DES":{"logical":"DESCRICAO","physical":"CMLN3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"CMLN3_COD_TXT":{"logical":"CODIGO TEXTO DA CAMADA","physical":"CMLN3_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"GRCM3_COD":{"logical":"CODIGO DO GRUPO","physical":"GRCM3_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB3_GRCM","column":"GRCM3_COD"}},"CMLN3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CMLN3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CMLN3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CMLN3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,1500]},"TB3_LNCM":{"logical":"MULTILINE DA CAMADA","columns":{"LNCM3_COD":{"logical":"CODIGO DO MULTILINE","physical":"LNCM3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LNCM3_NOM":{"logical":"TITULO DA GEOMETRIA","physical":"LNCM3_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"LNCM3_GEOM":{"logical":"GEOMETRIA","physical":"LNCM3_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":124},"CMLN3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMLN3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CMLN","column":"CMLN3_COD"}},"LNCM3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"LNCM3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LNCM3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LNCM3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,1500]},"TB3_VALN":{"logical":"VALOR DO ATRIBUTO NO MULTILINE","columns":{"VALN3_VAL_TXT":{"logical":"VALOR TEXTO","physical":"VALN3_VAL_TXT","type":"text","pk":false,"sequence":false,"nullable":true},"VALN3_VAL_NUM":{"logical":"VALOR NUMERICO","physical":"VALN3_VAL_NUM","type":"double precision","pk":false,"sequence":false,"nullable":true},"VALN3_VAL_DAT":{"logical":"VALOR DATA","physical":"VALN3_VAL_DAT","type":"date","pk":false,"sequence":false,"nullable":true},"LNCM3_COD":{"logical":"CODIGO DO MULTILINE","physical":"LNCM3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_LNCM","column":"LNCM3_COD"}},"ATLN3_COD":{"logical":"CODIGO DO ATRIBUTO","physical":"ATLN3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_ATLN","column":"ATLN3_COD"}},"VALN3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VALN3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"VALN3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VALN3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,1500]},"TB3_ATLN":{"logical":"ATRIBUTO DO MULTILINE","columns":{"ATLN3_COD":{"logical":"CODIGO DO ATRIBUTO","physical":"ATLN3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATLN3_NOM":{"logical":"NOME DO ATRIBUTO","physical":"ATLN3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ATLN3_BUSCA":{"logical":"INDICADOR DE ATRIBUTO BUSCAVEL","physical":"ATLN3_BUSCA","type":"bit","pk":false,"sequence":false,"nullable":true},"CMLN3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMLN3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CMLN","column":"CMLN3_COD"}},"ATLN3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATLN3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATLN3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATLN3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,1700]},"TB3_CMPL":{"logical":"CAMADA MULTIPOLY","columns":{"CMPL3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMPL3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CMPL3_NOM":{"logical":"NOME DA CAMADA","physical":"CMPL3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"CMPL3_DES":{"logical":"DESCRICAO","physical":"CMPL3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"CMPL3_COD_TXT":{"logical":"CODIGO TEXTO DA CAMADA","physical":"CMPL3_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"GRCM3_COD":{"logical":"CODIGO DO GRUPO","physical":"GRCM3_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB3_GRCM","column":"GRCM3_COD"}},"CMPL3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CMPL3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CMPL3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CMPL3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,1700]},"TB3_PLCM":{"logical":"MULTIPOLY DA CAMADA","columns":{"PLCM3_COD":{"logical":"CODIGO DO MULTIPOLY","physical":"PLCM3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PLCM3_NOM":{"logical":"TITULO DA GEOMETRIA","physical":"PLCM3_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"PLCM3_GEOM":{"logical":"GEOMETRIA","physical":"PLCM3_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":134},"CMPL3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMPL3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CMPL","column":"CMPL3_COD"}},"PLCM3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PLCM3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PLCM3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PLCM3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,1700]},"TB3_VAPL":{"logical":"VALOR DO ATRIBUTO NO MULTIPOLY","columns":{"VAPL3_VAL_TXT":{"logical":"VALOR TEXTO","physical":"VAPL3_VAL_TXT","type":"text","pk":false,"sequence":false,"nullable":true},"VAPL3_VAL_NUM":{"logical":"VALOR NUMERICO","physical":"VAPL3_VAL_NUM","type":"double precision","pk":false,"sequence":false,"nullable":true},"VAPL3_VAL_DAT":{"logical":"VALOR DATA","physical":"VAPL3_VAL_DAT","type":"date","pk":false,"sequence":false,"nullable":true},"PLCM3_COD":{"logical":"CODIGO DO MULTIPOLY","physical":"PLCM3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_PLCM","column":"PLCM3_COD"}},"ATPL3_COD":{"logical":"CODIGO DO ATRIBUTO","physical":"ATPL3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_ATPL","column":"ATPL3_COD"}},"VAPL3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VAPL3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"VAPL3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VAPL3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,1700]},"TB3_ATPL":{"logical":"ATRIBUTO DO MULTIPOLY","columns":{"ATPL3_COD":{"logical":"CODIGO DO ATRIBUTO","physical":"ATPL3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATPL3_NOM":{"logical":"NOME DO ATRIBUTO","physical":"ATPL3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ATPL3_BUSCA":{"logical":"INDICADOR DE ATRIBUTO BUSCAVEL","physical":"ATPL3_BUSCA","type":"bit","pk":false,"sequence":false,"nullable":true},"CMPL3_COD":{"logical":"CODIGO DA CAMADA","physical":"CMPL3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CMPL","column":"CMPL3_COD"}},"ATPL3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATPL3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATPL3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATPL3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,1700]},"TB1_STAG":{"logical":"STATUS AEROGERADOR","columns":{"STAG1_COD":{"logical":"CODIGO STATUS","physical":"STAG1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STAG1_NOM":{"logical":"STATUS","physical":"STAG1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"STAG1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STAG1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"STAG1_COD_ERP":{"logical":"CODIGO ERP","physical":"STAG1_COD_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"STAG1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STAG1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STAG1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STAG1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,1900]},"TB1_LOAG":{"logical":"LAYOUT DE AEROGERADORES","columns":{"LOAG1_COD":{"logical":"CODIGO DO LAYOUT","physical":"LOAG1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LOAG1_NOM":{"logical":"NOME DO LAYOUT","physical":"LOAG1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"LOAG1_ATI":{"logical":"INDICADOR DE LAYOUT ATIVO","physical":"LOAG1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"LOAG1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"LOAG1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"LOAG1_TIPO":{"logical":"TIPO DE LAYOUT","physical":"LOAG1_TIPO","type":"integer","pk":false,"sequence":false,"nullable":false},"COMPL1_COD":{"logical":"CODIGO DO COMPLEXO","physical":"COMPL1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"}},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"LOAG1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LOAG1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,1900]},"TB1_COMPL":{"logical":"COMPLEXO","columns":{"COMPL1_COD":{"logical":"CODIGO DO COMPLEXO","physical":"COMPL1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COMPL1_NOM":{"logical":"NOME DO COMPLEXO","physical":"COMPL1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"COMPL1_ABR":{"logical":"ABREVIATURA","physical":"COMPL1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":30},"COMPL1_GEOM":{"logical":"GEOMETRIA DO LIMITE","physical":"COMPL1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"COMPL1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"COMPL1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"STCP1_COD":{"logical":"CODIGO DO STATUS","physical":"STCP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STCP","column":"STCP1_COD"}},"COMPL1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"COMPL1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"COMPL1_RTT70":{"logical":"RTT 70 POR CENTO","physical":"COMPL1_RTT70","type":"bit","pk":false,"sequence":false,"nullable":false},"COMPL1_RTT100":{"logical":"RTT 100 POR CENTO","physical":"COMPL1_RTT100","type":"bit","pk":false,"sequence":false,"nullable":false},"COMPL1_DAT_RTT70":{"logical":"DATA RTT 70","physical":"COMPL1_DAT_RTT70","type":"date","pk":false,"sequence":false,"nullable":true},"COMPL1_DAT_RTT100":{"logical":"DATA RTT 100","physical":"COMPL1_DAT_RTT100","type":"date","pk":false,"sequence":false,"nullable":true},"COMPL1_GD_ID":{"logical":"GDRIVE ID","physical":"COMPL1_GD_ID","type":"text","pk":false,"sequence":false,"nullable":true},"COMPL1_VISITA_ANO":{"logical":"VISITAS ANO","physical":"COMPL1_VISITA_ANO","type":"integer","pk":false,"sequence":false,"nullable":true},"COMPL1_PERIOD_DIAS":{"logical":"PERIODICIDADE VISITA","physical":"COMPL1_PERIOD_DIAS","type":"integer","pk":false,"sequence":false,"nullable":true}},"position":[500,1900]},"TB1_MUCO":{"logical":"MUNICIPIO DA COMARCA","columns":{"COMA1_COD":{"logical":"ID COMARCA","physical":"COMA1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_COMA","column":"COMA1_COD"}},"MUNI1_COD":{"logical":"CODIGO IBGE MUNICIPIO","physical":"MUNI1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_MUNI","column":"MUNI1_COD"}},"MUCO1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"MUCO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"MUCO1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"MUCO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,1900]},"TB1_COPR":{"logical":"COMENTARIO DA PROPRIEDADE","columns":{"COPR1_COD":{"logical":"CODIGO","physical":"COPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COPR1_TIT":{"logical":"TITULO","physical":"COPR1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"COPR1_TXT":{"logical":"TEXTO","physical":"COPR1_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"COPR1_FORM":{"logical":"FORMATACAO","physical":"COPR1_FORM","type":"text","pk":false,"sequence":false,"nullable":true},"COPR1_DAT_HR_INI":{"logical":"DATA","physical":"COPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"COPR1_ATI":{"logical":"COMENTARIO ATIVO","physical":"COPR1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"COPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"COPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,1900]},"TB2_ANUS":{"logical":"ANOTACAO DO USUARIO","columns":{"ANUS2_COD":{"logical":"CODIGO ANOTACAO","physical":"ANUS2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ANUS2_TIT":{"logical":"TITULO","physical":"ANUS2_TIT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ANUS2_TXT":{"logical":"TEXTO","physical":"ANUS2_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"ANUS2_DAT_HAR_INI":{"logical":"DATA DA CRIACAO","physical":"ANUS2_DAT_HAR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ANUS2_FORM":{"logical":"FORMATACAO","physical":"ANUS2_FORM","type":"text","pk":false,"sequence":false,"nullable":true},"ANUS2_GEOM":{"logical":"GEOMETRIA","physical":"ANUS2_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"ANUS2_PUB":{"logical":"PUBLICA","physical":"ANUS2_PUB","type":"bit","pk":false,"sequence":false,"nullable":false},"ANUS2_IMG":{"logical":"IMAGEM","physical":"ANUS2_IMG","type":"text","pk":false,"sequence":false,"nullable":true},"ANUS2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ANUS2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ANUS2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ANUS2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,2100]},"TB2_PERM":{"logical":"PERMISSAO","columns":{"PERM2_COD":{"logical":"CODIGO PERMISSAO","physical":"PERM2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PERM2_NOM":{"logical":"NOME","physical":"PERM2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"PERM2_DES":{"logical":"DESCRICAO","physical":"PERM2_DES","type":"text","pk":false,"sequence":false,"nullable":true},"PERM2_COD_TXT":{"logical":"CODIGO TEXTO","physical":"PERM2_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"PERM2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PERM2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PERM2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PERM2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,2100]},"TB2_HIPE":{"logical":"HISTORICO PERMISSAO","columns":{"HIPE2_COD":{"logical":"CODIGO HISTORICO","physical":"HIPE2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HIPE2_DAT_HR":{"logical":"DATA DA ALTERACAO","physical":"HIPE2_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"USER2_COD_ATRIB":{"logical":"CODIGO USUARIO ATRIBUIDOR","physical":"USER2_COD_ATRIB","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ATRIBUIU","inverse":"FOI ATRIBUIDA POR"},"HIPE2_JSON":{"logical":"DEFINICAO DA PERMISSAO","physical":"HIPE2_JSON","type":"text","pk":false,"sequence":false,"nullable":false},"HIPE2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HIPE2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,2100]},"TB3_CAMA":{"logical":"CAMADA DE INTERFACE","columns":{"CAMA3_COD":{"logical":"ID DA CAMADA","physical":"CAMA3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CAMA3_NOM":{"logical":"NOME DA CAMADA","physical":"CAMA3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"CAMA3_COD_TXT":{"logical":"CODIGO TEXTO DA CAMADA","physical":"CAMA3_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"CAMA3_JSON":{"logical":"DEFINICAO JSON DA CAMADA","physical":"CAMA3_JSON","type":"text","pk":false,"sequence":false,"nullable":false},"CAMA3_PUB":{"logical":"INDICADOR DE CAMADA PUBLICA","physical":"CAMA3_PUB","type":"bit","pk":false,"sequence":false,"nullable":false},"CAMA3_ATI":{"logical":"CAMADA ATIVA","physical":"CAMA3_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"CAMA3_DES":{"logical":"DESCRICAO","physical":"CAMA3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"CAMA3_URL":{"logical":"URL FONTE","physical":"CAMA3_URL","type":"text","pk":false,"sequence":false,"nullable":true},"CAMA3_GRP":{"logical":"GRUPO","physical":"CAMA3_GRP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"CAMA3_DAT_ULT_ATU":{"logical":"DATA DA ULTIMA ATUALIZACAO","physical":"CAMA3_DAT_ULT_ATU","type":"date","pk":false,"sequence":false,"nullable":true},"CAMA3_RESP":{"logical":"RESPONSAVEL PELA CAMADA","physical":"CAMA3_RESP","type":"text","pk":false,"sequence":false,"nullable":true},"GRCA3_COD":{"logical":"CODIGO DO GRUPO","physical":"GRCA3_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB3_GRCA","column":"GRCA3_COD"}},"CAMA3_FONTE":{"logical":"FONTE DOS DADOS DA CAMADA","physical":"CAMA3_FONTE","type":"text","pk":false,"sequence":false,"nullable":true},"CAMA3_SHOW_ATU":{"logical":"INDICADOR DE EXIBICAO DA DATA DE ATUALIZACAO","physical":"CAMA3_SHOW_ATU","type":"bit","pk":false,"sequence":false,"nullable":true},"CAMA3_CQL":{"logical":"FILTRO CQL DA CAMADA","physical":"CAMA3_CQL","type":"text","pk":false,"sequence":false,"nullable":true},"PERM2_COD":{"logical":"CODIGO PERMISSAO","physical":"PERM2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_PERM","column":"PERM2_COD"}},"CAMA3_BUSCA":{"logical":"INDICADOR DE CAMADA BUSCAVEL","physical":"CAMA3_BUSCA","type":"bit","pk":false,"sequence":false,"nullable":true},"CAMA3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CAMA3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CAMA3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CAMA3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,2100]},"TB2_PUCA":{"logical":"PERMISSAO USUARIO CAMADA","columns":{"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CAMA3_COD":{"logical":"ID DA CAMADA","physical":"CAMA3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_CAMA","column":"CAMA3_COD"}},"PUCA2_EXIB":{"logical":"EXIBIR","physical":"PUCA2_EXIB","type":"bit","pk":false,"sequence":false,"nullable":false},"PUCA2_EDIT":{"logical":"EDITAR","physical":"PUCA2_EDIT","type":"bit","pk":false,"sequence":false,"nullable":false},"PUCA2_ADMIN":{"logical":"ADMINISTRAR","physical":"PUCA2_ADMIN","type":"bit","pk":false,"sequence":false,"nullable":false},"PUCA2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PUCA2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PUCA2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PUCA2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,2100]},"TB2_GRPM":{"logical":"GRUPO DE PERMISSOES","columns":{"GRPM2_COD":{"logical":"ID DO GRUPO","physical":"GRPM2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"GRPM2_NOM":{"logical":"NOME DO GRUPO","physical":"GRPM2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"GRPM2_DES":{"logical":"DESCRICAO","physical":"GRPM2_DES","type":"text","pk":false,"sequence":false,"nullable":true},"GRPM2_COD_TXT":{"logical":"CODIGO TEXTO","physical":"GRPM2_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"GRPM2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GRPM2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"GRPM2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"GRPM2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,2300]},"TB2_PEGR":{"logical":"PERMISSAO DO GRUPO","columns":{"GRPM2_COD":{"logical":"ID DO GRUPO","physical":"GRPM2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_GRPM","column":"GRPM2_COD"}},"PERM2_COD":{"logical":"CODIGO PERMISSAO","physical":"PERM2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PERM","column":"PERM2_COD"}},"PEGR2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PEGR2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PEGR2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PEGR2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,2300]},"TB2_GPUS":{"logical":"GRUPO DE PERMISSOES DO USUARIO","columns":{"GRPM2_COD":{"logical":"ID DO GRUPO","physical":"GRPM2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_GRPM","column":"GRPM2_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"GPUS2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GPUS2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"GPUS2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"GPUS2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,2300]},"TB2_PGCA":{"logical":"PERMISSAO GRUPO CAMADA","columns":{"GRPM2_COD":{"logical":"ID DO GRUPO","physical":"GRPM2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_GRPM","column":"GRPM2_COD"}},"CAMA3_COD":{"logical":"ID DA CAMADA","physical":"CAMA3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_CAMA","column":"CAMA3_COD"}},"PGCA2_EXIB":{"logical":"EXIBIR","physical":"PGCA2_EXIB","type":"bit","pk":false,"sequence":false,"nullable":false},"PGCA2_EDIT":{"logical":"EDITAR","physical":"PGCA2_EDIT","type":"bit","pk":false,"sequence":false,"nullable":false},"PGCA2_ADMIN":{"logical":"ADMINISTRAR","physical":"PGCA2_ADMIN","type":"bit","pk":false,"sequence":false,"nullable":false},"PGCA2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PGCA2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PGCA2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PGCA2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,2300]},"TB1_POCO":{"logical":"POLIGONO DE CONQUISTA","columns":{"POCO1_COD":{"logical":"ID DO POLIGONO","physical":"POCO1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"POCO1_NOM":{"logical":"NOME DO POLIGONO","physical":"POCO1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"POCO1_DES":{"logical":"DESCRICAO","physical":"POCO1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"POCO1_GEOM":{"logical":"GEOMETRIA","physical":"POCO1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":32},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"POCO1_ATI":{"logical":"INDICADOR DE POLIGONO ATIVO","physical":"POCO1_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"POCO1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"POCO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"POCO1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"POCO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,2300]},"TB2_FIUS":{"logical":"FILTRO DO USUARIO","columns":{"FIUS2_COD":{"logical":"CODIGO DA CONFIGURACAO","physical":"FIUS2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"FIUS2_NOM":{"logical":"NOME DA CONFIGURACAO","physical":"FIUS2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"FIUS2_JSON":{"logical":"JSON","physical":"FIUS2_JSON","type":"text","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"FIUS2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"FIUS2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"FIUS2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"FIUS2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,2500]},"TB1_PPCT":{"logical":"PROPRIETARIO DO CONTRATO","columns":{"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PPCT1_COD_TIPO_ERP":{"logical":"ID DO TIPO DE PROPRIETARIO NO ERP","physical":"PPCT1_COD_TIPO_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PPCT1_TIPO_ERP":{"logical":"TIPO DE PROPRIETARIO ERP","physical":"PPCT1_TIPO_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PPCT1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"PPCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PPCT1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PPCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PPCT1_DP":{"logical":"DOCUMENTOS PESSOAIS","physical":"PPCT1_DP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"PPCT1_DAT_INCL":{"logical":"DATA DE INCLUSAO","physical":"PPCT1_DAT_INCL","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,2500]},"TB1_VLOA":{"logical":"VERSAO DO LAYOUT","columns":{"VLOA1_COD":{"logical":"CODIGO DA VERSAO","physical":"VLOA1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"VLOA1_NOM":{"logical":"NOME DA VERSAO","physical":"VLOA1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"VLOA1_DAT_HR":{"logical":"DATA DA VERSAO","physical":"VLOA1_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"LOAG1_COD":{"logical":"CODIGO DO LAYOUT","physical":"LOAG1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_LOAG","column":"LOAG1_COD"}},"VLOA1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VLOA1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,2500]},"TB1_CSCT":{"logical":"CESSIONARIO DO CONTRATO","columns":{"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"CSCT1_PERC":{"logical":"PERCENTUAL","physical":"CSCT1_PERC","type":"double precision","pk":false,"sequence":false,"nullable":true},"CSCT1_DAT_INI_CES":{"logical":"DATA INICIO CESSAO","physical":"CSCT1_DAT_INI_CES","type":"date","pk":false,"sequence":false,"nullable":true},"CSCT1_COD_ERP":{"logical":"CHAVE DO CESSIONARIO NO ERP","physical":"CSCT1_COD_ERP","type":"text","pk":false,"sequence":false,"nullable":false},"CSCT1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"CSCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"CSCT1_COD":{"logical":"CODIGO","physical":"CSCT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CSCT1_ATUAL":{"logical":"CESSIONARIO ATUAL","physical":"CSCT1_ATUAL","type":"bit","pk":false,"sequence":false,"nullable":false},"CSCT1_NUM_SAP":{"logical":"NUMERO SAP DO CONTRATO","physical":"CSCT1_NUM_SAP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"CSCT1_COD_ERP_DIV":{"logical":"DIVISAO OU FILIAL","physical":"CSCT1_COD_ERP_DIV","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"CSCT1_JSON":{"logical":"DADOS ADICIONAIS","physical":"CSCT1_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"CSCT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CSCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false}},"position":[700,2500]},"TB1_MIDI":{"logical":"MIDIABIG","columns":{"MIDI1_COD":{"logical":"ID DA MIDIA","physical":"MIDI1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"MIDI1_NOM":{"logical":"TITULO","physical":"MIDI1_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"MIDI1_DES":{"logical":"DESCRICAO","physical":"MIDI1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"MIDI1_GEOM":{"logical":"GEOMETRIA","physical":"MIDI1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"MIDI1_ARQ":{"logical":"ARQUIVO ORIGINAL","physical":"MIDI1_ARQ","type":"text","pk":false,"sequence":false,"nullable":false},"MIDI1_DAT_HR_INI":{"logical":"DATA","physical":"MIDI1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"MIDI1_DAT_REF":{"logical":"DATA DE REFERENCIA","physical":"MIDI1_DAT_REF","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"MIDI1_JSON":{"logical":"CONFIGURACAO","physical":"MIDI1_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"ANUS2_COD":{"logical":"CODIGO ANOTACAO","physical":"ANUS2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_ANUS","column":"ANUS2_COD"}},"MIDI1_ARQ_FIS":{"logical":"ARQUIVO FISICO","physical":"MIDI1_ARQ_FIS","type":"text","pk":false,"sequence":false,"nullable":false},"USER2_BLOQ":{"logical":"USUARIO BLOQUEADOR","physical":"USER2_BLOQ","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"BLOQUEOU A","inverse":"FOI BLOQUEADA POR"},"USER2_COD_DEL":{"logical":"USUARIO EXCLUIDOR","physical":"USER2_COD_DEL","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"EXCLUIU","inverse":"FOI EXCLUIDA POR"},"MIDI1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"MIDI1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,2500]},"TB1_COCT":{"logical":"COMENTARIO DO CONTRATO","columns":{"COCT1_COD":{"logical":"CODIGO","physical":"COCT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COCT1_TIT":{"logical":"TITULO","physical":"COCT1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"COCT1_TXT":{"logical":"TEXTO","physical":"COCT1_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"COCT1_DAT_HR_INI":{"logical":"DATA","physical":"COCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"COCT1_FORM":{"logical":"FORMATACAO","physical":"COCT1_FORM","type":"text","pk":false,"sequence":false,"nullable":true},"COCT1_ATI":{"logical":"COMENTARIO ATIVO","physical":"COCT1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"COCT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"COCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,2700]},"TB0_LOGS":{"logical":"LOG DE ALTERACOES","columns":{"LOGS0_COD":{"logical":"CODIGO DO LOG","physical":"LOGS0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LOGS0_TAB":{"logical":"TABELA","physical":"LOGS0_TAB","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"LOGS0_DESCR":{"logical":"DESCRICAO","physical":"LOGS0_DESCR","type":"text","pk":false,"sequence":false,"nullable":true},"LOGS0_DAT_HR":{"logical":"DATA","physical":"LOGS0_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"LOGS0_JSON":{"logical":"JSON DO VALOR ANTERIOR","physical":"LOGS0_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"PESS2_COD":{"logical":"PESSOA","physical":"PESS2_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"LOGS0_IP":{"logical":"ENDERECO IP","physical":"LOGS0_IP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":30},"LOGS0_COD_ALT":{"logical":"ID DO REGISTRO ALTERADO","physical":"LOGS0_COD_ALT","type":"text","pk":false,"sequence":false,"nullable":true},"LOGS0_CAMPO":{"logical":"CAMPO ALTERADO","physical":"LOGS0_CAMPO","type":"text","pk":false,"sequence":false,"nullable":true},"LOGS0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LOGS0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,2700]},"TB1_DEFU":{"logical":"DESMEMBRAMENTO FUTURO","columns":{"DEFU1_ORD":{"logical":"ORDEM","physical":"DEFU1_ORD","type":"integer","pk":true,"sequence":false,"nullable":false},"DEFU1_NOM":{"logical":"NOME DA PROPRIEDAE","physical":"DEFU1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"DEFU1_GEOM":{"logical":"GEOMETRIA","physical":"DEFU1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":134},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"DEFU1_AREA":{"logical":"AREA","physical":"DEFU1_AREA","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_COD_EXIST":{"logical":"ID DE PROPRIEDADE EXISTENTE","physical":"PROP1_COD_EXIST","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"},"phrase":"EXISTENTE EM","inverse":"NA EXISTENTE"},"DEFU1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"DEFU1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"DEFU1_DAT_HR_PREV":{"logical":"DATA PREVISTA EFETIVACAO","physical":"DEFU1_DAT_HR_PREV","type":"date","pk":false,"sequence":false,"nullable":true},"DEFU1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"DEFU1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,2700]},"TB1_PRDF":{"logical":"PROPRIETARIO DA DESMEMBRADA FUTURA","columns":{"DEFU1_ORD":{"logical":"ORDEM","physical":"DEFU1_ORD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_DEFU","column":"DEFU1_ORD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_DEFU","column":"PROP1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PRDF1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PRDF1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PRDF1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PRDF1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,2700]},"TB0_LDOM":{"logical":"LOG DOMINIO","columns":{"LDOM0_COD":{"logical":"CODIGO DO LOG","physical":"LDOM0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LDOM0_ANT":{"logical":"ANTERIOR","physical":"LDOM0_ANT","type":"integer","pk":false,"sequence":false,"nullable":true},"LDOM0_NOVO":{"logical":"NOVO","physical":"LDOM0_NOVO","type":"integer","pk":false,"sequence":false,"nullable":false},"LDOM0_DAT_HR":{"logical":"DATA DA MUDANCA","physical":"LDOM0_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"LDOM0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LDOM0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,2700]},"TB2_CMCF":{"logical":"COMPARTILHAMENTO DE CONFIGURACAO","columns":{"CMCF2_COD":{"logical":"CODIGO","physical":"CMCF2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CMCF2_NOM":{"logical":"NOME DA CONFIGURACAO","physical":"CMCF2_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"USER2_COD_DEST":{"logical":"CODIGO USUARIO DESTINATARIO","physical":"USER2_COD_DEST","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ENVIOU","inverse":"RECEBIDAS"},"CMCF2_CONF":{"logical":"CONFIGURACAO","physical":"CMCF2_CONF","type":"text","pk":false,"sequence":false,"nullable":false},"CMCF2_DAT_HR_INI":{"logical":"DATA DO COMPARTILHAMENTO","physical":"CMCF2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CMCF2_OCULTA":{"logical":"OCULTA","physical":"CMCF2_OCULTA","type":"bit","pk":false,"sequence":false,"nullable":true},"CMCF2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CMCF2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,2900]},"TB3_SUCA":{"logical":"SUBCAMADA","columns":{"SUCA3_COD":{"logical":"ID DA SUBCAMADA","physical":"SUCA3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SUCA3_NOM":{"logical":"NOME DA SUBCAMADA","physical":"SUCA3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"SUCA3_GEOM_LIM":{"logical":"GEOMETRIA DO LIMITE","physical":"SUCA3_GEOM_LIM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"SUCA3_ORD":{"logical":"ORDEM NA CAMADA","physical":"SUCA3_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"SUCA3_ATI":{"logical":"SUBCAMADA ATIVA","physical":"SUCA3_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"SUCA3_DES":{"logical":"DESCRICAO","physical":"SUCA3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"SUCA3_JSON":{"logical":"DEFINICAO JSON DA SUBCAMADA","physical":"SUCA3_JSON","type":"text","pk":false,"sequence":false,"nullable":false},"SUCA3_URL":{"logical":"URL FONTE","physical":"SUCA3_URL","type":"text","pk":false,"sequence":false,"nullable":true},"CAMA3_COD":{"logical":"ID DA CAMADA","physical":"CAMA3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CAMA","column":"CAMA3_COD"}},"SUCA3_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"SUCA3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"SUCA3_DAT_HR_MOD":{"logical":"DATA DE MODIFICACAO","physical":"SUCA3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"SUCA3_DAT_REF":{"logical":"DATA DE REFERENCIA","physical":"SUCA3_DAT_REF","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,2900]},"TB0_LGEO":{"logical":"LOG GEO","columns":{"LGEO0_COD":{"logical":"CODIGO DO LOG","physical":"LGEO0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"LGEO0_ANT":{"logical":"ANTERIOR","physical":"LGEO0_ANT","type":"integer","pk":false,"sequence":false,"nullable":true},"LGEO0_NOVO":{"logical":"NOVO","physical":"LGEO0_NOVO","type":"integer","pk":false,"sequence":false,"nullable":false},"LGEO0_DAT_HR":{"logical":"DATA DA MUDANCA","physical":"LGEO0_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"LGEO0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LGEO0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,2900]},"TB1_GRPR":{"logical":"GRUPO DE PROPRIETARIOS","columns":{"GRPR1_COD":{"logical":"CODIGO DO GRUPO","physical":"GRPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"GRPR1_NOM":{"logical":"NOME DO GRUPO","physical":"GRPR1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"GRPR1_DES":{"logical":"DESCRICAO","physical":"GRPR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"GRPR1_CVER":{"logical":"INDICADOR CASA DOS VENTOS","physical":"GRPR1_CVER","type":"bit","pk":false,"sequence":false,"nullable":true},"GRPR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GRPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"GRPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"GRPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,2900]},"TB1_PRGR":{"logical":"PROPRIETARIO DO GRUPO","columns":{"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"GRPR1_COD":{"logical":"CODIGO DO GRUPO","physical":"GRPR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_GRPR","column":"GRPR1_COD"}},"PRGR1_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"PRGR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PRGR1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PRGR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,2900]},"TB1_TOCO":{"logical":"TORRE CONCORRENCIA","columns":{"TOCO1_COD":{"logical":"CODIGO DA TORRE","physical":"TOCO1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TOCO1_NOM":{"logical":"NOME DA TORRE","physical":"TOCO1_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"TOCO1_DES":{"logical":"DESCRICAO","physical":"TOCO1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TOCO1_GEOM":{"logical":"GEOMETRIA","physical":"TOCO1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":14},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"INCLUIU A","inverse":"INCLUIDA POR"},"TOCO1_DAT_HR_INI":{"logical":"DATA DA INCLUSAO","physical":"TOCO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"TOCO1_DAT_HR_FIM":{"logical":"DATA DE REMOCAO","physical":"TOCO1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD_REM":{"logical":"CODIGO USUARIO REMOVEU","physical":"USER2_COD_REM","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"REMOVEU A","inverse":"REMOVIDA POR"},"TOCO1_CONC":{"logical":"CONCORRENTE","physical":"TOCO1_CONC","type":"text","pk":false,"sequence":false,"nullable":true},"TOCO1_ALT":{"logical":"ALTURA","physical":"TOCO1_ALT","type":"double precision","pk":false,"sequence":false,"nullable":true},"TOCO1_TIPO":{"logical":"TIPO","physical":"TOCO1_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":30},"TOCO1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TOCO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,3100]},"TB1_STPJ":{"logical":"STATUS PROJETO","columns":{"STPJ1_COD":{"logical":"CODIGO DO STATUS","physical":"STPJ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STPJ1_NOM":{"logical":"NOME","physical":"STPJ1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STPJ1_DES":{"logical":"DESCRICAO","physical":"STPJ1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STPJ1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STPJ1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"STPJ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STPJ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"STPJ1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STPJ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,3100]},"TB1_STCP":{"logical":"STATUS COMPLEXO","columns":{"STCP1_COD":{"logical":"CODIGO DO STATUS","physical":"STCP1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STCP1_NOM":{"logical":"NOME","physical":"STCP1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STCP1_DES":{"logical":"DESCRICAO","physical":"STCP1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STCP1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STCP1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"STCP1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STCP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"STCP1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STCP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,3100]},"TB1_RFPR":{"logical":"REGULARIZACAO FUNDIARIA PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"RFPR1_TIPO":{"logical":"TIPO DE REGULARIZACAO","physical":"RFPR1_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"RFPR1_TI":{"logical":"TERMO DE INVENTARIANTE","physical":"RFPR1_TI","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_CCIR":{"logical":"SITUACAO CCIR","physical":"RFPR1_CCIR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_ITR":{"logical":"SITUACAO ITR","physical":"RFPR1_ITR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_GEO":{"logical":"SITUACAO GEO","physical":"RFPR1_GEO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_MATR":{"logical":"SITUACAO MATRICULA","physical":"RFPR1_MATR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_CAR":{"logical":"SITUACAO CAR","physical":"RFPR1_CAR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_CONTR":{"logical":"CONTRATO","physical":"RFPR1_CONTR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_CIT":{"logical":"CERTIDAO INTEIRO TEOR","physical":"RFPR1_CIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_CLIE":{"logical":"APTO CLIENTE","physical":"RFPR1_CLIE","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"RFPR1_DAT_CONQ":{"logical":"PRAZO CONQUISTA","physical":"RFPR1_DAT_CONQ","type":"date","pk":false,"sequence":false,"nullable":true},"RFPR1_DAT_REGU":{"logical":"PRAZO REGULARIZACAO","physical":"RFPR1_DAT_REGU","type":"date","pk":false,"sequence":false,"nullable":true},"RFPR1_DAT_APTO":{"logical":"PRAZO APTO CLIENTE","physical":"RFPR1_DAT_APTO","type":"date","pk":false,"sequence":false,"nullable":true},"RFPR1_CACHE":{"logical":"CACHE REGULARIZACAO","physical":"RFPR1_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"RFPR1_DAT_CIT":{"logical":"DATA CIT","physical":"RFPR1_DAT_CIT","type":"date","pk":false,"sequence":false,"nullable":true},"RFPR1_RANK":{"logical":"RANKING","physical":"RFPR1_RANK","type":"integer","pk":false,"sequence":false,"nullable":true},"RFPR1_DAT_HR_INI":{"logical":"DATA DE INICIO","physical":"RFPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"RFPR1_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"RFPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,3100]},"TB1_CTRF":{"logical":"CONTRATO REGULARIZACAO FUNDIARIA","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_RFPR","column":"PROP1_COD"}},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"CTRF1_SITU":{"logical":"SITUACAO CONTRATO","physical":"CTRF1_SITU","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"CTRF1_ADT":{"logical":"ADITIVO","physical":"CTRF1_ADT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"CTRF1_CESS":{"logical":"CESSAO DE CONTRATO","physical":"CTRF1_CESS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":8},"CTRF1_COD_TXT":{"logical":"DENOMINACAO","physical":"CTRF1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"CTRF1_COD_TXT_SUBS":{"logical":"SUBSTITUTO","physical":"CTRF1_COD_TXT_SUBS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"CTRF1_COD":{"logical":"CODIGO","physical":"CTRF1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COMPL1_COD":{"logical":"CODIGO DO COMPLEXO","physical":"COMPL1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"}},"CTRF1_CACHE":{"logical":"CACHE REGULARIZACAO","physical":"CTRF1_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"CTRF1_RANK":{"logical":"RANK","physical":"CTRF1_RANK","type":"integer","pk":false,"sequence":false,"nullable":true},"CTRF1_PESO":{"logical":"PESO","physical":"CTRF1_PESO","type":"integer","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CTRF1_DAT_HR_INI":{"logical":"DATA DE INICIO","physical":"CTRF1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CTRF1_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"CTRF1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,3100]},"TB1_SIGS":{"logical":"SIGEF SOBREPOSTA","columns":{"SIGS1_COD_IMO":{"logical":"CODIGO IMOVEL SIGEF","physical":"SIGS1_COD_IMO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":13},"SIGS1_COD_PARC":{"logical":"CODIGO PARCELA","physical":"SIGS1_COD_PARC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":64},"SIGS1_SOBREP_PROP":{"logical":"SOBREPOSICAO NA PROP","physical":"SIGS1_SOBREP_PROP","type":"double precision","pk":false,"sequence":false,"nullable":true},"SIGS1_SOBREP_SIGEF":{"logical":"SOBREPOSICAO NA SIGEF","physical":"SIGS1_SOBREP_SIGEF","type":"double precision","pk":false,"sequence":false,"nullable":true},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"SIGS1_COD":{"logical":"CODIGO SOBREP","physical":"SIGS1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SIGS1_DAT_HR_INI":{"logical":"DATA CALCULO","physical":"SIGS1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SIGS1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SIGS1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,3300]},"TB1_SNCS":{"logical":"SNCI SOBREPOSTA","columns":{"SNCS1_COD_IMO":{"logical":"CODIGO IMOVEL SNCI","physical":"SNCS1_COD_IMO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":254},"SNCS1_SOBREP_PROP":{"logical":"SOBREPOSICAO NA PROP","physical":"SNCS1_SOBREP_PROP","type":"double precision","pk":false,"sequence":false,"nullable":true},"SNCS1_SOBREP_SNCI":{"logical":"SOBREPOSICAO NA SIGEF","physical":"SNCS1_SOBREP_SNCI","type":"double precision","pk":false,"sequence":false,"nullable":true},"SNCS1_COD":{"logical":"CODIGO SOBREP","physical":"SNCS1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"SNCS1_DAT_HR_INI":{"logical":"DATA CALCULO","physical":"SNCS1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SNCS1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SNCS1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,3300]},"TB1_CARS":{"logical":"CAR SOBREPOSTA","columns":{"CARS1_COD_IMO":{"logical":"CODIGO IMOVEL CAR","physical":"CARS1_COD_IMO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"CARS1_SOBREP_PROP":{"logical":"SOBREPOSICAO NA PROP","physical":"CARS1_SOBREP_PROP","type":"double precision","pk":false,"sequence":false,"nullable":true},"CARS1_SOBREP_CAR":{"logical":"SOBREPOSICAO NA CAR","physical":"CARS1_SOBREP_CAR","type":"double precision","pk":false,"sequence":false,"nullable":true},"CARS1_COD":{"logical":"CODIGO SOBREP","physical":"CARS1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CARS1_DAT_HR_INI":{"logical":"DATA CALCULO","physical":"CARS1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"CARS1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CARS1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,3300]},"TB2_REPO":{"logical":"REPORT","columns":{"REPO2_COD":{"logical":"CODIGO REPORT","physical":"REPO2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"REPO2_NOM":{"logical":"TITULO","physical":"REPO2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"REPO2_DES":{"logical":"DESCRICAO","physical":"REPO2_DES","type":"text","pk":false,"sequence":false,"nullable":true},"REPO2_DAT_HR":{"logical":"DATA E HORA","physical":"REPO2_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"REPO2_TIPO":{"logical":"TIPO","physical":"REPO2_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"REPO2_CONF":{"logical":"CONFIGURACAO USUARIO","physical":"REPO2_CONF","type":"text","pk":false,"sequence":false,"nullable":true},"REPO2_JSON":{"logical":"DADOS ADICIONAIS","physical":"REPO2_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"REPO2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"REPO2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,3300]},"TB2_NOTI":{"logical":"NOTIFICACAO","columns":{"NOTI2_COD":{"logical":"CODIGO NOTIFICACAO","physical":"NOTI2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"NOTI2_NOM":{"logical":"TITULO","physical":"NOTI2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"NOTI2_TXT":{"logical":"TEXTO","physical":"NOTI2_TXT","type":"text","pk":false,"sequence":false,"nullable":true},"NOTI2_DAT_HR_INI":{"logical":"DATA E HORA","physical":"NOTI2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"NOTI2_LIDA":{"logical":"LIDA","physical":"NOTI2_LIDA","type":"bit","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"USER2_COD_DEST":{"logical":"CODIGO DESTINATARIO","physical":"USER2_COD_DEST","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"DESTINATARIO","inverse":"PARA"},"NOTI2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"NOTI2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,3300]},"TB2_NOLI":{"logical":"NOTIFICACAO LIDA","columns":{"NOTI2_COD":{"logical":"CODIGO NOTIFICACAO","physical":"NOTI2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_NOTI","column":"NOTI2_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"NOLI2_DAT_HR":{"logical":"DATA LEITURA","physical":"NOLI2_DAT_HR","type":"date","pk":false,"sequence":false,"nullable":false},"NOLI2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"NOLI2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,3500]},"TB1_ARQU":{"logical":"ARQUIVO BIG","columns":{"ARQU1_COD":{"logical":"CODIGO ARQUIVO","physical":"ARQU1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ARQU1_TIT":{"logical":"TITULO","physical":"ARQU1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"ARQU1_NOM":{"logical":"NOME","physical":"ARQU1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"ARQU1_DAT_UPLOAD":{"logical":"DATA UPLOAD","physical":"ARQU1_DAT_UPLOAD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU1_DAT_MOD":{"logical":"DATA MODIFICACAO","physical":"ARQU1_DAT_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU1_DES":{"logical":"DESCRICAO","physical":"ARQU1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU1_ID":{"logical":"ID GDRIVE","physical":"ARQU1_ID","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"ARQU1_ST_NOM":{"logical":"STATUS NOMENCLATURA","physical":"ARQU1_ST_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU1_ST_RF":{"logical":"STATUS REGFUND","physical":"ARQU1_ST_RF","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU1_DAT_ULT_SINC":{"logical":"DATA ULTIMA SINCRONIA","physical":"ARQU1_DAT_ULT_SINC","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"ARQU1_GD_CACHE":{"logical":"CACHE GDRIVE","physical":"ARQU1_GD_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU1_DEL":{"logical":"EXCLUIDO","physical":"ARQU1_DEL","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD_VERIF":{"logical":"CODIGO USUARIO VERIFICADOR","physical":"USER2_COD_VERIF","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"VERIFICOU","inverse":"VERIFICADO POR"},"ARQU1_DAT_VER":{"logical":"DATA VERIFICACAO","physical":"ARQU1_DAT_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU1_DAT_VAL_VER":{"logical":"DATA VALIDADE VERIFICACAO","physical":"ARQU1_DAT_VAL_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU1_DAT_RECU":{"logical":"DATA RECUSA","physical":"ARQU1_DAT_RECU","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU1_MOT_RECU":{"logical":"MOTIVO RECUSA","physical":"ARQU1_MOT_RECU","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD_RECU":{"logical":"CODIGO USUARIO RECUSA","physical":"USER2_COD_RECU","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"RECUSOU","inverse":"RECUSADO POR"},"ARQU1_ST_VERIF":{"logical":"STATUS VERIFICACAO","physical":"ARQU1_ST_VERIF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PAAR1_COD":{"logical":"CODIGO DO PADRAO","physical":"PAAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PAAR","column":"PAAR1_COD"}},"ARQU1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ARQU1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO REGISTRO","physical":"ARQU1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"STVARQ1_COD":{"logical":"CODIGO STATUS VERIFICACAO","physical":"STVARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STVARQ","column":"STVARQ1_COD"}},"ARQU1_ST_VERIFGEO":{"logical":"STATUS VERIFICACAO GEO","physical":"ARQU1_ST_VERIFGEO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"ARQU1_DAT_VERGEO":{"logical":"DATA VERIFICACAO GEO","physical":"ARQU1_DAT_VERGEO","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU1_MOT_RECUGEO":{"logical":"MOTIVO RECUSA GEO","physical":"ARQU1_MOT_RECUGEO","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD_VERIFGEO":{"logical":"CODIGO USUARIO VERIFICADOR GEO","physical":"USER2_COD_VERIFGEO","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"VERIFICOU GEO","inverse":"VERIFICADO GEO POR"}},"position":[300,3500]},"TB1_PAAR":{"logical":"PADRAO DE ARQUIVO","columns":{"PAAR1_COD":{"logical":"CODIGO DO PADRAO","physical":"PAAR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PAAR1_TIT":{"logical":"TITULO","physical":"PAAR1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"PAAR1_DES":{"logical":"DESCRICAO","physical":"PAAR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"PAAR1_TXT":{"logical":"TEXTO DO PADRAO","physical":"PAAR1_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"PAAR1_CLAS":{"logical":"CLASSE DO PADRAO","physical":"PAAR1_CLAS","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"PAAR1_INSTR":{"logical":"INSTRUCOES","physical":"PAAR1_INSTR","type":"text","pk":false,"sequence":false,"nullable":true},"PAAR1_OBRIG":{"logical":"OBRIGATORIO","physical":"PAAR1_OBRIG","type":"bit","pk":false,"sequence":false,"nullable":false},"PAAR1_TAB":{"logical":"TABELA DADO VINCULADO","physical":"PAAR1_TAB","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"PAAR1_CAMPO":{"logical":"CAMPO DADO VINCULADO","physical":"PAAR1_CAMPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PAAR1_DAT_VAL":{"logical":"OBRIGATORIO DATA VALIDADE","physical":"PAAR1_DAT_VAL","type":"bit","pk":false,"sequence":false,"nullable":true},"PAAR1_REF_DATVAL":{"logical":"PADRAO DATA VALIDADE","physical":"PAAR1_REF_DATVAL","type":"text","pk":false,"sequence":false,"nullable":true},"PAAR1_JSON":{"logical":"PARAMETROS JSON","physical":"PAAR1_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"PAAR1_PJ":{"logical":"INDICACOR PESSOA JURIDICA","physical":"PAAR1_PJ","type":"bit","pk":false,"sequence":false,"nullable":true},"PAAR1_INSTR_DATA":{"logical":"INSTRUCAO DATA AUTOMATICA","physical":"PAAR1_INSTR_DATA","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PAAR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PAAR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PAAR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PAAR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PAAR1_ABR":{"logical":"ABREVIATURA","physical":"PAAR1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20}},"position":[500,3500]},"TB1_RFCM":{"logical":"REGULARIZACAO FUNDIARIA COMPLEXO","columns":{"COMPL1_COD":{"logical":"CODIGO DO COMPLEXO","physical":"COMPL1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"}},"RFCM1_DAT_HR_INI":{"logical":"DATA DE ABERTURA","physical":"RFCM1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"RFCM1_DAT_CLIE":{"logical":"PRAZO CLIENTE","physical":"RFCM1_DAT_CLIE","type":"date","pk":false,"sequence":false,"nullable":true},"RFCM1_DAT_REG":{"logical":"PRAZO REGULARIZACAO","physical":"RFCM1_DAT_REG","type":"date","pk":false,"sequence":false,"nullable":true},"RFCM1_DAT_CONQ":{"logical":"PRAZO CONQUISTA","physical":"RFCM1_DAT_CONQ","type":"date","pk":false,"sequence":false,"nullable":true},"RFCM1_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"RFCM1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"RFCM1_DAT_CONQ_CONCL":{"logical":"DATA CONQUISTA","physical":"RFCM1_DAT_CONQ_CONCL","type":"date","pk":false,"sequence":false,"nullable":true},"RFCM1_DAT_REG_CONCL":{"logical":"DATA REGULARIZACAO","physical":"RFCM1_DAT_REG_CONCL","type":"date","pk":false,"sequence":false,"nullable":true},"RFCM1_DAT_CLIE_CONCL":{"logical":"DATA CLIENTE","physical":"RFCM1_DAT_CLIE_CONCL","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,3500]},"TB0_LGAR":{"logical":"LOG ARQUIVO","columns":{"LGAR0_COD":{"logical":"CODIGO LOG","physical":"LGAR0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LGAR0_DAT_HR_INI":{"logical":"DATA","physical":"LGAR0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LGAR0_CAMPO":{"logical":"CAMPO ALTERADO","physical":"LGAR0_CAMPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"LGAR0_ORIG":{"logical":"VALOR ORIGINAL","physical":"LGAR0_ORIG","type":"text","pk":false,"sequence":false,"nullable":true},"LGAR0_DES":{"logical":"DESCRICAO","physical":"LGAR0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"LGAR0_DADO":{"logical":"DADOS ADICIONAIS","physical":"LGAR0_DADO","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU1_COD":{"logical":"CODIGO ARQUIVO","physical":"ARQU1_COD","type":"integer","pk":false,"sequence":false,"nullable":false},"LGAR0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LGAR0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,3500]},"TB1_TIPR":{"logical":"TIPO DE PROJETO","columns":{"TIPR1_COD":{"logical":"CODIGO TIPO","physical":"TIPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TIPR1_NOM":{"logical":"NOME","physical":"TIPR1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TIPR1_DES":{"logical":"DESCRICAO","physical":"TIPR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TIPR1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TIPR1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"TIPR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TIPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"TIPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TIPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,3700]},"TB1_COPJ":{"logical":"COMENTARIO DO PROJETO","columns":{"COPJ1_COD":{"logical":"CODIGO","physical":"COPJ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COPJ1_TIT":{"logical":"TITULO","physical":"COPJ1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"COPJ1_TXT":{"logical":"TEXTO","physical":"COPJ1_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"COPJ1_DAT_HR_INI":{"logical":"DATA","physical":"COPJ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"COPJ1_FORM":{"logical":"FORMATACAO","physical":"COPJ1_FORM","type":"text","pk":false,"sequence":false,"nullable":true},"COPJ1_ATI":{"logical":"COMENTARIO ATIVO","physical":"COPJ1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"COPJ1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"COPJ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,3700]},"TB1_COPP":{"logical":"COMENTARIO DO PROPRIETARIO","columns":{"COPP1_COD":{"logical":"CODIGO","physical":"COPP1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COPP1_TIT":{"logical":"TITULO","physical":"COPP1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"COPP1_TXT":{"logical":"TEXTO","physical":"COPP1_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"COPP1_FORM":{"logical":"FORMATACAO","physical":"COPP1_FORM","type":"text","pk":false,"sequence":false,"nullable":true},"COPP1_DAT_HR_INI":{"logical":"DATA","physical":"COPP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"COPP1_ATI":{"logical":"COMENTARIO ATIVO","physical":"COPP1_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"COPP1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"COPP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,3700]},"TB1_CSCH":{"logical":"CESSIONARIO DO CONTRATO HISTORICO","columns":{"CSCH1_PERC":{"logical":"PERCENTUAL","physical":"CSCH1_PERC","type":"double precision","pk":false,"sequence":false,"nullable":true},"CSCH1_DAT_INI_CES":{"logical":"DATA INICIO CESSAO","physical":"CSCH1_DAT_INI_CES","type":"date","pk":false,"sequence":false,"nullable":true},"CSCH1_COD_ERP":{"logical":"CHAVE DO CESSIONARIO NO ERP","physical":"CSCH1_COD_ERP","type":"text","pk":false,"sequence":false,"nullable":false},"CSCH1_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"CSCH1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"CSCH1_COD":{"logical":"CODIGO","physical":"CSCH1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CSCH1_NUM_SAP":{"logical":"NUMERO SAP DO CONTRATO","physical":"CSCH1_NUM_SAP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"CSCH1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"CSCH1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"CCSCH1_NOM_ORIG":{"logical":"NOME ORIGINAL DO CESSIONARIO","physical":"CCSCH1_NOM_ORIG","type":"text","pk":false,"sequence":false,"nullable":true},"CSCH1_COD_ERP_DIV":{"logical":"DIVISAO OU FILIAL","physical":"CSCH1_COD_ERP_DIV","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100}},"position":[700,3700]},"TB2_TERM":{"logical":"TERMO","columns":{"TERM2_COD":{"logical":"CODIGO","physical":"TERM2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TERM2_NOM":{"logical":"TITULO","physical":"TERM2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"TERM2_DES":{"logical":"DESCRICAO","physical":"TERM2_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TERM2_TXT":{"logical":"TEXTO","physical":"TERM2_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"TERM2_ATI":{"logical":"ATIVO","physical":"TERM2_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"TERM2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TERM2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TERM2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TERM2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,3700]},"TB2_ACTM":{"logical":"ACEITE DE TERMO","columns":{"TERM2_COD":{"logical":"CODIGO","physical":"TERM2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_TERM","column":"TERM2_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"ACTM2_DAT_HR_INI":{"logical":"DATA DO ACEITE","physical":"ACTM2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ACTM2_TXT":{"logical":"TEXTO DO TERMO ACEITO","physical":"ACTM2_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"ACTM2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ACTM2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,3900]},"TB1_CRFS":{"logical":"CONTRATO REGFUND SUBSTITUIDO","columns":{"CTRF1_COD":{"logical":"CODIGO","physical":"CTRF1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRF","column":"CTRF1_COD"},"phrase":"SUBSTITUI OUTRO EM","inverse":"TEM COMO SUBSTITUTO O"},"CTRF1_COD_SUBS":{"logical":"CODIGO SUBSTITUIDO","physical":"CTRF1_COD_SUBS","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRF","column":"CTRF1_COD"},"phrase":"EH SUBSTITUIDO NO","inverse":"TEM COMO SUBSTITUIDO O"},"CRFS1_DAT_HR_INI":{"logical":"DATA DA INCLUSAO","physical":"CRFS1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CRFS1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CRFS1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,3900]},"TB2_BANC":{"logical":"BANCO","columns":{"BANC2_COD":{"logical":"CODIGO","physical":"BANC2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"BANC2_NOM":{"logical":"NOME","physical":"BANC2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"BANC2_COD_OFICIAL":{"logical":"CODIGO BACEN","physical":"BANC2_COD_OFICIAL","type":"integer","pk":false,"sequence":false,"nullable":true},"BANC2_DAT_HR_INI":{"logical":"DATA DE INCLUSAO","physical":"BANC2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"BANC2_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"BANC2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,3900]},"TB0_LGVL":{"logical":"LOGAVEL","columns":{"LGVL0_COD":{"logical":"CODIGO","physical":"LGVL0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LGVL0_TAB":{"logical":"TABELA","physical":"LGVL0_TAB","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"LGVL0_CAMPOS":{"logical":"CAMPOS","physical":"LGVL0_CAMPOS","type":"text","pk":false,"sequence":false,"nullable":true},"LGVL0_PARAMS":{"logical":"PARAMETROS","physical":"LGVL0_PARAMS","type":"text","pk":false,"sequence":false,"nullable":true},"LGVL0_DAT_HR_INI":{"logical":"DATA DE CADASTRAMENTO","physical":"LGVL0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"LGVL0_DAT_HR_MOD":{"logical":"DATA DE MODIFICACAO","physical":"LGVL0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,3900]},"TB3_CJAN":{"logical":"CONJUNTO DE ANOTACOES","columns":{"CJAN3_COD":{"logical":"CODIGO CONJUNTO","physical":"CJAN3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CJAN3_NOM":{"logical":"NOME","physical":"CJAN3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"CJAN3_DES":{"logical":"DESCRICAO","physical":"CJAN3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"CJAN3_PUB":{"logical":"PUBLICO","physical":"CJAN3_PUB","type":"bit","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CJAN3_BB":{"logical":"BOUNDING BOX","physical":"CJAN3_BB","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"CJAN3_MIN_ZOOM":{"logical":"ZOOM MINIMO","physical":"CJAN3_MIN_ZOOM","type":"integer","pk":false,"sequence":false,"nullable":true},"CJAN3_MAX_ZOOM":{"logical":"ZOOM MAXIMO","physical":"CJAN3_MAX_ZOOM","type":"integer","pk":false,"sequence":false,"nullable":true},"CJAN3_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"CJAN3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CJAN3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CJAN3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"CJAN3_ATI":{"logical":"ATIVO","physical":"CJAN3_ATI","type":"bit","pk":false,"sequence":false,"nullable":false}},"position":[900,3900]},"TB3_ANOT":{"logical":"ANOTACAO","columns":{"ANOT3_COD":{"logical":"CODIGO ANOTACAO","physical":"ANOT3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ANOT3_NOM":{"logical":"TITULO","physical":"ANOT3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"ANOT3_TXT":{"logical":"TEXTO","physical":"ANOT3_TXT","type":"text","pk":false,"sequence":false,"nullable":true},"ANOT3_GEOM":{"logical":"GEOMETRIA","physical":"ANOT3_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"CJAN3_COD":{"logical":"CODIGO CONJUNTO","physical":"CJAN3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CJAN","column":"CJAN3_COD"}},"ANOT3_JSON":{"logical":"CONFIGURACAO","physical":"ANOT3_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"ANOT3_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"ANOT3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ANOT3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ANOT3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ANOT3_ATI":{"logical":"ATIVA","physical":"ANOT3_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"ANOT3_GEOJSON":{"logical":"GEOJSON DA GEOMETRIA","physical":"ANOT3_GEOJSON","type":"text","pk":false,"sequence":false,"nullable":true}},"position":[100,4100]},"TB3_HANO":{"logical":"HISTORICO ANOTACAO","columns":{"HANO3_COD":{"logical":"CODIGO HISTORICO","physical":"HANO3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ANOT3_COD":{"logical":"CODIGO ANOTACAO","physical":"ANOT3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_ANOT","column":"ANOT3_COD"}},"HANO3_NOM":{"logical":"TITULO","physical":"HANO3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"HANO3_TXT":{"logical":"TEXTO","physical":"HANO3_TXT","type":"text","pk":false,"sequence":false,"nullable":true},"HANO3_GEOM":{"logical":"GEOMETRIA","physical":"HANO3_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"HANO3_JSON":{"logical":"CONFIGURACAO JSON","physical":"HANO3_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"HANO3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HANO3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HANO3_GEOJSON":{"logical":"GEOJSON DA GEOMETRIA","physical":"HANO3_GEOJSON","type":"text","pk":false,"sequence":false,"nullable":true},"HANO3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HANO3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,4100]},"TB2_EQUI":{"logical":"EQUIPE BIG","columns":{"EQUI2_COD":{"logical":"CODIGO EQUIPE","physical":"EQUI2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"EQUI2_NOM":{"logical":"NOME","physical":"EQUI2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"EQUI2_DES":{"logical":"DESCRICAO","physical":"EQUI2_DES","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"EQUI2_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"EQUI2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"EQUI2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"EQUI2_DAT_HR_MOD","type":"integer","pk":false,"sequence":false,"nullable":true}},"position":[500,4100]},"TB2_USEQ":{"logical":"USUARIO DA EQUIPE","columns":{"EQUI2_COD":{"logical":"CODIGO EQUIPE","physical":"EQUI2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_EQUI","column":"EQUI2_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"USEQ2_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"USEQ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD_ATRIB":{"logical":"CODIGO USUARIO ATRIBUIDOR","physical":"USER2_COD_ATRIB","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ATRIBUIU O","inverse":"ATRIBUIDO POR"},"USEQ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"USEQ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,4100]},"TB3_CACA":{"logical":"CAMADA CONJUNTO ANOTACOES","columns":{"CJAN3_COD":{"logical":"CODIGO CONJUNTO","physical":"CJAN3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_CJAN","column":"CJAN3_COD"}},"CAMA3_COD":{"logical":"ID DA CAMADA","physical":"CAMA3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_CAMA","column":"CAMA3_COD"}},"CACA3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CACA3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CACA3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CACA3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,4100]},"TB3_CCUS":{"logical":"COMPARTILHAMENTO CONJUNTO USUARIO","columns":{"CJAN3_COD":{"logical":"CODIGO CONJUNTO","physical":"CJAN3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_CJAN","column":"CJAN3_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CCUS3_EDIT":{"logical":"PODE EDITAR","physical":"CCUS3_EDIT","type":"bit","pk":false,"sequence":false,"nullable":true},"CCUS3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CCUS3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CCUS3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CCUS3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,4300]},"TB3_CCEQ":{"logical":"COMPARTILHAMENTO CONJUNTO EQUIPE","columns":{"CJAN3_COD":{"logical":"CODIGO CONJUNTO","physical":"CJAN3_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB3_CJAN","column":"CJAN3_COD"}},"EQUI2_COD":{"logical":"CODIGO EQUIPE","physical":"EQUI2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_EQUI","column":"EQUI2_COD"}},"CCEQ3_EDIT":{"logical":"PODE EDITAR","physical":"CCEQ3_EDIT","type":"bit","pk":false,"sequence":false,"nullable":true},"CCEQ3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CCEQ3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CCEQ3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CCEQ3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,4300]},"TB0_PACO":{"logical":"PARAMETRO CONFIGURACAO","columns":{"PACO0_COD":{"logical":"CODIGO","physical":"PACO0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PACO0_NOM":{"logical":"NOME","physical":"PACO0_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"PACO0_DES":{"logical":"DESCRICAO","physical":"PACO0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"PACO0_VAR":{"logical":"VARIAVEL","physical":"PACO0_VAR","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"PACO0_VAL":{"logical":"VALOR","physical":"PACO0_VAL","type":"text","pk":false,"sequence":false,"nullable":true},"PACO0_TIPO":{"logical":"TIPO DE DADO","physical":"PACO0_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":30},"PACO0_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PACO0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PACO0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PACO0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,4300]},"TB2_ENDE":{"logical":"ENDERECO","columns":{"ENDE2_COD":{"logical":"CODIGO DO ENDERECO","physical":"ENDE2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ENDE2_TIPO":{"logical":"TIPO","physical":"ENDE2_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"ENDE2_TIT":{"logical":"TITULO","physical":"ENDE2_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"ENDE2_LOGR":{"logical":"NOME LOGRADOURO","physical":"ENDE2_LOGR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"ENDE2_NUM":{"logical":"NUMERO","physical":"ENDE2_NUM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"ENDE2_COMPL":{"logical":"COMPLEMENTO","physical":"ENDE2_COMPL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"ENDE2_DES":{"logical":"DESCRICAO","physical":"ENDE2_DES","type":"text","pk":false,"sequence":false,"nullable":true},"ENDE2_CEP":{"logical":"CEP","physical":"ENDE2_CEP","type":"integer","pk":false,"sequence":false,"nullable":true},"ENDE2_BAI":{"logical":"BAIRRO","physical":"ENDE2_BAI","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"ENDE2_LAT":{"logical":"LATITUDE","physical":"ENDE2_LAT","type":"double precision","pk":false,"sequence":false,"nullable":true},"ENDE2_LNG":{"logical":"LONGITUDE","physical":"ENDE2_LNG","type":"double precision","pk":false,"sequence":false,"nullable":true},"ENDE2_GEOM":{"logical":"GEOMETRIA","physical":"ENDE2_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":12},"ENDE2_COD_LOTE":{"logical":"CODIGO DO LOTE","physical":"ENDE2_COD_LOTE","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"ENDE2_COD_LOGR":{"logical":"CODIGO DA RUA","physical":"ENDE2_COD_LOGR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"ENDE2_CIDA":{"logical":"CIDADE","physical":"ENDE2_CIDA","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"ENDE2_UF":{"logical":"ESTADO","physical":"ENDE2_UF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"ENDE2_DIST_IBGE":{"logical":"CODIGO DO DISTRITO","physical":"ENDE2_DIST_IBGE","type":"bigint","pk":false,"sequence":false,"nullable":true},"ENDE2_SUBD_IBGE":{"logical":"CODIGO DO SUBDISTRITO","physical":"ENDE2_SUBD_IBGE","type":"bigint","pk":false,"sequence":false,"nullable":true},"ENDE2_SECE_IBGE":{"logical":"CODIGO DO SETOR","physical":"ENDE2_SECE_IBGE","type":"bigint","pk":false,"sequence":false,"nullable":true},"ENDE2_TIPO_SECE":{"logical":"TIPO DE SETOR","physical":"ENDE2_TIPO_SECE","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":1},"ENDE2_ORIG_LOC":{"logical":"ORIGEM DA LOCALIZACAO","physical":"ENDE2_ORIG_LOC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":18},"MUNI1_COD":{"logical":"CODIGO IBGE MUNICIPIO","physical":"MUNI1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_MUNI","column":"MUNI1_COD"}},"ENDE2_PREC":{"logical":"PRECISAO","physical":"ENDE2_PREC","type":"integer","pk":false,"sequence":false,"nullable":true},"ENDE2_IDR":{"logical":"ID REMOTO","physical":"ENDE2_IDR","type":"integer","pk":false,"sequence":false,"nullable":true},"ENDE2_STRING":{"logical":"STRING VERIFICACAO","physical":"ENDE2_STRING","type":"text","pk":false,"sequence":false,"nullable":true},"ENDE2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ENDE2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"ENDE2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ENDE2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,4300]},"TB2_USER":{"logical":"USUARIO","columns":{"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"USER2_DAT_HR_INI":{"logical":"DATA E HORA DA CRIACAO","physical":"USER2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PESS2_IMG":{"logical":"IMAGEM DA PESSOA","physical":"PESS2_IMG","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":255},"CARG2_COD":{"logical":"CODIGO CARGO","physical":"CARG2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_CARG","column":"CARG2_COD"}},"USER2_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"USER2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD_CMS":{"logical":"CODIGO DO USUARIO NO CMS","physical":"USER2_COD_CMS","type":"integer","pk":false,"sequence":false,"nullable":false},"USER2_DEL":{"logical":"REMOVIDO","physical":"USER2_DEL","type":"bit","pk":false,"sequence":false,"nullable":true},"USER2_ID_FUNC":{"logical":"IDENTIFICACAO FUNCIONAL","physical":"USER2_ID_FUNC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"USER2_ATI":{"logical":"ATIVO","physical":"USER2_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"ESCR1_COD":{"logical":"ID DO ESCRITORIO","physical":"ESCR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_ESCR","column":"ESCR1_COD"},"phrase":"EH BASE DO","inverse":"PERTENCE AO"},"USER2_CONF":{"logical":"CONFIGURACAO MAPA","physical":"USER2_CONF","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_NOM":{"logical":"NOME","physical":"USER2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"USER2_CACHE_PERMS":{"logical":"CACHE PERMISSOES","physical":"USER2_CACHE_PERMS","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_GEST":{"logical":"GESTOR","physical":"USER2_GEST","type":"integer","pk":false,"sequence":false,"nullable":true},"USER2_EMAIL":{"logical":"EMAIL","physical":"USER2_EMAIL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"USER2_RAMAL":{"logical":"RAMAL","physical":"USER2_RAMAL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":250},"USER2_CEL":{"logical":"CELULAR","physical":"USER2_CEL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":250},"USER2_BASE_OPER":{"logical":"BASE OPERACIONAL","physical":"USER2_BASE_OPER","type":"text","pk":false,"sequence":false,"nullable":true}},"position":[900,4300]},"TB3_GRCA":{"logical":"GRUPO CAMADAS INTERFACE","columns":{"GRCA3_COD":{"logical":"CODIGO DO GRUPO","physical":"GRCA3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"GRCA3_NOM":{"logical":"NOME DO GRUPO","physical":"GRCA3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"GRCA3_COD_TXT":{"logical":"CODIGO TEXTO","physical":"GRCA3_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"GRCA3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GRCA3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"GRCA3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"GRCA3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,4500]},"TB1_RLPR":{"logical":"RELACIONAMENTO PROPRIETARIOS","columns":{"PRIO1_COD_PAI":{"logical":"ID DO PROPRIETARIO PAI","physical":"PRIO1_COD_PAI","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"},"phrase":"EH PAI","inverse":"TEM COMO PAI"},"PRIO1_COD_FILHO":{"logical":"ID DO PROPRIETARIO FILHO","physical":"PRIO1_COD_FILHO","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"},"phrase":"EH FILHO","inverse":"TEM COMO FILHO"},"TPRL1_COD":{"logical":"CODIGO TIPO","physical":"TPRL1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_TPRL","column":"TPRL1_COD"}},"RLPR1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"RLPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"RLPR1_DAT_HR_MOD":{"logical":"DATA DE MODIFICACAO","physical":"RLPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"RLPR1_DES":{"logical":"DESCRICAO","physical":"RLPR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}}},"position":[300,4500]},"TB1_TPRL":{"logical":"TIPO RELACIONAMENTO","columns":{"TPRL1_COD":{"logical":"CODIGO TIPO","physical":"TPRL1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TPRL1_NOM":{"logical":"NOME","physical":"TPRL1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"TPRL1_DES":{"logical":"DESCRICAO","physical":"TPRL1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TPRL1_TIPO_PAI":{"logical":"TIPO PESSOA PAI","physical":"TPRL1_TIPO_PAI","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":3},"TPRL1_TIT_PAI":{"logical":"TITULO PAI","physical":"TPRL1_TIT_PAI","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TPRL1_TIT_FILHO":{"logical":"TITULO FILHO","physical":"TPRL1_TIT_FILHO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TPRL1_TIPO_FILHO":{"logical":"TIPO PESSOA FILHO","physical":"TPRL1_TIPO_FILHO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":3},"TPRL1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TPRL1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TPRL1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TPRL1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TPRL1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TPRL1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50}},"position":[500,4500]},"TB1_CONC":{"logical":"CONCORRENTE","columns":{"CONC1_COD":{"logical":"CODIGO CONCORRENTE","physical":"CONC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CONC1_NOM":{"logical":"NOME","physical":"CONC1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"CONC1_DES":{"logical":"DESCRICAO","physical":"CONC1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"CONC1_DAT_HR_INI":{"logical":"DATA REGISTRO","physical":"CONC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CONC1_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"CONC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"CONC1_DOC":{"logical":"DOCUMENTO","physical":"CONC1_DOC","type":"bigint","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}}},"position":[700,4500]},"TB1_PRCO":{"logical":"PROPRIEDADE CONCORRENTE","columns":{"CONC1_COD":{"logical":"CODIGO CONCORRENTE","physical":"CONC1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CONC","column":"CONC1_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PRCO1_STATUS":{"logical":"STATUS CONTRATO","physical":"PRCO1_STATUS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PRCO1_TIPO_CTRT":{"logical":"TIPO CONTRATO","physical":"PRCO1_TIPO_CTRT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PRCO1_GEOM":{"logical":"GEOMETRIA PARCIAL","physical":"PRCO1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"PRCO1_DES":{"logical":"DESCRICAO","physical":"PRCO1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PRCO1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PRCO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PRCO1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PRCO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,4500]},"TB0_PTRF":{"logical":"PONTUACAO RF","columns":{"PTRF0_COD":{"logical":"CODIGO","physical":"PTRF0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PTRF0_NOM":{"logical":"NOME","physical":"PTRF0_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"PTRF0_DES":{"logical":"DESCRICAO","physical":"PTRF0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"PTRF0_OBJ":{"logical":"OBJETO","physical":"PTRF0_OBJ","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":30},"PTRF0_STATUS":{"logical":"STATUS","physical":"PTRF0_STATUS","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":30},"PTRF0_PERC":{"logical":"PERCENTUAL","physical":"PTRF0_PERC","type":"integer","pk":false,"sequence":false,"nullable":false},"PTRF0_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PTRF0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PTRF0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PTRF0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,4700]},"TB0_VRRF":{"logical":"VALORES REFERENCIA RF","columns":{"VRRF0_COD":{"logical":"CODIGO","physical":"VRRF0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"VRRF0_NOM":{"logical":"NOME","physical":"VRRF0_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"VRRF0_N1":{"logical":"NIVEL 1","physical":"VRRF0_N1","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"VRRF0_N2":{"logical":"NIVEL 2","physical":"VRRF0_N2","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"VRRF0_N3":{"logical":"NIVEL 3","physical":"VRRF0_N3","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"VRRF0_PTS":{"logical":"PONTOS","physical":"VRRF0_PTS","type":"integer","pk":false,"sequence":false,"nullable":false},"VRRF0_PT_TOT":{"logical":"PARTICIPACAO TOTAL","physical":"VRRF0_PT_TOT","type":"integer","pk":false,"sequence":false,"nullable":false},"VRRF0_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VRRF0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"VRRF0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VRRF0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,4700]},"TB5_COMP":{"logical":"COMPLEX ENGENHARIA","columns":{"COMP5_COD":{"logical":"CODIGO COMPLEX","physical":"COMP5_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COMP5_NAME":{"logical":"NOME","physical":"COMP5_NAME","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"PROJ5_COD":{"logical":"CODIGO PROJECT","physical":"PROJ5_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB5_PROJ","column":"PROJ5_COD"}},"COMP5_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"COMP5_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"COMP5_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"COMP5_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,4700]},"TB2_CARG":{"logical":"CARGO","columns":{"CARG2_COD":{"logical":"CODIGO CARGO","physical":"CARG2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CARG2_NOM":{"logical":"NOME DO CARGO","physical":"CARG2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"CARG2_DES":{"logical":"DESCRICAO DO CARGO","physical":"CARG2_DES","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"CARG2_ATI":{"logical":"INDICADOR DE CARGO ATIVO","physical":"CARG2_ATI","type":"bit","pk":false,"sequence":false,"nullable":true},"CARG2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CARG2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"CARG2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CARG2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,4700]},"TB5_PARK":{"logical":"PARK ENGENHARIA","columns":{"PARK5_COD":{"logical":"CODIGO PARK","physical":"PARK5_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PARK5_NAME":{"logical":"NOME","physical":"PARK5_NAME","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"COMP5_COD":{"logical":"CODIGO COMPLEX","physical":"COMP5_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB5_COMP","column":"COMP5_COD"}},"PARK5_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PARK5_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PARK5_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PARK5_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,4700]},"TB5_PROJ":{"logical":"PROJECT ENGENHARIA","columns":{"PROJ5_COD":{"logical":"CODIGO PROJECT","physical":"PROJ5_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PROJ5_NAME":{"logical":"NOME","physical":"PROJ5_NAME","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"OFFI5_COD":{"logical":"CODIGO OFFICE","physical":"OFFI5_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"PROJ5_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PROJ5_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PROJ5_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PROJ5_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,4900]},"TB5_SCEN":{"logical":"SCENARIO ENGENHARIA","columns":{"SCEN5_COD":{"logical":"CODIGO SCENARIO","physical":"SCEN5_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SCEN5_NAME":{"logical":"NOME","physical":"SCEN5_NAME","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":10},"SCEN5_WIND_MODEL":{"logical":"WIND MODEL","physical":"SCEN5_WIND_MODEL","type":"smallint","pk":false,"sequence":false,"nullable":false},"SCEN5_NUMBER":{"logical":"NUMERO","physical":"SCEN5_NUMBER","type":"smallint","pk":false,"sequence":false,"nullable":false},"SCEN5_DATETIME":{"logical":"DATA DO SCENARIO","physical":"SCEN5_DATETIME","type":"date","pk":false,"sequence":false,"nullable":true},"SCEN5_TYPE":{"logical":"TIPO","physical":"SCEN5_TYPE","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"COMP5_COD":{"logical":"CODIGO COMPLEX","physical":"COMP5_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB5_COMP","column":"COMP5_COD"}},"SCEN5_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SCEN5_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SCEN5_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SCEN5_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,4900]},"TB2_PEUS":{"logical":"PERMISSAO DO USUARIO","columns":{"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PERM2_COD":{"logical":"CODIGO PERMISSAO","physical":"PERM2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PERM","column":"PERM2_COD"}},"PEUS2_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"PEUS2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD_ATRIB":{"logical":"CODIGO USUARIO ATRIBUIDOR","physical":"USER2_COD_ATRIB","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ATRIBUIU A","inverse":"FOI DADA POR"},"PEUS2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PEUS2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,4900]},"TB5_SITE":{"logical":"SITE ENGENHARIA","columns":{"SITE5_COD":{"logical":"CODIGO SITE","physical":"SITE5_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SITE5_PORTFOLIO":{"logical":"PORTFOLIO","physical":"SITE5_PORTFOLIO","type":"bit","pk":false,"sequence":false,"nullable":true},"SCEN5_COD":{"logical":"CODIGO SCENARIO","physical":"SCEN5_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB5_SCEN","column":"SCEN5_COD"}},"PARK5_COD":{"logical":"CODIGO PARK","physical":"PARK5_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB5_PARK","column":"PARK5_COD"}},"STAG1_COD":{"logical":"CODIGO STATUS","physical":"STAG1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STAG","column":"STAG1_COD"}},"SITE5_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SITE5_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SITE5_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SITE5_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,4900]},"TB5_TURB":{"logical":"TURBINE ENGENHARIA","columns":{"TURB5_COD":{"logical":"CODIGO TURBINE","physical":"TURB5_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TURB5_INDEX_IN_SITE":{"logical":"INDICE NO SITE","physical":"TURB5_INDEX_IN_SITE","type":"smallint","pk":false,"sequence":false,"nullable":false},"TURB5_LABEL":{"logical":"LABEL","physical":"TURB5_LABEL","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"TURB5_TAB":{"logical":"TAB","physical":"TURB5_TAB","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TURB5_POSITION":{"logical":"POSITION","physical":"TURB5_POSITION","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":12},"TURB5_X":{"logical":"X","physical":"TURB5_X","type":"double precision","pk":false,"sequence":false,"nullable":true},"TURB5_Y":{"logical":"Y","physical":"TURB5_Y","type":"double precision","pk":false,"sequence":false,"nullable":false},"TURB5_TERRAIN_ELEVATION":{"logical":"ELEVATION","physical":"TURB5_TERRAIN_ELEVATION","type":"double precision","pk":false,"sequence":false,"nullable":true},"TURB5_DISTANCE_TO_CLOSEST_TURBINE":{"logical":"DISTANCE CLOSEST","physical":"TURB5_DISTANCE_TO_CLOSEST_TURBINE","type":"double precision","pk":false,"sequence":false,"nullable":true},"TURB5_AIR_DENSITY":{"logical":"AIR DENSITY","physical":"TURB5_AIR_DENSITY","type":"double precision","pk":false,"sequence":false,"nullable":true},"TURB5_NET_YELD":{"logical":"NET YELD","physical":"TURB5_NET_YELD","type":"double precision","pk":false,"sequence":false,"nullable":true},"TURB5_CAPACITY_FACTOR":{"logical":"CAPACITY FACTOR","physical":"TURB5_CAPACITY_FACTOR","type":"double precision","pk":false,"sequence":false,"nullable":true},"SITE5_COD":{"logical":"CODIGO SITE","physical":"SITE5_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB5_SITE","column":"SITE5_COD"}},"TURB5_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TURB5_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TURB5_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TURB5_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,4900]},"TB5_TYIN":{"logical":"TYPE INFORMATION ENGENHARIA","columns":{"TYIN5_COD":{"logical":"CODIGO TYIN","physical":"TYIN5_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TYIN5_ROTOR_DIAMETER":{"logical":"ROTOR DIAMETER","physical":"TYIN5_ROTOR_DIAMETER","type":"smallint","pk":false,"sequence":false,"nullable":false},"TYIN5_CAPACITY":{"logical":"CAPACITY","physical":"TYIN5_CAPACITY","type":"smallint","pk":false,"sequence":false,"nullable":false},"TYIN5_PEAK_OUTPUT":{"logical":"PEAK OUTPUT","physical":"TYIN5_PEAK_OUTPUT","type":"smallint","pk":false,"sequence":false,"nullable":false},"TYIN5_WEIBULLSHAPEK":{"logical":"WEIBULLSHAPEK","physical":"TYIN5_WEIBULLSHAPEK","type":"double precision","pk":false,"sequence":false,"nullable":true},"TYIN5_CUT_IN_MS":{"logical":"CUT IN MS","physical":"TYIN5_CUT_IN_MS","type":"smallint","pk":false,"sequence":false,"nullable":false},"TYIN5_CUT_OUT_MS":{"logical":"CUT OUT MS","physical":"TYIN5_CUT_OUT_MS","type":"double precision","pk":false,"sequence":false,"nullable":false},"TYIN5_POWER_UNCERTAINTY":{"logical":"POWER UNCERTAINTY","physical":"TYIN5_POWER_UNCERTAINTY","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":10},"TYIN5_NUMBER_OF_BLADES":{"logical":"NUMBER OF BLADES","physical":"TYIN5_NUMBER_OF_BLADES","type":"smallint","pk":false,"sequence":false,"nullable":false},"TYIN5_BLADE_WIDTH_MAXIMUM":{"logical":"BLADE WIDTH MAXIMUM","physical":"TYIN5_BLADE_WIDTH_MAXIMUM","type":"double precision","pk":false,"sequence":false,"nullable":true},"TYIN5_BLADE_WIDTH_AT_90_OF_RADIUS":{"logical":"BLADE WIDTH AT 90 RADIUS","physical":"TYIN5_BLADE_WIDTH_AT_90_OF_RADIUS","type":"smallint","pk":false,"sequence":false,"nullable":false},"TYIN5_ROTOR_TILT_DEGREES":{"logical":"ROTOR TILT DEGREES","physical":"TYIN5_ROTOR_TILT_DEGREES","type":"smallint","pk":false,"sequence":false,"nullable":false},"TYIN5_COMMENTS":{"logical":"COMMENTS","physical":"TYIN5_COMMENTS","type":"text","pk":false,"sequence":false,"nullable":false},"TYIN5_REGULATED":{"logical":"REGULATED","physical":"TYIN5_REGULATED","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":10},"TYIN5_IEC_METHOD":{"logical":"IEC METHOD","physical":"TYIN5_IEC_METHOD","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"TYIN5_NAME":{"logical":"NAME","physical":"TYIN5_NAME","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"TYIN5_ALT":{"logical":"ALT","physical":"TYIN5_ALT","type":"double precision","pk":false,"sequence":false,"nullable":true},"TYIN5_HUB_HEIGHT":{"logical":"HUB HEIGHT","physical":"TYIN5_HUB_HEIGHT","type":"double precision","pk":false,"sequence":false,"nullable":true},"TYIN5_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TYIN5_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TYIN5_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TYIN5_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,5100]},"TB1_PAIN":{"logical":"PARCIAL INTERESSE","columns":{"PAIN1_COD":{"logical":"CODIGO PARCIAL","physical":"PAIN1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PAIN1_GEOM":{"logical":"GEOMETRIA","physical":"PAIN1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":134},"PAIN1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"PAIN1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PAIN1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PAIN1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PAIN1_OBS":{"logical":"COMENTARIOS","physical":"PAIN1_OBS","type":"text","pk":false,"sequence":false,"nullable":true},"CTRT1_COD_REL":{"logical":"ID DO CONTRATO RELACIONADO","physical":"CTRT1_COD_REL","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"CTRT1_COD_EFET":{"logical":"ID DO CONTRATO EFETIVADO","physical":"CTRT1_COD_EFET","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"},"phrase":"EFETIVOU","inverse":"EFETIVADA"},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"USER2_COD_EFET":{"logical":"CODIGO USUARIO EFETIVADOR","physical":"USER2_COD_EFET","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"EFETIVOU","inverse":"EFETIVADA POR"},"TIPR1_COD":{"logical":"CODIGO TIPO","physical":"TIPR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_TIPR","column":"TIPR1_COD"}},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"PRIO1_COD_CESS":{"logical":"ID DO PROPRIETARIO CESSIONARIO","physical":"PRIO1_COD_CESS","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}}},"position":[300,5100]},"TB1_STGEO":{"logical":"STATUS GEO","columns":{"STGEO1_COD":{"logical":"CODIGO STATUS GEO","physical":"STGEO1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STGEO1_NOM":{"logical":"NOME","physical":"STGEO1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STGEO1_DES":{"logical":"DESCRICAO","physical":"STGEO1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STGEO1_CURTO":{"logical":"NOME CURTO","physical":"STGEO1_CURTO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"STGEO1_ORD":{"logical":"ORDEM DE APRESENTACAO","physical":"STGEO1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STGEO1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STGEO1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STGEO1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STGEO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"STGEO1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STGEO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,5100]},"TB1_TCTB":{"logical":"TIPO CONTRATO BIG","columns":{"TCTB1_COD":{"logical":"CODIGO TIPO CONTRATO BIG","physical":"TCTB1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TCTB1_NOM":{"logical":"NOME","physical":"TCTB1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TCTB1_DES":{"logical":"DESCRICAO","physical":"TCTB1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TCTB1_TIPO_ERP":{"logical":"NOME ERP","physical":"TCTB1_TIPO_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"TCTB1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"TCTB1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TCTB1_DAT_HR_MOD":{"logical":"DATA DE MODIFICACAO","physical":"TCTB1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TCTB1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TCTB1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TCTB1_SEL":{"logical":"INDICADOR SELECIONAVEL","physical":"TCTB1_SEL","type":"bit","pk":false,"sequence":false,"nullable":true},"GTCT1_COD":{"logical":"CODIGO DO GRUPO","physical":"GTCT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_GTCT","column":"GTCT1_COD"}},"TCTB1_ORD":{"logical":"ORDEM","physical":"TCTB1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"TCTB1_COD_ERP":{"logical":"CODIGO ERP","physical":"TCTB1_COD_ERP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20}},"position":[700,5100]},"TB1_GTCT":{"logical":"GRUPO DO TIPO DE CONTRATO","columns":{"GTCT1_COD":{"logical":"CODIGO DO GRUPO","physical":"GTCT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"GTCT1_NOM":{"logical":"NOME DO GRUPO","physical":"GTCT1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"GTCT1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"GTCT1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"GTCT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GTCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"GTCT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"GTCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,5100]},"TB1_STAP":{"logical":"STATUS AUTORIZACAO PASSAGEM","columns":{"STAP1_COD":{"logical":"CODIGO STATUS PASSAGEM","physical":"STAP1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STAP1_NOM":{"logical":"NOME","physical":"STAP1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"STAP1_DES":{"logical":"DESCRICAO","physical":"STAP1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STAP1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STAP1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STAP1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STAP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"STAP1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STAP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,5300]},"TB1_STSE":{"logical":"STATUS SERVIDAO","columns":{"STSE1_COD":{"logical":"CODIGO STATUS SERVIDAO","physical":"STSE1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STSE1_NOM":{"logical":"NOME","physical":"STSE1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"STSE1_DES":{"logical":"DESCRICAO","physical":"STSE1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STSE1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STSE1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STSE1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STSE1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"STSE1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STSE1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"STSE1_ORD":{"logical":"ORDEM","physical":"STSE1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false}},"position":[300,5300]},"TB1_TIRE":{"logical":"TIPO REGULARIZACAO","columns":{"TIRE1_COD":{"logical":"CODIGO TIPO RF","physical":"TIRE1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TIRE1_NOM":{"logical":"NOME TIPO","physical":"TIRE1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TIRE1_ORD":{"logical":"ORDEM","physical":"TIRE1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"TIRE1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TIRE1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"GRTR1_COD":{"logical":"CODIGO GRUPO","physical":"GRTR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_GRTR","column":"GRTR1_COD"}},"TIRE1_ATI":{"logical":"ATIVO","physical":"TIRE1_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"TIRE1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TIRE1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TIRE1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TIRE1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,5300]},"TB1_STRF":{"logical":"STATUS REGULARIZACAO","columns":{"STRF1_COD":{"logical":"ID STATUS","physical":"STRF1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STRF1_NOM":{"logical":"NOME STATUS","physical":"STRF1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STRF1_DES":{"logical":"DESCRICAO","physical":"STRF1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STRF1_ATI":{"logical":"ATIVO","physical":"STRF1_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"STRF1_ORD":{"logical":"ORDEM","physical":"STRF1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STRF1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STRF1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STRF1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STRF1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TIRE1_COD":{"logical":"CODIGO TIPO RF","physical":"TIRE1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_TIRE","column":"TIRE1_COD"}},"GRTR1_COD":{"logical":"CODIGO GRUPO","physical":"GRTR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_TIRE","column":"GRTR1_COD"}},"STRF1_PRAZ_MED":{"logical":"PRAZO MEDIO","physical":"STRF1_PRAZ_MED","type":"integer","pk":false,"sequence":false,"nullable":true}},"position":[700,5300]},"TB1_SRFP":{"logical":"STATUS DA RF DA PROPRIEDADE","columns":{"STRF1_COD":{"logical":"ID STATUS","physical":"STRF1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_STRF","column":"STRF1_COD"}},"SRFP1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SRFP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SRFP1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SRFP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_RFPR","column":"PROP1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"TIRE1_COD":{"logical":"CODIGO TIPO RF","physical":"TIRE1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_TIRE","column":"TIRE1_COD"}},"GRTR1_COD":{"logical":"CODIGO GRUPO","physical":"GRTR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_GRTR","column":"GRTR1_COD"}}},"position":[900,5300]},"TB0_LSRP":{"logical":"LOG STATUS RF","columns":{"LSRP0_COD":{"logical":"ID DO HISTORICO","physical":"LSRP0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LSRP0_NOM_NOVO":{"logical":"NOME STATUS NOVO","physical":"LSRP0_NOM_NOVO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"LSRP0_NOM_TIPO":{"logical":"NOME TIPO","physical":"LSRP0_NOM_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"LSRP0_NOM_GRUPO":{"logical":"NOME GRUPO","physical":"LSRP0_NOM_GRUPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"LSRP0_DAT_HR_INI":{"logical":"DATA ALTERACAO","physical":"LSRP0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LSRP0_NOM_ANT":{"logical":"NOME STATUS ANTERIOR","physical":"LSRP0_NOM_ANT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"TIRE1_COD":{"logical":"CODIGO TIPO RF","physical":"TIRE1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STRF","column":"TIRE1_COD"},"phrase":"EH NOVO","inverse":"TEM NOVO"},"GRTR1_COD":{"logical":"CODIGO GRUPO","physical":"GRTR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STRF","column":"GRTR1_COD"},"phrase":"EH NOVO","inverse":"TEM NOVO"},"STRF1_COD_ANT":{"logical":"ID STATUS ANTERIOR","physical":"STRF1_COD_ANT","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STRF","column":"STRF1_COD"},"phrase":"EH ANTERIOR","inverse":"TEM ANTERIOR"},"STRF1_COD_NOVO":{"logical":"ID STATUS NOVO","physical":"STRF1_COD_NOVO","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STRF","column":"STRF1_COD"},"phrase":"EH NOVO","inverse":"TEM NOVO"},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PROP1_COD":{"logical":"ID PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false},"LSRP0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LSRP0_DAT_HR_MOD","type":"integer","pk":false,"sequence":false,"nullable":true}},"position":[100,5500]},"TB1_GRTR":{"logical":"GRUPO TIPO REGULARIZACAO","columns":{"GRTR1_COD":{"logical":"CODIGO GRUPO","physical":"GRTR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"GRTR1_NOM":{"logical":"NOME GRUPO","physical":"GRTR1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"GRTR1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"GRTR1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"GRTR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GRTR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"GRTR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"GRTR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,5500]},"TB1_STDOM":{"logical":"STATUS DOMINIO","columns":{"STDOM1_COD":{"logical":"CODIGO STATUS DOMINIO","physical":"STDOM1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STDOM1_NOM":{"logical":"NOME","physical":"STDOM1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STDOM1_DES":{"logical":"DESCRICAO","physical":"STDOM1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STDOM1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STDOM1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STDOM1_ABR":{"logical":"NOME CURTO","physical":"STDOM1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STDOM1_ORD":{"logical":"ORDEM","physical":"STDOM1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STDOM1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STDOM1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STDOM1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STDOM1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,5500]},"TB1_STCTB":{"logical":"STATUS CONTRATO BIG","columns":{"STCTB1_COD":{"logical":"CODIGO STATUS CONTRATO","physical":"STCTB1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STCTB1_NOM":{"logical":"NOME","physical":"STCTB1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STCTB1_DES":{"logical":"DESCRICAO","physical":"STCTB1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STCTB1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STCTB1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STCTB1_ORD":{"logical":"ORDEM","physical":"STCTB1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STCTB1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STCTB1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STCTB1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STCTB1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"STCTB1_ABR":{"logical":"NOME CURTO","physical":"STCTB1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20}},"position":[700,5500]},"TB1_STVARQ":{"logical":"STATUS VERIFICACAO ARQUIVO","columns":{"STVARQ1_COD":{"logical":"CODIGO STATUS VERIFICACAO","physical":"STVARQ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STVARQ1_NOM":{"logical":"NOME","physical":"STVARQ1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STVARQ1_DES":{"logical":"DESCRICAO","physical":"STVARQ1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STVARQ1_ABR":{"logical":"NOME CURTO","physical":"STVARQ1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STVARQ1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STVARQ1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STVARQ1_ORD":{"logical":"ORDEM","physical":"STVARQ1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STVARQ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STVARQ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STVARQ1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STVARQ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,5500]},"TB1_REFU":{"logical":"REMEMBRAMENTO FUTURO","columns":{"PROP1_COD":{"logical":"ID DE PROPRIEDADE A REMEMBRAR","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PROP1_COD_DEST":{"logical":"ID DA PROPRIEDADE DESTINO","physical":"PROP1_COD_DEST","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"},"phrase":"EH DESTINO","inverse":"COM DESTINO NESTA"},"REFU1_NOM":{"logical":"NOME DA PROPRIEDADE","physical":"REFU1_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"REFU1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"REFU1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"REFU1_DAT_HR_MOD":{"logical":"ULTIMA MODIFICACAO","physical":"REFU1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"REFU1_DAT_HR_PREV":{"logical":"DATA PREVISTA EFETIVACAO","physical":"REFU1_DAT_HR_PREV","type":"date","pk":false,"sequence":false,"nullable":true},"REFU1_GEOM":{"logical":"GEOMETRIA","physical":"REFU1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":134},"REFU1_AREA":{"logical":"AREA","physical":"REFU1_AREA","type":"double precision","pk":false,"sequence":false,"nullable":true}},"position":[100,5700]},"TB3_GROF":{"logical":"GRUPO ORTOFOTOS","columns":{"GROF3_COD":{"logical":"ID GRUPO","physical":"GROF3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"GROF3_NOM":{"logical":"NOME DO GRUPO","physical":"GROF3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"GROF3_COD_TXT":{"logical":"CODIGO TEXTO","physical":"GROF3_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"GROF3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"GROF3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"GROF3_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"GROF3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,5700]},"TB3_ORTO":{"logical":"ORTOFOTO","columns":{"ORTO3_COD":{"logical":"ID ORTOFOTO","physical":"ORTO3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ORTO3_NOM":{"logical":"NOME","physical":"ORTO3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ORTO3_DES":{"logical":"DESCRICAO","physical":"ORTO3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"ORTO3_DAT_REF":{"logical":"DATA REFERENCIA","physical":"ORTO3_DAT_REF","type":"date","pk":false,"sequence":false,"nullable":false},"ORTO3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ORTO3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ORTO3_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"ORTO3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TIOF3_COD":{"logical":"ID TIPO ORTOFOTO","physical":"TIOF3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_TIOF","column":"TIOF3_COD"}},"CAMA3_COD":{"logical":"ID DA CAMADA","physical":"CAMA3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_CAMA","column":"CAMA3_COD"}},"ORTO3_FILENAME":{"logical":"NOME DO ARQUIVO","physical":"ORTO3_FILENAME","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"ORTO3_DIR":{"logical":"PASTA","physical":"ORTO3_DIR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":1000},"DTBK_TORRE":{"logical":"TORRE","physical":"DTBK_TORRE","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PROJ1_COD":{"logical":"CODIGO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"CTRT1_COD_TXT":{"logical":"DENOMINACAO CONTRATO","physical":"CTRT1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"PROP1_COD":{"logical":"CODIGO PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"ORTO3_GS_NOM":{"logical":"NOME GEOSERVER","physical":"ORTO3_GS_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"ORTO3_ATI":{"logical":"ORTOFOTO ATIVA","physical":"ORTO3_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"ORTO3_GEOM_LIM":{"logical":"GEOMETRIA LIMITE","physical":"ORTO3_GEOM_LIM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"ORTO3_ARQ":{"logical":"ARQUIVO","physical":"ORTO3_ARQ","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"ORTO3_ARQ_MD5":{"logical":"MD5 DO ARQUIVO","physical":"ORTO3_ARQ_MD5","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":32}},"position":[500,5700]},"TB3_TIOF":{"logical":"TIPO ORTOFOTO","columns":{"TIOF3_COD":{"logical":"ID TIPO ORTOFOTO","physical":"TIOF3_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TIOF3_NOM":{"logical":"NOME","physical":"TIOF3_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":150},"TIOF3_DES":{"logical":"DESCRICAO","physical":"TIOF3_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TIOF3_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TIOF3_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":30},"GROF3_COD":{"logical":"ID GRUPO","physical":"GROF3_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB3_GROF","column":"GROF3_COD"}},"TIOF3_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TIOF3_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TIOF3_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TIOF3_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,5700]},"TB1_MUNI":{"logical":"MUNICIPIO","columns":{"MUNI1_COD":{"logical":"CODIGO IBGE MUNICIPIO","physical":"MUNI1_COD","type":"integer","pk":true,"sequence":false,"nullable":false},"MUNI1_NOM":{"logical":"NOME DO MUNICIPIO","physical":"MUNI1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":300},"ESTA1_COD":{"logical":"CODIGO IBGE ESTADO","physical":"ESTA1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_ESTA","column":"ESTA1_COD"}},"MUNI1_GEOM":{"logical":"GEOMETRIA","physical":"MUNI1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"MUNI1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"MUNI1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"MUNI1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"MUNI1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,5700]},"TB0_JOBQ":{"logical":"JOB QUEUE","columns":{"JOBQ0_COD":{"logical":"ID JOB","physical":"JOBQ0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"JOBQ0_NOM":{"logical":"NOME","physical":"JOBQ0_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":300},"JOBQ0_DES":{"logical":"DESCRICAO","physical":"JOBQ0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"JOBQ0_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"JOBQ0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"JOBQ0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"JOBQ0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"JOBQ0_DAT_INI":{"logical":"DATA INICIO","physical":"JOBQ0_DAT_INI","type":"date","pk":false,"sequence":false,"nullable":true},"JOBQ0_DAT_FIN":{"logical":"DATA FINAL","physical":"JOBQ0_DAT_FIN","type":"date","pk":false,"sequence":false,"nullable":true},"JOBQ0_JSON":{"logical":"PARAMETROS JSON","physical":"JOBQ0_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"JOBQ0_PID":{"logical":"PID","physical":"JOBQ0_PID","type":"integer","pk":false,"sequence":false,"nullable":true},"JOBQ0_SAIDA":{"logical":"SAIDA","physical":"JOBQ0_SAIDA","type":"text","pk":false,"sequence":false,"nullable":true},"JOBQ0_STATUS":{"logical":"STATUS","physical":"JOBQ0_STATUS","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":10},"TJOB0_COD":{"logical":"ID TIPO JOB","physical":"TJOB0_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB0_TJOB","column":"TJOB0_COD"}}},"position":[100,5900]},"TB1_ESTA":{"logical":"ESTADO","columns":{"ESTA1_COD":{"logical":"CODIGO IBGE ESTADO","physical":"ESTA1_COD","type":"integer","pk":true,"sequence":false,"nullable":false},"ESTA1_NOM":{"logical":"NOME DO ESTADO","physical":"ESTA1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"ESTA1_UF":{"logical":"ABREVIATURA","physical":"ESTA1_UF","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":2},"ESTA1_REG":{"logical":"REGIAO","physical":"ESTA1_REG","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"ESTA1_ABR_REG":{"logical":"ABREVIATURA REGIAO","physical":"ESTA1_ABR_REG","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":2},"ESTA1_GEOM":{"logical":"GEOMETRIA","physical":"ESTA1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"ESTA1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ESTA1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"ESTA1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ESTA1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,5900]},"TB0_TJOB":{"logical":"TIPO JOB","columns":{"TJOB0_COD":{"logical":"ID TIPO JOB","physical":"TJOB0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TJOB0_NOM":{"logical":"NOME","physical":"TJOB0_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"TJOB0_DES":{"logical":"DESCRICAO","physical":"TJOB0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TJOB0_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TJOB0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TJOB0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TJOB0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TJOB0_ATI":{"logical":"ATIVO","physical":"TJOB0_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"TJOB0_JSON":{"logical":"PARAMETROS JSON","physical":"TJOB0_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"TJOB0_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TJOB0_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20}},"position":[500,5900]},"TB2_PJUR":{"logical":"PESSOA JURIDICA","columns":{"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":true,"sequence":false,"nullable":false},"PJUR2_RS":{"logical":"RAZAO SOCIAL","physical":"PJUR2_RS","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":true,"sequence":false,"nullable":false},"PJUR2_FANT":{"logical":"NOME FANTASIA","physical":"PJUR2_FANT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PJUR2_DAT_ABER":{"logical":"DATA DE ABERTURA","physical":"PJUR2_DAT_ABER","type":"date","pk":false,"sequence":false,"nullable":true},"PJUR2_LOGR":{"logical":"LOGRADOURO","physical":"PJUR2_LOGR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PJUR2_NUM":{"logical":"NUMERO","physical":"PJUR2_NUM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":40},"PJUR2_COMPL":{"logical":"COMPLEMENTO","physical":"PJUR2_COMPL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PJUR2_MATRIZ":{"logical":"MATRIZ","physical":"PJUR2_MATRIZ","type":"bit","pk":false,"sequence":false,"nullable":true},"PJUR2_CNAE":{"logical":"CNAE PRINCIPAL","physical":"PJUR2_CNAE","type":"integer","pk":false,"sequence":false,"nullable":true},"PJUR2_CAP_SOC":{"logical":"CAPITAL SOCIAL","physical":"PJUR2_CAP_SOC","type":"double precision","pk":false,"sequence":false,"nullable":true},"NAJU2_COD":{"logical":"CODIGO DA NATUREZA","physical":"NAJU2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_NAJU","column":"NAJU2_COD"}},"SITU2_COD":{"logical":"CODIGO DA SITUACAO","physical":"SITU2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_SITU","column":"SITU2_COD"}},"SIES2_COD":{"logical":"CODIGO DA SITUACAO ESPECIAL","physical":"SIES2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_SIES","column":"SIES2_COD"}},"PJUR2_DAT_SITU":{"logical":"DATA DA SITUACAO","physical":"PJUR2_DAT_SITU","type":"date","pk":false,"sequence":false,"nullable":true},"PJUR2_DAT_SIES":{"logical":"DATA DA SITUACAO ESPECIAL","physical":"PJUR2_DAT_SIES","type":"date","pk":false,"sequence":false,"nullable":true},"MUNI1_COD":{"logical":"CODIGO IBGE MUNICIPIO","physical":"MUNI1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_MUNI","column":"MUNI1_COD"}},"PJUR2_DAT_HR_INI":{"logical":"DATA DE CRIACAO DO REGISTRO","physical":"PJUR2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"PJUR2_DAT_HR_MOD":{"logical":"DATA DA ULTIMA MODIFICACAO","physical":"PJUR2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PJUR2_LAT":{"logical":"LATITUDE","physical":"PJUR2_LAT","type":"double precision","pk":false,"sequence":false,"nullable":true},"PJUR2_LONG":{"logical":"LONGITUDE","physical":"PJUR2_LONG","type":"double precision","pk":false,"sequence":false,"nullable":true},"PJUR2_GEOM":{"logical":"GEOMETRIA","physical":"PJUR2_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":12},"PJUR2_DV":{"logical":"DIGITO VERIFICADOR","physical":"PJUR2_DV","type":"integer","pk":false,"sequence":false,"nullable":true},"PJUR2_BAI":{"logical":"BAIRRO","physical":"PJUR2_BAI","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"PJUR2_CEP":{"logical":"CEP","physical":"PJUR2_CEP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":15},"PJUR2_CIDA":{"logical":"CIDADE","physical":"PJUR2_CIDA","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PJUR2_UF":{"logical":"ESTADO","physical":"PJUR2_UF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100}},"position":[700,5900]},"TB1_PQSL":{"logical":"PARQUE SOLAR","columns":{"PQSL1_COD":{"logical":"CODIGO PARQUE SOLAR","physical":"PQSL1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PQSL1_NOM":{"logical":"NOME","physical":"PQSL1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":300},"PQSL1_DES":{"logical":"DESCRICAO","physical":"PQSL1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"PQSL1_ABR":{"logical":"ABREVIATURA","physical":"PQSL1_ABR","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":30},"PQSL1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PQSL1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PQSL1_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"PQSL1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PQSL1_DAT_HR_FIM":{"logical":"DATA ENCERRAMENTO","physical":"PQSL1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"PQSL1_GEOM":{"logical":"GEOMETRIA","physical":"PQSL1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":134},"PQSL1_LAYOUT":{"logical":"LAYOUT","physical":"PQSL1_LAYOUT","type":"integer","pk":false,"sequence":false,"nullable":true},"PQSL1_ELETROCENTRO_QTD":{"logical":"ELETROCENTRO","physical":"PQSL1_ELETROCENTRO_QTD","type":"integer","pk":false,"sequence":false,"nullable":true},"PQSL1_POT_MW":{"logical":"POTENCIA","physical":"PQSL1_POT_MW","type":"double precision","pk":false,"sequence":false,"nullable":true},"PQSL1_INVERSORES_QTD":{"logical":"QTD INVERSORES","physical":"PQSL1_INVERSORES_QTD","type":"integer","pk":false,"sequence":false,"nullable":true},"PQSL1_INVERSORES_DESC":{"logical":"DESCRICAO INVERSORES","physical":"PQSL1_INVERSORES_DESC","type":"text","pk":false,"sequence":false,"nullable":true},"PQSL1_POT_MW_POR_INVERSOR":{"logical":"POTENCIA POR INVERSOR","physical":"PQSL1_POT_MW_POR_INVERSOR","type":"double precision","pk":false,"sequence":false,"nullable":true},"PQSL1_SHP_MD5":{"logical":"MD5 SHAPE","physical":"PQSL1_SHP_MD5","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PQSL1_DBF_MD5":{"logical":"MD5 DBF","physical":"PQSL1_DBF_MD5","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PQSL1_PRJ_MD5":{"logical":"MD5 PRJ","physical":"PQSL1_PRJ_MD5","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PQSL1_SHX_MD5":{"logical":"MD5 SHX","physical":"PQSL1_SHX_MD5","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PQSL1_CPG_MD5":{"logical":"MD5 CPG","physical":"PQSL1_CPG_MD5","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":32},"PQSL1_FILEPATH":{"logical":"CAMINHO","physical":"PQSL1_FILEPATH","type":"text","pk":false,"sequence":false,"nullable":true},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}}},"position":[900,5900]},"TB2_NAJU":{"logical":"NATUREZA JURIDICA","columns":{"NAJU2_COD":{"logical":"CODIGO DA NATUREZA","physical":"NAJU2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"NAJU2_NOM":{"logical":"NATUREZA JURIDICA","physical":"NAJU2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"NAJU2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"NAJU2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"NAJU2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"NAJU2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,6100]},"TB1_SECO":{"logical":"SETOR CONTRATO","columns":{"SECO1_COD":{"logical":"ID SETOR CONTRATO","physical":"SECO1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SECO1_NOM":{"logical":"NOME SETOR","physical":"SECO1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"SECO1_DES":{"logical":"DESCRICAO","physical":"SECO1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"SECO1_COD_ERP":{"logical":"CODIGO ERP","physical":"SECO1_COD_ERP","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"SECO1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SECO1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SECO1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SECO1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,6100]},"TB2_SITU":{"logical":"SITUACAO","columns":{"SITU2_COD":{"logical":"CODIGO DA SITUACAO","physical":"SITU2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SITU2_NOM":{"logical":"SITUACAO","physical":"SITU2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"SITU2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SITU2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SITU2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SITU2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,6100]},"TB1_MORR":{"logical":"MOTIVO RESSALVA RECUSA","columns":{"MORR1_COD":{"logical":"ID MOTIVO","physical":"MORR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"MORR1_NOM":{"logical":"NOME","physical":"MORR1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":250},"MORR1_DES":{"logical":"DESCRICAO","physical":"MORR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"MORR1_SITU":{"logical":"INDICADOR SITUACAO","physical":"MORR1_SITU","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":2},"MORR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"MORR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"MORR1_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"MORR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"MORR1_TODOS":{"logical":"SE APLICA A TODOS","physical":"MORR1_TODOS","type":"bit","pk":false,"sequence":false,"nullable":false},"MORR1_CLAS":{"logical":"CLASSES DO MOTIVO","physical":"MORR1_CLAS","type":"text","pk":false,"sequence":false,"nullable":true},"MORR1_GEO":{"logical":"INDICADOR DE MOTIVO GEO","physical":"MORR1_GEO","type":"bit","pk":false,"sequence":false,"nullable":true}},"position":[700,6100]},"TB2_SIES":{"logical":"SITUACAO ESPECIAL","columns":{"SIES2_COD":{"logical":"CODIGO DA SITUACAO ESPECIAL","physical":"SIES2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SIES2_NOM":{"logical":"SITUACAO ESPECIAL","physical":"SIES2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"SIES2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SIES2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SIES2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SIES2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,6100]},"TB1_MOPA":{"logical":"MOTIVO DO PADRAO","columns":{"PAAR1_COD":{"logical":"CODIGO DO PADRAO","physical":"PAAR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PAAR","column":"PAAR1_COD"}},"MORR1_COD":{"logical":"ID MOTIVO","physical":"MORR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_MORR","column":"MORR1_COD"}},"MOPA1_DAT_HR_INI":{"logical":"DATA DE CRIACAO","physical":"MOPA1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"MOPA1_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"MOPA1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,6300]},"TB2_PFIS":{"logical":"PESSOA FISICA","columns":{"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PFIS2_NOM":{"logical":"NOME DA PESSOA","physical":"PFIS2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"PFIS2_CPF":{"logical":"CPF","physical":"PFIS2_CPF","type":"bigint","pk":false,"sequence":false,"nullable":true},"PFIS2_SEX":{"logical":"SEXO","physical":"PFIS2_SEX","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":1},"PFIS2_DAT_NASC":{"logical":"DATA DE NASCIMENTO","physical":"PFIS2_DAT_NASC","type":"date","pk":false,"sequence":false,"nullable":true},"PFIS2_RG":{"logical":"RG","physical":"PFIS2_RG","type":"bigint","pk":false,"sequence":false,"nullable":true},"PFIS2_EMRG":{"logical":"EMISSOR RG","physical":"PFIS2_EMRG","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":150},"PFIS2_MAE":{"logical":"NOME DA MAE","physical":"PFIS2_MAE","type":"text","pk":false,"sequence":false,"nullable":true},"PFIS2_PAI":{"logical":"NOME DO PAI","physical":"PFIS2_PAI","type":"text","pk":false,"sequence":false,"nullable":true},"PFIS2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PFIS2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PFIS2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PFIS2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,6300]},"TB1_RRAR":{"logical":"RESSALVA RECUSA ARQUIVO","columns":{"ARQU1_COD":{"logical":"CODIGO ARQUIVO","physical":"ARQU1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_ARQU","column":"ARQU1_COD"}},"MORR1_COD":{"logical":"ID MOTIVO","physical":"MORR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_MORR","column":"MORR1_COD"}},"RRAR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"RRAR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"RRAR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"RRAR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,6300]},"TB2_SOAM":{"logical":"SOCIO OU ADMINISTRADOR","columns":{"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"}},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"}},"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PFIS","column":"PFIS2_COD"}},"SOAM2_ATRIB":{"logical":"ATRIBUICAO","physical":"SOAM2_ATRIB","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"SOAM2_PART":{"logical":"PARTICIPACAO","physical":"SOAM2_PART","type":"float","pk":false,"sequence":false,"nullable":true},"SOAM2_DAT_ENTR":{"logical":"DATA ENTRADA","physical":"SOAM2_DAT_ENTR","type":"date","pk":false,"sequence":false,"nullable":true},"SOAM2_RL":{"logical":"REPRESENTANTE LEGAL","physical":"SOAM2_RL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"SOAM2_ATRIB_RL":{"logical":"ATRIBUICAO REPRESENTANTE","physical":"SOAM2_ATRIB_RL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"SOAM2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SOAM2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SOAM2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SOAM2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,6300]},"TB1_STIN":{"logical":"STATUS INDENIZACAO","columns":{"STIN1_COD":{"logical":"CODIGO STATUS INDENIZACAO","physical":"STIN1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STIN1_NOM":{"logical":"NOME","physical":"STIN1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":18},"STIN1_DES":{"logical":"DESCRICAO","physical":"STIN1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STIN1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STIN1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STIN1_ORD":{"logical":"ORDEM","physical":"STIN1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STIN1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STIN1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STIN1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STIN1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,6300]},"TB2_ENPF":{"logical":"ENDERECO PESSOA FISICA","columns":{"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PFIS","column":"PFIS2_COD"}},"ENDE2_COD":{"logical":"CODIGO DO ENDERECO","physical":"ENDE2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_ENDE","column":"ENDE2_COD"}},"ENPF2_DESC":{"logical":"DESCRICAO","physical":"ENPF2_DESC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"ENPF2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ENPF2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ENPF2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ENPF2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,6500]},"TB1_EDIN":{"logical":"EDIFICACAO INDENIZADA","columns":{"EDIN1_COD":{"logical":"CODIGO EDIFICACAO","physical":"EDIN1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"EDIN1_NOM":{"logical":"NOME","physical":"EDIN1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"EDIN1_DES":{"logical":"DESCRICAO","physical":"EDIN1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"EDIN1_GEOM":{"logical":"GEOMETRIA","physical":"EDIN1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":14},"EDIN1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"EDIN1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"EDIN1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"EDIN1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_GPCT","column":"CTRT1_COD"}},"GLEP1_COD":{"logical":"ID DA GLEBA","physical":"GLEP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_GPCT","column":"GLEP1_COD"}}},"position":[300,6500]},"TB1_TPPC":{"logical":"TIPO PROCESSO","columns":{"TPPC1_COD":{"logical":"CODIGO TIPO PROCESSO","physical":"TPPC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TPPC1_NOM":{"logical":"NOME","physical":"TPPC1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":300},"TPPC1_DES":{"logical":"DESCRICAO","physical":"TPPC1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TPPC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TPPC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TPPC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TPPC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,6500]},"TB2_TLPF":{"logical":"TELEFONE PESSOA FISICA","columns":{"TELE2_COD":{"logical":"CODIGO DO TELEFONE","physical":"TELE2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_TELE","column":"TELE2_COD"}},"TLPF2_DESC":{"logical":"DESCRICAO","physical":"TLPF2_DESC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"TLPF2_DAT_ALO":{"logical":"DATA DO ULTIMO ALO","physical":"TLPF2_DAT_ALO","type":"date","pk":false,"sequence":false,"nullable":true},"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PFIS","column":"PFIS2_COD"}},"TLPF2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TLPF2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TLPF2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TLPF2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,6500]},"TB1_PCPR":{"logical":"PROCESSO PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"TPPC1_COD":{"logical":"CODIGO TIPO PROCESSO","physical":"TPPC1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_TPPC","column":"TPPC1_COD"}},"PCPR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PCPR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PCPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PCPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PCPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"STPC1_COD":{"logical":"CODIGO STATUS PROCESSO","physical":"STPC1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STPC","column":"STPC1_COD"}},"PCPR1_DAT_PREV_CONCL":{"logical":"DATA PREVISTA CONCLUSAO","physical":"PCPR1_DAT_PREV_CONCL","type":"date","pk":false,"sequence":false,"nullable":true},"PCPR1_DAT_CONCL":{"logical":"DATA CONCLUSAO","physical":"PCPR1_DAT_CONCL","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"USER2_COD_STATUS":{"logical":"CODIGO USUARIO ALTERADOR STATUS","physical":"USER2_COD_STATUS","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ALTEROU STATUS","inverse":"TEVE STATUS ALTERADO POR"},"PCPR1_DES":{"logical":"DESCRICAO","physical":"PCPR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"PCPR1_NUM_PROC":{"logical":"NUMERO PROCESSO","physical":"PCPR1_NUM_PROC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PCPR1_VAL_CAUSA":{"logical":"VALOR DA CAUSA","physical":"PCPR1_VAL_CAUSA","type":"double precision","pk":false,"sequence":false,"nullable":true},"PCPR1_CART":{"logical":"VARA \/ CARTORIO","physical":"PCPR1_CART","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PCPR1_CNS":{"logical":"CNS VARA\/CARTORIO","physical":"PCPR1_CNS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":18},"PCPR1_DAT_ABE":{"logical":"DATA ABERTURA","physical":"PCPR1_DAT_ABE","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,6500]},"TB2_TLPJ":{"logical":"TELEFONE PESSOA JURIDICA","columns":{"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"}},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"}},"TELE2_COD":{"logical":"CODIGO DO TELEFONE","physical":"TELE2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_TELE","column":"TELE2_COD"}},"TLPJ2_DESC":{"logical":"DESCRICAO","physical":"TLPJ2_DESC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"TLPJ2_DAT_ALO":{"logical":"DATA DO ULTIMO ALO","physical":"TLPJ2_DAT_ALO","type":"date","pk":false,"sequence":false,"nullable":true},"TLPJ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TLPJ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TLPJ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TLPJ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,6700]},"TB1_STPC":{"logical":"STATUS PROCESSO","columns":{"STPC1_COD":{"logical":"CODIGO STATUS PROCESSO","physical":"STPC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STPC1_NOM":{"logical":"NOME","physical":"STPC1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":300},"STPC1_DES":{"logical":"DESCRICAO","physical":"STPC1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STPC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STPC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STPC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STPC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,6700]},"TB1_HSPC":{"logical":"HISTORICO STATUS PROCESSO","columns":{"HSPC1_COD":{"logical":"ID HISTORICO","physical":"HSPC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HSPC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HSPC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PCPR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCPR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCPR","column":"PCPR1_COD"}},"STPC1_COD":{"logical":"CODIGO STATUS PROCESSO","physical":"STPC1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_STPC","column":"STPC1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HSPC1_DES":{"logical":"DESCRICAO","physical":"HSPC1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"HSPC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HSPC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,6700]},"TB1_PTPC":{"logical":"PARTE PROCESSO","columns":{"PTPC1_COD":{"logical":"ID PARTE","physical":"PTPC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PTPC1_NOM":{"logical":"NOME","physical":"PTPC1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"PTPC1_QUALI":{"logical":"QUALIFICACAO","physical":"PTPC1_QUALI","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"PCPR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCPR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCPR","column":"PCPR1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"CONC1_COD":{"logical":"CODIGO CONCORRENTE","physical":"CONC1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CONC","column":"CONC1_COD"}},"PTPC1_DOC":{"logical":"DOCUMENTO","physical":"PTPC1_DOC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PTPC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PTPC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PTPC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PTPC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,6700]},"TB1_CMPC":{"logical":"COMENTARIO PROCESSO","columns":{"CMPC1_COD":{"logical":"ID COMENTARIO","physical":"CMPC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CMPC1_COMENT":{"logical":"COMENTARIO","physical":"CMPC1_COMENT","type":"text","pk":false,"sequence":false,"nullable":false},"CMPC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CMPC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PCPR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCPR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCPR","column":"PCPR1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CMPC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CMPC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,6700]},"TB1_COND":{"logical":"CONDICAO","columns":{"COND1_COD":{"logical":"ID CONDICAO","physical":"COND1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"COND1_NOM":{"logical":"NOME CONDICAO","physical":"COND1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"COND1_COD_SAP":{"logical":"CODIGO SAP CONDICAO","physical":"COND1_COD_SAP","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"COND1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"COND1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"COND1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"COND1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,6900]},"TB2_CNAE":{"logical":"CNAE","columns":{"CNAE2_COD":{"logical":"CODIGO DO CNAE","physical":"CNAE2_COD","type":"integer","pk":true,"sequence":false,"nullable":false},"CNAE2_TIT":{"logical":"TITULO","physical":"CNAE2_TIT","type":"text","pk":false,"sequence":false,"nullable":false},"CNAE2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CNAE2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CNAE2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CNAE2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,6900]},"TB1_CNCT":{"logical":"CONDICAO DO CONTRATO","columns":{"CNCT1_DAT_INI":{"logical":"DATA INICIO CONDICAO","physical":"CNCT1_DAT_INI","type":"date","pk":false,"sequence":false,"nullable":true},"CNCT1_DAT_FIM":{"logical":"DATA FINAL CONDICAO","physical":"CNCT1_DAT_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"CNCT1_VALOR":{"logical":"VALOR","physical":"CNCT1_VALOR","type":"integer","pk":false,"sequence":false,"nullable":true},"COND1_COD":{"logical":"ID CONDICAO","physical":"COND1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_COND","column":"COND1_COD"}},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"CNCT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CNCT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CNCT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CNCT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,6900]},"TB2_CNPJ":{"logical":"CNAE DA PJ","columns":{"CNAE2_COD":{"logical":"CODIGO DO CNAE","physical":"CNAE2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_CNAE","column":"CNAE2_COD"}},"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"}},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"}},"CNPJ2_ORD":{"logical":"ORDEM","physical":"CNPJ2_ORD","type":"integer","pk":true,"sequence":false,"nullable":false},"CNPJ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CNPJ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CNPJ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CNPJ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,6900]},"TB0_PEAT":{"logical":"PENDENCIA ATUALIZACAO","columns":{"PEAT0_COD":{"logical":"ID PENDENCIA","physical":"PEAT0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PEAT0_CLAS":{"logical":"CLASSE","physical":"PEAT0_CLAS","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"PEAT0_KEY":{"logical":"CHAVE OBJETO","physical":"PEAT0_KEY","type":"integer","pk":false,"sequence":false,"nullable":false},"PEAT0_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PEAT0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PEAT0_DAT_HR_FIM":{"logical":"DATA CONCLUSAO","physical":"PEAT0_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"PEAT0_PARAM":{"logical":"PARAMETROS","physical":"PEAT0_PARAM","type":"text","pk":false,"sequence":false,"nullable":true},"PEAT0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PEAT0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,6900]},"TB0_PRIE":{"logical":"PROPRIEDADE INEXISTENTE ERP","columns":{"PRIE0_COD":{"logical":"ID PROPRIEDADE INEXISTENTE","physical":"PRIE0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PRIE0_NOM":{"logical":"NOME","physical":"PRIE0_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"PRIE0_COD_ERP":{"logical":"ID PROPRIEDADE NO ERP","physical":"PRIE0_COD_ERP","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"PRIE0_CHAVES_CTRT":{"logical":"CONTRATOS DA PROPRIEDADE","physical":"PRIE0_CHAVES_CTRT","type":"text","pk":false,"sequence":false,"nullable":true},"PRIE0_TXT_CTRT":{"logical":"SAIDA ERP CONTRATO","physical":"PRIE0_TXT_CTRT","type":"text","pk":false,"sequence":false,"nullable":false},"PRIE0_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PRIE0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PRIE0_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PRIE0_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PRIE0_TXT_FAZ":{"logical":"SAIDA ERP FAZENDA","physical":"PRIE0_TXT_FAZ","type":"text","pk":false,"sequence":false,"nullable":false}},"position":[100,7100]},"TB6_INTR":{"logical":"INTERACAO","columns":{"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"INTR6_DAT_INTR":{"logical":"DATA INTERACAO","physical":"INTR6_DAT_INTR","type":"date","pk":false,"sequence":false,"nullable":false},"INTR6_ASSUNTO":{"logical":"ASSUNTO INTERACAO","physical":"INTR6_ASSUNTO","type":"text","pk":false,"sequence":false,"nullable":true},"FNCQ6_COD":{"logical":"ID FUNIL","physical":"FNCQ6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_FNCQ","column":"FNCQ6_COD"}},"INTR6_COD_CRM":{"logical":"CODIGO CRM","physical":"INTR6_COD_CRM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"INTR6_GEOM":{"logical":"COORDENADA","physical":"INTR6_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"INTR6_ENDE":{"logical":"ENDERECO","physical":"INTR6_ENDE","type":"text","pk":false,"sequence":false,"nullable":true},"INTR6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"INTR6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"INTR6_DAT_HR_MOD":{"logical":"DATA ULTIMA MODIFICACAO","physical":"INTR6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"INTR6_INSUC":{"logical":"INSUCESSO","physical":"INTR6_INSUC","type":"bit","pk":false,"sequence":false,"nullable":true},"TIIN6_COD":{"logical":"CODIGO TIPO","physical":"TIIN6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_TIIN","column":"TIIN6_COD"}},"INTR6_FT":{"logical":"INDICADOR DE FORCA TAREFA","physical":"INTR6_FT","type":"bit","pk":false,"sequence":false,"nullable":true}},"position":[300,7100]},"TB6_ACIN":{"logical":"ACOMPANHANTE INTERACAO","columns":{"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"ACIN6_OBS":{"logical":"OBSERVACOES ACOMPANHANTE","physical":"ACIN6_OBS","type":"text","pk":false,"sequence":false,"nullable":true},"ACIN6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ACIN6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ACIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ACIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,7100]},"TB6_PJCQ":{"logical":"PROJETO CONQUISTA","columns":{"PJCQ6_COD":{"logical":"ID PROJETO CONQUISTA","physical":"PJCQ6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PJCQ6_NOM":{"logical":"NOME PROJETO","physical":"PJCQ6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"PJCQ6_DES":{"logical":"DESCRICAO","physical":"PJCQ6_DES","type":"text","pk":false,"sequence":false,"nullable":true},"PJCQ6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PJCQ6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PJCQ6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PJCQ6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PJCQ6_DAT_HR_FIM":{"logical":"DATA CONCLUSAO","physical":"PJCQ6_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"PJCQ6_GEOM":{"logical":"GEOMETRIA CONQUISTA","physical":"PJCQ6_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}}},"position":[700,7100]},"TB6_FNCQ":{"logical":"FUNIL DE CONQUISTA","columns":{"FNCQ6_COD":{"logical":"ID FUNIL","physical":"FNCQ6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"FNCQ6_NOM":{"logical":"NOME","physical":"FNCQ6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"FNCQ6_DES":{"logical":"DESCRICAO","physical":"FNCQ6_DES","type":"text","pk":false,"sequence":false,"nullable":true},"FNCQ6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"FNCQ6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"FNCQ6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"FNCQ6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"FNCQ6_DAT_HR_FIM":{"logical":"DATA CONCLUSAO","physical":"FNCQ6_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"FNCQ6_GEOM":{"logical":"GEOMETRIA CONQUISTA","physical":"FNCQ6_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PJCQ6_COD":{"logical":"ID PROJETO CONQUISTA","physical":"PJCQ6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_PJCQ","column":"PJCQ6_COD"}},"FNCQ6_COD_PAI":{"logical":"ID FUNIL PAI","physical":"FNCQ6_COD_PAI","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_FNCQ","column":"FNCQ6_COD"},"phrase":"FILHO","inverse":"PAI"},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}}},"position":[900,7100]},"TB2_ARUS":{"logical":"AREA DO USUARIO","columns":{"ARUS2_COD":{"logical":"CODIGO DA AREA","physical":"ARUS2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ARUS2_NOM":{"logical":"NOME DA AREA","physical":"ARUS2_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ARUS2_GEOM":{"logical":"GEOMETRIA","physical":"ARUS2_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"ARUS2_PUB":{"logical":"COMPARTILHADA","physical":"ARUS2_PUB","type":"bit","pk":false,"sequence":false,"nullable":false},"ARUS2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ARUS2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ARUS2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ARUS2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,7300]},"TB6_ATIN":{"logical":"ATRIBUTO INTERACAO","columns":{"ATIN6_COD":{"logical":"ID ATRIBUTO","physical":"ATIN6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATIN6_NOM":{"logical":"NOME ATRIBUTO","physical":"ATIN6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ATIN6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"ATIN6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"TDAT6_COD":{"logical":"ID TIPO DADO","physical":"TDAT6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_TDAT","column":"TDAT6_COD"}},"ATIN6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATIN6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,7300]},"TB6_VATI":{"logical":"VALOR ATRIBUTO INTERACAO","columns":{"ATIN6_COD":{"logical":"ID ATRIBUTO","physical":"ATIN6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATIN","column":"ATIN6_COD"}},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"VATI6_VAL":{"logical":"VALOR","physical":"VATI6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"VATI6_COMPL":{"logical":"COMPLEMENTO","physical":"VATI6_COMPL","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"VATI6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VATI6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"VATI6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VATI6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,7300]},"TB6_PCRM":{"logical":"PESSOA CRM","columns":{"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":false,"sequence":false,"nullable":true},"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":false,"sequence":false,"nullable":true},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":false,"sequence":false,"nullable":true},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PCRM6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PCRM6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PCRM6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PCRM6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,7300]},"TB6_IMIN":{"logical":"IMAGEM INTERACAO","columns":{"IMIN6_COD":{"logical":"ID IMAGEM","physical":"IMIN6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"IMIN6_PATH":{"logical":"CAMINHO","physical":"IMIN6_PATH","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"IMIN6_DES":{"logical":"DESCRICAO","physical":"IMIN6_DES","type":"text","pk":false,"sequence":false,"nullable":true},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"IMIN6_ID":{"logical":"ID GDRIVE","physical":"IMIN6_ID","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"IMIN6_FILENAME":{"logical":"NOME ARQUIVO","physical":"IMIN6_FILENAME","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"IMIN6_NOM":{"logical":"TITULO","physical":"IMIN6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"IMIN6_GEOM":{"logical":"GEOMETRIA","physical":"IMIN6_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":14},"IMIN6_JSON":{"logical":"DADOS ADICIONAIS","physical":"IMIN6_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"IMIN6_GD_CACHE":{"logical":"CACHE GDRIVE","physical":"IMIN6_GD_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"IMIN6_DAT_UPLOAD":{"logical":"DATA UPLOAD","physical":"IMIN6_DAT_UPLOAD","type":"date","pk":false,"sequence":false,"nullable":true},"IMIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"IMIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,7300]},"TB6_DOIN":{"logical":"DOCUMENTO INTERACAO","columns":{"DOIN6_COD":{"logical":"ID DOCUMENTO","physical":"DOIN6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"DOIN6_TIPO":{"logical":"TIPO","physical":"DOIN6_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"DOIN6_DES":{"logical":"DESCRICAO","physical":"DOIN6_DES","type":"text","pk":false,"sequence":false,"nullable":true},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"DOIN6_COD_ARQ":{"logical":"CODIGO ARQUIVO BIG","physical":"DOIN6_COD_ARQ","type":"integer","pk":false,"sequence":false,"nullable":true},"DOIN6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"DOIN6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"DOIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"DOIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,7500]},"TB6_PROC":{"logical":"PROPRIEDADE CRM","columns":{"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PROC6_NOM":{"logical":"NOME PROPRIEDADE","physical":"PROC6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"PROC6_DAT_PROP_BIG":{"logical":"DATA VINCULACAO PROPRIEDADE BIG","physical":"PROC6_DAT_PROP_BIG","type":"date","pk":false,"sequence":false,"nullable":true},"PROC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PROC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PROC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PROC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"PROC6_GD_ID":{"logical":"GDRIVE ID","physical":"PROC6_GD_ID","type":"text","pk":false,"sequence":false,"nullable":true},"PROC6_CACHE":{"logical":"CACHE","physical":"PROC6_CACHE","type":"text","pk":false,"sequence":false,"nullable":true}},"position":[300,7500]},"TB6_ATPR":{"logical":"ATRIBUTO PROPRIEDADE","columns":{"ATPR6_COD":{"logical":"ID ATRIBUTO","physical":"ATPR6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATPR6_NOM":{"logical":"NOME ATRIBUTO","physical":"ATPR6_NOM","type":"text","pk":false,"sequence":false,"nullable":false},"ATPR6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"ATPR6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"TDAT6_COD":{"logical":"ID TIPO DADO","physical":"TDAT6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_TDAT","column":"TDAT6_COD"}},"ATPR6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATPR6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATPR6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATPR6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,7500]},"TB6_VAPR":{"logical":"VALOR ATRIBUTO PROPRIEDADE","columns":{"ATPR6_COD":{"logical":"ID ATRIBUTO","physical":"ATPR6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATPR","column":"ATPR6_COD"}},"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"VAPR6_VAL":{"logical":"VALOR","physical":"VAPR6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"ATPR6_COMPL":{"logical":"COMPLEMENTO","physical":"ATPR6_COMPL","type":"text","pk":false,"sequence":false,"nullable":true},"VAPR6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VAPR6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"VAPR6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VAPR6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,7500]},"TB6_PEIN":{"logical":"PESSOA INTERACAO","columns":{"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"PEIN6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PEIN6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PEIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PEIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,7500]},"TB6_HPEC":{"logical":"HISTORICO PESSOA CRM","columns":{"HPEC6_COD":{"logical":"ID HISTORICO","physical":"HPEC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HPEC6_VAL":{"logical":"ALTERACOES","physical":"HPEC6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"ATPC6_COD":{"logical":"ID ATRIBUTO","physical":"ATPC6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_ATPC","column":"ATPC6_COD"}},"HPEC6_DAT_ORIG":{"logical":"DATA ORIGINAL","physical":"HPEC6_DAT_ORIG","type":"date","pk":false,"sequence":false,"nullable":false},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HPEC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HPEC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HPEC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HPEC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,7700]},"TB2_EMPJ":{"logical":"EMAIL PJ","columns":{"EMPJ2_COD":{"logical":"CODIGO","physical":"EMPJ2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"EMPJ2_EMAIL":{"logical":"EMAIL","physical":"EMPJ2_EMAIL","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"}},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"}},"EMPJ2_VALIDO":{"logical":"INDICADOR DE EMAIL VALIDADO","physical":"EMPJ2_VALIDO","type":"bit","pk":false,"sequence":false,"nullable":true},"EMPJ2_DESC":{"logical":"DESCRICAO","physical":"EMPJ2_DESC","type":"text","pk":false,"sequence":false,"nullable":true},"EMPJ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"EMPJ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"EMPJ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"EMPJ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,7700]},"TB6_ATPC":{"logical":"ATRIBUTO PESSOA","columns":{"ATPC6_COD":{"logical":"ID ATRIBUTO","physical":"ATPC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATPC6_NOM":{"logical":"NOME ATRIBUTO","physical":"ATPC6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ATPC6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"ATPC6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"TDAT6_COD":{"logical":"ID TIPO DADO","physical":"TDAT6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_TDAT","column":"TDAT6_COD"}},"ATPC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATPC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATPC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATPC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,7700]},"TB2_EMPF":{"logical":"EMAIL PF","columns":{"EMPF2_COD":{"logical":"CODIGO","physical":"EMPF2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"EMPF2_EMAIL":{"logical":"EMAIL","physical":"EMPF2_EMAIL","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"EMPF2_VALIDO":{"logical":"INDICADOR DE EMAIL VALIDADO","physical":"EMPF2_VALIDO","type":"bit","pk":false,"sequence":false,"nullable":true},"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PFIS","column":"PFIS2_COD"}},"EMPF2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"EMPF2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"EMPF2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"EMPF2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,7700]},"TB6_VAPC":{"logical":"VALOR ATRIBUTO PESSOA","columns":{"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"ATPC6_COD":{"logical":"ID ATRIBUTO","physical":"ATPC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATPC","column":"ATPC6_COD"}},"VAPC6_VAL":{"logical":"VALOR","physical":"VAPC6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"VAPC6_COMPL":{"logical":"COMPLEMENTO","physical":"VAPC6_COMPL","type":"text","pk":false,"sequence":false,"nullable":true},"VAPC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VAPC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"VAPC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VAPC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,7700]},"TB2_SOPJ":{"logical":"SOCIO PJ","columns":{"SOPJ2_COD":{"logical":"CODIGO","physical":"SOPJ2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"}},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"}},"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_PFIS","column":"PFIS2_COD"}},"SOPJ2_RL":{"logical":"REPRESENTANTE LEGAL","physical":"SOPJ2_RL","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"SOPJ2_ATRIB":{"logical":"ATRIBUICAO","physical":"SOPJ2_ATRIB","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"SOPJ2_PAIS_ORIG":{"logical":"PAIS ORIGEM","physical":"SOPJ2_PAIS_ORIG","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"SOPJ2_DAT_ENTR":{"logical":"DATA ENTRADA","physical":"SOPJ2_DAT_ENTR","type":"date","pk":false,"sequence":false,"nullable":true},"SOPJ2_CNPJ":{"logical":"CNPJ DO SOCIO","physical":"SOPJ2_CNPJ","type":"bigint","pk":false,"sequence":false,"nullable":false},"PJUR2_CNPJ_SOCIO":{"logical":"RAIZ CNPJ DO SOCIO","physical":"PJUR2_CNPJ_SOCIO","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"},"phrase":"EH SOCIA PJ EM","inverse":"TEM COMO SOCIO A"},"PJUR2_SEQ_SOCIO":{"logical":"SEQUENCIAL DO SOCIO","physical":"PJUR2_SEQ_SOCIO","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"},"phrase":"EH SOCIA PJ EM","inverse":"TEM COMO SOCIO A"},"SOPJ2_RS":{"logical":"RAZAO SOCIAL","physical":"SOPJ2_RS","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"SOPJ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SOPJ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SOPJ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SOPJ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,7900]},"TB6_HPRC":{"logical":"HISTORICO PROPRIEDADE CRM","columns":{"HPRC6_COD":{"logical":"ID HISTORICO","physical":"HPRC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HPRC6_VAL":{"logical":"VALOR ORIGINAL","physical":"HPRC6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"ATPR6_COD":{"logical":"ID ATRIBUTO","physical":"ATPR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_ATPR","column":"ATPR6_COD"}},"HPRC6_DAT_ORIG":{"logical":"DATA ORIGINAL","physical":"HPRC6_DAT_ORIG","type":"date","pk":false,"sequence":false,"nullable":false},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HPRC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HPRC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HPRC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HPRC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,7900]},"TB2_TSPJ":{"logical":"TELEFONE SOCIO PJ","columns":{"SOPJ2_COD":{"logical":"CODIGO","physical":"SOPJ2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_SOPJ","column":"SOPJ2_COD"}},"TELE2_COD":{"logical":"CODIGO DO TELEFONE","physical":"TELE2_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB2_TELE","column":"TELE2_COD"}},"TSPJ2_DESC":{"logical":"DESCRICAO","physical":"TSPJ2_DESC","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"TSPJ2_DAT_ALO":{"logical":"DATA DO ULTIMO ALO","physical":"TSPJ2_DAT_ALO","type":"date","pk":false,"sequence":false,"nullable":true},"TSPJ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TSPJ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TSPJ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TSPJ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,7900]},"TB6_LAIN":{"logical":"LOOKUP ATRIBUTO INTERACAO","columns":{"LAIN6_TXT":{"logical":"TEXTO","physical":"LAIN6_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"LAIN6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"LAIN6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"ATIN6_COD":{"logical":"ID ATRIBUTO","physical":"ATIN6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATIN","column":"ATIN6_COD"}},"LAIN6_ORD":{"logical":"ORDEM","physical":"LAIN6_ORD","type":"integer","pk":true,"sequence":false,"nullable":false},"LAIN6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"LAIN6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LAIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LAIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,7900]},"TB2_ESPJ":{"logical":"EMAIL SOCIO PJ","columns":{"ESPJ2_COD":{"logical":"CODIGO EMAIL","physical":"ESPJ2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ESPJ2_EMAIL":{"logical":"EMAIL","physical":"ESPJ2_EMAIL","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"SOPJ2_COD":{"logical":"CODIGO","physical":"SOPJ2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_SOPJ","column":"SOPJ2_COD"}},"ESPJ2_VALIDO":{"logical":"INDICADOR DE EMAIL VALIDADO","physical":"ESPJ2_VALIDO","type":"bit","pk":false,"sequence":false,"nullable":true},"ESPJ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ESPJ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ESPJ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ESPJ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,7900]},"TB6_LATP":{"logical":"LOOKUP ATRIBUTO PESSOA","columns":{"LATP6_TXT":{"logical":"TEXTO","physical":"LATP6_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"ATPC6_COD":{"logical":"ID ATRIBUTO","physical":"ATPC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATPC","column":"ATPC6_COD"}},"LATP6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"LATP6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"LATP6_ORD":{"logical":"ORDEM","physical":"LATP6_ORD","type":"integer","pk":true,"sequence":false,"nullable":false},"LATP6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"LATP6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LATP6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LATP6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,8100]},"TB6_LAPR":{"logical":"LOOKUP ATRIBUTO PROPRIEDADE","columns":{"LAPR6_TXT":{"logical":"TEXTO","physical":"LAPR6_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"ATPR6_COD":{"logical":"ID ATRIBUTO","physical":"ATPR6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATPR","column":"ATPR6_COD"}},"LAPR6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"LAPR6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"LAPR6_ORD":{"logical":"ORDEM","physical":"LAPR6_ORD","type":"integer","pk":true,"sequence":false,"nullable":false},"LAPR6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"LAPR6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LAPR6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LAPR6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,8100]},"TB6_PCPR":{"logical":"PESSOA CRM PROPRIEDADE","columns":{"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"PCPR6_REL":{"logical":"DESCRICAO RELACIONAMENTO","physical":"PCPR6_REL","type":"text","pk":false,"sequence":false,"nullable":true},"PCPR6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PCPR6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PCPR6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PCPR6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,8100]},"TB2_FUPJ":{"logical":"FUNCIONARIO PJ","columns":{"FUPJ2_COD":{"logical":"CODIGO","physical":"FUPJ2_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"FUPJ2_CARGO":{"logical":"CARGO","physical":"FUPJ2_CARGO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":400},"PJUR2_CNPJ":{"logical":"RAIZ CNPJ","physical":"PJUR2_CNPJ","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_CNPJ"}},"PJUR2_SEQ":{"logical":"SEQUENCIAL","physical":"PJUR2_SEQ","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PJUR","column":"PJUR2_SEQ"}},"PFIS2_COD":{"logical":"CODIGO DA PESSOA FISICA","physical":"PFIS2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_PFIS","column":"PFIS2_COD"}},"FUPJ2_DAT_ADM":{"logical":"DATA ADMISSAO","physical":"FUPJ2_DAT_ADM","type":"date","pk":false,"sequence":false,"nullable":true},"FUPJ2_DAT_DEM":{"logical":"DATA DEMISSAO","physical":"FUPJ2_DAT_DEM","type":"date","pk":false,"sequence":false,"nullable":true},"FUPJ2_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"FUPJ2_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"FUPJ2_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"FUPJ2_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,8100]},"TB6_TDAT":{"logical":"TIPO DADO ATRIBUTO","columns":{"TDAT6_COD":{"logical":"ID TIPO DADO","physical":"TDAT6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TDAT6_NOM":{"logical":"NOME TIPO DADO","physical":"TDAT6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TDAT6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TDAT6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"TDAT6_CONF":{"logical":"CONFIGURACAO","physical":"TDAT6_CONF","type":"text","pk":false,"sequence":false,"nullable":true},"TDAT6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TDAT6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TDAT6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TDAT6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,8100]},"TB6_PRIN":{"logical":"PROPRIEDADE INTERACAO","columns":{"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"PRIN6_TXT":{"logical":"COMENTARIOS","physical":"PRIN6_TXT","type":"text","pk":false,"sequence":false,"nullable":true},"PRIN6_PRIN":{"logical":"PRINCIPAL","physical":"PRIN6_PRIN","type":"bit","pk":false,"sequence":false,"nullable":true},"PRIN6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PRIN6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PRIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PRIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,8300]},"TB6_CTRC":{"logical":"CONTRATO CRM","columns":{"CTRC6_COD":{"logical":"ID CONTRATO CRM","physical":"CTRC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"CTRC6_NOM":{"logical":"NOME CONTRATO","physical":"CTRC6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"CTRC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"CTRC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CTRT1_COD":{"logical":"ID DO CONTRATO","physical":"CTRT1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CTRT","column":"CTRT1_COD"}},"TCTB1_COD":{"logical":"CODIGO TIPO CONTRATO BIG","physical":"TCTB1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_TCTB","column":"TCTB1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CTRC6_DAT_CTRT":{"logical":"DATA ASSOCIACAO CONTRATO","physical":"CTRC6_DAT_CTRT","type":"date","pk":false,"sequence":false,"nullable":true},"CTRC6_CACHE":{"logical":"CACHE","physical":"CTRC6_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"CTRC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CTRC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,8300]},"TB6_ATCC":{"logical":"ATRIBUTO CONTRATO","columns":{"ATCC6_COD":{"logical":"ID ATRIBUTO","physical":"ATCC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATCC6_NOM":{"logical":"NOME ATRIBUTO","physical":"ATCC6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"ATCC6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"ATCC6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"TDAT6_COD":{"logical":"ID TIPO DADO","physical":"TDAT6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_TDAT","column":"TDAT6_COD"}},"ATCC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATCC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATCC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATCC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,8300]},"TB6_VACC":{"logical":"VALOR ATRIBUTO CONTRATO","columns":{"VACC6_VAL":{"logical":"VALOR","physical":"VACC6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"VACC6_COMPL":{"logical":"COMPLEMENTO","physical":"VACC6_COMPL","type":"text","pk":false,"sequence":false,"nullable":true},"ATCC6_COD":{"logical":"ID ATRIBUTO","physical":"ATCC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATCC","column":"ATCC6_COD"}},"CTRC6_COD":{"logical":"ID CONTRATO CRM","physical":"CTRC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_CTRC","column":"CTRC6_COD"}},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"VACC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VACC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"VACC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VACC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,8300]},"TB6_LATC":{"logical":"LOOKUP ATRIBUTO CONTRATO","columns":{"LATC6_ORD":{"logical":"ORDEM","physical":"LATC6_ORD","type":"integer","pk":true,"sequence":false,"nullable":false},"LATC6_TXT":{"logical":"TEXTO","physical":"LATC6_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"LATC6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"LATC6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"ATCC6_COD":{"logical":"ID ATRIBUTO","physical":"ATCC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATCC","column":"ATCC6_COD"}},"LATC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"LATC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LATC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LATC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,8300]},"TB6_PCTC":{"logical":"PROPRIEDADE CONTRATO CRM","columns":{"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"CTRC6_COD":{"logical":"ID CONTRATO CRM","physical":"CTRC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_CTRC","column":"CTRC6_COD"}},"PCTC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PCTC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PCTC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PCTC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,8500]},"TB6_HCTC":{"logical":"HISTORICO CONTRATO CRM","columns":{"HCTC6_COD":{"logical":"ID HISTORICO","physical":"HCTC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HCTC6_VAL":{"logical":"VALOR ORIGINAL","physical":"HCTC6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"HCTC6_DAT_ORIG":{"logical":"DATA ORIGINAL","physical":"HCTC6_DAT_ORIG","type":"date","pk":false,"sequence":false,"nullable":false},"CTRC6_COD":{"logical":"ID CONTRATO CRM","physical":"CTRC6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_CTRC","column":"CTRC6_COD"}},"ATCC6_COD":{"logical":"ID ATRIBUTO","physical":"ATCC6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_ATCC","column":"ATCC6_COD"}},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HCTC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HCTC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HCTC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HCTC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,8500]},"TB6_RESC":{"logical":"RESISTENCIA CRM","columns":{"RESC6_COD":{"logical":"ID RESISTENCIA CRM","physical":"RESC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"RESC6_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"RESC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"RESC6_DAT_HR_FIM":{"logical":"DATA ENCERRAMENTO","physical":"RESC6_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"RESC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"RESC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,8500]},"TB6_ATRE":{"logical":"ATRIBUTO RESISTENCIA","columns":{"ATRE6_COD":{"logical":"ID ATRIBUTO","physical":"ATRE6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ATRE6_NOM":{"logical":"NOME ATRIBUTO","physical":"ATRE6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"ATRE6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"ATRE6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"TDAT6_COD":{"logical":"ID TIPO DADO","physical":"TDAT6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_TDAT","column":"TDAT6_COD"}},"ATRE6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ATRE6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ATRE6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"ATRE6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,8500]},"TB6_VARE":{"logical":"VALOR ATRIBUTO RESISTENCIA","columns":{"ATRE6_COD":{"logical":"ID ATRIBUTO","physical":"ATRE6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATRE","column":"ATRE6_COD"}},"RESC6_COD":{"logical":"ID RESISTENCIA CRM","physical":"RESC6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_RESC","column":"RESC6_COD"}},"VARE6_VAL":{"logical":"VALOR","physical":"VARE6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"VARE6_COMPL":{"logical":"COMPLEMENTO","physical":"VARE6_COMPL","type":"text","pk":false,"sequence":false,"nullable":true},"VARE6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"VARE6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"VARE6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"VARE6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,8500]},"TB6_LARE":{"logical":"LOOKUP ATRIBUTO RESISTENCIA","columns":{"LARE6_TXT":{"logical":"TEXTO","physical":"LARE6_TXT","type":"text","pk":false,"sequence":false,"nullable":false},"LARE6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"LARE6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"LARE6_ORD":{"logical":"ORDEM","physical":"LARE6_ORD","type":"integer","pk":true,"sequence":false,"nullable":false},"ATRE6_COD":{"logical":"ID ATRIBUTO","physical":"ATRE6_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATRE","column":"ATRE6_COD"}},"LARE6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"LARE6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LARE6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"LARE6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,8700]},"TB6_HREC":{"logical":"HISTORICO RESISTENCIA CRM","columns":{"HREC6_COD":{"logical":"ID HISTORICO","physical":"HREC6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HREC6_VAL":{"logical":"VALOR ORIGINAL","physical":"HREC6_VAL","type":"text","pk":false,"sequence":false,"nullable":false},"HREC6_DAT_ORIG":{"logical":"DATA ORIGINAL","physical":"HREC6_DAT_ORIG","type":"date","pk":false,"sequence":false,"nullable":false},"RESC6_COD":{"logical":"ID RESISTENCIA CRM","physical":"RESC6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_RESC","column":"RESC6_COD"}},"ATRE6_COD":{"logical":"ID ATRIBUTO","physical":"ATRE6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_ATRE","column":"ATRE6_COD"}},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HREC6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HREC6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HREC6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HREC6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,8700]},"TB6_INTE":{"logical":"INTERVENCAO","columns":{"INTE6_COD":{"logical":"CODIGO INTERVENCAO","physical":"INTE6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"INTE6_DAT_HR_INI":{"logical":"DATA DA INTERVENCAO","physical":"INTE6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"INTE6_DES":{"logical":"DESCRICAO","physical":"INTE6_DES","type":"text","pk":false,"sequence":false,"nullable":true},"INTE6_TIPO":{"logical":"TIPO","physical":"INTE6_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"PROC6_COD":{"logical":"ID PROPRIEDADE CRM","physical":"PROC6_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB6_PROC","column":"PROC6_COD"}},"PCRM6_COD":{"logical":"ID PESSOA CRM","physical":"PCRM6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_PCRM","column":"PCRM6_COD"}},"RESC6_COD":{"logical":"ID RESISTENCIA CRM","physical":"RESC6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_RESC","column":"RESC6_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"USER2_COD_INTE":{"logical":"CODIGO USUARIO INTERVEIO","physical":"USER2_COD_INTE","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"INTERVEIO","inverse":"PELO USUARIO"},"INTE6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"INTE6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[500,8700]},"TB1_RTPR":{"logical":"ROTA PROPRIEDADE","columns":{"RTPR1_COD":{"logical":"CODIGO ROTA","physical":"RTPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"RTPR1_GEOM":{"logical":"GEOMETRIA","physical":"RTPR1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"RTPR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"RTPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"INTR6_COD":{"logical":"CODIGO INTERACAO","physical":"INTR6_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB6_INTR","column":"INTR6_COD"}},"RTPR1_DES":{"logical":"DESCRICAO","physical":"RTPR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"RTPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"RTPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,8700]},"TB1_CARP":{"logical":"CAR PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"CARP1_COD":{"logical":"CODIGO DO CAR","physical":"CARP1_COD","type":"text","pk":false,"sequence":false,"nullable":true},"CARP1_DAT_HR_INI":{"logical":"DATA INICIO","physical":"CARP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"CARP1_DAT_REC":{"logical":"DATA RECIBO","physical":"CARP1_DAT_REC","type":"date","pk":false,"sequence":false,"nullable":true},"CARP1_DAT_HR_FIM":{"logical":"DATA FINAL","physical":"CARP1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"CARP1_CACHE_DOCS":{"logical":"CACHE DOCUMENTOS","physical":"CARP1_CACHE_DOCS","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_COD0":{"logical":"ID DA PROPRIEDADE MAE","physical":"PROP1_COD0","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PROP","column":"PROP1_COD"},"phrase":"MAE DA","inverse":"FILHA DA"},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"CARP1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"CARP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,8700]},"TB1_IGCA":{"logical":"INFORMACAO GEOGRAFICA CAR","columns":{"TIGC1_COD":{"logical":"CODIGO DO TIPO DE INFORMACAO","physical":"TIGC1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_TIGC","column":"TIGC1_COD"}},"IGCA1_COM":{"logical":"COMENTARIO DA INFO","physical":"IGCA1_COM","type":"text","pk":false,"sequence":false,"nullable":true},"IGCA1_DAT_HR_INI":{"logical":"DATA UPLOAD","physical":"IGCA1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"IGCA1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"IGCA1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PCAR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCAR","column":"PCAR1_COD"}},"IGCA1_COD":{"logical":"ID DA INFORMACAO","physical":"IGCA1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"IGCA1_SRID":{"logical":"SRID ORIGINAL","physical":"IGCA1_SRID","type":"integer","pk":false,"sequence":false,"nullable":false},"IGCA1_GDRIVE_ID":{"logical":"ID GDRIVE","physical":"IGCA1_GDRIVE_ID","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100}},"position":[100,8900]},"TB1_TIGC":{"logical":"TIPO INFORMACAO GEOGRAFICA CAR","columns":{"TIGC1_COD":{"logical":"CODIGO DO TIPO DE INFORMACAO","physical":"TIGC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TIGC1_NOM":{"logical":"NOME DA INFO","physical":"TIGC1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TIGC1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TIGC1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"TIGC1_JSON":{"logical":"CONFIGURACAO","physical":"TIGC1_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"TIGC1_ORD":{"logical":"ORDEM","physical":"TIGC1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"TIGC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TIGC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TIGC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TIGC1_DAT_HR_MOD","type":"integer","pk":false,"sequence":false,"nullable":true}},"position":[300,8900]},"TB1_SCAR":{"logical":"STATUS CAR","columns":{"SCAR1_COD":{"logical":"CODIGO STATUS","physical":"SCAR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"SCAR1_NOM":{"logical":"NOME STATUS","physical":"SCAR1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"SCAR1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"SCAR1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"SCAR1_JSON":{"logical":"CONFIGURACAO","physical":"SCAR1_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"SCAR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"SCAR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"SCAR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"SCAR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"SCAR1_ORD":{"logical":"ORDEM","physical":"SCAR1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false}},"position":[500,8900]},"TB1_HCAR":{"logical":"HISTORICO CAR","columns":{"HCAR1_COD":{"logical":"ID HISTORICO","physical":"HCAR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HCAR1_DAT_HR_INI":{"logical":"DATA ALTERACAO","physical":"HCAR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HCAR1_DES":{"logical":"DESCRICAO","physical":"HCAR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"SCAR1_COD":{"logical":"CODIGO STATUS","physical":"SCAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_SCAR","column":"SCAR1_COD"}},"TIGC1_COD":{"logical":"CODIGO DO TIPO DE INFORMACAO","physical":"TIGC1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_TIGC","column":"TIGC1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_CARP","column":"PROP1_COD"}},"PCAR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PCAR","column":"PCAR1_COD"}},"HCAR1_JSON":{"logical":"JSON DA ALTERACAO","physical":"HCAR1_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"HCAR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HCAR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,8900]},"TB1_PCAR":{"logical":"PROCESSO CAR","columns":{"PCAR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCAR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PCAR1_COD_CAR":{"logical":"CODIGO CAR","physical":"PCAR1_COD_CAR","type":"text","pk":false,"sequence":false,"nullable":false},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"SCAR1_COD":{"logical":"CODIGO STATUS","physical":"SCAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_SCAR","column":"SCAR1_COD"}},"PCAR1_DAT_HR_INI":{"logical":"DATA INICIO","physical":"PCAR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PCAR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PCAR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"PCAR1_DAT_REC":{"logical":"DATA RECIBO","physical":"PCAR1_DAT_REC","type":"date","pk":false,"sequence":false,"nullable":true},"PCAR1_DAT_HR_FIM":{"logical":"DATA FINAL","physical":"PCAR1_DAT_HR_FIM","type":"date","pk":false,"sequence":false,"nullable":true},"PCAR1_DOC_PROP":{"logical":"DOCUMENTO PROPRIEDADE DISPONIVEL","physical":"PCAR1_DOC_PROP","type":"bit","pk":false,"sequence":false,"nullable":false},"PCAR1_DOC_ID":{"logical":"DOCUMENTO PROPRIEDADE","physical":"PCAR1_DOC_ID","type":"text","pk":false,"sequence":false,"nullable":true},"PCAR1_ATI":{"logical":"PROCESSO ATIVO","physical":"PCAR1_ATI","type":"bit","pk":false,"sequence":false,"nullable":false},"PCAR1_PROCUR":{"logical":"NECESSITA PROCURACAO","physical":"PCAR1_PROCUR","type":"bit","pk":false,"sequence":false,"nullable":true},"PCAR1_PROC_OFIC":{"logical":"INDICADOR DE PROCESSO OFICIAL","physical":"PCAR1_PROC_OFIC","type":"bit","pk":false,"sequence":false,"nullable":true},"PCAR1_TIPO":{"logical":"TIPO DO PROCESSO","physical":"PCAR1_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":10},"PCAR1_NOM":{"logical":"TITULO DO PROCESSO","physical":"PCAR1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"PCAR1_REF":{"logical":"REFERENCIA DO PROCESSO","physical":"PCAR1_REF","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":18},"PCAR1_PRA":{"logical":"POSSUI PRA","physical":"PCAR1_PRA","type":"bit","pk":false,"sequence":false,"nullable":true},"PCAR1_PRAD":{"logical":"POSSUI PRAD","physical":"PCAR1_PRAD","type":"bit","pk":false,"sequence":false,"nullable":true},"PCAR1_DAT_PROT":{"logical":"DATA DO PROTOCOLO","physical":"PCAR1_DAT_PROT","type":"date","pk":false,"sequence":false,"nullable":true},"PCAR1_EQUIP":{"logical":"EQUIPAMENTO MEDICAO","physical":"PCAR1_EQUIP","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":200},"STCC1_COD":{"logical":"ID SITUACAO","physical":"STCC1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STCC","column":"STCC1_COD"}},"PCAR1_ID_CEFIR":{"logical":"ID CEFIR","physical":"PCAR1_ID_CEFIR","type":"integer","pk":false,"sequence":false,"nullable":true}},"position":[900,8900]},"TB1_PRPC":{"logical":"PROPRIETARIO PROCESSO CAR","columns":{"PCAR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCAR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCAR","column":"PCAR1_COD"}},"PRIO1_COD":{"logical":"ID DO PROPRIETARIO","physical":"PRIO1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PRIO","column":"PRIO1_COD"}},"PRPC1_DAT_HR_INI":{"logical":"DATA DA CRIACAO","physical":"PRPC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"PRPC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PRPC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[100,9100]},"TB1_FICA":{"logical":"FEICAO INFORMACAO CAR","columns":{"FICA1_COD":{"logical":"ID DA FEICAO","physical":"FICA1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"FICA1_GEOM":{"logical":"GEOMETRIA","physical":"FICA1_GEOM","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":100},"FICA1_DADOS":{"logical":"DADOS DA GEOMETRIA","physical":"FICA1_DADOS","type":"text","pk":false,"sequence":false,"nullable":true},"IGCA1_COD":{"logical":"ID DA INFORMACAO","physical":"IGCA1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_IGCA","column":"IGCA1_COD"}},"FICA1_DIM":{"logical":"DIMENSAO","physical":"FICA1_DIM","type":"double precision","pk":false,"sequence":false,"nullable":true},"FICA1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"FICA1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"FICA1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"FICA1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,9100]},"TB1_TPRC":{"logical":"TIPO PROTOCOLO CAR","columns":{"TPRC1_COD":{"logical":"CODIGO TIPO PROTOCOLO","physical":"TPRC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TPRC1_NOM":{"logical":"NOME","physical":"TPRC1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TPRC1_DES":{"logical":"DESCRICAO","physical":"TPRC1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TPRC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TPRC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TPRC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TPRC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TPRC1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TPRC1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20}},"position":[500,9100]},"TB1_TPPR":{"logical":"TIPO PROTOCOLO PROCESSO","columns":{"PCAR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCAR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCAR","column":"PCAR1_COD"}},"TPRC1_COD":{"logical":"CODIGO TIPO PROTOCOLO","physical":"TPRC1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_TPRC","column":"TPRC1_COD"}},"TPPR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TPPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TPPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TPPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,9100]},"TB1_PCAP":{"logical":"PROCESSOS CAR PROPRIEDADE","columns":{"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_CARP","column":"PROP1_COD"}},"PCAR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCAR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCAR","column":"PCAR1_COD"}},"PCAP1_REF":{"logical":"REFERENCIA","physical":"PCAP1_REF","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":30},"PCAP1_DAT_HR_INI":{"logical":"DATA INICIO","physical":"PCAP1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PCAP1_NOM":{"logical":"TITULO PROCESSO","physical":"PCAP1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":400},"PCAP1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PCAP1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,9100]},"TB1_RRACM":{"logical":"RESSALVA RECUSA ARQUIVO COMPLEXO","columns":{"RRACM1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"RRACM1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ARQU_COMPL1_COD":{"logical":"ID ARQUIVO","physical":"ARQU_COMPL1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_ARQU_COMPL","column":"ARQU_COMPL1_COD"}},"MORR1_COD":{"logical":"ID MOTIVO","physical":"MORR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_MORR","column":"MORR1_COD"}}},"position":[100,9300]},"TB1_ARQU_PROJ":{"logical":"ARQUIVO PROJETO","columns":{"ARQU_PROJ1_COD":{"logical":"ID ARQUIVO","physical":"ARQU_PROJ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ARQU_PROJ1_TIT":{"logical":"TITULO","physical":"ARQU_PROJ1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"ARQU_PROJ1_DES":{"logical":"DESCRICAO","physical":"ARQU_PROJ1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_NOM":{"logical":"NOME","physical":"ARQU_PROJ1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"ARQU_PROJ1_DAT_UPLOAD":{"logical":"DATA UPLOAD","physical":"ARQU_PROJ1_DAT_UPLOAD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_DAT_MOD":{"logical":"DATA MODIFICACAO","physical":"ARQU_PROJ1_DAT_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_ID":{"logical":"ID GDRIVE","physical":"ARQU_PROJ1_ID","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"ARQU_PROJ1_DAT_ULT_SINC":{"logical":"DATA ULTIMA SINCRONIA","physical":"ARQU_PROJ1_DAT_ULT_SINC","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_DAT_VER":{"logical":"DATA VERIFICACAO","physical":"ARQU_PROJ1_DAT_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_ST_VERIF":{"logical":"STATUS VERIFICACAO","physical":"ARQU_PROJ1_ST_VERIF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"ARQU_PROJ1_DAT_VAL_VER":{"logical":"DATA VALIDADE VERIFICACAO","physical":"ARQU_PROJ1_DAT_VAL_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_DAT_RECU":{"logical":"DATA RECUSA","physical":"ARQU_PROJ1_DAT_RECU","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_MOT_RECU":{"logical":"MOTIVO RECUSA","physical":"ARQU_PROJ1_MOT_RECU","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_GD_CACHE":{"logical":"CACHE GDRIVE","physical":"ARQU_PROJ1_GD_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_DEL":{"logical":"EXCLUIDO","physical":"ARQU_PROJ1_DEL","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ARQU_PROJ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO REGISTRO","physical":"ARQU_PROJ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_ST_NOM":{"logical":"STATUS NOMENCLATURA","physical":"ARQU_PROJ1_ST_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"PROJ1_COD":{"logical":"ID DO PROJETO","physical":"PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROJ","column":"PROJ1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"STVARQ1_COD":{"logical":"CODIGO STATUS VERIFICACAO","physical":"STVARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STVARQ","column":"STVARQ1_COD"}},"PAAR1_COD":{"logical":"CODIGO DO PADRAO","physical":"PAAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PAAR","column":"PAAR1_COD"}},"USER2_COD_VERIF":{"logical":"USUARIO VERIFICADOR","physical":"USER2_COD_VERIF","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"VERIFICOU","inverse":"VERIFICADO POR"}},"position":[300,9300]},"TB0_LAPJ":{"logical":"LOG ARQUIVO PROJETO","columns":{"LAPJ0_COD":{"logical":"CODIGO LOG","physical":"LAPJ0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LAPJ0_DAT_HR_INI":{"logical":"DATA","physical":"LAPJ0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LAPJ0_CAMPO":{"logical":"CAMPO ALTERADO","physical":"LAPJ0_CAMPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"LAPJ0_ORIG":{"logical":"VALOR ORIGINAL","physical":"LAPJ0_ORIG","type":"text","pk":false,"sequence":false,"nullable":true},"LAPJ0_DES":{"logical":"DESCRICAO","physical":"LAPJ0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"LAPJ0_DADO":{"logical":"DADOS ADICIONAIS","physical":"LAPJ0_DADO","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PROJ1_COD":{"logical":"CODIGO ARQUIVO","physical":"ARQU_PROJ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false}},"position":[500,9300]},"TB1_RRAPJ":{"logical":"RESSALVA RECUSA ARQUIVO PROJETO","columns":{"RRAPJ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"RRAPJ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ARQU_PROJ1_COD":{"logical":"ID ARQUIVO","physical":"ARQU_PROJ1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_ARQU_PROJ","column":"ARQU_PROJ1_COD"}},"MORR1_COD":{"logical":"ID MOTIVO","physical":"MORR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_MORR","column":"MORR1_COD"}}},"position":[700,9300]},"TB1_ARQU_PARQ":{"logical":"ARQUIVO PARQUE","columns":{"ARQU_PARQ1_COD":{"logical":"ID ARQUIVO","physical":"ARQU_PARQ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ARQU_PARQ1_TIT":{"logical":"TITULO","physical":"ARQU_PARQ1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"ARQU_PARQ1_DES":{"logical":"DESCRICAO","physical":"ARQU_PARQ1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_NOM":{"logical":"NOME","physical":"ARQU_PARQ1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"ARQU_PARQ1_DAT_UPLOAD":{"logical":"DATA UPLOAD","physical":"ARQU_PARQ1_DAT_UPLOAD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_DAT_MOD":{"logical":"DATA MODIFICACAO","physical":"ARQU_PARQ1_DAT_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_ID":{"logical":"ID GDRIVE","physical":"ARQU_PARQ1_ID","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"ARQU_PARQ1_DAT_ULT_SINC":{"logical":"DATA ULTIMA SINCRONIA","physical":"ARQU_PARQ1_DAT_ULT_SINC","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_DAT_VER":{"logical":"DATA VERIFICACAO","physical":"ARQU_PARQ1_DAT_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_ST_VERIF":{"logical":"STATUS VERIFICACAO","physical":"ARQU_PARQ1_ST_VERIF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"ARQU_PARQ1_DAT_VAL_VER":{"logical":"DATA VALIDADE VERIFICACAO","physical":"ARQU_PARQ1_DAT_VAL_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_DAT_RECU":{"logical":"DATA RECUSA","physical":"ARQU_PARQ1_DAT_RECU","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_MOT_RECU":{"logical":"MOTIVO RECUSA","physical":"ARQU_PARQ1_MOT_RECU","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_GD_CACHE":{"logical":"CACHE GDRIVE","physical":"ARQU_PARQ1_GD_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_DEL":{"logical":"EXCLUIDO","physical":"ARQU_PARQ1_DEL","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ARQU_PARQ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO REGISTRO","physical":"ARQU_PARQ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_ST_NOM":{"logical":"STATUS NOMENCLATURA","physical":"ARQU_PARQ1_ST_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"STVARQ1_COD":{"logical":"CODIGO STATUS VERIFICACAO","physical":"STVARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STVARQ","column":"STVARQ1_COD"}},"PAAR1_COD":{"logical":"CODIGO DO PADRAO","physical":"PAAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PAAR","column":"PAAR1_COD"}},"USER2_COD_VERIF":{"logical":"CODIGO USUARIO VERIFICADOR","physical":"USER2_COD_VERIF","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"VERIFICOU","inverse":"VERIFICADO POR"}},"position":[900,9300]},"TB0_LAPQ":{"logical":"LOG ARQUIVO_PARQUE","columns":{"LAPQ0_COD":{"logical":"CODIGO LOG","physical":"LAPQ0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LAPQ0_DAT_HR_INI":{"logical":"DATA","physical":"LAPQ0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LAPQ0_CAMPO":{"logical":"CAMPO ALTERADO","physical":"LAPQ0_CAMPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"LAPQ0_ORIG":{"logical":"VALOR ORIGINAL","physical":"LAPQ0_ORIG","type":"text","pk":false,"sequence":false,"nullable":true},"LAPQ0_DES":{"logical":"DESCRICAO","physical":"LAPQ0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"LAPQ0_DADO":{"logical":"DADOS ADICIONAIS","physical":"LAPQ0_DADO","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_PARQ1_COD":{"logical":"CODIGO ARQUIVO","physical":"ARQU_PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false}},"position":[100,9500]},"TB1_RRAPQ":{"logical":"RESSALVA RECUSA ARQUIVO PARQUE","columns":{"RRAPQ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"RRAPQ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"ARQU_PARQ1_COD":{"logical":"ID ARQUIVO","physical":"ARQU_PARQ1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_ARQU_PARQ","column":"ARQU_PARQ1_COD"}},"MORR1_COD":{"logical":"ID MOTIVO","physical":"MORR1_COD","type":"integer","pk":true,"sequence":false,"nullable":false,"fk":{"table":"TB1_MORR","column":"MORR1_COD"}}},"position":[300,9500]},"TB1_ARQU_COMPL":{"logical":"ARQUIVO COMPLEXO","columns":{"ARQU_COMPL1_COD":{"logical":"ID ARQUIVO","physical":"ARQU_COMPL1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"ARQU_COMPL1_TIT":{"logical":"TITULO","physical":"ARQU_COMPL1_TIT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":500},"ARQU_COMPL1_DES":{"logical":"DESCRICAO","physical":"ARQU_COMPL1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_NOM":{"logical":"NOME","physical":"ARQU_COMPL1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":500},"ARQU_COMPL1_DAT_UPLOAD":{"logical":"DATA UPLOAD","physical":"ARQU_COMPL1_DAT_UPLOAD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_DAT_MOD":{"logical":"DATA MODIFICACAO","physical":"ARQU_COMPL1_DAT_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_ID":{"logical":"ID GDRIVE","physical":"ARQU_COMPL1_ID","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":100},"ARQU_COMPL1_DAT_ULT_SINC":{"logical":"DATA ULTIMA SINCRONIA","physical":"ARQU_COMPL1_DAT_ULT_SINC","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_DAT_VER":{"logical":"DATA VERIFICACAO","physical":"ARQU_COMPL1_DAT_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_ST_VERIF":{"logical":"STATUS VERIFICACAO","physical":"ARQU_COMPL1_ST_VERIF","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":20},"ARQU_COMPL1_DAT_VAL_VER":{"logical":"DATA VALIDADE VERIFICACAO","physical":"ARQU_COMPL1_DAT_VAL_VER","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_DAT_RECU":{"logical":"DATA RECUSA","physical":"ARQU_COMPL1_DAT_RECU","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_MOT_RECU":{"logical":"MOTIVO RECUSA","physical":"ARQU_COMPL1_MOT_RECU","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_GD_CACHE":{"logical":"CACHE GDRIVE","physical":"ARQU_COMPL1_GD_CACHE","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_DEL":{"logical":"EXCLUIDO","physical":"ARQU_COMPL1_DEL","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"ARQU_COMPL1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO REGISTRO","physical":"ARQU_COMPL1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_ST_NOM":{"logical":"STATUS NOMENCLATURA","physical":"ARQU_COMPL1_ST_NOM","type":"text","pk":false,"sequence":false,"nullable":true},"COMPL1_COD":{"logical":"CODIGO DO COMPLEXO","physical":"COMPL1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_COMPL","column":"COMPL1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"STVARQ1_COD":{"logical":"CODIGO STATUS VERIFICACAO","physical":"STVARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_STVARQ","column":"STVARQ1_COD"}},"PAAR1_COD":{"logical":"CODIGO DO PADRAO","physical":"PAAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB1_PAAR","column":"PAAR1_COD"}},"USER2_COD_VERIF":{"logical":"USUARIO VERIFICADOR","physical":"USER2_COD_VERIF","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"VERIFICOU","inverse":"VERIFICADO POR"}},"position":[500,9500]},"TB0_LACM":{"logical":"LOG ARQUIVO_COMPLEXO","columns":{"LACM0_COD":{"logical":"CODIGO LOG","physical":"LACM0_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"LACM0_DAT_HR_INI":{"logical":"DATA","physical":"LACM0_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"LACM0_CAMPO":{"logical":"CAMPO ALTERADO","physical":"LACM0_CAMPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"LACM0_ORIG":{"logical":"VALOR ORIGINAL","physical":"LACM0_ORIG","type":"text","pk":false,"sequence":false,"nullable":true},"LACM0_DES":{"logical":"DESCRICAO","physical":"LACM0_DES","type":"text","pk":false,"sequence":false,"nullable":true},"LACM0_DADO":{"logical":"DADOS ADICIONAIS","physical":"LACM0_DADO","type":"text","pk":false,"sequence":false,"nullable":true},"ARQU_COMPL1_COD":{"logical":"CODIGO ARQUIVO","physical":"ARQU_COMPL1_COD","type":"integer","pk":false,"sequence":false,"nullable":false}},"position":[700,9500]},"TB1_STCC":{"logical":"SITUACAO CENTRAL CAR","columns":{"STCC1_COD":{"logical":"ID SITUACAO","physical":"STCC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STCC1_NOM":{"logical":"NOME SITUACAO","physical":"STCC1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STCC1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STCC1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STCC1_JSON":{"logical":"DEFINICOES JSON","physical":"STCC1_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"STCC1_ORD":{"logical":"ORDEM","physical":"STCC1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STCC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STCC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STCC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STCC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[900,9500]},"TB6_TIIN":{"logical":"TIPO INTERACAO","columns":{"TIIN6_COD":{"logical":"CODIGO TIPO","physical":"TIIN6_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TIIN6_NOM":{"logical":"NOME","physical":"TIIN6_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TIIN6_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TIIN6_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"TIIN6_JSON":{"logical":"PARAMETROS JSON","physical":"TIIN6_JSON","type":"text","pk":false,"sequence":false,"nullable":true},"TIIN6_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TIIN6_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TIIN6_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TIIN6_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TIIN6_ORD":{"logical":"ORDEM","physical":"TIIN6_ORD","type":"integer","pk":false,"sequence":false,"nullable":false}},"position":[100,9700]},"TB1_PPRC":{"logical":"PROTOCOLO DO PROCESSO CAR","columns":{"PPRC1_COD":{"logical":"ID PROTOCOLO","physical":"PPRC1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"PPRC1_NUM":{"logical":"NUMERO","physical":"PPRC1_NUM","type":"text","pk":false,"sequence":false,"nullable":true},"PPRC1_COMENT":{"logical":"COMENTARIO","physical":"PPRC1_COMENT","type":"text","pk":false,"sequence":false,"nullable":true},"PPRC1_DAT_PROT":{"logical":"DATA PROTOCOLO","physical":"PPRC1_DAT_PROT","type":"date","pk":false,"sequence":false,"nullable":true},"PPRC1_DAT_CONC":{"logical":"DATA CONCLUSAO","physical":"PPRC1_DAT_CONC","type":"date","pk":false,"sequence":false,"nullable":true},"PPRC1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"PPRC1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"PPRC1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"PPRC1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TPRC1_COD":{"logical":"CODIGO TIPO PROTOCOLO","physical":"TPRC1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_TPRC","column":"TPRC1_COD"}},"PCAR1_COD":{"logical":"CODIGO PROCESSO","physical":"PCAR1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PCAR","column":"PCAR1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"ABRIU","inverse":"ABERTO POR"},"USER2_COD_CONC":{"logical":"CODIGO USUARIO CONCLUIU","physical":"USER2_COD_CONC","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"},"phrase":"CONCLUIU","inverse":"CONCLUIDO POR"}},"position":[300,9700]},"TB1_HIPR":{"logical":"HISTORICO PROPRIEDADE","columns":{"HIPR1_COD":{"logical":"ID HISTORICO","physical":"HIPR1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HIPR1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HIPR1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HIPR1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HIPR1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"HIPR1_CAMPO":{"logical":"CAMPO","physical":"HIPR1_CAMPO","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":50},"HIPR1_DES":{"logical":"DESCRICAO","physical":"HIPR1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"HIPR1_VAL_ORIG":{"logical":"VALOR ORIGINAL","physical":"HIPR1_VAL_ORIG","type":"text","pk":false,"sequence":false,"nullable":true},"HIPR1_VAL_NOVO":{"logical":"NOVO VALOR","physical":"HIPR1_VAL_NOVO","type":"text","pk":false,"sequence":false,"nullable":true},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":true,"fk":{"table":"TB2_USER","column":"USER2_COD"}}},"position":[500,9700]},"TB1_STBE":{"logical":"STATUS BENFEITORIA","columns":{"STBE1_COD":{"logical":"CODIGO STATUS BENFEITORIA","physical":"STBE1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"STBE1_NOM":{"logical":"NOME","physical":"STBE1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"STBE1_DES":{"logical":"DESCRICAO","physical":"STBE1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"STBE1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"STBE1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"STBE1_ORD":{"logical":"ORDEM","physical":"STBE1_ORD","type":"integer","pk":false,"sequence":false,"nullable":false},"STBE1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"STBE1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"STBE1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"STBE1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[700,9700]},"TB1_TDFT":{"logical":"TIPO DEMANDA FT","columns":{"TDFT1_COD":{"logical":"CODIGO TIPO DEMANDA","physical":"TDFT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TDFT1_NOM":{"logical":"NOME","physical":"TDFT1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TDFT1_DES":{"logical":"DESCRICAO","physical":"TDFT1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TDFT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TDFT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TDFT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TDFT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TDFT1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TDFT1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"TAFT1_COD":{"logical":"CODIGO TIPO ATUACAO","physical":"TAFT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_TAFT","column":"TAFT1_COD"}}},"position":[900,9700]},"TB1_TAFT":{"logical":"TIPO ATUACAO FT","columns":{"TAFT1_COD":{"logical":"CODIGO TIPO ATUACAO","physical":"TAFT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"TAFT1_NOM":{"logical":"NOME","physical":"TAFT1_NOM","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":200},"TAFT1_DES":{"logical":"DESCRICAO","physical":"TAFT1_DES","type":"text","pk":false,"sequence":false,"nullable":true},"TAFT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"TAFT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"TAFT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"TAFT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"TAFT1_COD_TXT":{"logical":"CODIGO TEXTO","physical":"TAFT1_COD_TXT","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20}},"position":[100,9900]},"TB1_DPFT":{"logical":"DEMANDA PROPRIEDADE FT","columns":{"DPFT1_COD":{"logical":"CODIGO DEMANDA","physical":"DPFT1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"DPFT1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"DPFT1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"DPFT1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"DPFT1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"DPFT1_ENC":{"logical":"INDICADOR ENCAMINHAMENTO","physical":"DPFT1_ENC","type":"bit","pk":false,"sequence":false,"nullable":true},"PROP1_COD":{"logical":"ID DA PROPRIEDADE","physical":"PROP1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PROP","column":"PROP1_COD"}},"TDFT1_COD":{"logical":"CODIGO TIPO DEMANDA","physical":"TDFT1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_TDFT","column":"TDFT1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"DPFT1_DAT_CONC":{"logical":"DATA CONCLUSAO","physical":"DPFT1_DAT_CONC","type":"date","pk":false,"sequence":false,"nullable":true},"DPFT1_DAT_PREV_CONCL":{"logical":"DATA PREVISTA CONCLUSAO","physical":"DPFT1_DAT_PREV_CONCL","type":"date","pk":false,"sequence":false,"nullable":true}},"position":[300,9900]},"TB1_HIPQ":{"logical":"HISTORICO PARQUE","columns":{"HIPQ1_COD":{"logical":"CODIGO HISTORICO","physical":"HIPQ1_COD","type":"integer","pk":true,"sequence":true,"nullable":false},"HIPQ1_DES":{"logical":"DESCRICAO","physical":"HIPQ1_DES","type":"text","pk":false,"sequence":false,"nullable":false},"HIPQ1_DAT_HR_INI":{"logical":"DATA CRIACAO","physical":"HIPQ1_DAT_HR_INI","type":"date","pk":false,"sequence":false,"nullable":false},"HIPQ1_DAT_HR_MOD":{"logical":"DATA MODIFICACAO","physical":"HIPQ1_DAT_HR_MOD","type":"date","pk":false,"sequence":false,"nullable":true},"HIPQ1_TIPO":{"logical":"TIPO","physical":"HIPQ1_TIPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":20},"PARQ1_COD":{"logical":"ID DO PARQUE","physical":"PARQ1_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB1_PARQ","column":"PARQ1_COD"}},"USER2_COD":{"logical":"CODIGO USUARIO","physical":"USER2_COD","type":"integer","pk":false,"sequence":false,"nullable":false,"fk":{"table":"TB2_USER","column":"USER2_COD"}},"HIPQ1_CAMPO":{"logical":"CAMPO","physical":"HIPQ1_CAMPO","type":"varchar","pk":false,"sequence":false,"nullable":false,"size":50},"HIPQ1_GEOM_ANT":{"logical":"GEOMETRIA ANTERIOR","physical":"HIPQ1_GEOM_ANT","type":"varchar","pk":false,"sequence":false,"nullable":true,"size":132},"HIPQ1_VAL_ANT":{"logical":"VALOR ANTERIOR","physical":"HIPQ1_VAL_ANT","type":"text","pk":false,"sequence":false,"nullable":true}},"position":[500,9900]}};