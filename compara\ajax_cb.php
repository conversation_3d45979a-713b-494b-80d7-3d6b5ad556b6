<?
include_once('../codebase/function/func_geramodelos.inc');
$a=$_REQUEST['a'];
if (empty($a)) $a=$_REQUEST['ajax'];
$host=$_POST['s'];
$pers=$_POST['pe'];
$type=$_POST['t'];
$user=$_POST['u'];
$pass=$_POST['p'];
$name=$_POST['n'];
$host=$_REQUEST['s'];
$pers=$_REQUEST['pe'];
$type=$_REQUEST['t'];
$user=$_REQUEST['u'];
$pass=$_REQUEST['p'];
$ops=$_REQUEST['o'];
$name=$_REQUEST['n'];
$debug=false;
if ($_REQUEST['debug']=='true') $debug=true;
require_once('func_comparabanco.inc');
session_write_close();
switch($a){
	case 'sqlGo':
		$sql=$_REQUEST['sql'];
		$resp=executaSql($sql);
		if ($resp===true){
			hhtml();
			echo 'OK';
			exit;
		}else{
			hjs();
			echo 'showMsg("'.addslashes($resp).'");';
			exit;
		}
		break;
	case 'ignoreTables':
		$resp=ignoreTables();
		if ($resp===true){
			hhtml();
			echo 'OK';
			exit;
		}else{
			hjs();
			echo 'showMsg("'.addslashes($resp).'");';
			exit;
		}
		break;
	case 'ignoreSeqs':
		$resp=ignoreSeqs();
		if ($resp===true){
			hhtml();
			echo 'OK';
			exit;
		}else{
			hjs();
			echo 'showMsg("'.addslashes($resp).'");';
			exit;
		}
		break;
	case 'ignoreCols':
		$resp=ignoreCols();
		if ($resp===true){
			hhtml();
			echo 'OK';
			exit;
		}else{
			hjs();
			echo 'showMsg("'.addslashes($resp).'");';
			exit;
		}
		break;
	case 'compara':
		doCompara();
		break;
}
function doCompara(){
	require_once('func_comparabanco.inc');
	comparaViaAjax();
}
function hjs(){
	header('Cache-Control: no-cache, must-revalidate');
	header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
	header('Content-type: application/javascript; charset=utf-8');
}
function hhtml(){
	header('Content-Type: text/html; charset=utf-8');
}
function &getDbCompara(){
	global $host, $pers, $type, $user, $pass, $name, $ops;
	//include_once('func_allsites.inc');
	$umDB=(object)null;
	$umDB->configDbType=$type;
	$umDB->configDbPers=$pers;
	$umDB->configDbServer=$host;
	$umDB->configDbUser=$user;
	$umDB->configDbPass=$pass;
	$umDB->configDbName=$name;
	$umDB->configDbOptions=$ops;
	if (!($umDB->configDbType && $umDB->configDbUser && $umDB->configDbPass)){// && $umDB->configDbServer
		echo("Dados incompletos para a base<br>");
		return;
	}
	if (!$db=getConexao($umDB)){
		return false;
	}else{
		return $db;
	}
}
function executaSql($sql){
	$db=getDbCompara();
	$sqlSP='set search_path to app,public;';
	$db->ExecuteBind($sqlSP, array());
	if ((!is_array($sql)) && strpos($sql, '<user>')!==false){
		$sql=explode('<user>', $_REQUEST['sql']);
	}
	$semErro=true;
	if (is_array($sql)){
		$db->BeginTrans();
		//echo nl2br(print_r($sql, true))."<br>";exit;
		foreach($sql as $s){
			$s=trim($s);
			if ($s=='' || $s==';')continue;
			if (strpos($s, '<alt>')!==false){
				$altSql=explode('<alt>', $s);
				$altPassa=false;
				$msg="";
				$msgSuccess="";
				foreach($altSql as $k=>$sql){
					if (substr($sql, -1)==';') $sql=substr($sql, 0, -1);
					if ($resp=$db->ExecuteBind($sql, array())){
						$altPassa=true;
						$msgSuccess.="sql ".($k+1).": ".preg_replace('/\s/', ' ', $db->ErrorMsg())." - ";
					}else{
						$msg.="sql ".($k+1).": ".preg_replace('/\s/', ' ', $db->ErrorMsg())." - ";
					}
				}
				if ($altPassa){
					return true;
				}else{
					return $msg;
				}
			}else{
				if (substr($s, -1)==';') $s=substr($s, 0, -1);
				if (!$resp=$db->ExecuteBind($s, array())){
					$msg=$s.preg_replace('/\s/', ' ', $db->ErrorMsg());
					$db->RollbackTrans();
					return 'Erro: '.$msg;
				}
			}
		}
		$db->CommitTrans();
	}else{
		if (strpos($sql, '<alt>')!==false){
			$altSql=explode('<alt>', $sql);
			$altPassa=false;
			$msg="";
			$msgSuccess="";
			foreach($altSql as $k=>$sql){
				if ($resp=$db->ExecuteBind($sql, array())){
					$altPassa=true;
					$msgSuccess.="sql ".($k+1).": ".preg_replace('/\s/', ' ', $db->ErrorMsg()).' - ';
				}else{
					$msg.="sql ".($k+1).": ".preg_replace('/\s/', ' ', $db->ErrorMsg()).' - ';
				}
			}
			if ($altPassa){
				return true;
			}else{
				return $msg;
			}
		}else{
			//echo $sql.'<br/>';
			//echo nl2br(print_r($db, true))."<br/>";
			if (substr($sql, -1)==';') $sql=substr($sql, 0, -1);
			if (!$resp=$db->ExecuteBind($sql, array())){
				$msg=preg_replace('/\s/', ' ', $db->ErrorMsg());
				$db->RollbackTrans();
				return 'Erro: '.$msg;
			}
		}
	}
	return true;
}
function ignoreTables(){
	$toIgnore=$_REQUEST['t'];
	$toIgnore=explode(',', $toIgnore);
	if (file_exists('ignoreTables.json')){
		$json=json_decode(file_get_contents('ignoreTables.json'), true);
	}else{
		$json=[];
	}
	$json=array_merge($json, $toIgnore);
	$json=array_unique($json);
	file_put_contents('ignoreTables.json', json_encode($json));
	return true;
}
function ignoreSeqs(){
	$toIgnore=$_REQUEST['t'];
	$toIgnore=explode(',', $toIgnore);
	if (file_exists('ignoreSeqs.json')){
		$json=json_decode(file_get_contents('ignoreSeqs.json'), true);
	}else{
		$json=[];
	}
	// echo nl2br(print_r($json, true))."<br/>";
	foreach($json as $ignore){
		// echo $ignore.' - '.(in_array($ignore, $toIgnore)?'Veio':'Não veio')." ignore<br/>";
		if (!in_array($ignore, $toIgnore)){
			// echo $ignore.' - '.array_search($ignore, $json)."<br/>";
			unset($json[array_search($ignore, $json)]);
		}
	}
	// echo nl2br(print_r($json, true))."<br/>";
	$json=array_merge($json, $toIgnore);
	$json=array_unique($json);
	file_put_contents('ignoreSeqs.json', json_encode($json));
	// echo nl2br(print_r($json, true))."<br/>";exit;
	return true;
}
function ignoreCols(){
	$toIgnore=$_REQUEST['t'];
	$toIgnore=explode(',', $toIgnore);
	if (file_exists('ignoreCols.json')){
		$json=json_decode(file_get_contents('ignoreCols.json'), true);
	}else{
		$json=[];
	}
	$json=array_merge($json, $toIgnore);
	$json=array_unique($json);
	file_put_contents('ignoreCols.json', json_encode($json));
	return true;
}
?>
