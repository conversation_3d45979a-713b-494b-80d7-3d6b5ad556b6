<?xml version="1.0"?>
<Modelo>
  <Entidades quantos="249">
    <Entidade NomeFisico="TB1_COMA" NomeLogico="COMARCA" id="E1000">
      <Campos quantos="5">
        <Campo NomeFisico="COMA1_COD" NomeLogico="ID COMARCA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COMA1_NOM" NomeLogico="NOME" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="MUNI1_COD" NomeLogico="CODIGO IBGE MUNICIPIO" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="COMA1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="COMA1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_GPCT" NomeLogico="GLEBA DE PROPRIEDADE NO CONTRATO" id="E1001">
      <Campos quantos="6">
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="GLEP1_COD" NomeLogico="ID DA GLEBA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="GPCT1_AREA_CONT" NomeLogico="AREA DA GLEBA NO CONTRATO" DataType="DOUBLE PRECISION" id="A10002"/>
        <Campo NomeFisico="GPCT1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="GPCT1_AREA_PARC_SAP" NomeLogico="AREA PARCIAL SAP" DataType="DOUBLE PRECISION" id="A10004"/>
        <Campo NomeFisico="GPCT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_DESE" NomeLogico="DESENVOLVEDOR" id="E1002">
      <Campos quantos="7">
        <Campo NomeFisico="DESE1_COD" NomeLogico="ID DO DESENVOLVEDOR" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="DESE1_NOM" NomeLogico="NOME DO DESENVOLVEDOR" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="ESCR1_COD" NomeLogico="ID DO ESCRITORIO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="DESE1_ATI" NomeLogico="ATIVO" DataType="BIT" id="A10004"/>
        <Campo NomeFisico="DESE1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="DESE1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_GRCM" NomeLogico="GRUPO DE CAMADAS" id="E1003">
      <Campos quantos="5">
        <Campo NomeFisico="GRCM3_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="GRCM3_NOM" NomeLogico="NOME DO GRUPO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="GRCM3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="GRCM3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="GRCM3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CMPT" NomeLogico="CAMADA MULTIPOINT" id="E1004">
      <Campos quantos="7">
        <Campo NomeFisico="CMPT3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CMPT3_NOM" NomeLogico="NOME DA CAMADA" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="CMPT3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="GRCM3_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="CMPT3_COD_TXT" NomeLogico="CODIGO TEXTO CAMADA" DataType="VARCHAR" NotNull="true" Size="100" id="A10004"/>
        <Campo NomeFisico="CMPT3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CMPT3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_PTCM" NomeLogico="MULTIPOINT DA CAMADA" id="E1005">
      <Campos quantos="6">
        <Campo NomeFisico="PTCM3_COD" NomeLogico="CODIGO DO MULTIPOINT" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PTCM3_NOM" NomeLogico="TITULO DA GEOMETRIA" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="PTCM3_GEOM" NomeLogico="GEOMETRIA" DataType="POINTZ" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CMPT3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="PTCM3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PTCM3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_ATPT" NomeLogico="ATRIBUTO DO MULTIPOINT" id="E1006">
      <Campos quantos="6">
        <Campo NomeFisico="ATPT3_COD" NomeLogico="CODIGO DO ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATPT3_NOM" NomeLogico="NOME DO ATRIBUTO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="CMPT3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="ATPT3_BUSCA" NomeLogico="INDICADOR DE ATRIBUTO BUSCAVEL" DataType="BIT" id="A10003"/>
        <Campo NomeFisico="ATPT3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATPT3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_VAPT" NomeLogico="VALOR DO ATRIBUTO NO MULTIPOINT" id="E1007">
      <Campos quantos="7">
        <Campo NomeFisico="ATPT3_COD" NomeLogico="CODIGO DO ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PTCM3_COD" NomeLogico="CODIGO DO MULTIPOINT" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="VAPT3_VAL_TXT" NomeLogico="VALOR TEXTO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="VAPT3_VAL_NUM" NomeLogico="VALOR NUMERICO" DataType="DOUBLE PRECISION" id="A10003"/>
        <Campo NomeFisico="VAPT3_VAL_DAT" NomeLogico="VALOR DATA" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="VAPT3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="VAPT3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CMLN" NomeLogico="CAMADA MULTILINE" id="E1008">
      <Campos quantos="7">
        <Campo NomeFisico="CMLN3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CMLN3_NOM" NomeLogico="NOME DA CAMADA" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="CMLN3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="CMLN3_COD_TXT" NomeLogico="CODIGO TEXTO DA CAMADA" DataType="VARCHAR" NotNull="true" Size="100" id="A10003"/>
        <Campo NomeFisico="GRCM3_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="CMLN3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CMLN3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_LNCM" NomeLogico="MULTILINE DA CAMADA" id="E1009">
      <Campos quantos="6">
        <Campo NomeFisico="LNCM3_COD" NomeLogico="CODIGO DO MULTILINE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LNCM3_NOM" NomeLogico="TITULO DA GEOMETRIA" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="LNCM3_GEOM" NomeLogico="GEOMETRIA" DataType="MULTILINESTRINGZ" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CMLN3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="LNCM3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LNCM3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_VALN" NomeLogico="VALOR DO ATRIBUTO NO MULTILINE" id="E1010">
      <Campos quantos="7">
        <Campo NomeFisico="VALN3_VAL_TXT" NomeLogico="VALOR TEXTO" DataType="TEXT" id="A10000"/>
        <Campo NomeFisico="VALN3_VAL_NUM" NomeLogico="VALOR NUMERICO" DataType="DOUBLE PRECISION" id="A10001"/>
        <Campo NomeFisico="VALN3_VAL_DAT" NomeLogico="VALOR DATA" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="LNCM3_COD" NomeLogico="CODIGO DO MULTILINE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ATLN3_COD" NomeLogico="CODIGO DO ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="VALN3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="VALN3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_ATLN" NomeLogico="ATRIBUTO DO MULTILINE" id="E1011">
      <Campos quantos="6">
        <Campo NomeFisico="ATLN3_COD" NomeLogico="CODIGO DO ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATLN3_NOM" NomeLogico="NOME DO ATRIBUTO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ATLN3_BUSCA" NomeLogico="INDICADOR DE ATRIBUTO BUSCAVEL" DataType="BIT" id="A10002"/>
        <Campo NomeFisico="CMLN3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ATLN3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATLN3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CMPL" NomeLogico="CAMADA MULTIPOLY" id="E1012">
      <Campos quantos="7">
        <Campo NomeFisico="CMPL3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CMPL3_NOM" NomeLogico="NOME DA CAMADA" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="CMPL3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="CMPL3_COD_TXT" NomeLogico="CODIGO TEXTO DA CAMADA" DataType="VARCHAR" NotNull="true" Size="100" id="A10003"/>
        <Campo NomeFisico="GRCM3_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="CMPL3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CMPL3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_PLCM" NomeLogico="MULTIPOLY DA CAMADA" id="E1013">
      <Campos quantos="6">
        <Campo NomeFisico="PLCM3_COD" NomeLogico="CODIGO DO MULTIPOLY" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PLCM3_NOM" NomeLogico="TITULO DA GEOMETRIA" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="PLCM3_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGONZ" id="A10002"/>
        <Campo NomeFisico="CMPL3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PLCM3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PLCM3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_VAPL" NomeLogico="VALOR DO ATRIBUTO NO MULTIPOLY" id="E1014">
      <Campos quantos="7">
        <Campo NomeFisico="VAPL3_VAL_TXT" NomeLogico="VALOR TEXTO" DataType="TEXT" id="A10000"/>
        <Campo NomeFisico="VAPL3_VAL_NUM" NomeLogico="VALOR NUMERICO" DataType="DOUBLE PRECISION" id="A10001"/>
        <Campo NomeFisico="VAPL3_VAL_DAT" NomeLogico="VALOR DATA" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PLCM3_COD" NomeLogico="CODIGO DO MULTIPOLY" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ATPL3_COD" NomeLogico="CODIGO DO ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="VAPL3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="VAPL3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_ATPL" NomeLogico="ATRIBUTO DO MULTIPOLY" id="E1015">
      <Campos quantos="6">
        <Campo NomeFisico="ATPL3_COD" NomeLogico="CODIGO DO ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATPL3_NOM" NomeLogico="NOME DO ATRIBUTO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ATPL3_BUSCA" NomeLogico="INDICADOR DE ATRIBUTO BUSCAVEL" DataType="BIT" id="A10002"/>
        <Campo NomeFisico="CMPL3_COD" NomeLogico="CODIGO DA CAMADA" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ATPL3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATPL3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STAG" NomeLogico="STATUS AEROGERADOR" id="E1016">
      <Campos quantos="6">
        <Campo NomeFisico="STAG1_COD" NomeLogico="CODIGO STATUS" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STAG1_NOM" NomeLogico="STATUS" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="STAG1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="STAG1_COD_ERP" NomeLogico="CODIGO ERP" DataType="VARCHAR" Size="100" id="A10003"/>
        <Campo NomeFisico="STAG1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STAG1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_LOAG" NomeLogico="LAYOUT DE AEROGERADORES" id="E1017">
      <Campos quantos="9">
        <Campo NomeFisico="LOAG1_COD" NomeLogico="CODIGO DO LAYOUT" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LOAG1_NOM" NomeLogico="NOME DO LAYOUT" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="LOAG1_ATI" NomeLogico="INDICADOR DE LAYOUT ATIVO" DataType="BIT" id="A10003"/>
        <Campo NomeFisico="LOAG1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="LOAG1_TIPO" NomeLogico="TIPO DE LAYOUT" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="COMPL1_COD" NomeLogico="CODIGO DO COMPLEXO" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="LOAG1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_COMPL" NomeLogico="COMPLEXO" id="E1018">
      <Campos quantos="15">
        <Campo NomeFisico="COMPL1_COD" NomeLogico="CODIGO DO COMPLEXO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COMPL1_NOM" NomeLogico="NOME DO COMPLEXO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="COMPL1_ABR" NomeLogico="ABREVIATURA" DataType="VARCHAR" Size="30" id="A10002"/>
        <Campo NomeFisico="COMPL1_GEOM" NomeLogico="GEOMETRIA DO LIMITE" DataType="POLYGON" id="A10003"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="COMPL1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="STCP1_COD" NomeLogico="CODIGO DO STATUS" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="COMPL1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="COMPL1_RTT70" NomeLogico="RTT 70 POR CENTO" DataType="BIT" NotNull="true" id="A10008"/>
        <Campo NomeFisico="COMPL1_RTT100" NomeLogico="RTT 100 POR CENTO" DataType="BIT" NotNull="true" id="A10009"/>
        <Campo NomeFisico="COMPL1_DAT_RTT70" NomeLogico="DATA RTT 70" DataType="DATE" id="A10010"/>
        <Campo NomeFisico="COMPL1_DAT_RTT100" NomeLogico="DATA RTT 100" DataType="DATE" id="A10011"/>
        <Campo NomeFisico="COMPL1_GD_ID" NomeLogico="GDRIVE ID" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="COMPL1_VISITA_ANO" NomeLogico="VISITAS ANO" DataType="INTEGER" id="A10013"/>
        <Campo NomeFisico="COMPL1_PERIOD_DIAS" NomeLogico="PERIODICIDADE VISITA" DataType="INTEGER" id="A10014"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_MUCO" NomeLogico="MUNICIPIO DA COMARCA" id="E1019">
      <Campos quantos="4">
        <Campo NomeFisico="COMA1_COD" NomeLogico="ID COMARCA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="MUNI1_COD" NomeLogico="CODIGO IBGE MUNICIPIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="MUCO1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="MUCO1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_COPR" NomeLogico="COMENTARIO DA PROPRIEDADE" id="E1020">
      <Campos quantos="9">
        <Campo NomeFisico="COPR1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COPR1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="COPR1_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="COPR1_FORM" NomeLogico="FORMATACAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="COPR1_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="COPR1_ATI" NomeLogico="COMENTARIO ATIVO" DataType="BIT" id="A10007"/>
        <Campo NomeFisico="COPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_ANUS" NomeLogico="ANOTACAO DO USUARIO" id="E1021">
      <Campos quantos="11">
        <Campo NomeFisico="ANUS2_COD" NomeLogico="CODIGO ANOTACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ANUS2_TIT" NomeLogico="TITULO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ANUS2_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="ANUS2_DAT_HAR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ANUS2_FORM" NomeLogico="FORMATACAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="ANUS2_GEOM" NomeLogico="GEOMETRIA" DataType="GEOMETRY" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="ANUS2_PUB" NomeLogico="PUBLICA" DataType="BIT" NotNull="true" id="A10007"/>
        <Campo NomeFisico="ANUS2_IMG" NomeLogico="IMAGEM" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="ANUS2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10009"/>
        <Campo NomeFisico="ANUS2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PERM" NomeLogico="PERMISSAO" id="E1022">
      <Campos quantos="6">
        <Campo NomeFisico="PERM2_COD" NomeLogico="CODIGO PERMISSAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PERM2_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="PERM2_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PERM2_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="50" id="A10003"/>
        <Campo NomeFisico="PERM2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PERM2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_HIPE" NomeLogico="HISTORICO PERMISSAO" id="E1023">
      <Campos quantos="6">
        <Campo NomeFisico="HIPE2_COD" NomeLogico="CODIGO HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HIPE2_DAT_HR" NomeLogico="DATA DA ALTERACAO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD_ATRIB" NomeLogico="CODIGO USUARIO ATRIBUIDOR" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="HIPE2_JSON" NomeLogico="DEFINICAO DA PERMISSAO" DataType="TEXT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="HIPE2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CAMA" NomeLogico="CAMADA DE INTERFACE" id="E1024">
      <Campos quantos="19">
        <Campo NomeFisico="CAMA3_COD" NomeLogico="ID DA CAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CAMA3_NOM" NomeLogico="NOME DA CAMADA" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="CAMA3_COD_TXT" NomeLogico="CODIGO TEXTO DA CAMADA" DataType="VARCHAR" NotNull="true" Size="50" id="A10002"/>
        <Campo NomeFisico="CAMA3_JSON" NomeLogico="DEFINICAO JSON DA CAMADA" DataType="TEXT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CAMA3_PUB" NomeLogico="INDICADOR DE CAMADA PUBLICA" DataType="BIT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CAMA3_ATI" NomeLogico="CAMADA ATIVA" DataType="BIT" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CAMA3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="CAMA3_URL" NomeLogico="URL FONTE" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="CAMA3_GRP" NomeLogico="GRUPO" DataType="VARCHAR" Size="50" id="A10008"/>
        <Campo NomeFisico="CAMA3_DAT_ULT_ATU" NomeLogico="DATA DA ULTIMA ATUALIZACAO" DataType="DATE" id="A10009"/>
        <Campo NomeFisico="CAMA3_RESP" NomeLogico="RESPONSAVEL PELA CAMADA" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="GRCA3_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="CAMA3_FONTE" NomeLogico="FONTE DOS DADOS DA CAMADA" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="CAMA3_SHOW_ATU" NomeLogico="INDICADOR DE EXIBICAO DA DATA DE ATUALIZACAO" DataType="BIT" id="A10013"/>
        <Campo NomeFisico="CAMA3_CQL" NomeLogico="FILTRO CQL DA CAMADA" DataType="TEXT" id="A10014"/>
        <Campo NomeFisico="PERM2_COD" NomeLogico="CODIGO PERMISSAO" DataType="INTEGER" id="A10015"/>
        <Campo NomeFisico="CAMA3_BUSCA" NomeLogico="INDICADOR DE CAMADA BUSCAVEL" DataType="BIT" id="A10016"/>
        <Campo NomeFisico="CAMA3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10017"/>
        <Campo NomeFisico="CAMA3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10018"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PUCA" NomeLogico="PERMISSAO USUARIO CAMADA" id="E1025">
      <Campos quantos="7">
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CAMA3_COD" NomeLogico="ID DA CAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PUCA2_EXIB" NomeLogico="EXIBIR" DataType="BIT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PUCA2_EDIT" NomeLogico="EDITAR" DataType="BIT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PUCA2_ADMIN" NomeLogico="ADMINISTRAR" DataType="BIT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PUCA2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PUCA2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_GRPM" NomeLogico="GRUPO DE PERMISSOES" id="E1026">
      <Campos quantos="6">
        <Campo NomeFisico="GRPM2_COD" NomeLogico="ID DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="GRPM2_NOM" NomeLogico="NOME DO GRUPO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="GRPM2_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="GRPM2_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="GRPM2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="GRPM2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PEGR" NomeLogico="PERMISSAO DO GRUPO" id="E1027">
      <Campos quantos="4">
        <Campo NomeFisico="GRPM2_COD" NomeLogico="ID DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PERM2_COD" NomeLogico="CODIGO PERMISSAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PEGR2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PEGR2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_GPUS" NomeLogico="GRUPO DE PERMISSOES DO USUARIO" id="E1028">
      <Campos quantos="4">
        <Campo NomeFisico="GRPM2_COD" NomeLogico="ID DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="GPUS2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="GPUS2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PGCA" NomeLogico="PERMISSAO GRUPO CAMADA" id="E1029">
      <Campos quantos="7">
        <Campo NomeFisico="GRPM2_COD" NomeLogico="ID DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CAMA3_COD" NomeLogico="ID DA CAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PGCA2_EXIB" NomeLogico="EXIBIR" DataType="BIT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PGCA2_EDIT" NomeLogico="EDITAR" DataType="BIT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PGCA2_ADMIN" NomeLogico="ADMINISTRAR" DataType="BIT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PGCA2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PGCA2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_POCO" NomeLogico="POLIGONO DE CONQUISTA" id="E1030">
      <Campos quantos="8">
        <Campo NomeFisico="POCO1_COD" NomeLogico="ID DO POLIGONO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="POCO1_NOM" NomeLogico="NOME DO POLIGONO" DataType="VARCHAR" NotNull="true" Size="500" id="A10001"/>
        <Campo NomeFisico="POCO1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="POCO1_GEOM" NomeLogico="GEOMETRIA" DataType="POLYGON" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="POCO1_ATI" NomeLogico="INDICADOR DE POLIGONO ATIVO" DataType="BIT" NotNull="true" id="A10005"/>
        <Campo NomeFisico="POCO1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="POCO1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_FIUS" NomeLogico="FILTRO DO USUARIO" id="E1031">
      <Campos quantos="6">
        <Campo NomeFisico="FIUS2_COD" NomeLogico="CODIGO DA CONFIGURACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="FIUS2_NOM" NomeLogico="NOME DA CONFIGURACAO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="FIUS2_JSON" NomeLogico="JSON" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="FIUS2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="FIUS2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PPCT" NomeLogico="PROPRIETARIO DO CONTRATO" id="E1032">
      <Campos quantos="9">
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PPCT1_COD_TIPO_ERP" NomeLogico="ID DO TIPO DE PROPRIETARIO NO ERP" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="PPCT1_TIPO_ERP" NomeLogico="TIPO DE PROPRIETARIO ERP" DataType="VARCHAR" Size="200" id="A10003"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PPCT1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="PPCT1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="PPCT1_DP" NomeLogico="DOCUMENTOS PESSOAIS" DataType="VARCHAR" Size="8" id="A10007"/>
        <Campo NomeFisico="PPCT1_DAT_INCL" NomeLogico="DATA DE INCLUSAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_VLOA" NomeLogico="VERSAO DO LAYOUT" id="E1033">
      <Campos quantos="5">
        <Campo NomeFisico="VLOA1_COD" NomeLogico="CODIGO DA VERSAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="VLOA1_NOM" NomeLogico="NOME DA VERSAO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="VLOA1_DAT_HR" NomeLogico="DATA DA VERSAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="LOAG1_COD" NomeLogico="CODIGO DO LAYOUT" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="VLOA1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CSCT" NomeLogico="CESSIONARIO DO CONTRATO" id="E1034">
      <Campos quantos="12">
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CSCT1_PERC" NomeLogico="PERCENTUAL" DataType="DOUBLE PRECISION" id="A10002"/>
        <Campo NomeFisico="CSCT1_DAT_INI_CES" NomeLogico="DATA INICIO CESSAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="CSCT1_COD_ERP" NomeLogico="CHAVE DO CESSIONARIO NO ERP" DataType="TEXT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CSCT1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="CSCT1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10006"/>
        <Campo NomeFisico="CSCT1_ATUAL" NomeLogico="CESSIONARIO ATUAL" DataType="BIT" NotNull="true" id="A10007"/>
        <Campo NomeFisico="CSCT1_NUM_SAP" NomeLogico="NUMERO SAP DO CONTRATO" DataType="VARCHAR" Size="100" id="A10008"/>
        <Campo NomeFisico="CSCT1_COD_ERP_DIV" NomeLogico="DIVISAO OU FILIAL" DataType="VARCHAR" Size="100" id="A10009"/>
        <Campo NomeFisico="CSCT1_JSON" NomeLogico="DADOS ADICIONAIS" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="CSCT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10011"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_MIDI" NomeLogico="MIDIABIG" id="E1035">
      <Campos quantos="14">
        <Campo NomeFisico="MIDI1_COD" NomeLogico="ID DA MIDIA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="MIDI1_NOM" NomeLogico="TITULO" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="MIDI1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="MIDI1_GEOM" NomeLogico="GEOMETRIA" DataType="GEOMETRY" id="A10003"/>
        <Campo NomeFisico="MIDI1_ARQ" NomeLogico="ARQUIVO ORIGINAL" DataType="TEXT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="MIDI1_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="MIDI1_DAT_REF" NomeLogico="DATA DE REFERENCIA" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="MIDI1_JSON" NomeLogico="CONFIGURACAO" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="ANUS2_COD" NomeLogico="CODIGO ANOTACAO" DataType="INTEGER" id="A10009"/>
        <Campo NomeFisico="MIDI1_ARQ_FIS" NomeLogico="ARQUIVO FISICO" DataType="TEXT" NotNull="true" id="A10010"/>
        <Campo NomeFisico="USER2_BLOQ" NomeLogico="USUARIO BLOQUEADOR" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="USER2_COD_DEL" NomeLogico="USUARIO EXCLUIDOR" DataType="INTEGER" id="A10012"/>
        <Campo NomeFisico="MIDI1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10013"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_COCT" NomeLogico="COMENTARIO DO CONTRATO" id="E1036">
      <Campos quantos="9">
        <Campo NomeFisico="COCT1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COCT1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="COCT1_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="COCT1_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="COCT1_FORM" NomeLogico="FORMATACAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="COCT1_ATI" NomeLogico="COMENTARIO ATIVO" DataType="BIT" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="COCT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LOGS" NomeLogico="LOG DE ALTERACOES" id="E1037">
      <Campos quantos="11">
        <Campo NomeFisico="LOGS0_COD" NomeLogico="CODIGO DO LOG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LOGS0_TAB" NomeLogico="TABELA" DataType="VARCHAR" Size="100" id="A10001"/>
        <Campo NomeFisico="LOGS0_DESCR" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="LOGS0_DAT_HR" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="LOGS0_JSON" NomeLogico="JSON DO VALOR ANTERIOR" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="USUARIO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="PESS2_COD" NomeLogico="PESSOA" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="LOGS0_IP" NomeLogico="ENDERECO IP" DataType="VARCHAR" Size="30" id="A10007"/>
        <Campo NomeFisico="LOGS0_COD_ALT" NomeLogico="ID DO REGISTRO ALTERADO" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="LOGS0_CAMPO" NomeLogico="CAMPO ALTERADO" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="LOGS0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_DEFU" NomeLogico="DESMEMBRAMENTO FUTURO" id="E1038">
      <Campos quantos="9">
        <Campo NomeFisico="DEFU1_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="DEFU1_NOM" NomeLogico="NOME DA PROPRIEDAE" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="DEFU1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGONZ" id="A10002"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="DEFU1_AREA" NomeLogico="AREA" DataType="DOUBLE PRECISION" id="A10004"/>
        <Campo NomeFisico="PROP1_COD_EXIST" NomeLogico="ID DE PROPRIEDADE EXISTENTE" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="DEFU1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="DEFU1_DAT_HR_PREV" NomeLogico="DATA PREVISTA EFETIVACAO" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="DEFU1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PRDF" NomeLogico="PROPRIETARIO DA DESMEMBRADA FUTURA" id="E1039">
      <Campos quantos="5">
        <Campo NomeFisico="DEFU1_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PRDF1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PRDF1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LDOM" NomeLogico="LOG DOMINIO" id="E1040">
      <Campos quantos="7">
        <Campo NomeFisico="LDOM0_COD" NomeLogico="CODIGO DO LOG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LDOM0_ANT" NomeLogico="ANTERIOR" DataType="INTEGER" id="A10001"/>
        <Campo NomeFisico="LDOM0_NOVO" NomeLogico="NOVO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="LDOM0_DAT_HR" NomeLogico="DATA DA MUDANCA" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="LDOM0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_CMCF" NomeLogico="COMPARTILHAMENTO DE CONFIGURACAO" id="E1041">
      <Campos quantos="8">
        <Campo NomeFisico="CMCF2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CMCF2_NOM" NomeLogico="NOME DA CONFIGURACAO" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD_DEST" NomeLogico="CODIGO USUARIO DESTINATARIO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="CMCF2_CONF" NomeLogico="CONFIGURACAO" DataType="TEXT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CMCF2_DAT_HR_INI" NomeLogico="DATA DO COMPARTILHAMENTO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CMCF2_OCULTA" NomeLogico="OCULTA" DataType="BIT" id="A10006"/>
        <Campo NomeFisico="CMCF2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_SUCA" NomeLogico="SUBCAMADA" id="E1042">
      <Campos quantos="12">
        <Campo NomeFisico="SUCA3_COD" NomeLogico="ID DA SUBCAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="SUCA3_NOM" NomeLogico="NOME DA SUBCAMADA" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="SUCA3_GEOM_LIM" NomeLogico="GEOMETRIA DO LIMITE" DataType="POLYGON" id="A10002"/>
        <Campo NomeFisico="SUCA3_ORD" NomeLogico="ORDEM NA CAMADA" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="SUCA3_ATI" NomeLogico="SUBCAMADA ATIVA" DataType="BIT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="SUCA3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="SUCA3_JSON" NomeLogico="DEFINICAO JSON DA SUBCAMADA" DataType="TEXT" NotNull="true" id="A10006"/>
        <Campo NomeFisico="SUCA3_URL" NomeLogico="URL FONTE" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="CAMA3_COD" NomeLogico="ID DA CAMADA" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="SUCA3_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" id="A10009"/>
        <Campo NomeFisico="SUCA3_DAT_HR_MOD" NomeLogico="DATA DE MODIFICACAO" DataType="DATE" id="A10010"/>
        <Campo NomeFisico="SUCA3_DAT_REF" NomeLogico="DATA DE REFERENCIA" DataType="DATE" id="A10011"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LGEO" NomeLogico="LOG GEO" id="E1043">
      <Campos quantos="7">
        <Campo NomeFisico="LGEO0_COD" NomeLogico="CODIGO DO LOG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10001"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="LGEO0_ANT" NomeLogico="ANTERIOR" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="LGEO0_NOVO" NomeLogico="NOVO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LGEO0_DAT_HR" NomeLogico="DATA DA MUDANCA" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="LGEO0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_GRPR" NomeLogico="GRUPO DE PROPRIETARIOS" id="E1044">
      <Campos quantos="6">
        <Campo NomeFisico="GRPR1_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="GRPR1_NOM" NomeLogico="NOME DO GRUPO" DataType="VARCHAR" NotNull="true" Size="500" id="A10001"/>
        <Campo NomeFisico="GRPR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="GRPR1_CVER" NomeLogico="INDICADOR CASA DOS VENTOS" DataType="BIT" id="A10003"/>
        <Campo NomeFisico="GRPR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="GRPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PRGR" NomeLogico="PROPRIETARIO DO GRUPO" id="E1045">
      <Campos quantos="4">
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="GRPR1_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRGR1_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PRGR1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TOCO" NomeLogico="TORRE CONCORRENCIA" id="E1046">
      <Campos quantos="12">
        <Campo NomeFisico="TOCO1_COD" NomeLogico="CODIGO DA TORRE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TOCO1_NOM" NomeLogico="NOME DA TORRE" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="TOCO1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TOCO1_GEOM" NomeLogico="GEOMETRIA" DataType="POINTZ" id="A10003"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="TOCO1_DAT_HR_INI" NomeLogico="DATA DA INCLUSAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="TOCO1_DAT_HR_FIM" NomeLogico="DATA DE REMOCAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="USER2_COD_REM" NomeLogico="CODIGO USUARIO REMOVEU" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="TOCO1_CONC" NomeLogico="CONCORRENTE" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="TOCO1_ALT" NomeLogico="ALTURA" DataType="DOUBLE PRECISION" id="A10009"/>
        <Campo NomeFisico="TOCO1_TIPO" NomeLogico="TIPO" DataType="VARCHAR" Size="30" id="A10010"/>
        <Campo NomeFisico="TOCO1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10011"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STPJ" NomeLogico="STATUS PROJETO" id="E1047">
      <Campos quantos="6">
        <Campo NomeFisico="STPJ1_COD" NomeLogico="CODIGO DO STATUS" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STPJ1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STPJ1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STPJ1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10003"/>
        <Campo NomeFisico="STPJ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="STPJ1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STCP" NomeLogico="STATUS COMPLEXO" id="E1048">
      <Campos quantos="6">
        <Campo NomeFisico="STCP1_COD" NomeLogico="CODIGO DO STATUS" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STCP1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STCP1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STCP1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10003"/>
        <Campo NomeFisico="STCP1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="STCP1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RFPR" NomeLogico="REGULARIZACAO FUNDIARIA PROPRIEDADE" id="E1049">
      <Campos quantos="20">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="RFPR1_TIPO" NomeLogico="TIPO DE REGULARIZACAO" DataType="VARCHAR" Size="50" id="A10001"/>
        <Campo NomeFisico="RFPR1_TI" NomeLogico="TERMO DE INVENTARIANTE" DataType="VARCHAR" Size="8" id="A10002"/>
        <Campo NomeFisico="RFPR1_CCIR" NomeLogico="SITUACAO CCIR" DataType="VARCHAR" Size="8" id="A10003"/>
        <Campo NomeFisico="RFPR1_ITR" NomeLogico="SITUACAO ITR" DataType="VARCHAR" Size="8" id="A10004"/>
        <Campo NomeFisico="RFPR1_GEO" NomeLogico="SITUACAO GEO" DataType="VARCHAR" Size="8" id="A10005"/>
        <Campo NomeFisico="RFPR1_MATR" NomeLogico="SITUACAO MATRICULA" DataType="VARCHAR" Size="8" id="A10006"/>
        <Campo NomeFisico="RFPR1_CAR" NomeLogico="SITUACAO CAR" DataType="VARCHAR" Size="8" id="A10007"/>
        <Campo NomeFisico="RFPR1_CONTR" NomeLogico="CONTRATO" DataType="VARCHAR" Size="8" id="A10008"/>
        <Campo NomeFisico="RFPR1_CIT" NomeLogico="CERTIDAO INTEIRO TEOR" DataType="VARCHAR" Size="8" id="A10009"/>
        <Campo NomeFisico="RFPR1_CLIE" NomeLogico="APTO CLIENTE" DataType="VARCHAR" Size="8" id="A10010"/>
        <Campo NomeFisico="RFPR1_DAT_CONQ" NomeLogico="PRAZO CONQUISTA" DataType="DATE" id="A10011"/>
        <Campo NomeFisico="RFPR1_DAT_REGU" NomeLogico="PRAZO REGULARIZACAO" DataType="DATE" id="A10012"/>
        <Campo NomeFisico="RFPR1_DAT_APTO" NomeLogico="PRAZO APTO CLIENTE" DataType="DATE" id="A10013"/>
        <Campo NomeFisico="RFPR1_CACHE" NomeLogico="CACHE REGULARIZACAO" DataType="TEXT" id="A10014"/>
        <Campo NomeFisico="RFPR1_DAT_CIT" NomeLogico="DATA CIT" DataType="DATE" id="A10015"/>
        <Campo NomeFisico="RFPR1_RANK" NomeLogico="RANKING" DataType="INTEGER" id="A10016"/>
        <Campo NomeFisico="RFPR1_DAT_HR_INI" NomeLogico="DATA DE INICIO" DataType="DATE" NotNull="true" id="A10017"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10018"/>
        <Campo NomeFisico="RFPR1_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10019"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CTRF" NomeLogico="CONTRATO REGULARIZACAO FUNDIARIA" id="E1050">
      <Campos quantos="15">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CTRF1_SITU" NomeLogico="SITUACAO CONTRATO" DataType="VARCHAR" Size="8" id="A10002"/>
        <Campo NomeFisico="CTRF1_ADT" NomeLogico="ADITIVO" DataType="VARCHAR" Size="8" id="A10003"/>
        <Campo NomeFisico="CTRF1_CESS" NomeLogico="CESSAO DE CONTRATO" DataType="VARCHAR" Size="8" id="A10004"/>
        <Campo NomeFisico="CTRF1_COD_TXT" NomeLogico="DENOMINACAO" DataType="VARCHAR" NotNull="true" Size="50" id="A10005"/>
        <Campo NomeFisico="CTRF1_COD_TXT_SUBS" NomeLogico="SUBSTITUTO" DataType="VARCHAR" Size="100" id="A10006"/>
        <Campo NomeFisico="CTRF1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10007"/>
        <Campo NomeFisico="COMPL1_COD" NomeLogico="CODIGO DO COMPLEXO" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="CTRF1_CACHE" NomeLogico="CACHE REGULARIZACAO" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="CTRF1_RANK" NomeLogico="RANK" DataType="INTEGER" id="A10010"/>
        <Campo NomeFisico="CTRF1_PESO" NomeLogico="PESO" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10012"/>
        <Campo NomeFisico="CTRF1_DAT_HR_INI" NomeLogico="DATA DE INICIO" DataType="DATE" NotNull="true" id="A10013"/>
        <Campo NomeFisico="CTRF1_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10014"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_SIGS" NomeLogico="SIGEF SOBREPOSTA" id="E1051">
      <Campos quantos="8">
        <Campo NomeFisico="SIGS1_COD_IMO" NomeLogico="CODIGO IMOVEL SIGEF" DataType="VARCHAR" NotNull="true" Size="13" id="A10000"/>
        <Campo NomeFisico="SIGS1_COD_PARC" NomeLogico="CODIGO PARCELA" DataType="VARCHAR" Size="64" id="A10001"/>
        <Campo NomeFisico="SIGS1_SOBREP_PROP" NomeLogico="SOBREPOSICAO NA PROP" DataType="DOUBLE PRECISION" id="A10002"/>
        <Campo NomeFisico="SIGS1_SOBREP_SIGEF" NomeLogico="SOBREPOSICAO NA SIGEF" DataType="DOUBLE PRECISION" id="A10003"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="SIGS1_COD" NomeLogico="CODIGO SOBREP" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10005"/>
        <Campo NomeFisico="SIGS1_DAT_HR_INI" NomeLogico="DATA CALCULO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="SIGS1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_SNCS" NomeLogico="SNCI SOBREPOSTA" id="E1052">
      <Campos quantos="7">
        <Campo NomeFisico="SNCS1_COD_IMO" NomeLogico="CODIGO IMOVEL SNCI" DataType="VARCHAR" NotNull="true" Size="254" id="A10000"/>
        <Campo NomeFisico="SNCS1_SOBREP_PROP" NomeLogico="SOBREPOSICAO NA PROP" DataType="DOUBLE PRECISION" id="A10001"/>
        <Campo NomeFisico="SNCS1_SOBREP_SNCI" NomeLogico="SOBREPOSICAO NA SIGEF" DataType="DOUBLE PRECISION" id="A10002"/>
        <Campo NomeFisico="SNCS1_COD" NomeLogico="CODIGO SOBREP" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10003"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="SNCS1_DAT_HR_INI" NomeLogico="DATA CALCULO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="SNCS1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CARS" NomeLogico="CAR SOBREPOSTA" id="E1053">
      <Campos quantos="7">
        <Campo NomeFisico="CARS1_COD_IMO" NomeLogico="CODIGO IMOVEL CAR" DataType="VARCHAR" NotNull="true" Size="100" id="A10000"/>
        <Campo NomeFisico="CARS1_SOBREP_PROP" NomeLogico="SOBREPOSICAO NA PROP" DataType="DOUBLE PRECISION" id="A10001"/>
        <Campo NomeFisico="CARS1_SOBREP_CAR" NomeLogico="SOBREPOSICAO NA CAR" DataType="DOUBLE PRECISION" id="A10002"/>
        <Campo NomeFisico="CARS1_COD" NomeLogico="CODIGO SOBREP" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10003"/>
        <Campo NomeFisico="CARS1_DAT_HR_INI" NomeLogico="DATA CALCULO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CARS1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_REPO" NomeLogico="REPORT" id="E1054">
      <Campos quantos="9">
        <Campo NomeFisico="REPO2_COD" NomeLogico="CODIGO REPORT" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="REPO2_NOM" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="REPO2_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="REPO2_DAT_HR" NomeLogico="DATA E HORA" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="REPO2_TIPO" NomeLogico="TIPO" DataType="VARCHAR" NotNull="true" Size="50" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="REPO2_CONF" NomeLogico="CONFIGURACAO USUARIO" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="REPO2_JSON" NomeLogico="DADOS ADICIONAIS" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="REPO2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_NOTI" NomeLogico="NOTIFICACAO" id="E1055">
      <Campos quantos="8">
        <Campo NomeFisico="NOTI2_COD" NomeLogico="CODIGO NOTIFICACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="NOTI2_NOM" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="NOTI2_TXT" NomeLogico="TEXTO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="NOTI2_DAT_HR_INI" NomeLogico="DATA E HORA" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="NOTI2_LIDA" NomeLogico="LIDA" DataType="BIT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD_DEST" NomeLogico="CODIGO DESTINATARIO" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="NOTI2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_NOLI" NomeLogico="NOTIFICACAO LIDA" id="E1056">
      <Campos quantos="4">
        <Campo NomeFisico="NOTI2_COD" NomeLogico="CODIGO NOTIFICACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="NOLI2_DAT_HR" NomeLogico="DATA LEITURA" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="NOLI2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_ARQU" NomeLogico="ARQUIVO BIG" id="E1057">
      <Campos quantos="31">
        <Campo NomeFisico="ARQU1_COD" NomeLogico="CODIGO ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ARQU1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="500" id="A10001"/>
        <Campo NomeFisico="ARQU1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="500" id="A10002"/>
        <Campo NomeFisico="ARQU1_DAT_UPLOAD" NomeLogico="DATA UPLOAD" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="ARQU1_DAT_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="ARQU1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="ARQU1_ID" NomeLogico="ID GDRIVE" DataType="VARCHAR" NotNull="true" Size="100" id="A10006"/>
        <Campo NomeFisico="ARQU1_ST_NOM" NomeLogico="STATUS NOMENCLATURA" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="ARQU1_ST_RF" NomeLogico="STATUS REGFUND" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="ARQU1_DAT_ULT_SINC" NomeLogico="DATA ULTIMA SINCRONIA" DataType="DATE" id="A10009"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" id="A10010"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" id="A10012"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10013"/>
        <Campo NomeFisico="ARQU1_GD_CACHE" NomeLogico="CACHE GDRIVE" DataType="TEXT" id="A10014"/>
        <Campo NomeFisico="ARQU1_DEL" NomeLogico="EXCLUIDO" DataType="DATE" id="A10015"/>
        <Campo NomeFisico="USER2_COD_VERIF" NomeLogico="CODIGO USUARIO VERIFICADOR" DataType="INTEGER" id="A10016"/>
        <Campo NomeFisico="ARQU1_DAT_VER" NomeLogico="DATA VERIFICACAO" DataType="DATE" id="A10017"/>
        <Campo NomeFisico="ARQU1_DAT_VAL_VER" NomeLogico="DATA VALIDADE VERIFICACAO" DataType="DATE" id="A10018"/>
        <Campo NomeFisico="ARQU1_DAT_RECU" NomeLogico="DATA RECUSA" DataType="DATE" id="A10019"/>
        <Campo NomeFisico="ARQU1_MOT_RECU" NomeLogico="MOTIVO RECUSA" DataType="TEXT" id="A10020"/>
        <Campo NomeFisico="USER2_COD_RECU" NomeLogico="CODIGO USUARIO RECUSA" DataType="INTEGER" id="A10021"/>
        <Campo NomeFisico="ARQU1_ST_VERIF" NomeLogico="STATUS VERIFICACAO" DataType="VARCHAR" Size="20" id="A10022"/>
        <Campo NomeFisico="PAAR1_COD" NomeLogico="CODIGO DO PADRAO" DataType="INTEGER" id="A10023"/>
        <Campo NomeFisico="ARQU1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10024"/>
        <Campo NomeFisico="ARQU1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO REGISTRO" DataType="DATE" id="A10025"/>
        <Campo NomeFisico="STVARQ1_COD" NomeLogico="CODIGO STATUS VERIFICACAO" DataType="INTEGER" id="A10026"/>
        <Campo NomeFisico="ARQU1_ST_VERIFGEO" NomeLogico="STATUS VERIFICACAO GEO" DataType="VARCHAR" Size="20" id="A10027"/>
        <Campo NomeFisico="ARQU1_DAT_VERGEO" NomeLogico="DATA VERIFICACAO GEO" DataType="DATE" id="A10028"/>
        <Campo NomeFisico="ARQU1_MOT_RECUGEO" NomeLogico="MOTIVO RECUSA GEO" DataType="TEXT" id="A10029"/>
        <Campo NomeFisico="USER2_COD_VERIFGEO" NomeLogico="CODIGO USUARIO VERIFICADOR GEO" DataType="INTEGER" id="A10030"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PAAR" NomeLogico="PADRAO DE ARQUIVO" id="E1058">
      <Campos quantos="17">
        <Campo NomeFisico="PAAR1_COD" NomeLogico="CODIGO DO PADRAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PAAR1_TIT" NomeLogico="TITULO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="PAAR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PAAR1_TXT" NomeLogico="TEXTO DO PADRAO" DataType="VARCHAR" NotNull="true" Size="400" id="A10003"/>
        <Campo NomeFisico="PAAR1_CLAS" NomeLogico="CLASSE DO PADRAO" DataType="VARCHAR" NotNull="true" Size="100" id="A10004"/>
        <Campo NomeFisico="PAAR1_INSTR" NomeLogico="INSTRUCOES" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="PAAR1_OBRIG" NomeLogico="OBRIGATORIO" DataType="BIT" NotNull="true" id="A10006"/>
        <Campo NomeFisico="PAAR1_TAB" NomeLogico="TABELA DADO VINCULADO" DataType="VARCHAR" Size="50" id="A10007"/>
        <Campo NomeFisico="PAAR1_CAMPO" NomeLogico="CAMPO DADO VINCULADO" DataType="VARCHAR" Size="100" id="A10008"/>
        <Campo NomeFisico="PAAR1_DAT_VAL" NomeLogico="OBRIGATORIO DATA VALIDADE" DataType="BIT" id="A10009"/>
        <Campo NomeFisico="PAAR1_REF_DATVAL" NomeLogico="PADRAO DATA VALIDADE" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="PAAR1_JSON" NomeLogico="PARAMETROS JSON" DataType="TEXT" id="A10011"/>
        <Campo NomeFisico="PAAR1_PJ" NomeLogico="INDICACOR PESSOA JURIDICA" DataType="BIT" id="A10012"/>
        <Campo NomeFisico="PAAR1_INSTR_DATA" NomeLogico="INSTRUCAO DATA AUTOMATICA" DataType="VARCHAR" Size="100" id="A10013"/>
        <Campo NomeFisico="PAAR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10014"/>
        <Campo NomeFisico="PAAR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10015"/>
        <Campo NomeFisico="PAAR1_ABR" NomeLogico="ABREVIATURA" DataType="VARCHAR" NotNull="true" Size="20" id="A10016"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RFCM" NomeLogico="REGULARIZACAO FUNDIARIA COMPLEXO" id="E1059">
      <Campos quantos="10">
        <Campo NomeFisico="COMPL1_COD" NomeLogico="CODIGO DO COMPLEXO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="RFCM1_DAT_HR_INI" NomeLogico="DATA DE ABERTURA" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="RFCM1_DAT_CLIE" NomeLogico="PRAZO CLIENTE" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="RFCM1_DAT_REG" NomeLogico="PRAZO REGULARIZACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="RFCM1_DAT_CONQ" NomeLogico="PRAZO CONQUISTA" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="RFCM1_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="RFCM1_DAT_CONQ_CONCL" NomeLogico="DATA CONQUISTA" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="RFCM1_DAT_REG_CONCL" NomeLogico="DATA REGULARIZACAO" DataType="DATE" id="A10008"/>
        <Campo NomeFisico="RFCM1_DAT_CLIE_CONCL" NomeLogico="DATA CLIENTE" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LGAR" NomeLogico="LOG ARQUIVO" id="E1060">
      <Campos quantos="8">
        <Campo NomeFisico="LGAR0_COD" NomeLogico="CODIGO LOG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LGAR0_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="LGAR0_CAMPO" NomeLogico="CAMPO ALTERADO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="LGAR0_ORIG" NomeLogico="VALOR ORIGINAL" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="LGAR0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="LGAR0_DADO" NomeLogico="DADOS ADICIONAIS" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="ARQU1_COD" NomeLogico="CODIGO ARQUIVO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="LGAR0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TIPR" NomeLogico="TIPO DE PROJETO" id="E1061">
      <Campos quantos="6">
        <Campo NomeFisico="TIPR1_COD" NomeLogico="CODIGO TIPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TIPR1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TIPR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TIPR1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="TIPR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="TIPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_COPJ" NomeLogico="COMENTARIO DO PROJETO" id="E1062">
      <Campos quantos="9">
        <Campo NomeFisico="COPJ1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COPJ1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="COPJ1_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="COPJ1_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="COPJ1_FORM" NomeLogico="FORMATACAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="COPJ1_ATI" NomeLogico="COMENTARIO ATIVO" DataType="BIT" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="COPJ1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_COPP" NomeLogico="COMENTARIO DO PROPRIETARIO" id="E1063">
      <Campos quantos="9">
        <Campo NomeFisico="COPP1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COPP1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="COPP1_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="COPP1_FORM" NomeLogico="FORMATACAO" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="COPP1_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="COPP1_ATI" NomeLogico="COMENTARIO ATIVO" DataType="BIT" id="A10005"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="COPP1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CSCH" NomeLogico="CESSIONARIO DO CONTRATO HISTORICO" id="E1064">
      <Campos quantos="11">
        <Campo NomeFisico="CSCH1_PERC" NomeLogico="PERCENTUAL" DataType="DOUBLE PRECISION" id="A10000"/>
        <Campo NomeFisico="CSCH1_DAT_INI_CES" NomeLogico="DATA INICIO CESSAO" DataType="DATE" id="A10001"/>
        <Campo NomeFisico="CSCH1_COD_ERP" NomeLogico="CHAVE DO CESSIONARIO NO ERP" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CSCH1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="CSCH1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10004"/>
        <Campo NomeFisico="CSCH1_NUM_SAP" NomeLogico="NUMERO SAP DO CONTRATO" DataType="VARCHAR" Size="100" id="A10005"/>
        <Campo NomeFisico="CSCH1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="CCSCH1_NOM_ORIG" NomeLogico="NOME ORIGINAL DO CESSIONARIO" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="CSCH1_COD_ERP_DIV" NomeLogico="DIVISAO OU FILIAL" DataType="VARCHAR" Size="100" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_TERM" NomeLogico="TERMO" id="E1065">
      <Campos quantos="7">
        <Campo NomeFisico="TERM2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TERM2_NOM" NomeLogico="TITULO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="TERM2_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TERM2_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TERM2_ATI" NomeLogico="ATIVO" DataType="BIT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TERM2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="TERM2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_ACTM" NomeLogico="ACEITE DE TERMO" id="E1066">
      <Campos quantos="5">
        <Campo NomeFisico="TERM2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="ACTM2_DAT_HR_INI" NomeLogico="DATA DO ACEITE" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="ACTM2_TXT" NomeLogico="TEXTO DO TERMO ACEITO" DataType="TEXT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ACTM2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CRFS" NomeLogico="CONTRATO REGFUND SUBSTITUIDO" id="E1067">
      <Campos quantos="5">
        <Campo NomeFisico="CTRF1_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CTRF1_COD_SUBS" NomeLogico="CODIGO SUBSTITUIDO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CRFS1_DAT_HR_INI" NomeLogico="DATA DA INCLUSAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CRFS1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_BANC" NomeLogico="BANCO" id="E1068">
      <Campos quantos="5">
        <Campo NomeFisico="BANC2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="BANC2_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="BANC2_COD_OFICIAL" NomeLogico="CODIGO BACEN" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="BANC2_DAT_HR_INI" NomeLogico="DATA DE INCLUSAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="BANC2_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LGVL" NomeLogico="LOGAVEL" id="E1069">
      <Campos quantos="6">
        <Campo NomeFisico="LGVL0_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LGVL0_TAB" NomeLogico="TABELA" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="LGVL0_CAMPOS" NomeLogico="CAMPOS" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="LGVL0_PARAMS" NomeLogico="PARAMETROS" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="LGVL0_DAT_HR_INI" NomeLogico="DATA DE CADASTRAMENTO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="LGVL0_DAT_HR_MOD" NomeLogico="DATA DE MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CJAN" NomeLogico="CONJUNTO DE ANOTACOES" id="E1070">
      <Campos quantos="11">
        <Campo NomeFisico="CJAN3_COD" NomeLogico="CODIGO CONJUNTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CJAN3_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="CJAN3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="CJAN3_PUB" NomeLogico="PUBLICO" DataType="BIT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CJAN3_BB" NomeLogico="BOUNDING BOX" DataType="POLYGON" id="A10005"/>
        <Campo NomeFisico="CJAN3_MIN_ZOOM" NomeLogico="ZOOM MINIMO" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="CJAN3_MAX_ZOOM" NomeLogico="ZOOM MAXIMO" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="CJAN3_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10008"/>
        <Campo NomeFisico="CJAN3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
        <Campo NomeFisico="CJAN3_ATI" NomeLogico="ATIVO" DataType="BIT" NotNull="true" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_ANOT" NomeLogico="ANOTACAO" id="E1071">
      <Campos quantos="10">
        <Campo NomeFisico="ANOT3_COD" NomeLogico="CODIGO ANOTACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ANOT3_NOM" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="ANOT3_TXT" NomeLogico="TEXTO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="ANOT3_GEOM" NomeLogico="GEOMETRIA" DataType="GEOMETRY" id="A10003"/>
        <Campo NomeFisico="CJAN3_COD" NomeLogico="CODIGO CONJUNTO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ANOT3_JSON" NomeLogico="CONFIGURACAO" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="ANOT3_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="ANOT3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="ANOT3_ATI" NomeLogico="ATIVA" DataType="BIT" NotNull="true" id="A10008"/>
        <Campo NomeFisico="ANOT3_GEOJSON" NomeLogico="GEOJSON DA GEOMETRIA" DataType="TEXT" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_HANO" NomeLogico="HISTORICO ANOTACAO" id="E1072">
      <Campos quantos="10">
        <Campo NomeFisico="HANO3_COD" NomeLogico="CODIGO HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ANOT3_COD" NomeLogico="CODIGO ANOTACAO" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HANO3_NOM" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10002"/>
        <Campo NomeFisico="HANO3_TXT" NomeLogico="TEXTO" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="HANO3_GEOM" NomeLogico="GEOMETRIA" DataType="GEOMETRY" id="A10004"/>
        <Campo NomeFisico="HANO3_JSON" NomeLogico="CONFIGURACAO JSON" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="HANO3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="HANO3_GEOJSON" NomeLogico="GEOJSON DA GEOMETRIA" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="HANO3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_EQUI" NomeLogico="EQUIPE BIG" id="E1073">
      <Campos quantos="6">
        <Campo NomeFisico="EQUI2_COD" NomeLogico="CODIGO EQUIPE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="EQUI2_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="EQUI2_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="EQUI2_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="EQUI2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="INTEGER" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_USEQ" NomeLogico="USUARIO DA EQUIPE" id="E1074">
      <Campos quantos="5">
        <Campo NomeFisico="EQUI2_COD" NomeLogico="CODIGO EQUIPE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="USEQ2_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD_ATRIB" NomeLogico="CODIGO USUARIO ATRIBUIDOR" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="USEQ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CACA" NomeLogico="CAMADA CONJUNTO ANOTACOES" id="E1075">
      <Campos quantos="4">
        <Campo NomeFisico="CJAN3_COD" NomeLogico="CODIGO CONJUNTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CAMA3_COD" NomeLogico="ID DA CAMADA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CACA3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CACA3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CCUS" NomeLogico="COMPARTILHAMENTO CONJUNTO USUARIO" id="E1076">
      <Campos quantos="5">
        <Campo NomeFisico="CJAN3_COD" NomeLogico="CODIGO CONJUNTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CCUS3_EDIT" NomeLogico="PODE EDITAR" DataType="BIT" id="A10002"/>
        <Campo NomeFisico="CCUS3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CCUS3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_CCEQ" NomeLogico="COMPARTILHAMENTO CONJUNTO EQUIPE" id="E1077">
      <Campos quantos="5">
        <Campo NomeFisico="CJAN3_COD" NomeLogico="CODIGO CONJUNTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="EQUI2_COD" NomeLogico="CODIGO EQUIPE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CCEQ3_EDIT" NomeLogico="PODE EDITAR" DataType="BIT" id="A10002"/>
        <Campo NomeFisico="CCEQ3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CCEQ3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_PACO" NomeLogico="PARAMETRO CONFIGURACAO" id="E1078">
      <Campos quantos="8">
        <Campo NomeFisico="PACO0_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PACO0_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="PACO0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PACO0_VAR" NomeLogico="VARIAVEL" DataType="VARCHAR" NotNull="true" Size="100" id="A10003"/>
        <Campo NomeFisico="PACO0_VAL" NomeLogico="VALOR" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="PACO0_TIPO" NomeLogico="TIPO DE DADO" DataType="VARCHAR" NotNull="true" Size="30" id="A10005"/>
        <Campo NomeFisico="PACO0_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="PACO0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_GRCA" NomeLogico="GRUPO CAMADAS INTERFACE" id="E1079">
      <Campos quantos="5">
        <Campo NomeFisico="GRCA3_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="GRCA3_NOM" NomeLogico="NOME DO GRUPO" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="GRCA3_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="GRCA3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="GRCA3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RLPR" NomeLogico="RELACIONAMENTO PROPRIETARIOS" id="E1080">
      <Campos quantos="7">
        <Campo NomeFisico="PRIO1_COD_PAI" NomeLogico="ID DO PROPRIETARIO PAI" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PRIO1_COD_FILHO" NomeLogico="ID DO PROPRIETARIO FILHO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="TPRL1_COD" NomeLogico="CODIGO TIPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="RLPR1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="RLPR1_DAT_HR_MOD" NomeLogico="DATA DE MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="RLPR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TPRL" NomeLogico="TIPO RELACIONAMENTO" id="E1081">
      <Campos quantos="10">
        <Campo NomeFisico="TPRL1_COD" NomeLogico="CODIGO TIPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TPRL1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="TPRL1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TPRL1_TIPO_PAI" NomeLogico="TIPO PESSOA PAI" DataType="VARCHAR" Size="3" id="A10003"/>
        <Campo NomeFisico="TPRL1_TIT_PAI" NomeLogico="TITULO PAI" DataType="VARCHAR" NotNull="true" Size="200" id="A10004"/>
        <Campo NomeFisico="TPRL1_TIT_FILHO" NomeLogico="TITULO FILHO" DataType="VARCHAR" NotNull="true" Size="200" id="A10005"/>
        <Campo NomeFisico="TPRL1_TIPO_FILHO" NomeLogico="TIPO PESSOA FILHO" DataType="VARCHAR" Size="3" id="A10006"/>
        <Campo NomeFisico="TPRL1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="TPRL1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
        <Campo NomeFisico="TPRL1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="50" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CONC" NomeLogico="CONCORRENTE" id="E1082">
      <Campos quantos="7">
        <Campo NomeFisico="CONC1_COD" NomeLogico="CODIGO CONCORRENTE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CONC1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="CONC1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="CONC1_DAT_HR_INI" NomeLogico="DATA REGISTRO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CONC1_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="CONC1_DOC" NomeLogico="DOCUMENTO" DataType="BIGINT" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PRCO" NomeLogico="PROPRIEDADE CONCORRENTE" id="E1083">
      <Campos quantos="9">
        <Campo NomeFisico="CONC1_COD" NomeLogico="CODIGO CONCORRENTE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRCO1_STATUS" NomeLogico="STATUS CONTRATO" DataType="VARCHAR" Size="20" id="A10002"/>
        <Campo NomeFisico="PRCO1_TIPO_CTRT" NomeLogico="TIPO CONTRATO" DataType="VARCHAR" Size="20" id="A10003"/>
        <Campo NomeFisico="PRCO1_GEOM" NomeLogico="GEOMETRIA PARCIAL" DataType="MULTIPOLYGON" id="A10004"/>
        <Campo NomeFisico="PRCO1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="PRCO1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="PRCO1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_PTRF" NomeLogico="PONTUACAO RF" id="E1084">
      <Campos quantos="8">
        <Campo NomeFisico="PTRF0_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PTRF0_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="PTRF0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PTRF0_OBJ" NomeLogico="OBJETO" DataType="VARCHAR" NotNull="true" Size="30" id="A10003"/>
        <Campo NomeFisico="PTRF0_STATUS" NomeLogico="STATUS" DataType="VARCHAR" NotNull="true" Size="30" id="A10004"/>
        <Campo NomeFisico="PTRF0_PERC" NomeLogico="PERCENTUAL" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PTRF0_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="PTRF0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_VRRF" NomeLogico="VALORES REFERENCIA RF" id="E1085">
      <Campos quantos="9">
        <Campo NomeFisico="VRRF0_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="VRRF0_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="VRRF0_N1" NomeLogico="NIVEL 1" DataType="VARCHAR" NotNull="true" Size="50" id="A10002"/>
        <Campo NomeFisico="VRRF0_N2" NomeLogico="NIVEL 2" DataType="VARCHAR" NotNull="true" Size="50" id="A10003"/>
        <Campo NomeFisico="VRRF0_N3" NomeLogico="NIVEL 3" DataType="VARCHAR" NotNull="true" Size="50" id="A10004"/>
        <Campo NomeFisico="VRRF0_PTS" NomeLogico="PONTOS" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="VRRF0_PT_TOT" NomeLogico="PARTICIPACAO TOTAL" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="VRRF0_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="VRRF0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB5_COMP" NomeLogico="COMPLEX ENGENHARIA" id="E1086">
      <Campos quantos="5">
        <Campo NomeFisico="COMP5_COD" NomeLogico="CODIGO COMPLEX" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COMP5_NAME" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="PROJ5_COD" NomeLogico="CODIGO PROJECT" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="COMP5_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="COMP5_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB5_PARK" NomeLogico="PARK ENGENHARIA" id="E1087">
      <Campos quantos="5">
        <Campo NomeFisico="PARK5_COD" NomeLogico="CODIGO PARK" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PARK5_NAME" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="COMP5_COD" NomeLogico="CODIGO COMPLEX" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="PARK5_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PARK5_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB5_PROJ" NomeLogico="PROJECT ENGENHARIA" id="E1088">
      <Campos quantos="5">
        <Campo NomeFisico="PROJ5_COD" NomeLogico="CODIGO PROJECT" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PROJ5_NAME" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="OFFI5_COD" NomeLogico="CODIGO OFFICE" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="PROJ5_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PROJ5_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB5_SCEN" NomeLogico="SCENARIO ENGENHARIA" id="E1089">
      <Campos quantos="9">
        <Campo NomeFisico="SCEN5_COD" NomeLogico="CODIGO SCENARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="SCEN5_NAME" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="10" id="A10001"/>
        <Campo NomeFisico="SCEN5_WIND_MODEL" NomeLogico="WIND MODEL" DataType="SMALLINT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="SCEN5_NUMBER" NomeLogico="NUMERO" DataType="SMALLINT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="SCEN5_DATETIME" NomeLogico="DATA DO SCENARIO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="SCEN5_TYPE" NomeLogico="TIPO" DataType="VARCHAR" NotNull="true" Size="100" id="A10005"/>
        <Campo NomeFisico="COMP5_COD" NomeLogico="CODIGO COMPLEX" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="SCEN5_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="SCEN5_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB5_SITE" NomeLogico="SITE ENGENHARIA" id="E1090">
      <Campos quantos="7">
        <Campo NomeFisico="SITE5_COD" NomeLogico="CODIGO SITE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="SITE5_PORTFOLIO" NomeLogico="PORTFOLIO" DataType="BIT" id="A10001"/>
        <Campo NomeFisico="SCEN5_COD" NomeLogico="CODIGO SCENARIO" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="PARK5_COD" NomeLogico="CODIGO PARK" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="STAG1_COD" NomeLogico="CODIGO STATUS" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="SITE5_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="SITE5_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB5_TURB" NomeLogico="TURBINE ENGENHARIA" id="E1091">
      <Campos quantos="15">
        <Campo NomeFisico="TURB5_COD" NomeLogico="CODIGO TURBINE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TURB5_INDEX_IN_SITE" NomeLogico="INDICE NO SITE" DataType="SMALLINT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="TURB5_LABEL" NomeLogico="LABEL" DataType="VARCHAR" NotNull="true" Size="50" id="A10002"/>
        <Campo NomeFisico="TURB5_TAB" NomeLogico="TAB" DataType="VARCHAR" NotNull="true" Size="200" id="A10003"/>
        <Campo NomeFisico="TURB5_POSITION" NomeLogico="POSITION" DataType="POINT" id="A10004"/>
        <Campo NomeFisico="TURB5_X" NomeLogico="X" DataType="DOUBLE PRECISION" id="A10005"/>
        <Campo NomeFisico="TURB5_Y" NomeLogico="Y" DataType="DOUBLE PRECISION" NotNull="true" id="A10006"/>
        <Campo NomeFisico="TURB5_TERRAIN_ELEVATION" NomeLogico="ELEVATION" DataType="DOUBLE PRECISION" id="A10007"/>
        <Campo NomeFisico="TURB5_DISTANCE_TO_CLOSEST_TURBINE" NomeLogico="DISTANCE CLOSEST" DataType="DOUBLE PRECISION" id="A10008"/>
        <Campo NomeFisico="TURB5_AIR_DENSITY" NomeLogico="AIR DENSITY" DataType="DOUBLE PRECISION" id="A10009"/>
        <Campo NomeFisico="TURB5_NET_YELD" NomeLogico="NET YELD" DataType="DOUBLE PRECISION" id="A10010"/>
        <Campo NomeFisico="TURB5_CAPACITY_FACTOR" NomeLogico="CAPACITY FACTOR" DataType="DOUBLE PRECISION" id="A10011"/>
        <Campo NomeFisico="SITE5_COD" NomeLogico="CODIGO SITE" DataType="INTEGER" id="A10012"/>
        <Campo NomeFisico="TURB5_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10013"/>
        <Campo NomeFisico="TURB5_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10014"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB5_TYIN" NomeLogico="TYPE INFORMATION ENGENHARIA" id="E1092">
      <Campos quantos="20">
        <Campo NomeFisico="TYIN5_COD" NomeLogico="CODIGO TYIN" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TYIN5_ROTOR_DIAMETER" NomeLogico="ROTOR DIAMETER" DataType="SMALLINT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="TYIN5_CAPACITY" NomeLogico="CAPACITY" DataType="SMALLINT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="TYIN5_PEAK_OUTPUT" NomeLogico="PEAK OUTPUT" DataType="SMALLINT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TYIN5_WEIBULLSHAPEK" NomeLogico="WEIBULLSHAPEK" DataType="DOUBLE PRECISION" id="A10004"/>
        <Campo NomeFisico="TYIN5_CUT_IN_MS" NomeLogico="CUT IN MS" DataType="SMALLINT" NotNull="true" id="A10005"/>
        <Campo NomeFisico="TYIN5_CUT_OUT_MS" NomeLogico="CUT OUT MS" DataType="DOUBLE PRECISION" NotNull="true" id="A10006"/>
        <Campo NomeFisico="TYIN5_POWER_UNCERTAINTY" NomeLogico="POWER UNCERTAINTY" DataType="VARCHAR" NotNull="true" Size="10" id="A10007"/>
        <Campo NomeFisico="TYIN5_NUMBER_OF_BLADES" NomeLogico="NUMBER OF BLADES" DataType="SMALLINT" NotNull="true" id="A10008"/>
        <Campo NomeFisico="TYIN5_BLADE_WIDTH_MAXIMUM" NomeLogico="BLADE WIDTH MAXIMUM" DataType="DOUBLE PRECISION" id="A10009"/>
        <Campo NomeFisico="TYIN5_BLADE_WIDTH_AT_90_OF_RADIUS" NomeLogico="BLADE WIDTH AT 90 RADIUS" DataType="SMALLINT" NotNull="true" id="A10010"/>
        <Campo NomeFisico="TYIN5_ROTOR_TILT_DEGREES" NomeLogico="ROTOR TILT DEGREES" DataType="SMALLINT" NotNull="true" id="A10011"/>
        <Campo NomeFisico="TYIN5_COMMENTS" NomeLogico="COMMENTS" DataType="TEXT" NotNull="true" id="A10012"/>
        <Campo NomeFisico="TYIN5_REGULATED" NomeLogico="REGULATED" DataType="VARCHAR" NotNull="true" Size="10" id="A10013"/>
        <Campo NomeFisico="TYIN5_IEC_METHOD" NomeLogico="IEC METHOD" DataType="VARCHAR" NotNull="true" Size="20" id="A10014"/>
        <Campo NomeFisico="TYIN5_NAME" NomeLogico="NAME" DataType="VARCHAR" NotNull="true" Size="100" id="A10015"/>
        <Campo NomeFisico="TYIN5_ALT" NomeLogico="ALT" DataType="DOUBLE PRECISION" id="A10016"/>
        <Campo NomeFisico="TYIN5_HUB_HEIGHT" NomeLogico="HUB HEIGHT" DataType="DOUBLE PRECISION" id="A10017"/>
        <Campo NomeFisico="TYIN5_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10018"/>
        <Campo NomeFisico="TYIN5_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10019"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PAIN" NomeLogico="PARCIAL INTERESSE" id="E1093">
      <Campos quantos="14">
        <Campo NomeFisico="PAIN1_COD" NomeLogico="CODIGO PARCIAL" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PAIN1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGONZ" id="A10001"/>
        <Campo NomeFisico="PAIN1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PAIN1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="PAIN1_OBS" NomeLogico="COMENTARIOS" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="CTRT1_COD_REL" NomeLogico="ID DO CONTRATO RELACIONADO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="CTRT1_COD_EFET" NomeLogico="ID DO CONTRATO EFETIVADO" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="USER2_COD_EFET" NomeLogico="CODIGO USUARIO EFETIVADOR" DataType="INTEGER" id="A10009"/>
        <Campo NomeFisico="TIPR1_COD" NomeLogico="CODIGO TIPO" DataType="INTEGER" id="A10010"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="PRIO1_COD_CESS" NomeLogico="ID DO PROPRIETARIO CESSIONARIO" DataType="INTEGER" id="A10012"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" NotNull="true" id="A10013"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STGEO" NomeLogico="STATUS GEO" id="E1094">
      <Campos quantos="8">
        <Campo NomeFisico="STGEO1_COD" NomeLogico="CODIGO STATUS GEO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STGEO1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STGEO1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STGEO1_CURTO" NomeLogico="NOME CURTO" DataType="VARCHAR" Size="50" id="A10003"/>
        <Campo NomeFisico="STGEO1_ORD" NomeLogico="ORDEM DE APRESENTACAO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STGEO1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10005"/>
        <Campo NomeFisico="STGEO1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="STGEO1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TCTB" NomeLogico="TIPO CONTRATO BIG" id="E1095">
      <Campos quantos="11">
        <Campo NomeFisico="TCTB1_COD" NomeLogico="CODIGO TIPO CONTRATO BIG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TCTB1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TCTB1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TCTB1_TIPO_ERP" NomeLogico="NOME ERP" DataType="VARCHAR" Size="200" id="A10003"/>
        <Campo NomeFisico="TCTB1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TCTB1_DAT_HR_MOD" NomeLogico="DATA DE MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="TCTB1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="200" id="A10006"/>
        <Campo NomeFisico="TCTB1_SEL" NomeLogico="INDICADOR SELECIONAVEL" DataType="BIT" id="A10007"/>
        <Campo NomeFisico="GTCT1_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="TCTB1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10009"/>
        <Campo NomeFisico="TCTB1_COD_ERP" NomeLogico="CODIGO ERP" DataType="VARCHAR" Size="20" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_GTCT" NomeLogico="GRUPO DO TIPO DE CONTRATO" id="E1096">
      <Campos quantos="5">
        <Campo NomeFisico="GTCT1_COD" NomeLogico="CODIGO DO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="GTCT1_NOM" NomeLogico="NOME DO GRUPO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="GTCT1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="GTCT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="GTCT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STAP" NomeLogico="STATUS AUTORIZACAO PASSAGEM" id="E1097">
      <Campos quantos="6">
        <Campo NomeFisico="STAP1_COD" NomeLogico="CODIGO STATUS PASSAGEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STAP1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="50" id="A10001"/>
        <Campo NomeFisico="STAP1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STAP1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="STAP1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="STAP1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STSE" NomeLogico="STATUS SERVIDAO" id="E1098">
      <Campos quantos="7">
        <Campo NomeFisico="STSE1_COD" NomeLogico="CODIGO STATUS SERVIDAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STSE1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="STSE1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STSE1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="STSE1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="STSE1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="STSE1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TIRE" NomeLogico="TIPO REGULARIZACAO" id="E1099">
      <Campos quantos="8">
        <Campo NomeFisico="TIRE1_COD" NomeLogico="CODIGO TIPO RF" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TIRE1_NOM" NomeLogico="NOME TIPO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TIRE1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="TIRE1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="GRTR1_COD" NomeLogico="CODIGO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TIRE1_ATI" NomeLogico="ATIVO" DataType="BIT" NotNull="true" id="A10005"/>
        <Campo NomeFisico="TIRE1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="TIRE1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STRF" NomeLogico="STATUS REGULARIZACAO" id="E1100">
      <Campos quantos="10">
        <Campo NomeFisico="STRF1_COD" NomeLogico="ID STATUS" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STRF1_NOM" NomeLogico="NOME STATUS" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STRF1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STRF1_ATI" NomeLogico="ATIVO" DataType="BIT" NotNull="true" id="A10003"/>
        <Campo NomeFisico="STRF1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STRF1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="STRF1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="TIRE1_COD" NomeLogico="CODIGO TIPO RF" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10007"/>
        <Campo NomeFisico="GRTR1_COD" NomeLogico="CODIGO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10008"/>
        <Campo NomeFisico="STRF1_PRAZ_MED" NomeLogico="PRAZO MEDIO" DataType="INTEGER" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_SRFP" NomeLogico="STATUS DA RF DA PROPRIEDADE" id="E1101">
      <Campos quantos="7">
        <Campo NomeFisico="STRF1_COD" NomeLogico="ID STATUS" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="SRFP1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="SRFP1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TIRE1_COD" NomeLogico="CODIGO TIPO RF" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10005"/>
        <Campo NomeFisico="GRTR1_COD" NomeLogico="CODIGO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LSRP" NomeLogico="LOG STATUS RF" id="E1102">
      <Campos quantos="13">
        <Campo NomeFisico="LSRP0_COD" NomeLogico="ID DO HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LSRP0_NOM_NOVO" NomeLogico="NOME STATUS NOVO" DataType="VARCHAR" Size="200" id="A10001"/>
        <Campo NomeFisico="LSRP0_NOM_TIPO" NomeLogico="NOME TIPO" DataType="VARCHAR" NotNull="true" Size="200" id="A10002"/>
        <Campo NomeFisico="LSRP0_NOM_GRUPO" NomeLogico="NOME GRUPO" DataType="VARCHAR" NotNull="true" Size="200" id="A10003"/>
        <Campo NomeFisico="LSRP0_DAT_HR_INI" NomeLogico="DATA ALTERACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LSRP0_NOM_ANT" NomeLogico="NOME STATUS ANTERIOR" DataType="VARCHAR" Size="200" id="A10005"/>
        <Campo NomeFisico="TIRE1_COD" NomeLogico="CODIGO TIPO RF" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="GRTR1_COD" NomeLogico="CODIGO GRUPO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="STRF1_COD_ANT" NomeLogico="ID STATUS ANTERIOR" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="STRF1_COD_NOVO" NomeLogico="ID STATUS NOVO" DataType="INTEGER" id="A10009"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10010"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10011"/>
        <Campo NomeFisico="LSRP0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="INTEGER" id="A10012"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_GRTR" NomeLogico="GRUPO TIPO REGULARIZACAO" id="E1103">
      <Campos quantos="5">
        <Campo NomeFisico="GRTR1_COD" NomeLogico="CODIGO GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="GRTR1_NOM" NomeLogico="NOME GRUPO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="GRTR1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="GRTR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="GRTR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STDOM" NomeLogico="STATUS DOMINIO" id="E1104">
      <Campos quantos="8">
        <Campo NomeFisico="STDOM1_COD" NomeLogico="CODIGO STATUS DOMINIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STDOM1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STDOM1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STDOM1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="STDOM1_ABR" NomeLogico="NOME CURTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10004"/>
        <Campo NomeFisico="STDOM1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="STDOM1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="STDOM1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STCTB" NomeLogico="STATUS CONTRATO BIG" id="E1105">
      <Campos quantos="8">
        <Campo NomeFisico="STCTB1_COD" NomeLogico="CODIGO STATUS CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STCTB1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STCTB1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STCTB1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="STCTB1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STCTB1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="STCTB1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="STCTB1_ABR" NomeLogico="NOME CURTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STVARQ" NomeLogico="STATUS VERIFICACAO ARQUIVO" id="E1106">
      <Campos quantos="8">
        <Campo NomeFisico="STVARQ1_COD" NomeLogico="CODIGO STATUS VERIFICACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STVARQ1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STVARQ1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STVARQ1_ABR" NomeLogico="NOME CURTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="STVARQ1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10004"/>
        <Campo NomeFisico="STVARQ1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="STVARQ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="STVARQ1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_REFU" NomeLogico="REMEMBRAMENTO FUTURO" id="E1107">
      <Campos quantos="8">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DE PROPRIEDADE A REMEMBRAR" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PROP1_COD_DEST" NomeLogico="ID DA PROPRIEDADE DESTINO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="REFU1_NOM" NomeLogico="NOME DA PROPRIEDADE" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="REFU1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="REFU1_DAT_HR_MOD" NomeLogico="ULTIMA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="REFU1_DAT_HR_PREV" NomeLogico="DATA PREVISTA EFETIVACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="REFU1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGONZ" id="A10006"/>
        <Campo NomeFisico="REFU1_AREA" NomeLogico="AREA" DataType="DOUBLE PRECISION" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_GROF" NomeLogico="GRUPO ORTOFOTOS" id="E1108">
      <Campos quantos="5">
        <Campo NomeFisico="GROF3_COD" NomeLogico="ID GRUPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="GROF3_NOM" NomeLogico="NOME DO GRUPO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="GROF3_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="50" id="A10002"/>
        <Campo NomeFisico="GROF3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="GROF3_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_ORTO" NomeLogico="ORTOFOTO" id="E1109">
      <Campos quantos="19">
        <Campo NomeFisico="ORTO3_COD" NomeLogico="ID ORTOFOTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ORTO3_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ORTO3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="ORTO3_DAT_REF" NomeLogico="DATA REFERENCIA" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ORTO3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ORTO3_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="TIOF3_COD" NomeLogico="ID TIPO ORTOFOTO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="CAMA3_COD" NomeLogico="ID DA CAMADA" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="ORTO3_FILENAME" NomeLogico="NOME DO ARQUIVO" DataType="VARCHAR" NotNull="true" Size="500" id="A10008"/>
        <Campo NomeFisico="ORTO3_DIR" NomeLogico="PASTA" DataType="VARCHAR" Size="1000" id="A10009"/>
        <Campo NomeFisico="DTBK_TORRE" NomeLogico="TORRE" DataType="VARCHAR" Size="20" id="A10010"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="CODIGO PROJETO" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="CTRT1_COD_TXT" NomeLogico="DENOMINACAO CONTRATO" DataType="VARCHAR" Size="20" id="A10012"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="CODIGO PROPRIEDADE" DataType="INTEGER" id="A10013"/>
        <Campo NomeFisico="ORTO3_GS_NOM" NomeLogico="NOME GEOSERVER" DataType="VARCHAR" NotNull="true" Size="50" id="A10014"/>
        <Campo NomeFisico="ORTO3_ATI" NomeLogico="ORTOFOTO ATIVA" DataType="BIT" NotNull="true" id="A10015"/>
        <Campo NomeFisico="ORTO3_GEOM_LIM" NomeLogico="GEOMETRIA LIMITE" DataType="POLYGON" id="A10016"/>
        <Campo NomeFisico="ORTO3_ARQ" NomeLogico="ARQUIVO" DataType="VARCHAR" Size="500" id="A10017"/>
        <Campo NomeFisico="ORTO3_ARQ_MD5" NomeLogico="MD5 DO ARQUIVO" DataType="VARCHAR" NotNull="true" Size="32" id="A10018"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB3_TIOF" NomeLogico="TIPO ORTOFOTO" id="E1110">
      <Campos quantos="7">
        <Campo NomeFisico="TIOF3_COD" NomeLogico="ID TIPO ORTOFOTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TIOF3_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="150" id="A10001"/>
        <Campo NomeFisico="TIOF3_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TIOF3_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="30" id="A10003"/>
        <Campo NomeFisico="GROF3_COD" NomeLogico="ID GRUPO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TIOF3_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="TIOF3_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_JOBQ" NomeLogico="JOB QUEUE" id="E1111">
      <Campos quantos="12">
        <Campo NomeFisico="JOBQ0_COD" NomeLogico="ID JOB" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="JOBQ0_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="300" id="A10001"/>
        <Campo NomeFisico="JOBQ0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="JOBQ0_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="JOBQ0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="JOBQ0_DAT_INI" NomeLogico="DATA INICIO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="JOBQ0_DAT_FIN" NomeLogico="DATA FINAL" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="JOBQ0_JSON" NomeLogico="PARAMETROS JSON" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="JOBQ0_PID" NomeLogico="PID" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="JOBQ0_SAIDA" NomeLogico="SAIDA" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="JOBQ0_STATUS" NomeLogico="STATUS" DataType="VARCHAR" NotNull="true" Size="10" id="A10010"/>
        <Campo NomeFisico="TJOB0_COD" NomeLogico="ID TIPO JOB" DataType="INTEGER" NotNull="true" id="A10011"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_TJOB" NomeLogico="TIPO JOB" id="E1112">
      <Campos quantos="8">
        <Campo NomeFisico="TJOB0_COD" NomeLogico="ID TIPO JOB" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TJOB0_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="100" id="A10001"/>
        <Campo NomeFisico="TJOB0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TJOB0_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TJOB0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="TJOB0_ATI" NomeLogico="ATIVO" DataType="BIT" NotNull="true" id="A10005"/>
        <Campo NomeFisico="TJOB0_JSON" NomeLogico="PARAMETROS JSON" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="TJOB0_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PQSL" NomeLogico="PARQUE SOLAR" id="E1113">
      <Campos quantos="21">
        <Campo NomeFisico="PQSL1_COD" NomeLogico="CODIGO PARQUE SOLAR" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PQSL1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="300" id="A10001"/>
        <Campo NomeFisico="PQSL1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PQSL1_ABR" NomeLogico="ABREVIATURA" DataType="VARCHAR" Size="30" id="A10003"/>
        <Campo NomeFisico="PQSL1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PQSL1_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="PQSL1_DAT_HR_FIM" NomeLogico="DATA ENCERRAMENTO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="PQSL1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGONZ" id="A10007"/>
        <Campo NomeFisico="PQSL1_LAYOUT" NomeLogico="LAYOUT" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="PQSL1_ELETROCENTRO_QTD" NomeLogico="ELETROCENTRO" DataType="INTEGER" id="A10009"/>
        <Campo NomeFisico="PQSL1_POT_MW" NomeLogico="POTENCIA" DataType="DOUBLE PRECISION" id="A10010"/>
        <Campo NomeFisico="PQSL1_INVERSORES_QTD" NomeLogico="QTD INVERSORES" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="PQSL1_INVERSORES_DESC" NomeLogico="DESCRICAO INVERSORES" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="PQSL1_POT_MW_POR_INVERSOR" NomeLogico="POTENCIA POR INVERSOR" DataType="DOUBLE PRECISION" id="A10013"/>
        <Campo NomeFisico="PQSL1_SHP_MD5" NomeLogico="MD5 SHAPE" DataType="VARCHAR" Size="32" id="A10014"/>
        <Campo NomeFisico="PQSL1_DBF_MD5" NomeLogico="MD5 DBF" DataType="VARCHAR" Size="32" id="A10015"/>
        <Campo NomeFisico="PQSL1_PRJ_MD5" NomeLogico="MD5 PRJ" DataType="VARCHAR" Size="32" id="A10016"/>
        <Campo NomeFisico="PQSL1_SHX_MD5" NomeLogico="MD5 SHX" DataType="VARCHAR" Size="32" id="A10017"/>
        <Campo NomeFisico="PQSL1_CPG_MD5" NomeLogico="MD5 CPG" DataType="VARCHAR" Size="32" id="A10018"/>
        <Campo NomeFisico="PQSL1_FILEPATH" NomeLogico="CAMINHO" DataType="TEXT" id="A10019"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" NotNull="true" id="A10020"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_SECO" NomeLogico="SETOR CONTRATO" id="E1114">
      <Campos quantos="6">
        <Campo NomeFisico="SECO1_COD" NomeLogico="ID SETOR CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="SECO1_NOM" NomeLogico="NOME SETOR" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="SECO1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="SECO1_COD_ERP" NomeLogico="CODIGO ERP" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="SECO1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="SECO1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_MORR" NomeLogico="MOTIVO RESSALVA RECUSA" id="E1115">
      <Campos quantos="9">
        <Campo NomeFisico="MORR1_COD" NomeLogico="ID MOTIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="MORR1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="250" id="A10001"/>
        <Campo NomeFisico="MORR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="MORR1_SITU" NomeLogico="INDICADOR SITUACAO" DataType="VARCHAR" NotNull="true" Size="2" id="A10003"/>
        <Campo NomeFisico="MORR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="MORR1_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="MORR1_TODOS" NomeLogico="SE APLICA A TODOS" DataType="BIT" NotNull="true" id="A10006"/>
        <Campo NomeFisico="MORR1_CLAS" NomeLogico="CLASSES DO MOTIVO" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="MORR1_GEO" NomeLogico="INDICADOR DE MOTIVO GEO" DataType="BIT" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_MOPA" NomeLogico="MOTIVO DO PADRAO" id="E1116">
      <Campos quantos="4">
        <Campo NomeFisico="PAAR1_COD" NomeLogico="CODIGO DO PADRAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="MORR1_COD" NomeLogico="ID MOTIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="MOPA1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="MOPA1_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RRAR" NomeLogico="RESSALVA RECUSA ARQUIVO" id="E1117">
      <Campos quantos="4">
        <Campo NomeFisico="ARQU1_COD" NomeLogico="CODIGO ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="MORR1_COD" NomeLogico="ID MOTIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="RRAR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="RRAR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STIN" NomeLogico="STATUS INDENIZACAO" id="E1118">
      <Campos quantos="7">
        <Campo NomeFisico="STIN1_COD" NomeLogico="CODIGO STATUS INDENIZACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STIN1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="18" id="A10001"/>
        <Campo NomeFisico="STIN1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STIN1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="STIN1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STIN1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="STIN1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_EDIN" NomeLogico="EDIFICACAO INDENIZADA" id="E1119">
      <Campos quantos="9">
        <Campo NomeFisico="EDIN1_COD" NomeLogico="CODIGO EDIFICACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="EDIN1_NOM" NomeLogico="NOME" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="EDIN1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="EDIN1_GEOM" NomeLogico="GEOMETRIA" DataType="POINTZ" id="A10003"/>
        <Campo NomeFisico="EDIN1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="EDIN1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="GLEP1_COD" NomeLogico="ID DA GLEBA" DataType="INTEGER" NotNull="true" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TPPC" NomeLogico="TIPO PROCESSO" id="E1120">
      <Campos quantos="5">
        <Campo NomeFisico="TPPC1_COD" NomeLogico="CODIGO TIPO PROCESSO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TPPC1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="300" id="A10001"/>
        <Campo NomeFisico="TPPC1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TPPC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TPPC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PCPR" NomeLogico="PROCESSO PROPRIEDADE" id="E1121">
      <Campos quantos="16">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10000"/>
        <Campo NomeFisico="TPPC1_COD" NomeLogico="CODIGO TIPO PROCESSO" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PCPR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10002"/>
        <Campo NomeFisico="PCPR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PCPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="STPC1_COD" NomeLogico="CODIGO STATUS PROCESSO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PCPR1_DAT_PREV_CONCL" NomeLogico="DATA PREVISTA CONCLUSAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="PCPR1_DAT_CONCL" NomeLogico="DATA CONCLUSAO" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="USER2_COD_STATUS" NomeLogico="CODIGO USUARIO ALTERADOR STATUS" DataType="INTEGER" NotNull="true" id="A10009"/>
        <Campo NomeFisico="PCPR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="PCPR1_NUM_PROC" NomeLogico="NUMERO PROCESSO" DataType="VARCHAR" Size="100" id="A10011"/>
        <Campo NomeFisico="PCPR1_VAL_CAUSA" NomeLogico="VALOR DA CAUSA" DataType="DOUBLE PRECISION" id="A10012"/>
        <Campo NomeFisico="PCPR1_CART" NomeLogico="VARA / CARTORIO" DataType="VARCHAR" Size="400" id="A10013"/>
        <Campo NomeFisico="PCPR1_CNS" NomeLogico="CNS VARA/CARTORIO" DataType="VARCHAR" Size="18" id="A10014"/>
        <Campo NomeFisico="PCPR1_DAT_ABE" NomeLogico="DATA ABERTURA" DataType="DATE" id="A10015"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STPC" NomeLogico="STATUS PROCESSO" id="E1122">
      <Campos quantos="5">
        <Campo NomeFisico="STPC1_COD" NomeLogico="CODIGO STATUS PROCESSO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STPC1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="300" id="A10001"/>
        <Campo NomeFisico="STPC1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STPC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="STPC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HSPC" NomeLogico="HISTORICO STATUS PROCESSO" id="E1123">
      <Campos quantos="8">
        <Campo NomeFisico="HSPC1_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HSPC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PCPR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="STPC1_COD" NomeLogico="CODIGO STATUS PROCESSO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="HSPC1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="HSPC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PTPC" NomeLogico="PARTE PROCESSO" id="E1124">
      <Campos quantos="9">
        <Campo NomeFisico="PTPC1_COD" NomeLogico="ID PARTE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PTPC1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="PTPC1_QUALI" NomeLogico="QUALIFICACAO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="PCPR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="CONC1_COD" NomeLogico="CODIGO CONCORRENTE" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="PTPC1_DOC" NomeLogico="DOCUMENTO" DataType="VARCHAR" Size="100" id="A10006"/>
        <Campo NomeFisico="PTPC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="PTPC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CMPC" NomeLogico="COMENTARIO PROCESSO" id="E1125">
      <Campos quantos="6">
        <Campo NomeFisico="CMPC1_COD" NomeLogico="ID COMENTARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CMPC1_COMENT" NomeLogico="COMENTARIO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CMPC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PCPR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CMPC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_COND" NomeLogico="CONDICAO" id="E1126">
      <Campos quantos="5">
        <Campo NomeFisico="COND1_COD" NomeLogico="ID CONDICAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="COND1_NOM" NomeLogico="NOME CONDICAO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="COND1_COD_SAP" NomeLogico="CODIGO SAP CONDICAO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="COND1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="COND1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CNCT" NomeLogico="CONDICAO DO CONTRATO" id="E1127">
      <Campos quantos="7">
        <Campo NomeFisico="CNCT1_DAT_INI" NomeLogico="DATA INICIO CONDICAO" DataType="DATE" id="A10000"/>
        <Campo NomeFisico="CNCT1_DAT_FIM" NomeLogico="DATA FINAL CONDICAO" DataType="DATE" id="A10001"/>
        <Campo NomeFisico="CNCT1_VALOR" NomeLogico="VALOR" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="COND1_COD" NomeLogico="ID CONDICAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CNCT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CNCT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_PEAT" NomeLogico="PENDENCIA ATUALIZACAO" id="E1128">
      <Campos quantos="7">
        <Campo NomeFisico="PEAT0_COD" NomeLogico="ID PENDENCIA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PEAT0_CLAS" NomeLogico="CLASSE" DataType="VARCHAR" NotNull="true" Size="50" id="A10001"/>
        <Campo NomeFisico="PEAT0_KEY" NomeLogico="CHAVE OBJETO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PEAT0_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PEAT0_DAT_HR_FIM" NomeLogico="DATA CONCLUSAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PEAT0_PARAM" NomeLogico="PARAMETROS" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="PEAT0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_PRIE" NomeLogico="PROPRIEDADE INEXISTENTE ERP" id="E1129">
      <Campos quantos="8">
        <Campo NomeFisico="PRIE0_COD" NomeLogico="ID PROPRIEDADE INEXISTENTE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PRIE0_NOM" NomeLogico="NOME" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="PRIE0_COD_ERP" NomeLogico="ID PROPRIEDADE NO ERP" DataType="VARCHAR" NotNull="true" Size="50" id="A10002"/>
        <Campo NomeFisico="PRIE0_CHAVES_CTRT" NomeLogico="CONTRATOS DA PROPRIEDADE" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="PRIE0_TXT_CTRT" NomeLogico="SAIDA ERP CONTRATO" DataType="TEXT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PRIE0_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PRIE0_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="PRIE0_TXT_FAZ" NomeLogico="SAIDA ERP FAZENDA" DataType="TEXT" NotNull="true" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_INTR" NomeLogico="INTERACAO" id="E1130">
      <Campos quantos="13">
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="INTR6_DAT_INTR" NomeLogico="DATA INTERACAO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="INTR6_ASSUNTO" NomeLogico="ASSUNTO INTERACAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="FNCQ6_COD" NomeLogico="ID FUNIL" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="INTR6_COD_CRM" NomeLogico="CODIGO CRM" DataType="VARCHAR" Size="50" id="A10004"/>
        <Campo NomeFisico="INTR6_GEOM" NomeLogico="COORDENADA" DataType="GEOMETRY" id="A10005"/>
        <Campo NomeFisico="INTR6_ENDE" NomeLogico="ENDERECO" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="INTR6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="INTR6_DAT_HR_MOD" NomeLogico="DATA ULTIMA MODIFICACAO" DataType="DATE" id="A10009"/>
        <Campo NomeFisico="INTR6_INSUC" NomeLogico="INSUCESSO" DataType="BIT" id="A10010"/>
        <Campo NomeFisico="TIIN6_COD" NomeLogico="CODIGO TIPO" DataType="INTEGER" NotNull="true" id="A10011"/>
        <Campo NomeFisico="INTR6_FT" NomeLogico="INDICADOR DE FORCA TAREFA" DataType="BIT" id="A10012"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_ACIN" NomeLogico="ACOMPANHANTE INTERACAO" id="E1131">
      <Campos quantos="5">
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="ACIN6_OBS" NomeLogico="OBSERVACOES ACOMPANHANTE" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="ACIN6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ACIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_PJCQ" NomeLogico="PROJETO CONQUISTA" id="E1132">
      <Campos quantos="8">
        <Campo NomeFisico="PJCQ6_COD" NomeLogico="ID PROJETO CONQUISTA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PJCQ6_NOM" NomeLogico="NOME PROJETO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="PJCQ6_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PJCQ6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PJCQ6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PJCQ6_DAT_HR_FIM" NomeLogico="DATA CONCLUSAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="PJCQ6_GEOM" NomeLogico="GEOMETRIA CONQUISTA" DataType="GEOMETRY" id="A10006"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_FNCQ" NomeLogico="FUNIL DE CONQUISTA" id="E1133">
      <Campos quantos="10">
        <Campo NomeFisico="FNCQ6_COD" NomeLogico="ID FUNIL" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="FNCQ6_NOM" NomeLogico="NOME" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="FNCQ6_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="FNCQ6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="FNCQ6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="FNCQ6_DAT_HR_FIM" NomeLogico="DATA CONCLUSAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="FNCQ6_GEOM" NomeLogico="GEOMETRIA CONQUISTA" DataType="GEOMETRY" id="A10006"/>
        <Campo NomeFisico="PJCQ6_COD" NomeLogico="ID PROJETO CONQUISTA" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="FNCQ6_COD_PAI" NomeLogico="ID FUNIL PAI" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_ATIN" NomeLogico="ATRIBUTO INTERACAO" id="E1134">
      <Campos quantos="6">
        <Campo NomeFisico="ATIN6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATIN6_NOM" NomeLogico="NOME ATRIBUTO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ATIN6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="TDAT6_COD" NomeLogico="ID TIPO DADO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ATIN6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_VATI" NomeLogico="VALOR ATRIBUTO INTERACAO" id="E1135">
      <Campos quantos="7">
        <Campo NomeFisico="ATIN6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="VATI6_VAL" NomeLogico="VALOR" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="VATI6_COMPL" NomeLogico="COMPLEMENTO" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="VATI6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="VATI6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_PCRM" NomeLogico="PESSOA CRM" id="E1136">
      <Campos quantos="7">
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" id="A10001"/>
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="PCRM6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PCRM6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_IMIN" NomeLogico="IMAGEM INTERACAO" id="E1137">
      <Campos quantos="15">
        <Campo NomeFisico="IMIN6_COD" NomeLogico="ID IMAGEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="IMIN6_PATH" NomeLogico="CAMINHO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="IMIN6_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="IMIN6_ID" NomeLogico="ID GDRIVE" DataType="VARCHAR" Size="100" id="A10006"/>
        <Campo NomeFisico="IMIN6_FILENAME" NomeLogico="NOME ARQUIVO" DataType="VARCHAR" NotNull="true" Size="500" id="A10007"/>
        <Campo NomeFisico="IMIN6_NOM" NomeLogico="TITULO" DataType="VARCHAR" Size="400" id="A10008"/>
        <Campo NomeFisico="IMIN6_GEOM" NomeLogico="GEOMETRIA" DataType="POINTZ" id="A10009"/>
        <Campo NomeFisico="IMIN6_JSON" NomeLogico="DADOS ADICIONAIS" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10011"/>
        <Campo NomeFisico="IMIN6_GD_CACHE" NomeLogico="CACHE GDRIVE" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="IMIN6_DAT_UPLOAD" NomeLogico="DATA UPLOAD" DataType="DATE" id="A10013"/>
        <Campo NomeFisico="IMIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10014"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_DOIN" NomeLogico="DOCUMENTO INTERACAO" id="E1138">
      <Campos quantos="9">
        <Campo NomeFisico="DOIN6_COD" NomeLogico="ID DOCUMENTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="DOIN6_TIPO" NomeLogico="TIPO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="DOIN6_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="DOIN6_COD_ARQ" NomeLogico="CODIGO ARQUIVO BIG" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="DOIN6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="DOIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_PROC" NomeLogico="PROPRIEDADE CRM" id="E1139">
      <Campos quantos="8">
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PROC6_NOM" NomeLogico="NOME PROPRIEDADE" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="PROC6_DAT_PROP_BIG" NomeLogico="DATA VINCULACAO PROPRIEDADE BIG" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PROC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PROC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="PROC6_GD_ID" NomeLogico="GDRIVE ID" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="PROC6_CACHE" NomeLogico="CACHE" DataType="TEXT" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_ATPR" NomeLogico="ATRIBUTO PROPRIEDADE" id="E1140">
      <Campos quantos="6">
        <Campo NomeFisico="ATPR6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATPR6_NOM" NomeLogico="NOME ATRIBUTO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="ATPR6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="TDAT6_COD" NomeLogico="ID TIPO DADO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ATPR6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATPR6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_VAPR" NomeLogico="VALOR ATRIBUTO PROPRIEDADE" id="E1141">
      <Campos quantos="8">
        <Campo NomeFisico="ATPR6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="VAPR6_VAL" NomeLogico="VALOR" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ATPR6_COMPL" NomeLogico="COMPLEMENTO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="VAPR6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="VAPR6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_PEIN" NomeLogico="PESSOA INTERACAO" id="E1142">
      <Campos quantos="4">
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PEIN6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PEIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_HPEC" NomeLogico="HISTORICO PESSOA CRM" id="E1143">
      <Campos quantos="9">
        <Campo NomeFisico="HPEC6_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HPEC6_VAL" NomeLogico="ALTERACOES" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="ATPC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="HPEC6_DAT_ORIG" NomeLogico="DATA ORIGINAL" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="HPEC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="HPEC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_ATPC" NomeLogico="ATRIBUTO PESSOA" id="E1144">
      <Campos quantos="6">
        <Campo NomeFisico="ATPC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATPC6_NOM" NomeLogico="NOME ATRIBUTO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ATPC6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="TDAT6_COD" NomeLogico="ID TIPO DADO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ATPC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATPC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_VAPC" NomeLogico="VALOR ATRIBUTO PESSOA" id="E1145">
      <Campos quantos="8">
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ATPC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="VAPC6_VAL" NomeLogico="VALOR" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="VAPC6_COMPL" NomeLogico="COMPLEMENTO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="VAPC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="VAPC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_HPRC" NomeLogico="HISTORICO PROPRIEDADE CRM" id="E1146">
      <Campos quantos="9">
        <Campo NomeFisico="HPRC6_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HPRC6_VAL" NomeLogico="VALOR ORIGINAL" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="ATPR6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="HPRC6_DAT_ORIG" NomeLogico="DATA ORIGINAL" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="HPRC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="HPRC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_LAIN" NomeLogico="LOOKUP ATRIBUTO INTERACAO" id="E1147">
      <Campos quantos="6">
        <Campo NomeFisico="LAIN6_TXT" NomeLogico="TEXTO" DataType="VARCHAR" NotNull="true" Size="400" id="A10000"/>
        <Campo NomeFisico="LAIN6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10001"/>
        <Campo NomeFisico="ATIN6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="LAIN6_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="LAIN6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LAIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_LATP" NomeLogico="LOOKUP ATRIBUTO PESSOA" id="E1148">
      <Campos quantos="6">
        <Campo NomeFisico="LATP6_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ATPC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="LATP6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="LATP6_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="LATP6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LATP6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_LAPR" NomeLogico="LOOKUP ATRIBUTO PROPRIEDADE" id="E1149">
      <Campos quantos="6">
        <Campo NomeFisico="LAPR6_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ATPR6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="LAPR6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="LAPR6_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="LAPR6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LAPR6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_PCPR" NomeLogico="PESSOA CRM PROPRIEDADE" id="E1150">
      <Campos quantos="5">
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PCPR6_REL" NomeLogico="DESCRICAO RELACIONAMENTO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PCPR6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PCPR6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_TDAT" NomeLogico="TIPO DADO ATRIBUTO" id="E1151">
      <Campos quantos="6">
        <Campo NomeFisico="TDAT6_COD" NomeLogico="ID TIPO DADO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TDAT6_NOM" NomeLogico="NOME TIPO DADO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TDAT6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="TDAT6_CONF" NomeLogico="CONFIGURACAO" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="TDAT6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TDAT6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_PRIN" NomeLogico="PROPRIEDADE INTERACAO" id="E1152">
      <Campos quantos="6">
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRIN6_TXT" NomeLogico="COMENTARIOS" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PRIN6_PRIN" NomeLogico="PRINCIPAL" DataType="BIT" id="A10003"/>
        <Campo NomeFisico="PRIN6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PRIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_ENDE" NomeLogico="ENDERECO" id="E1153">
      <Campos quantos="27">
        <Campo NomeFisico="ENDE2_COD" NomeLogico="CODIGO DO ENDERECO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ENDE2_TIPO" NomeLogico="TIPO" DataType="VARCHAR" Size="50" id="A10001"/>
        <Campo NomeFisico="ENDE2_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="100" id="A10002"/>
        <Campo NomeFisico="ENDE2_LOGR" NomeLogico="NOME LOGRADOURO" DataType="VARCHAR" Size="400" id="A10003"/>
        <Campo NomeFisico="ENDE2_NUM" NomeLogico="NUMERO" DataType="VARCHAR" Size="100" id="A10004"/>
        <Campo NomeFisico="ENDE2_COMPL" NomeLogico="COMPLEMENTO" DataType="VARCHAR" Size="200" id="A10005"/>
        <Campo NomeFisico="ENDE2_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="ENDE2_CEP" NomeLogico="CEP" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="ENDE2_BAI" NomeLogico="BAIRRO" DataType="VARCHAR" Size="200" id="A10008"/>
        <Campo NomeFisico="ENDE2_LAT" NomeLogico="LATITUDE" DataType="DOUBLE PRECISION" id="A10009"/>
        <Campo NomeFisico="ENDE2_LNG" NomeLogico="LONGITUDE" DataType="DOUBLE PRECISION" id="A10010"/>
        <Campo NomeFisico="ENDE2_GEOM" NomeLogico="GEOMETRIA" DataType="POINT" id="A10011"/>
        <Campo NomeFisico="ENDE2_COD_LOTE" NomeLogico="CODIGO DO LOTE" DataType="VARCHAR" Size="50" id="A10012"/>
        <Campo NomeFisico="ENDE2_COD_LOGR" NomeLogico="CODIGO DA RUA" DataType="VARCHAR" Size="50" id="A10013"/>
        <Campo NomeFisico="ENDE2_CIDA" NomeLogico="CIDADE" DataType="VARCHAR" Size="200" id="A10014"/>
        <Campo NomeFisico="ENDE2_UF" NomeLogico="ESTADO" DataType="VARCHAR" Size="50" id="A10015"/>
        <Campo NomeFisico="ENDE2_DIST_IBGE" NomeLogico="CODIGO DO DISTRITO" DataType="BIGINT" id="A10016"/>
        <Campo NomeFisico="ENDE2_SUBD_IBGE" NomeLogico="CODIGO DO SUBDISTRITO" DataType="BIGINT" id="A10017"/>
        <Campo NomeFisico="ENDE2_SECE_IBGE" NomeLogico="CODIGO DO SETOR" DataType="BIGINT" id="A10018"/>
        <Campo NomeFisico="ENDE2_TIPO_SECE" NomeLogico="TIPO DE SETOR" DataType="VARCHAR" Size="1" id="A10019"/>
        <Campo NomeFisico="ENDE2_ORIG_LOC" NomeLogico="ORIGEM DA LOCALIZACAO" DataType="VARCHAR" Size="18" id="A10020"/>
        <Campo NomeFisico="MUNI1_COD" NomeLogico="CODIGO IBGE MUNICIPIO" DataType="INTEGER" id="A10021"/>
        <Campo NomeFisico="ENDE2_PREC" NomeLogico="PRECISAO" DataType="INTEGER" id="A10022"/>
        <Campo NomeFisico="ENDE2_IDR" NomeLogico="ID REMOTO" DataType="INTEGER" id="A10023"/>
        <Campo NomeFisico="ENDE2_STRING" NomeLogico="STRING VERIFICACAO" DataType="TEXT" id="A10024"/>
        <Campo NomeFisico="ENDE2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10025"/>
        <Campo NomeFisico="ENDE2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10026"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_CTRC" NomeLogico="CONTRATO CRM" id="E1154">
      <Campos quantos="9">
        <Campo NomeFisico="CTRC6_COD" NomeLogico="ID CONTRATO CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CTRC6_NOM" NomeLogico="NOME CONTRATO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="CTRC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="TCTB1_COD" NomeLogico="CODIGO TIPO CONTRATO BIG" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CTRC6_DAT_CTRT" NomeLogico="DATA ASSOCIACAO CONTRATO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="CTRC6_CACHE" NomeLogico="CACHE" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="CTRC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_ATCC" NomeLogico="ATRIBUTO CONTRATO" id="E1155">
      <Campos quantos="6">
        <Campo NomeFisico="ATCC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATCC6_NOM" NomeLogico="NOME ATRIBUTO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ATCC6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="TDAT6_COD" NomeLogico="ID TIPO DADO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ATCC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATCC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_VACC" NomeLogico="VALOR ATRIBUTO CONTRATO" id="E1156">
      <Campos quantos="8">
        <Campo NomeFisico="VACC6_VAL" NomeLogico="VALOR" DataType="TEXT" NotNull="true" id="A10000"/>
        <Campo NomeFisico="VACC6_COMPL" NomeLogico="COMPLEMENTO" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="ATCC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CTRC6_COD" NomeLogico="ID CONTRATO CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="VACC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="VACC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_LATC" NomeLogico="LOOKUP ATRIBUTO CONTRATO" id="E1157">
      <Campos quantos="6">
        <Campo NomeFisico="LATC6_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="LATC6_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="LATC6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="ATCC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="LATC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LATC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_PCTC" NomeLogico="PROPRIEDADE CONTRATO CRM" id="E1158">
      <Campos quantos="4">
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CTRC6_COD" NomeLogico="ID CONTRATO CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PCTC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PCTC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_HCTC" NomeLogico="HISTORICO CONTRATO CRM" id="E1159">
      <Campos quantos="9">
        <Campo NomeFisico="HCTC6_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HCTC6_VAL" NomeLogico="VALOR ORIGINAL" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HCTC6_DAT_ORIG" NomeLogico="DATA ORIGINAL" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CTRC6_COD" NomeLogico="ID CONTRATO CRM" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ATCC6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="HCTC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="HCTC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_RESC" NomeLogico="RESISTENCIA CRM" id="E1160">
      <Campos quantos="8">
        <Campo NomeFisico="RESC6_COD" NomeLogico="ID RESISTENCIA CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="RESC6_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="RESC6_DAT_HR_FIM" NomeLogico="DATA ENCERRAMENTO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="RESC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_ATRE" NomeLogico="ATRIBUTO RESISTENCIA" id="E1161">
      <Campos quantos="6">
        <Campo NomeFisico="ATRE6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ATRE6_NOM" NomeLogico="NOME ATRIBUTO" DataType="VARCHAR" NotNull="true" Size="500" id="A10001"/>
        <Campo NomeFisico="ATRE6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="TDAT6_COD" NomeLogico="ID TIPO DADO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ATRE6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ATRE6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_VARE" NomeLogico="VALOR ATRIBUTO RESISTENCIA" id="E1162">
      <Campos quantos="8">
        <Campo NomeFisico="ATRE6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="RESC6_COD" NomeLogico="ID RESISTENCIA CRM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="VARE6_VAL" NomeLogico="VALOR" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="VARE6_COMPL" NomeLogico="COMPLEMENTO" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="VARE6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="VARE6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_LARE" NomeLogico="LOOKUP ATRIBUTO RESISTENCIA" id="E1163">
      <Campos quantos="6">
        <Campo NomeFisico="LARE6_TXT" NomeLogico="TEXTO" DataType="TEXT" NotNull="true" id="A10000"/>
        <Campo NomeFisico="LARE6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" Size="50" id="A10001"/>
        <Campo NomeFisico="LARE6_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="ATRE6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="LARE6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="LARE6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_HREC" NomeLogico="HISTORICO RESISTENCIA CRM" id="E1164">
      <Campos quantos="9">
        <Campo NomeFisico="HREC6_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HREC6_VAL" NomeLogico="VALOR ORIGINAL" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HREC6_DAT_ORIG" NomeLogico="DATA ORIGINAL" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="RESC6_COD" NomeLogico="ID RESISTENCIA CRM" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ATRE6_COD" NomeLogico="ID ATRIBUTO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="HREC6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="HREC6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_INTE" NomeLogico="INTERVENCAO" id="E1165">
      <Campos quantos="10">
        <Campo NomeFisico="INTE6_COD" NomeLogico="CODIGO INTERVENCAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="INTE6_DAT_HR_INI" NomeLogico="DATA DA INTERVENCAO" DataType="DATE" id="A10001"/>
        <Campo NomeFisico="INTE6_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="INTE6_TIPO" NomeLogico="TIPO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="PROC6_COD" NomeLogico="ID PROPRIEDADE CRM" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PCRM6_COD" NomeLogico="ID PESSOA CRM" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="RESC6_COD" NomeLogico="ID RESISTENCIA CRM" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="USER2_COD_INTE" NomeLogico="CODIGO USUARIO INTERVEIO" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="INTE6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PEUS" NomeLogico="PERMISSAO DO USUARIO" id="E1166">
      <Campos quantos="5">
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PERM2_COD" NomeLogico="CODIGO PERMISSAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PEUS2_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD_ATRIB" NomeLogico="CODIGO USUARIO ATRIBUIDOR" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PEUS2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RTPR" NomeLogico="ROTA PROPRIEDADE" id="E1167">
      <Campos quantos="8">
        <Campo NomeFisico="RTPR1_COD" NomeLogico="CODIGO ROTA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="RTPR1_GEOM" NomeLogico="GEOMETRIA" DataType="GEOMETRY" id="A10001"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="RTPR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="INTR6_COD" NomeLogico="CODIGO INTERACAO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="RTPR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="RTPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CARP" NomeLogico="CAR PROPRIEDADE" id="E1168">
      <Campos quantos="9">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CARP1_COD" NomeLogico="CODIGO DO CAR" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="CARP1_DAT_HR_INI" NomeLogico="DATA INICIO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CARP1_DAT_REC" NomeLogico="DATA RECIBO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="CARP1_DAT_HR_FIM" NomeLogico="DATA FINAL" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="CARP1_CACHE_DOCS" NomeLogico="CACHE DOCUMENTOS" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="PROP1_COD0" NomeLogico="ID DA PROPRIEDADE MAE" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="CARP1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_IGCA" NomeLogico="INFORMACAO GEOGRAFICA CAR" id="E1169">
      <Campos quantos="9">
        <Campo NomeFisico="TIGC1_COD" NomeLogico="CODIGO DO TIPO DE INFORMACAO" DataType="INTEGER" NotNull="true" id="A10000"/>
        <Campo NomeFisico="IGCA1_COM" NomeLogico="COMENTARIO DA INFO" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="IGCA1_DAT_HR_INI" NomeLogico="DATA UPLOAD" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="IGCA1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PCAR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="IGCA1_COD" NomeLogico="ID DA INFORMACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10006"/>
        <Campo NomeFisico="IGCA1_SRID" NomeLogico="SRID ORIGINAL" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="IGCA1_GDRIVE_ID" NomeLogico="ID GDRIVE" DataType="VARCHAR" Size="100" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TIGC" NomeLogico="TIPO INFORMACAO GEOGRAFICA CAR" id="E1170">
      <Campos quantos="7">
        <Campo NomeFisico="TIGC1_COD" NomeLogico="CODIGO DO TIPO DE INFORMACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TIGC1_NOM" NomeLogico="NOME DA INFO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TIGC1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="50" id="A10002"/>
        <Campo NomeFisico="TIGC1_JSON" NomeLogico="CONFIGURACAO" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="TIGC1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TIGC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="TIGC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="INTEGER" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_USER" NomeLogico="USUARIO" id="E1171">
      <Campos quantos="18">
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="USER2_DAT_HR_INI" NomeLogico="DATA E HORA DA CRIACAO" DataType="DATE" id="A10001"/>
        <Campo NomeFisico="PESS2_IMG" NomeLogico="IMAGEM DA PESSOA" DataType="VARCHAR" Size="255" id="A10002"/>
        <Campo NomeFisico="CARG2_COD" NomeLogico="CODIGO CARGO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="USER2_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="USER2_COD_CMS" NomeLogico="CODIGO DO USUARIO NO CMS" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_DEL" NomeLogico="REMOVIDO" DataType="BIT" id="A10006"/>
        <Campo NomeFisico="USER2_ID_FUNC" NomeLogico="IDENTIFICACAO FUNCIONAL" DataType="VARCHAR" Size="50" id="A10007"/>
        <Campo NomeFisico="USER2_ATI" NomeLogico="ATIVO" DataType="BIT" NotNull="true" id="A10008"/>
        <Campo NomeFisico="ESCR1_COD" NomeLogico="ID DO ESCRITORIO" DataType="INTEGER" id="A10009"/>
        <Campo NomeFisico="USER2_CONF" NomeLogico="CONFIGURACAO MAPA" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="USER2_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10011"/>
        <Campo NomeFisico="USER2_CACHE_PERMS" NomeLogico="CACHE PERMISSOES" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="USER2_GEST" NomeLogico="GESTOR" DataType="INTEGER" id="A10013"/>
        <Campo NomeFisico="USER2_EMAIL" NomeLogico="EMAIL" DataType="VARCHAR" Size="200" id="A10014"/>
        <Campo NomeFisico="USER2_RAMAL" NomeLogico="RAMAL" DataType="VARCHAR" Size="250" id="A10015"/>
        <Campo NomeFisico="USER2_CEL" NomeLogico="CELULAR" DataType="VARCHAR" Size="250" id="A10016"/>
        <Campo NomeFisico="USER2_BASE_OPER" NomeLogico="BASE OPERACIONAL" DataType="TEXT" id="A10017"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_SCAR" NomeLogico="STATUS CAR" id="E1172">
      <Campos quantos="7">
        <Campo NomeFisico="SCAR1_COD" NomeLogico="CODIGO STATUS" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="SCAR1_NOM" NomeLogico="NOME STATUS" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="SCAR1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="SCAR1_JSON" NomeLogico="CONFIGURACAO" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="SCAR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="SCAR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="SCAR1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HCAR" NomeLogico="HISTORICO CAR" id="E1173">
      <Campos quantos="10">
        <Campo NomeFisico="HCAR1_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HCAR1_DAT_HR_INI" NomeLogico="DATA ALTERACAO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HCAR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="SCAR1_COD" NomeLogico="CODIGO STATUS" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="TIGC1_COD" NomeLogico="CODIGO DO TIPO DE INFORMACAO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="PCAR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="HCAR1_JSON" NomeLogico="JSON DA ALTERACAO" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="HCAR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PCAR" NomeLogico="PROCESSO CAR" id="E1174">
      <Campos quantos="22">
        <Campo NomeFisico="PCAR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PCAR1_COD_CAR" NomeLogico="CODIGO CAR" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="SCAR1_COD" NomeLogico="CODIGO STATUS" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PCAR1_DAT_HR_INI" NomeLogico="DATA INICIO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PCAR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="PCAR1_DAT_REC" NomeLogico="DATA RECIBO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="PCAR1_DAT_HR_FIM" NomeLogico="DATA FINAL" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="PCAR1_DOC_PROP" NomeLogico="DOCUMENTO PROPRIEDADE DISPONIVEL" DataType="BIT" NotNull="true" id="A10008"/>
        <Campo NomeFisico="PCAR1_DOC_ID" NomeLogico="DOCUMENTO PROPRIEDADE" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="PCAR1_ATI" NomeLogico="PROCESSO ATIVO" DataType="BIT" NotNull="true" id="A10010"/>
        <Campo NomeFisico="PCAR1_PROCUR" NomeLogico="NECESSITA PROCURACAO" DataType="BIT" id="A10011"/>
        <Campo NomeFisico="PCAR1_PROC_OFIC" NomeLogico="INDICADOR DE PROCESSO OFICIAL" DataType="BIT" id="A10012"/>
        <Campo NomeFisico="PCAR1_TIPO" NomeLogico="TIPO DO PROCESSO" DataType="VARCHAR" NotNull="true" Size="10" id="A10013"/>
        <Campo NomeFisico="PCAR1_NOM" NomeLogico="TITULO DO PROCESSO" DataType="VARCHAR" NotNull="true" Size="400" id="A10014"/>
        <Campo NomeFisico="PCAR1_REF" NomeLogico="REFERENCIA DO PROCESSO" DataType="VARCHAR" NotNull="true" Size="18" id="A10015"/>
        <Campo NomeFisico="PCAR1_PRA" NomeLogico="POSSUI PRA" DataType="BIT" id="A10016"/>
        <Campo NomeFisico="PCAR1_PRAD" NomeLogico="POSSUI PRAD" DataType="BIT" id="A10017"/>
        <Campo NomeFisico="PCAR1_DAT_PROT" NomeLogico="DATA DO PROTOCOLO" DataType="DATE" id="A10018"/>
        <Campo NomeFisico="PCAR1_EQUIP" NomeLogico="EQUIPAMENTO MEDICAO" DataType="VARCHAR" Size="200" id="A10019"/>
        <Campo NomeFisico="STCC1_COD" NomeLogico="ID SITUACAO" DataType="INTEGER" id="A10020"/>
        <Campo NomeFisico="PCAR1_ID_CEFIR" NomeLogico="ID CEFIR" DataType="INTEGER" id="A10021"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PRPC" NomeLogico="PROPRIETARIO PROCESSO CAR" id="E1175">
      <Campos quantos="5">
        <Campo NomeFisico="PCAR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRPC1_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PRPC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_FICA" NomeLogico="FEICAO INFORMACAO CAR" id="E1176">
      <Campos quantos="7">
        <Campo NomeFisico="FICA1_COD" NomeLogico="ID DA FEICAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="FICA1_GEOM" NomeLogico="GEOMETRIA" DataType="GEOMETRY" id="A10001"/>
        <Campo NomeFisico="FICA1_DADOS" NomeLogico="DADOS DA GEOMETRIA" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="IGCA1_COD" NomeLogico="ID DA INFORMACAO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="FICA1_DIM" NomeLogico="DIMENSAO" DataType="DOUBLE PRECISION" id="A10004"/>
        <Campo NomeFisico="FICA1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="FICA1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TPRC" NomeLogico="TIPO PROTOCOLO CAR" id="E1177">
      <Campos quantos="6">
        <Campo NomeFisico="TPRC1_COD" NomeLogico="CODIGO TIPO PROTOCOLO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TPRC1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TPRC1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TPRC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TPRC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="TPRC1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TPPR" NomeLogico="TIPO PROTOCOLO PROCESSO" id="E1178">
      <Campos quantos="4">
        <Campo NomeFisico="PCAR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="TPRC1_COD" NomeLogico="CODIGO TIPO PROTOCOLO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="TPPR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="TPPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PCAP" NomeLogico="PROCESSOS CAR PROPRIEDADE" id="E1179">
      <Campos quantos="6">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PCAR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PCAP1_REF" NomeLogico="REFERENCIA" DataType="VARCHAR" NotNull="true" Size="30" id="A10002"/>
        <Campo NomeFisico="PCAP1_DAT_HR_INI" NomeLogico="DATA INICIO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PCAP1_NOM" NomeLogico="TITULO PROCESSO" DataType="VARCHAR" NotNull="true" Size="400" id="A10004"/>
        <Campo NomeFisico="PCAP1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_CARG" NomeLogico="CARGO" id="E1180">
      <Campos quantos="6">
        <Campo NomeFisico="CARG2_COD" NomeLogico="CODIGO CARGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CARG2_NOM" NomeLogico="NOME DO CARGO" DataType="VARCHAR" Size="200" id="A10001"/>
        <Campo NomeFisico="CARG2_DES" NomeLogico="DESCRICAO DO CARGO" DataType="VARCHAR" Size="500" id="A10002"/>
        <Campo NomeFisico="CARG2_ATI" NomeLogico="INDICADOR DE CARGO ATIVO" DataType="BIT" id="A10003"/>
        <Campo NomeFisico="CARG2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="CARG2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RRACM" NomeLogico="RESSALVA RECUSA ARQUIVO COMPLEXO" id="E1181">
      <Campos quantos="3">
        <Campo NomeFisico="RRACM1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ARQU_COMPL1_COD" NomeLogico="ID ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="MORR1_COD" NomeLogico="ID MOTIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_ARQU_PROJ" NomeLogico="ARQUIVO PROJETO" id="E1182">
      <Campos quantos="23">
        <Campo NomeFisico="ARQU_PROJ1_DAT_UPLOAD" NomeLogico="DATA UPLOAD" DataType="DATE" id="A10000"/>
        <Campo NomeFisico="ARQU_PROJ1_DAT_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10001"/>
        <Campo NomeFisico="ARQU_PROJ1_ID" NomeLogico="ID GDRIVE" DataType="VARCHAR" NotNull="true" Size="100" id="A10002"/>
        <Campo NomeFisico="ARQU_PROJ1_DAT_ULT_SINC" NomeLogico="DATA ULTIMA SINCRONIA" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="ARQU_PROJ1_DAT_VER" NomeLogico="DATA VERIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="ARQU_PROJ1_ST_VERIF" NomeLogico="STATUS VERIFICACAO" DataType="VARCHAR" Size="20" id="A10005"/>
        <Campo NomeFisico="ARQU_PROJ1_DAT_VAL_VER" NomeLogico="DATA VALIDADE VERIFICACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="ARQU_PROJ1_DAT_RECU" NomeLogico="DATA RECUSA" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="ARQU_PROJ1_MOT_RECU" NomeLogico="MOTIVO RECUSA" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="ARQU_PROJ1_GD_CACHE" NomeLogico="CACHE GDRIVE" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="ARQU_PROJ1_DEL" NomeLogico="EXCLUIDO" DataType="DATE" id="A10010"/>
        <Campo NomeFisico="ARQU_PROJ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10011"/>
        <Campo NomeFisico="ARQU_PROJ1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO REGISTRO" DataType="DATE" id="A10012"/>
        <Campo NomeFisico="ARQU_PROJ1_ST_NOM" NomeLogico="STATUS NOMENCLATURA" DataType="TEXT" id="A10013"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" NotNull="true" id="A10014"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10015"/>
        <Campo NomeFisico="STVARQ1_COD" NomeLogico="CODIGO STATUS VERIFICACAO" DataType="INTEGER" id="A10016"/>
        <Campo NomeFisico="PAAR1_COD" NomeLogico="CODIGO DO PADRAO" DataType="INTEGER" id="A10017"/>
        <Campo NomeFisico="USER2_COD_VERIF" NomeLogico="USUARIO VERIFICADOR" DataType="INTEGER" id="A10018"/>
        <Campo NomeFisico="ARQU_PROJ1_COD" NomeLogico="ID ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10019"/>
        <Campo NomeFisico="ARQU_PROJ1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="500" id="A10020"/>
        <Campo NomeFisico="ARQU_PROJ1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10021"/>
        <Campo NomeFisico="ARQU_PROJ1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="500" id="A10022"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LAPJ" NomeLogico="LOG ARQUIVO PROJETO" id="E1183">
      <Campos quantos="7">
        <Campo NomeFisico="LAPJ0_COD" NomeLogico="CODIGO LOG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LAPJ0_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="LAPJ0_CAMPO" NomeLogico="CAMPO ALTERADO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="LAPJ0_ORIG" NomeLogico="VALOR ORIGINAL" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="LAPJ0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="LAPJ0_DADO" NomeLogico="DADOS ADICIONAIS" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="ARQU_PROJ1_COD" NomeLogico="CODIGO ARQUIVO" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RRAPJ" NomeLogico="RESSALVA RECUSA ARQUIVO PROJETO" id="E1184">
      <Campos quantos="3">
        <Campo NomeFisico="RRAPJ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ARQU_PROJ1_COD" NomeLogico="ID ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="MORR1_COD" NomeLogico="ID MOTIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_ARQU_PARQ" NomeLogico="ARQUIVO PARQUE" id="E1185">
      <Campos quantos="23">
        <Campo NomeFisico="ARQU_PARQ1_COD" NomeLogico="ID ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ARQU_PARQ1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="500" id="A10001"/>
        <Campo NomeFisico="ARQU_PARQ1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="ARQU_PARQ1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="500" id="A10003"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_UPLOAD" NomeLogico="DATA UPLOAD" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="ARQU_PARQ1_ID" NomeLogico="ID GDRIVE" DataType="VARCHAR" NotNull="true" Size="100" id="A10006"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_ULT_SINC" NomeLogico="DATA ULTIMA SINCRONIA" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_VER" NomeLogico="DATA VERIFICACAO" DataType="DATE" id="A10008"/>
        <Campo NomeFisico="ARQU_PARQ1_ST_VERIF" NomeLogico="STATUS VERIFICACAO" DataType="VARCHAR" Size="20" id="A10009"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_VAL_VER" NomeLogico="DATA VALIDADE VERIFICACAO" DataType="DATE" id="A10010"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_RECU" NomeLogico="DATA RECUSA" DataType="DATE" id="A10011"/>
        <Campo NomeFisico="ARQU_PARQ1_MOT_RECU" NomeLogico="MOTIVO RECUSA" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="ARQU_PARQ1_GD_CACHE" NomeLogico="CACHE GDRIVE" DataType="TEXT" id="A10013"/>
        <Campo NomeFisico="ARQU_PARQ1_DEL" NomeLogico="EXCLUIDO" DataType="DATE" id="A10014"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10015"/>
        <Campo NomeFisico="ARQU_PARQ1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO REGISTRO" DataType="DATE" id="A10016"/>
        <Campo NomeFisico="ARQU_PARQ1_ST_NOM" NomeLogico="STATUS NOMENCLATURA" DataType="TEXT" id="A10017"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" NotNull="true" id="A10018"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10019"/>
        <Campo NomeFisico="STVARQ1_COD" NomeLogico="CODIGO STATUS VERIFICACAO" DataType="INTEGER" id="A10020"/>
        <Campo NomeFisico="PAAR1_COD" NomeLogico="CODIGO DO PADRAO" DataType="INTEGER" id="A10021"/>
        <Campo NomeFisico="USER2_COD_VERIF" NomeLogico="CODIGO USUARIO VERIFICADOR" DataType="INTEGER" id="A10022"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LAPQ" NomeLogico="LOG ARQUIVO_PARQUE" id="E1186">
      <Campos quantos="7">
        <Campo NomeFisico="LAPQ0_COD" NomeLogico="CODIGO LOG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LAPQ0_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="LAPQ0_CAMPO" NomeLogico="CAMPO ALTERADO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="LAPQ0_ORIG" NomeLogico="VALOR ORIGINAL" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="LAPQ0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="LAPQ0_DADO" NomeLogico="DADOS ADICIONAIS" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="ARQU_PARQ1_COD" NomeLogico="CODIGO ARQUIVO" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_RRAPQ" NomeLogico="RESSALVA RECUSA ARQUIVO PARQUE" id="E1187">
      <Campos quantos="3">
        <Campo NomeFisico="RRAPQ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ARQU_PARQ1_COD" NomeLogico="ID ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="MORR1_COD" NomeLogico="ID MOTIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_ARQU_COMPL" NomeLogico="ARQUIVO COMPLEXO" id="E1188">
      <Campos quantos="23">
        <Campo NomeFisico="ARQU_COMPL1_COD" NomeLogico="ID ARQUIVO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ARQU_COMPL1_TIT" NomeLogico="TITULO" DataType="VARCHAR" Size="500" id="A10001"/>
        <Campo NomeFisico="ARQU_COMPL1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="ARQU_COMPL1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="500" id="A10003"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_UPLOAD" NomeLogico="DATA UPLOAD" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="ARQU_COMPL1_ID" NomeLogico="ID GDRIVE" DataType="VARCHAR" NotNull="true" Size="100" id="A10006"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_ULT_SINC" NomeLogico="DATA ULTIMA SINCRONIA" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_VER" NomeLogico="DATA VERIFICACAO" DataType="DATE" id="A10008"/>
        <Campo NomeFisico="ARQU_COMPL1_ST_VERIF" NomeLogico="STATUS VERIFICACAO" DataType="VARCHAR" Size="20" id="A10009"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_VAL_VER" NomeLogico="DATA VALIDADE VERIFICACAO" DataType="DATE" id="A10010"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_RECU" NomeLogico="DATA RECUSA" DataType="DATE" id="A10011"/>
        <Campo NomeFisico="ARQU_COMPL1_MOT_RECU" NomeLogico="MOTIVO RECUSA" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="ARQU_COMPL1_GD_CACHE" NomeLogico="CACHE GDRIVE" DataType="TEXT" id="A10013"/>
        <Campo NomeFisico="ARQU_COMPL1_DEL" NomeLogico="EXCLUIDO" DataType="DATE" id="A10014"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10015"/>
        <Campo NomeFisico="ARQU_COMPL1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO REGISTRO" DataType="DATE" id="A10016"/>
        <Campo NomeFisico="ARQU_COMPL1_ST_NOM" NomeLogico="STATUS NOMENCLATURA" DataType="TEXT" id="A10017"/>
        <Campo NomeFisico="COMPL1_COD" NomeLogico="CODIGO DO COMPLEXO" DataType="INTEGER" NotNull="true" id="A10018"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10019"/>
        <Campo NomeFisico="STVARQ1_COD" NomeLogico="CODIGO STATUS VERIFICACAO" DataType="INTEGER" id="A10020"/>
        <Campo NomeFisico="PAAR1_COD" NomeLogico="CODIGO DO PADRAO" DataType="INTEGER" id="A10021"/>
        <Campo NomeFisico="USER2_COD_VERIF" NomeLogico="USUARIO VERIFICADOR" DataType="INTEGER" id="A10022"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB0_LACM" NomeLogico="LOG ARQUIVO_COMPLEXO" id="E1189">
      <Campos quantos="7">
        <Campo NomeFisico="LACM0_COD" NomeLogico="CODIGO LOG" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="LACM0_DAT_HR_INI" NomeLogico="DATA" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="LACM0_CAMPO" NomeLogico="CAMPO ALTERADO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="LACM0_ORIG" NomeLogico="VALOR ORIGINAL" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="LACM0_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="LACM0_DADO" NomeLogico="DADOS ADICIONAIS" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="ARQU_COMPL1_COD" NomeLogico="CODIGO ARQUIVO" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STCC" NomeLogico="SITUACAO CENTRAL CAR" id="E1190">
      <Campos quantos="7">
        <Campo NomeFisico="STCC1_COD" NomeLogico="ID SITUACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STCC1_NOM" NomeLogico="NOME SITUACAO" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STCC1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10002"/>
        <Campo NomeFisico="STCC1_JSON" NomeLogico="DEFINICOES JSON" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="STCC1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STCC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="STCC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB6_TIIN" NomeLogico="TIPO INTERACAO" id="E1191">
      <Campos quantos="7">
        <Campo NomeFisico="TIIN6_COD" NomeLogico="CODIGO TIPO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TIIN6_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TIIN6_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="50" id="A10002"/>
        <Campo NomeFisico="TIIN6_JSON" NomeLogico="PARAMETROS JSON" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="TIIN6_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TIIN6_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="TIIN6_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PPRC" NomeLogico="PROTOCOLO DO PROCESSO CAR" id="E1192">
      <Campos quantos="11">
        <Campo NomeFisico="PPRC1_COD" NomeLogico="ID PROTOCOLO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PPRC1_NUM" NomeLogico="NUMERO" DataType="TEXT" id="A10001"/>
        <Campo NomeFisico="PPRC1_COMENT" NomeLogico="COMENTARIO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="PPRC1_DAT_PROT" NomeLogico="DATA PROTOCOLO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="PPRC1_DAT_CONC" NomeLogico="DATA CONCLUSAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PPRC1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PPRC1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="TPRC1_COD" NomeLogico="CODIGO TIPO PROTOCOLO" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="PCAR1_COD" NomeLogico="CODIGO PROCESSO" DataType="INTEGER" NotNull="true" id="A10008"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10009"/>
        <Campo NomeFisico="USER2_COD_CONC" NomeLogico="CODIGO USUARIO CONCLUIU" DataType="INTEGER" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HIPR" NomeLogico="HISTORICO PROPRIEDADE" id="E1193">
      <Campos quantos="9">
        <Campo NomeFisico="HIPR1_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HIPR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HIPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="HIPR1_CAMPO" NomeLogico="CAMPO" DataType="VARCHAR" Size="50" id="A10003"/>
        <Campo NomeFisico="HIPR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="HIPR1_VAL_ORIG" NomeLogico="VALOR ORIGINAL" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="HIPR1_VAL_NOVO" NomeLogico="NOVO VALOR" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10007"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STBE" NomeLogico="STATUS BENFEITORIA" id="E1194">
      <Campos quantos="7">
        <Campo NomeFisico="STBE1_COD" NomeLogico="CODIGO STATUS BENFEITORIA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STBE1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="STBE1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STBE1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10003"/>
        <Campo NomeFisico="STBE1_ORD" NomeLogico="ORDEM" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STBE1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="STBE1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_MUNI" NomeLogico="MUNICIPIO" id="E1195">
      <Campos quantos="6">
        <Campo NomeFisico="MUNI1_COD" NomeLogico="CODIGO IBGE MUNICIPIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="MUNI1_NOM" NomeLogico="NOME DO MUNICIPIO" DataType="VARCHAR" NotNull="true" Size="300" id="A10001"/>
        <Campo NomeFisico="ESTA1_COD" NomeLogico="CODIGO IBGE ESTADO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="MUNI1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGON" id="A10003"/>
        <Campo NomeFisico="MUNI1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="MUNI1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TDFT" NomeLogico="TIPO DEMANDA FT" id="E1196">
      <Campos quantos="7">
        <Campo NomeFisico="TDFT1_COD" NomeLogico="CODIGO TIPO DEMANDA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TDFT1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TDFT1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TDFT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TDFT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="TDFT1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10005"/>
        <Campo NomeFisico="TAFT1_COD" NomeLogico="CODIGO TIPO ATUACAO" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_ESTA" NomeLogico="ESTADO" id="E1197">
      <Campos quantos="8">
        <Campo NomeFisico="ESTA1_COD" NomeLogico="CODIGO IBGE ESTADO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ESTA1_NOM" NomeLogico="NOME DO ESTADO" DataType="VARCHAR" NotNull="true" Size="50" id="A10001"/>
        <Campo NomeFisico="ESTA1_UF" NomeLogico="ABREVIATURA" DataType="VARCHAR" NotNull="true" Size="2" id="A10002"/>
        <Campo NomeFisico="ESTA1_REG" NomeLogico="REGIAO" DataType="VARCHAR" Size="50" id="A10003"/>
        <Campo NomeFisico="ESTA1_ABR_REG" NomeLogico="ABREVIATURA REGIAO" DataType="VARCHAR" Size="2" id="A10004"/>
        <Campo NomeFisico="ESTA1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGON" id="A10005"/>
        <Campo NomeFisico="ESTA1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="ESTA1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_TAFT" NomeLogico="TIPO ATUACAO FT" id="E1198">
      <Campos quantos="6">
        <Campo NomeFisico="TAFT1_COD" NomeLogico="CODIGO TIPO ATUACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TAFT1_NOM" NomeLogico="NOME" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="TAFT1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="TAFT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TAFT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="TAFT1_COD_TXT" NomeLogico="CODIGO TEXTO" DataType="VARCHAR" NotNull="true" Size="20" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PJUR" NomeLogico="PESSOA JURIDICA" id="E1199">
      <Campos quantos="27">
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PJUR2_RS" NomeLogico="RAZAO SOCIAL" DataType="VARCHAR" NotNull="true" Size="500" id="A10001"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PJUR2_FANT" NomeLogico="NOME FANTASIA" DataType="VARCHAR" Size="400" id="A10003"/>
        <Campo NomeFisico="PJUR2_DAT_ABER" NomeLogico="DATA DE ABERTURA" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PJUR2_LOGR" NomeLogico="LOGRADOURO" DataType="VARCHAR" Size="400" id="A10005"/>
        <Campo NomeFisico="PJUR2_NUM" NomeLogico="NUMERO" DataType="VARCHAR" Size="40" id="A10006"/>
        <Campo NomeFisico="PJUR2_COMPL" NomeLogico="COMPLEMENTO" DataType="VARCHAR" Size="200" id="A10007"/>
        <Campo NomeFisico="PJUR2_MATRIZ" NomeLogico="MATRIZ" DataType="BIT" id="A10008"/>
        <Campo NomeFisico="PJUR2_CNAE" NomeLogico="CNAE PRINCIPAL" DataType="INTEGER" id="A10009"/>
        <Campo NomeFisico="PJUR2_CAP_SOC" NomeLogico="CAPITAL SOCIAL" DataType="DOUBLE PRECISION" id="A10010"/>
        <Campo NomeFisico="NAJU2_COD" NomeLogico="CODIGO DA NATUREZA" DataType="INTEGER" NotNull="true" id="A10011"/>
        <Campo NomeFisico="SITU2_COD" NomeLogico="CODIGO DA SITUACAO" DataType="INTEGER" NotNull="true" id="A10012"/>
        <Campo NomeFisico="SIES2_COD" NomeLogico="CODIGO DA SITUACAO ESPECIAL" DataType="INTEGER" id="A10013"/>
        <Campo NomeFisico="PJUR2_DAT_SITU" NomeLogico="DATA DA SITUACAO" DataType="DATE" id="A10014"/>
        <Campo NomeFisico="PJUR2_DAT_SIES" NomeLogico="DATA DA SITUACAO ESPECIAL" DataType="DATE" id="A10015"/>
        <Campo NomeFisico="MUNI1_COD" NomeLogico="CODIGO IBGE MUNICIPIO" DataType="INTEGER" id="A10016"/>
        <Campo NomeFisico="PJUR2_DAT_HR_INI" NomeLogico="DATA DE CRIACAO DO REGISTRO" DataType="DATE" id="A10017"/>
        <Campo NomeFisico="PJUR2_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10018"/>
        <Campo NomeFisico="PJUR2_LAT" NomeLogico="LATITUDE" DataType="DOUBLE PRECISION" id="A10019"/>
        <Campo NomeFisico="PJUR2_LONG" NomeLogico="LONGITUDE" DataType="DOUBLE PRECISION" id="A10020"/>
        <Campo NomeFisico="PJUR2_GEOM" NomeLogico="GEOMETRIA" DataType="POINT" id="A10021"/>
        <Campo NomeFisico="PJUR2_DV" NomeLogico="DIGITO VERIFICADOR" DataType="INTEGER" id="A10022"/>
        <Campo NomeFisico="PJUR2_BAI" NomeLogico="BAIRRO" DataType="VARCHAR" Size="200" id="A10023"/>
        <Campo NomeFisico="PJUR2_CEP" NomeLogico="CEP" DataType="VARCHAR" Size="15" id="A10024"/>
        <Campo NomeFisico="PJUR2_CIDA" NomeLogico="CIDADE" DataType="VARCHAR" Size="400" id="A10025"/>
        <Campo NomeFisico="PJUR2_UF" NomeLogico="ESTADO" DataType="VARCHAR" Size="100" id="A10026"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_NAJU" NomeLogico="NATUREZA JURIDICA" id="E1200">
      <Campos quantos="4">
        <Campo NomeFisico="NAJU2_COD" NomeLogico="CODIGO DA NATUREZA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="NAJU2_NOM" NomeLogico="NATUREZA JURIDICA" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="NAJU2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="NAJU2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_DPFT" NomeLogico="DEMANDA PROPRIEDADE FT" id="E1201">
      <Campos quantos="9">
        <Campo NomeFisico="DPFT1_COD" NomeLogico="CODIGO DEMANDA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="DPFT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="DPFT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="DPFT1_ENC" NomeLogico="INDICADOR ENCAMINHAMENTO" DataType="BIT" id="A10003"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TDFT1_COD" NomeLogico="CODIGO TIPO DEMANDA" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="DPFT1_DAT_CONC" NomeLogico="DATA CONCLUSAO" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="DPFT1_DAT_PREV_CONCL" NomeLogico="DATA PREVISTA CONCLUSAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_SITU" NomeLogico="SITUACAO" id="E1202">
      <Campos quantos="4">
        <Campo NomeFisico="SITU2_COD" NomeLogico="CODIGO DA SITUACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="SITU2_NOM" NomeLogico="SITUACAO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="SITU2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="SITU2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HIPQ" NomeLogico="HISTORICO PARQUE" id="E1203">
      <Campos quantos="10">
        <Campo NomeFisico="HIPQ1_COD" NomeLogico="CODIGO HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HIPQ1_DES" NomeLogico="DESCRICAO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HIPQ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="HIPQ1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="HIPQ1_TIPO" NomeLogico="TIPO" DataType="VARCHAR" NotNull="true" Size="20" id="A10004"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
        <Campo NomeFisico="HIPQ1_CAMPO" NomeLogico="CAMPO" DataType="VARCHAR" NotNull="true" Size="50" id="A10007"/>
        <Campo NomeFisico="HIPQ1_GEOM_ANT" NomeLogico="GEOMETRIA ANTERIOR" DataType="MULTIPOLYGON" id="A10008"/>
        <Campo NomeFisico="HIPQ1_VAL_ANT" NomeLogico="VALOR ANTERIOR" DataType="TEXT" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_SIES" NomeLogico="SITUACAO ESPECIAL" id="E1204">
      <Campos quantos="4">
        <Campo NomeFisico="SIES2_COD" NomeLogico="CODIGO DA SITUACAO ESPECIAL" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="SIES2_NOM" NomeLogico="SITUACAO ESPECIAL" DataType="VARCHAR" NotNull="true" Size="500" id="A10001"/>
        <Campo NomeFisico="SIES2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="SIES2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PFIS" NomeLogico="PESSOA FISICA" id="E1205">
      <Campos quantos="11">
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PFIS2_NOM" NomeLogico="NOME DA PESSOA" DataType="VARCHAR" NotNull="true" Size="500" id="A10001"/>
        <Campo NomeFisico="PFIS2_CPF" NomeLogico="CPF" DataType="BIGINT" id="A10002"/>
        <Campo NomeFisico="PFIS2_SEX" NomeLogico="SEXO" DataType="VARCHAR" Size="1" id="A10003"/>
        <Campo NomeFisico="PFIS2_DAT_NASC" NomeLogico="DATA DE NASCIMENTO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PFIS2_RG" NomeLogico="RG" DataType="BIGINT" id="A10005"/>
        <Campo NomeFisico="PFIS2_EMRG" NomeLogico="EMISSOR RG" DataType="VARCHAR" Size="150" id="A10006"/>
        <Campo NomeFisico="PFIS2_MAE" NomeLogico="NOME DA MAE" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="PFIS2_PAI" NomeLogico="NOME DO PAI" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="PFIS2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10009"/>
        <Campo NomeFisico="PFIS2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_SOAM" NomeLogico="SOCIO OU ADMINISTRADOR" id="E1206">
      <Campos quantos="10">
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="SOAM2_ATRIB" NomeLogico="ATRIBUICAO" DataType="VARCHAR" Size="200" id="A10003"/>
        <Campo NomeFisico="SOAM2_PART" NomeLogico="PARTICIPACAO" DataType="FLOAT" id="A10004"/>
        <Campo NomeFisico="SOAM2_DAT_ENTR" NomeLogico="DATA ENTRADA" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="SOAM2_RL" NomeLogico="REPRESENTANTE LEGAL" DataType="VARCHAR" Size="400" id="A10006"/>
        <Campo NomeFisico="SOAM2_ATRIB_RL" NomeLogico="ATRIBUICAO REPRESENTANTE" DataType="VARCHAR" Size="200" id="A10007"/>
        <Campo NomeFisico="SOAM2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10008"/>
        <Campo NomeFisico="SOAM2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_ENPF" NomeLogico="ENDERECO PESSOA FISICA" id="E1207">
      <Campos quantos="5">
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="ENDE2_COD" NomeLogico="CODIGO DO ENDERECO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="ENPF2_DESC" NomeLogico="DESCRICAO" DataType="VARCHAR" Size="500" id="A10002"/>
        <Campo NomeFisico="ENPF2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ENPF2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_TLPF" NomeLogico="TELEFONE PESSOA FISICA" id="E1208">
      <Campos quantos="6">
        <Campo NomeFisico="TELE2_COD" NomeLogico="CODIGO DO TELEFONE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="TLPF2_DESC" NomeLogico="DESCRICAO" DataType="VARCHAR" Size="500" id="A10001"/>
        <Campo NomeFisico="TLPF2_DAT_ALO" NomeLogico="DATA DO ULTIMO ALO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="TLPF2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TLPF2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_TLPJ" NomeLogico="TELEFONE PESSOA JURIDICA" id="E1209">
      <Campos quantos="7">
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="TELE2_COD" NomeLogico="CODIGO DO TELEFONE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="TLPJ2_DESC" NomeLogico="DESCRICAO" DataType="VARCHAR" Size="500" id="A10003"/>
        <Campo NomeFisico="TLPJ2_DAT_ALO" NomeLogico="DATA DO ULTIMO ALO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="TLPJ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="TLPJ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_CNAE" NomeLogico="CNAE" id="E1210">
      <Campos quantos="4">
        <Campo NomeFisico="CNAE2_COD" NomeLogico="CODIGO DO CNAE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CNAE2_TIT" NomeLogico="TITULO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CNAE2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CNAE2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_CNPJ" NomeLogico="CNAE DA PJ" id="E1211">
      <Campos quantos="6">
        <Campo NomeFisico="CNAE2_COD" NomeLogico="CODIGO DO CNAE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CNPJ2_ORD" NomeLogico="ORDEM" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CNPJ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CNPJ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_ARUS" NomeLogico="AREA DO USUARIO" id="E1212">
      <Campos quantos="7">
        <Campo NomeFisico="ARUS2_COD" NomeLogico="CODIGO DA AREA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ARUS2_NOM" NomeLogico="NOME DA AREA" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ARUS2_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGON" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ARUS2_PUB" NomeLogico="COMPARTILHADA" DataType="BIT" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ARUS2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="ARUS2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_EMPJ" NomeLogico="EMAIL PJ" id="E1213">
      <Campos quantos="8">
        <Campo NomeFisico="EMPJ2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="EMPJ2_EMAIL" NomeLogico="EMAIL" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="EMPJ2_VALIDO" NomeLogico="INDICADOR DE EMAIL VALIDADO" DataType="BIT" id="A10004"/>
        <Campo NomeFisico="EMPJ2_DESC" NomeLogico="DESCRICAO" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="EMPJ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10006"/>
        <Campo NomeFisico="EMPJ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_EMPF" NomeLogico="EMAIL PF" id="E1214">
      <Campos quantos="6">
        <Campo NomeFisico="EMPF2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="EMPF2_EMAIL" NomeLogico="EMAIL" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="EMPF2_VALIDO" NomeLogico="INDICADOR DE EMAIL VALIDADO" DataType="BIT" id="A10002"/>
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="EMPF2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="EMPF2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_SOPJ" NomeLogico="SOCIO PJ" id="E1215">
      <Campos quantos="14">
        <Campo NomeFisico="SOPJ2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="SOPJ2_RL" NomeLogico="REPRESENTANTE LEGAL" DataType="VARCHAR" Size="400" id="A10004"/>
        <Campo NomeFisico="SOPJ2_ATRIB" NomeLogico="ATRIBUICAO" DataType="VARCHAR" Size="200" id="A10005"/>
        <Campo NomeFisico="SOPJ2_PAIS_ORIG" NomeLogico="PAIS ORIGEM" DataType="VARCHAR" Size="200" id="A10006"/>
        <Campo NomeFisico="SOPJ2_DAT_ENTR" NomeLogico="DATA ENTRADA" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="SOPJ2_CNPJ" NomeLogico="CNPJ DO SOCIO" DataType="BIGINT" NotNull="true" id="A10008"/>
        <Campo NomeFisico="PJUR2_CNPJ_SOCIO" NomeLogico="RAIZ CNPJ DO SOCIO" DataType="INTEGER" id="A10009"/>
        <Campo NomeFisico="PJUR2_SEQ_SOCIO" NomeLogico="SEQUENCIAL DO SOCIO" DataType="INTEGER" id="A10010"/>
        <Campo NomeFisico="SOPJ2_RS" NomeLogico="RAZAO SOCIAL" DataType="VARCHAR" Size="400" id="A10011"/>
        <Campo NomeFisico="SOPJ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10012"/>
        <Campo NomeFisico="SOPJ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10013"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_TSPJ" NomeLogico="TELEFONE SOCIO PJ" id="E1216">
      <Campos quantos="6">
        <Campo NomeFisico="SOPJ2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="TELE2_COD" NomeLogico="CODIGO DO TELEFONE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="TSPJ2_DESC" NomeLogico="DESCRICAO" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="TSPJ2_DAT_ALO" NomeLogico="DATA DO ULTIMO ALO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="TSPJ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="TSPJ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_ESPJ" NomeLogico="EMAIL SOCIO PJ" id="E1217">
      <Campos quantos="6">
        <Campo NomeFisico="ESPJ2_COD" NomeLogico="CODIGO EMAIL" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ESPJ2_EMAIL" NomeLogico="EMAIL" DataType="VARCHAR" NotNull="true" Size="200" id="A10001"/>
        <Campo NomeFisico="SOPJ2_COD" NomeLogico="CODIGO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="ESPJ2_VALIDO" NomeLogico="INDICADOR DE EMAIL VALIDADO" DataType="BIT" id="A10003"/>
        <Campo NomeFisico="ESPJ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="ESPJ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_FUPJ" NomeLogico="FUNCIONARIO PJ" id="E1218">
      <Campos quantos="9">
        <Campo NomeFisico="FUPJ2_COD" NomeLogico="CODIGO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="FUPJ2_CARGO" NomeLogico="CARGO" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="FUPJ2_DAT_ADM" NomeLogico="DATA ADMISSAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="FUPJ2_DAT_DEM" NomeLogico="DATA DEMISSAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="FUPJ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10007"/>
        <Campo NomeFisico="FUPJ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CTRT" NomeLogico="CONTRATO" id="E1219">
      <Campos quantos="52">
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CTRT1_COD_TXT" NomeLogico="CODIGO CONTRATO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="CTRT1_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="CTRT1_DAT_HR_FIM" NomeLogico="DATA DA CONCLUSAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="CTRT1_COD_ERP" NomeLogico="CHAVE  ERP" DataType="TEXT" NotNull="true" id="A10005"/>
        <Campo NomeFisico="CTRT1_COD_DOCS" NomeLogico="CODIGO GODOCS" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" id="A10007"/>
        <Campo NomeFisico="STCT1_COD" NomeLogico="COD STATUS CONTRATO" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="CTRT1_DAT_ENC" NomeLogico="DATA DE FINAL DE VALIDADE" DataType="DATE" id="A10009"/>
        <Campo NomeFisico="CTRT1_INDISP" NomeLogico="INDICADOR DE CONTRATO INDISPONIVEL" DataType="BIT" id="A10010"/>
        <Campo NomeFisico="CTRT1_ERP_JSON" NomeLogico="ULTIMO VALOR ERP" DataType="TEXT" id="A10011"/>
        <Campo NomeFisico="CTRT1_DAT_ERP" NomeLogico="DATA ATUALIZACAO ERP" DataType="DATE" id="A10012"/>
        <Campo NomeFisico="CTRT1_ORIG" NomeLogico="ORIGEM DO DADO" DataType="VARCHAR" NotNull="true" Size="100" id="A10013"/>
        <Campo NomeFisico="CTRT1_DAT_PRIO_ERP" NomeLogico="DATA ATUALIZACAO PROPRIETARIOS ERP" DataType="DATE" id="A10014"/>
        <Campo NomeFisico="CTRT1_ADITIVO" NomeLogico="STATUS ADITIVO" DataType="INTEGER" id="A10015"/>
        <Campo NomeFisico="CTRT1_TIPO" NomeLogico="STATUS TIPO" DataType="INTEGER" id="A10016"/>
        <Campo NomeFisico="CTRT1_APTO" NomeLogico="STATUS APTO LEILAO" DataType="INTEGER" id="A10017"/>
        <Campo NomeFisico="CTRT1_REV" NomeLogico="CONTRATO REVISADO" DataType="BIT" id="A10018"/>
        <Campo NomeFisico="CTRT1_VAL_ANO" NomeLogico="VALOR ANO CORRENTE" DataType="DOUBLE PRECISION" id="A10019"/>
        <Campo NomeFisico="CTRT1_VAL_ACUM" NomeLogico="VALOR ACUMULADO" DataType="DOUBLE PRECISION" id="A10020"/>
        <Campo NomeFisico="CTRT1_VAL_CTRT" NomeLogico="VALOR CONTRATO" DataType="DOUBLE PRECISION" id="A10021"/>
        <Campo NomeFisico="CTRT1_VAL_EXTRA" NomeLogico="VALOR EXTRA" DataType="DOUBLE PRECISION" id="A10022"/>
        <Campo NomeFisico="CTRT1_DAT_REG" NomeLogico="DATA DE REGISTRO" DataType="DATE" id="A10023"/>
        <Campo NomeFisico="CTRT1_VAL_ATRASO" NomeLogico="VALOR ATRASO" DataType="DOUBLE PRECISION" id="A10024"/>
        <Campo NomeFisico="CTRT1_TIPO_ERP" NomeLogico="TIPO CONTRATO NO ERP" DataType="TEXT" id="A10025"/>
        <Campo NomeFisico="CTRT1_PRIO_CACHE" NomeLogico="CACHE PROPRIETARIOS" DataType="TEXT" id="A10026"/>
        <Campo NomeFisico="CTRT1_COD_SUC" NomeLogico="ID DO SUCESSOR" DataType="INTEGER" id="A10027"/>
        <Campo NomeFisico="CTRT1_DAT_CSCT" NomeLogico="DATA ATUALIZACAO CESSIONARIOS ERP" DataType="DATE" id="A10028"/>
        <Campo NomeFisico="CTRT1_CESS_CACHE" NomeLogico="CACHE CESSIONARIOS" DataType="TEXT" id="A10029"/>
        <Campo NomeFisico="CTRT1_VAL_PROX" NomeLogico="VALOR PROXIMO PAGAMENTO" DataType="DOUBLE PRECISION" id="A10030"/>
        <Campo NomeFisico="CTRT1_DAT_PROX" NomeLogico="DATA PROXIMO PAGAMENTO" DataType="DATE" id="A10031"/>
        <Campo NomeFisico="CTRT1_DIST_AUT_DAT" NomeLogico="INDICADOR DISTRATO AUTORIZADO" DataType="DATE" id="A10032"/>
        <Campo NomeFisico="USER2_COD_AUTDIST" NomeLogico="CODIGO USUARIO AUTORIZOU DISTRATO" DataType="INTEGER" id="A10033"/>
        <Campo NomeFisico="CTRT1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10034"/>
        <Campo NomeFisico="CTRT1_COD_BEMA" NomeLogico="CHAVE BEMATECH" DataType="TEXT" id="A10035"/>
        <Campo NomeFisico="CTRT1_COD_SAP" NomeLogico="CHAVE SAP" DataType="TEXT" id="A10036"/>
        <Campo NomeFisico="CTRT1_ARQ_CACHE" NomeLogico="CACHE ARQUIVOS" DataType="TEXT" id="A10037"/>
        <Campo NomeFisico="CTRT1_GD_ID" NomeLogico="GDRIVE ID" DataType="TEXT" id="A10038"/>
        <Campo NomeFisico="USER2_COD_CRIA" NomeLogico="CODIGO USUARIO CRIADOR" DataType="INTEGER" id="A10039"/>
        <Campo NomeFisico="TCTB1_COD" NomeLogico="CODIGO TIPO CONTRATO BIG" DataType="INTEGER" NotNull="true" id="A10040"/>
        <Campo NomeFisico="STSE1_COD" NomeLogico="CODIGO STATUS SERVIDAO" DataType="INTEGER" id="A10041"/>
        <Campo NomeFisico="CTRT1_STATUS_CTRT" NomeLogico="CODIGO STATUS CONTRATO BIG" DataType="INTEGER" id="A10042"/>
        <Campo NomeFisico="CTRT1_VAL_BIG" NomeLogico="VALOR CONTRATO BIG" DataType="DOUBLE PRECISION" id="A10043"/>
        <Campo NomeFisico="CTRT1_VAL_ACUM_BIG" NomeLogico="VALOR ACUMULADO BIG" DataType="DOUBLE PRECISION" id="A10044"/>
        <Campo NomeFisico="CTRT1_VAL_LAUDO" NomeLogico="VALOR LAUDO" DataType="DOUBLE PRECISION" id="A10045"/>
        <Campo NomeFisico="SECO1_COD" NomeLogico="ID SETOR CONTRATO" DataType="INTEGER" id="A10046"/>
        <Campo NomeFisico="STIN1_COD" NomeLogico="CODIGO STATUS INDENIZACAO" DataType="INTEGER" id="A10047"/>
        <Campo NomeFisico="CTRT1_VAL_CONTRA" NomeLogico="VALOR CONTRA PROPOSTA" DataType="DOUBLE PRECISION" id="A10048"/>
        <Campo NomeFisico="CTRT1_DAT_CONTRA" NomeLogico="DATA CONTRA PROPOSTA" DataType="DATE" id="A10049"/>
        <Campo NomeFisico="CTRT1_DAT_AVERB" NomeLogico="DATA AVERBACAO" DataType="DATE" id="A10050"/>
        <Campo NomeFisico="STBE1_COD" NomeLogico="CODIGO STATUS BENFEITORIA" DataType="INTEGER" id="A10051"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PROP" NomeLogico="PROPRIEDADE" id="E1220">
      <Campos quantos="79">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PROP1_NOM" NomeLogico="NOME" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PROP1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGONZ" id="A10002"/>
        <Campo NomeFisico="PROP1_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" id="A10003"/>
        <Campo NomeFisico="PROP1_METOBT" NomeLogico="METODO DE OBTENCAO" DataType="TEXT" id="A10004"/>
        <Campo NomeFisico="STPG1_COD" NomeLogico="COD STATUS PROPRIEDADE GLEBA" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="PROP1_MATR" NomeLogico="MATRICULA" DataType="VARCHAR" Size="400" id="A10006"/>
        <Campo NomeFisico="PROP1_AREA_DOC" NomeLogico="AREA DOCUMENTAL" DataType="DOUBLE PRECISION" id="A10007"/>
        <Campo NomeFisico="PROP1_CCIR" NomeLogico="CERTIFICADO CADASTRO IMOVEL RURAL" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="PROP1_NIRF" NomeLogico="IDENTIFICACAO RECEITA FEDERAL ITR" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="PROP1_COMA" NomeLogico="COMARCA" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="CART1_COD" NomeLogico="ID DO CARTORIO" DataType="INTEGER" id="A10011"/>
        <Campo NomeFisico="PROP1_CONC" NomeLogico="INDICADOR DE INTERESSE DE CONCORRENTE" DataType="BIT" id="A10012"/>
        <Campo NomeFisico="PROP1_OBS_CONC" NomeLogico="OBSERVACAO CONCORRENTE" DataType="TEXT" id="A10013"/>
        <Campo NomeFisico="ESCR1_COD" NomeLogico="ID DO ESCRITORIO" DataType="INTEGER" id="A10014"/>
        <Campo NomeFisico="PROP1_CART" NomeLogico="CARTORIO" DataType="TEXT" id="A10015"/>
        <Campo NomeFisico="COMA1_COD" NomeLogico="ID COMARCA" DataType="INTEGER" id="A10016"/>
        <Campo NomeFisico="PROP1_DAT_GEOM" NomeLogico="DATA DA GEOMETRIA" DataType="DATE" id="A10017"/>
        <Campo NomeFisico="PROP1_IND_GEO" NomeLogico="INDICADOR DE GEORREFERENCIAMENTO" DataType="BIT" id="A10018"/>
        <Campo NomeFisico="DESE1_COD" NomeLogico="ID DO DESENVOLVEDOR" DataType="INTEGER" id="A10019"/>
        <Campo NomeFisico="STPG1_COD_ERP" NomeLogico="ID DO STATUS NO ERP" DataType="INTEGER" id="A10020"/>
        <Campo NomeFisico="PROP1_ATI" NomeLogico="INDICADOR DE PROPRIEDADE ATIVA" DataType="BIT" id="A10021"/>
        <Campo NomeFisico="PROP1_INDISP" NomeLogico="INDICADOR DE PROPRIEDADE INDISPONIVEL NO ERP" DataType="BIT" id="A10022"/>
        <Campo NomeFisico="PROP1_DAT_ERP" NomeLogico="DATA DA ULTIMA VERIFICACAO NO ERP" DataType="DATE" id="A10023"/>
        <Campo NomeFisico="MUNI1_COD" NomeLogico="CODIGO IBGE MUNICIPIO" DataType="INTEGER" id="A10024"/>
        <Campo NomeFisico="PROP1_COD_ERP" NomeLogico="ID PROPRIEDADE NO ERP" DataType="VARCHAR" Size="50" id="A10025"/>
        <Campo NomeFisico="PROP1_DAT_PRIO_ERP" NomeLogico="DATA DA ULTIMA VERIFICACAO DE PROPRIETARIOS NO ERP" DataType="DATE" id="A10026"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10027"/>
        <Campo NomeFisico="PROP1_CTRT_CACHE" NomeLogico="CACHE CONTRATOS" DataType="TEXT" id="A10028"/>
        <Campo NomeFisico="PROP1_CONQUISTA" NomeLogico="STATUS CONQUISTA" DataType="INTEGER" id="A10029"/>
        <Campo NomeFisico="PROP1_PRIO_STATUS" NomeLogico="STATUS PROPRIETARIO" DataType="INTEGER" id="A10030"/>
        <Campo NomeFisico="PROP1_MATR_PARC" NomeLogico="INDICADOR DE MATRICULA PARCIAL" DataType="BIT" id="A10031"/>
        <Campo NomeFisico="PROP1_CCIR_DESAT" NomeLogico="INDICADOR DE CCIR DESATUALIZADO" DataType="BIT" id="A10032"/>
        <Campo NomeFisico="PROP1_NIRF_DESAT" NomeLogico="INDICADOR DE ITR DESATUALIZADO" DataType="BIT" id="A10033"/>
        <Campo NomeFisico="PROP1_CCIR_ERP" NomeLogico="CERTIFCADO CADASTRO IMOVEL RURAL NO ERP" DataType="TEXT" id="A10034"/>
        <Campo NomeFisico="PROP1_NIRF_ERP" NomeLogico="IDENTIFICACAO RECEITA FEDERAL ITR NO ERP" DataType="TEXT" id="A10035"/>
        <Campo NomeFisico="PROP1_MATR_ERP" NomeLogico="MATRICULA NO ERP" DataType="VARCHAR" Size="400" id="A10036"/>
        <Campo NomeFisico="PROP1_NOM_ERP" NomeLogico="NOME ERP" DataType="TEXT" id="A10037"/>
        <Campo NomeFisico="PROP1_PRIO_CACHE" NomeLogico="CACHE PROPRIETARIOS" DataType="TEXT" id="A10038"/>
        <Campo NomeFisico="PROP1_AREA_CONSID" NomeLogico="AREA CONSIDERADA" DataType="DOUBLE PRECISION" id="A10039"/>
        <Campo NomeFisico="PROP1_COD_ORIG" NomeLogico="ID DA PROPRIEDADE ORIGINADORA" DataType="INTEGER" id="A10040"/>
        <Campo NomeFisico="PROP1_COD_REMEMB" NomeLogico="ID DA PROPRIEDADE REMEMBRADA" DataType="INTEGER" id="A10041"/>
        <Campo NomeFisico="PROP1_DAT_HR_REMEMB" NomeLogico="DATA REMEMBRAMENTO" DataType="DATE" id="A10042"/>
        <Campo NomeFisico="PROP1_DAT_HR_DESMEMB" NomeLogico="DATA DESMEMBRAMENTO" DataType="DATE" id="A10043"/>
        <Campo NomeFisico="CART1_COD_ERP" NomeLogico="ID DO CARTORIO ERP" DataType="INTEGER" id="A10044"/>
        <Campo NomeFisico="PROP1_COD_CAR" NomeLogico="CODIGO NO CAR" DataType="TEXT" id="A10045"/>
        <Campo NomeFisico="PROP1_COD_SIGEF" NomeLogico="CODIGO NO SIGEF" DataType="TEXT" id="A10046"/>
        <Campo NomeFisico="PROP1_COD_SNCI" NomeLogico="CODIGO SNCI" DataType="TEXT" id="A10047"/>
        <Campo NomeFisico="PROP1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10048"/>
        <Campo NomeFisico="PROP1_CMPMATR" NomeLogico="COMPLEMENTO MATRICULA" DataType="VARCHAR" Size="100" id="A10049"/>
        <Campo NomeFisico="PROP1_CMPMATR_ERP" NomeLogico="COMPLEMENTO MATRICULA ERP" DataType="VARCHAR" Size="50" id="A10050"/>
        <Campo NomeFisico="PROP1_CCIR_DAT" NomeLogico="DATA VALIDADE CCIR" DataType="DATE" id="A10051"/>
        <Campo NomeFisico="PROP1_CCIR_DAT_ERP" NomeLogico="DATA VALIDADE CCIR ERP" DataType="DATE" id="A10052"/>
        <Campo NomeFisico="PROP1_NIRF_DAT" NomeLogico="DATA VALIDADE ITR" DataType="DATE" id="A10053"/>
        <Campo NomeFisico="PROP1_NIRF_DAT_ERP" NomeLogico="DATA VALIDADE ITR ERP" DataType="DATE" id="A10054"/>
        <Campo NomeFisico="PROP1_CIT_DAT" NomeLogico="EMISSAO CERTIDAO INTEIRO TEOR" DataType="DATE" id="A10055"/>
        <Campo NomeFisico="PROP1_COD_BEMATECH" NomeLogico="ID DA PROPRIEDADE NO BEMATECH" DataType="VARCHAR" Size="50" id="A10056"/>
        <Campo NomeFisico="PROP1_ARQ_CACHE" NomeLogico="CACHE ARQUIVOS" DataType="TEXT" id="A10057"/>
        <Campo NomeFisico="PROP1_GD_ID" NomeLogico="GDRIVE ID" DataType="TEXT" id="A10058"/>
        <Campo NomeFisico="PROP1_AUT_GEO" NomeLogico="GEO AUTORIZADO" DataType="INTEGER" id="A10059"/>
        <Campo NomeFisico="PROP1_AREA_MATR" NomeLogico="AREA MATRICULA BIG" DataType="DOUBLE PRECISION" id="A10060"/>
        <Campo NomeFisico="PROP1_AREA_CCIR" NomeLogico="AREA DOCUMENTAL CCIR" DataType="DOUBLE PRECISION" id="A10061"/>
        <Campo NomeFisico="PROP1_AREA_ITR" NomeLogico="AREA DOCUMENTAL ITR" DataType="DOUBLE PRECISION" id="A10062"/>
        <Campo NomeFisico="PROP1_SITU_CACHE" NomeLogico="CACHE SITUACAO" DataType="TEXT" id="A10063"/>
        <Campo NomeFisico="PROP1_GEO_STATUS" NomeLogico="CODIGO STATUS GEO" DataType="INTEGER" id="A10064"/>
        <Campo NomeFisico="STAP1_COD" NomeLogico="CODIGO STATUS PASSAGEM" DataType="INTEGER" NotNull="true" id="A10065"/>
        <Campo NomeFisico="PROP1_DOMINIO" NomeLogico="CODIGO STATUS DOMINIO" DataType="INTEGER" id="A10066"/>
        <Campo NomeFisico="PROP1_ATIFUND" NomeLogico="INDICADOR ATIVO FUNDIARIO" DataType="BIT" id="A10067"/>
        <Campo NomeFisico="PROP1_DAT_RTT" NomeLogico="DATA READY TO TRANSFER" DataType="DATE" id="A10068"/>
        <Campo NomeFisico="PROP1_CONQ_PROV" NomeLogico="INDICADOR CONQUISTA PROVISORIA" DataType="BIT" id="A10069"/>
        <Campo NomeFisico="PROP1_AREA_TOTAL_SAP" NomeLogico="AREA TOTAL SAP" DataType="DOUBLE PRECISION" id="A10070"/>
        <Campo NomeFisico="PROP1_AREA_EFET_SAP" NomeLogico="AREA EFETIVA SAP" DataType="DOUBLE PRECISION" id="A10071"/>
        <Campo NomeFisico="PROP1_AREA_DOC_SAP" NomeLogico="AREA DOCUMENTAL SAP" DataType="DOUBLE PRECISION" id="A10072"/>
        <Campo NomeFisico="PROP1_TC_CP" NomeLogico="TIPO TERMO COMPROMISSO CONQUISTA PROVISORIA" DataType="VARCHAR" Size="50" id="A10073"/>
        <Campo NomeFisico="PROP1_REPR_CAR" NomeLogico="INDICADOR REPRESENTANTE CADASTRADO" DataType="BIT" id="A10074"/>
        <Campo NomeFisico="PROP1_ST_CLGEO" NomeLogico="STATUS CHECKLIST GEO" DataType="VARCHAR" Size="30" id="A10075"/>
        <Campo NomeFisico="PROP1_ST_SIGEF" NomeLogico="STATUS CERTIFICACAO SIGEF" DataType="VARCHAR" Size="20" id="A10076"/>
        <Campo NomeFisico="PROP1_PROT_SIGEF" NomeLogico="PROTOCOLOS SIGEF" DataType="TEXT" id="A10077"/>
        <Campo NomeFisico="PROP1_NEWGEOM_GEO" NomeLogico="ALERTA NOVA GEOMETRIA GEO" DataType="DATE" id="A10078"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_GLEP" NomeLogico="GLEBA DE PROPRIEDADE" id="E1221">
      <Campos quantos="10">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" id="A10000"/>
        <Campo NomeFisico="GLEP1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGON" id="A10001"/>
        <Campo NomeFisico="GLEP1_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="STPG1_COD" NomeLogico="COD STATUS PROPRIEDADE GLEBA" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="GLEP1_AREA" NomeLogico="AREA DA GLEBA" DataType="DOUBLE PRECISION" id="A10004"/>
        <Campo NomeFisico="GLEP1_COD" NomeLogico="ID DA GLEBA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="GLEP1_NOM" NomeLogico="NOME DA GEOMETRIA" DataType="VARCHAR" Size="400" id="A10007"/>
        <Campo NomeFisico="GLEP1_DES" NomeLogico="DESCRICAO DA GLEBA" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="GLEP1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HGPR" NomeLogico="HISTORICO GEOMETRIA DA PROPRIEDADE" id="E1222">
      <Campos quantos="7">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10000"/>
        <Campo NomeFisico="HGPR1_COD" NomeLogico="ID DO HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10001"/>
        <Campo NomeFisico="HGPR1_DAT_HR_INI" NomeLogico="DATA DA OBTENCAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="HGPR1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGONZ" id="A10003"/>
        <Campo NomeFisico="HGPR1_DAT_HR_SUBS" NomeLogico="DATA DA SUBSTITUICAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="HGPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PROJ" NomeLogico="PROJETO" id="E1223">
      <Campos quantos="32">
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PROJ1_NOM" NomeLogico="NOME" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PROJ1_ATI" NomeLogico="ATIVO" DataType="BIT" id="A10002"/>
        <Campo NomeFisico="PROJ1_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PROJ1_DAT_HR_FIM" NomeLogico="DATA DA CONCLUSAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="ESCR1_COD" NomeLogico="ID DO ESCRITORIO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="USER2_COD_ENG1" NomeLogico="CODIGO USUARIO ENGENHEIRO 1" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="PROJ1_PREF" NomeLogico="PREFIXO" DataType="VARCHAR" Size="50" id="A10007"/>
        <Campo NomeFisico="PROJ1_GEOM_INT" NomeLogico="POLIGONAL DE INTERESSE" DataType="MULTIPOLYGON" id="A10008"/>
        <Campo NomeFisico="PROJ1_GEOM" NomeLogico="GEOMETRIA DO LIMITE" DataType="POLYGON" id="A10009"/>
        <Campo NomeFisico="PROJ1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10010"/>
        <Campo NomeFisico="STPJ1_COD" NomeLogico="CODIGO DO STATUS" DataType="INTEGER" NotNull="true" id="A10011"/>
        <Campo NomeFisico="USER2_COD_ENG2" NomeLogico="CODIGO USUARIO ENGENHEIRO 2" DataType="INTEGER" id="A10012"/>
        <Campo NomeFisico="DESE1_COD" NomeLogico="ID DO DESENVOLVEDOR" DataType="INTEGER" id="A10013"/>
        <Campo NomeFisico="USER2_COD_AMB" NomeLogico="CODIGO USUARIO AMBIENTAL" DataType="INTEGER" id="A10014"/>
        <Campo NomeFisico="USER2_COD_JUR" NomeLogico="CODIGO USUARIO JURIDICO" DataType="INTEGER" id="A10015"/>
        <Campo NomeFisico="PROJ1_TIPO" NomeLogico="TIPO DE PROJETO" DataType="TEXT" id="A10016"/>
        <Campo NomeFisico="USER2_COD_RESP" NomeLogico="CODIGO USUARIO RESPONSAVEL" DataType="INTEGER" id="A10017"/>
        <Campo NomeFisico="TIPR1_COD" NomeLogico="CODIGO TIPO" DataType="INTEGER" NotNull="true" id="A10018"/>
        <Campo NomeFisico="PRIO1_COD_CESS" NomeLogico="ID DO CESSIONARIO" DataType="INTEGER" id="A10019"/>
        <Campo NomeFisico="PROJ1_ABR" NomeLogico="ABREVIATURA" DataType="VARCHAR" Size="15" id="A10020"/>
        <Campo NomeFisico="USER2_COD_ENG_NEG" NomeLogico="ENGENHEIRO NEGOCIOS" DataType="INTEGER" id="A10021"/>
        <Campo NomeFisico="USER2_COD_AMB_GEO" NomeLogico="CODIGO USUARIO RESPONSAVEL AMBIENTAL GEO" DataType="INTEGER" id="A10022"/>
        <Campo NomeFisico="USER2_COD_GEORREF" NomeLogico="CODIGO RESPONSAVEL GEORREFERENCIAMENTO" DataType="INTEGER" id="A10023"/>
        <Campo NomeFisico="USER2_COD_GOVFUND" NomeLogico="ESPECIALISTA GOVERNANCA FUNDIARIA" DataType="INTEGER" id="A10024"/>
        <Campo NomeFisico="USER2_COD_COORDFUND" NomeLogico="COORDENADOR FUNDIARIO" DataType="INTEGER" id="A10025"/>
        <Campo NomeFisico="PROJ1_GD_ID" NomeLogico="GDRIVE ID" DataType="TEXT" id="A10026"/>
        <Campo NomeFisico="COMPL1_COD_UG" NomeLogico="COMPLEXO UG" DataType="INTEGER" id="A10027"/>
        <Campo NomeFisico="COMPL1_COD_BOP" NomeLogico="COMPLEXO BOP" DataType="INTEGER" id="A10028"/>
        <Campo NomeFisico="COMPL1_COD_XTRA" NomeLogico="COMPLEXO EXTRA" DataType="INTEGER" id="A10029"/>
        <Campo NomeFisico="COMPL1_COD_LI" NomeLogico="CODIGO DO COMPLEXO LICENCA INSTALACAO" DataType="INTEGER" id="A10030"/>
        <Campo NomeFisico="COMPL1_COD_LP" NomeLogico="CODIGO DO COMPLEXO LICENCA PREVIA" DataType="INTEGER" id="A10031"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PRPR" NomeLogico="PROPRIEDADE DO PROJETO" id="E1224">
      <Campos quantos="4">
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRPR1_DAT_HR_INI" NomeLogico="DATA DA ENTRADA" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PRPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HGGL" NomeLogico="HISTORICO DA GEOMETRIA DA GLEBA" id="E1225">
      <Campos quantos="9">
        <Campo NomeFisico="HGGL1_COD" NomeLogico="ID DO HISTORICO DA AREA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HGGL1_GEOM" NomeLogico="GEOMETRIA" DataType="MULTIPOLYGON" id="A10001"/>
        <Campo NomeFisico="HGGL1_DAT_HR_INI" NomeLogico="DATA DA OBENTCAO" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="HGGL1_DAT_HR_SUBS" NomeLogico="DATA DA SUBSTITUICAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="GLEP1_COD" NomeLogico="ID DA GLEBA" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="HGGL1_NOM" NomeLogico="NOME DA GLEBA" DataType="VARCHAR" Size="400" id="A10006"/>
        <Campo NomeFisico="HGGL1_DES" NomeLogico="DESCRICAO DA GLEBA" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="HGGL1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10008"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PRIO" NomeLogico="PROPRIETARIO" id="E1226">
      <Campos quantos="43">
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PRIO1_NOM" NomeLogico="NOME OU RAZAO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PJUR2_CNPJ" NomeLogico="RAIZ CNPJ" DataType="INTEGER" id="A10002"/>
        <Campo NomeFisico="PJUR2_SEQ" NomeLogico="SEQUENCIAL" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="PFIS2_COD" NomeLogico="CODIGO DA PESSOA FISICA" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="PRIO1_MAE" NomeLogico="NOME DA MAE" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="PRIO1_DOC" NomeLogico="DOCUMENTO" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="PRIO1_COD_ERP" NomeLogico="ID NO ERP" DataType="VARCHAR" Size="100" id="A10007"/>
        <Campo NomeFisico="PRIO1_RG" NomeLogico="RG" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="PRIO1_EMRG" NomeLogico="EMISSOR RG" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="PRIO1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" id="A10010"/>
        <Campo NomeFisico="PRIO1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10011"/>
        <Campo NomeFisico="PRIO1_DP" NomeLogico="DOCUMENTOS PESSOAIS" DataType="VARCHAR" Size="8" id="A10012"/>
        <Campo NomeFisico="PRIO1_ARQ_CACHE" NomeLogico="CACHE ARQUIVOS" DataType="TEXT" id="A10013"/>
        <Campo NomeFisico="PRIO1_GD_ID" NomeLogico="GDRIVE ID" DataType="TEXT" id="A10014"/>
        <Campo NomeFisico="PRIO1_TELES" NomeLogico="TELEFONES" DataType="TEXT" id="A10015"/>
        <Campo NomeFisico="PRIO1_TELES_OBS" NomeLogico="OBSERVACAO TELEFONES" DataType="TEXT" id="A10016"/>
        <Campo NomeFisico="PRIO1_EMAILS" NomeLogico="EMAILS" DataType="TEXT" id="A10017"/>
        <Campo NomeFisico="PRIO1_EMAILS_OBS" NomeLogico="OBSERVACAO EMAILS" DataType="TEXT" id="A10018"/>
        <Campo NomeFisico="PRIO1_ENDES" NomeLogico="ENDERECOS" DataType="TEXT" id="A10019"/>
        <Campo NomeFisico="PRIO1_ENDES_OBS" NomeLogico="OBSERVACAO ENDERECOS" DataType="TEXT" id="A10020"/>
        <Campo NomeFisico="PRIO1_CESS" NomeLogico="EH CESSIONARIO" DataType="BIT" id="A10021"/>
        <Campo NomeFisico="PRIO1_COD_ERP_CESS" NomeLogico="CHAVE ERP COMO CESSIONARIO" DataType="VARCHAR" Size="100" id="A10022"/>
        <Campo NomeFisico="PRIO1_COD_ERP_DIV" NomeLogico="DIVISAO OU FILIAL CESSIONARIO" DataType="VARCHAR" Size="100" id="A10023"/>
        <Campo NomeFisico="ENDE2_COD" NomeLogico="CODIGO DO ENDERECO" DataType="INTEGER" id="A10024"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" id="A10025"/>
        <Campo NomeFisico="PRIO1_DAT_RG" NomeLogico="DATA DE EMISSAO RG" DataType="DATE" id="A10026"/>
        <Campo NomeFisico="PRIO1_SEX" NomeLogico="SEXO" DataType="VARCHAR" Size="100" id="A10027"/>
        <Campo NomeFisico="PRIO1_EST_CIVIL" NomeLogico="ESTADO CIVIL" DataType="VARCHAR" Size="100" id="A10028"/>
        <Campo NomeFisico="PRIO1_NACION" NomeLogico="NACIONALIDADE" DataType="VARCHAR" Size="200" id="A10029"/>
        <Campo NomeFisico="PRIO1_PROF" NomeLogico="PROFISSAO" DataType="VARCHAR" Size="200" id="A10030"/>
        <Campo NomeFisico="PRIO1_BANCO_NUM" NomeLogico="NUMERO BANCO" DataType="INTEGER" id="A10031"/>
        <Campo NomeFisico="PRIO1_BANCO" NomeLogico="BANCO" DataType="VARCHAR" Size="200" id="A10032"/>
        <Campo NomeFisico="PRIO1_BANCO_AG" NomeLogico="AGENCIA" DataType="VARCHAR" Size="20" id="A10033"/>
        <Campo NomeFisico="PRIO1_TIPO_CONTA" NomeLogico="TIPO CONTA" DataType="VARCHAR" Size="20" id="A10034"/>
        <Campo NomeFisico="PRIO1_CONTA" NomeLogico="CONTA" DataType="VARCHAR" Size="20" id="A10035"/>
        <Campo NomeFisico="PRIO1_NOM_FANT" NomeLogico="NOME FANTASIA" DataType="TEXT" id="A10036"/>
        <Campo NomeFisico="BANC2_COD" NomeLogico="CODIGO" DataType="INTEGER" id="A10037"/>
        <Campo NomeFisico="PRIO1_TI" NomeLogico="TERMO DE INVENTARIANTE" DataType="VARCHAR" Size="8" id="A10038"/>
        <Campo NomeFisico="PRIO1_LOGIN_CAR" NomeLogico="LOGIN CAR" DataType="VARCHAR" Size="200" id="A10039"/>
        <Campo NomeFisico="PRIO1_PW_CAR" NomeLogico="SENHA CAR" DataType="VARCHAR" Size="200" id="A10040"/>
        <Campo NomeFisico="PRIO1_LOGIN_CEFIR" NomeLogico="LOGIN CEFIR" DataType="VARCHAR" Size="200" id="A10041"/>
        <Campo NomeFisico="PRIO1_PW_CEFIR" NomeLogico="SENHA CEFIR" DataType="VARCHAR" Size="200" id="A10042"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HSGL" NomeLogico="HISTORICO STATUS GLEBA" id="E1227">
      <Campos quantos="6">
        <Campo NomeFisico="HSGL1_COD" NomeLogico="ID HISTORICO STATUS" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HSGL1_DAT_HR_INI" NomeLogico="DATA INICIO" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HSGL1_DAT_HR_FIM" NomeLogico="DATA FINAL" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="STPG1_COD" NomeLogico="COD STATUS PROPRIEDADE GLEBA" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="GLEP1_COD" NomeLogico="ID DA GLEBA" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="HSGL1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HSPR" NomeLogico="HISTORICO STATUS PROPRIEDADE" id="E1228">
      <Campos quantos="8">
        <Campo NomeFisico="HSPR1_COD" NomeLogico="ID HISTORICO STATUS PROPR" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HSPR1_DAT_HR_INI" NomeLogico="DATA INICIO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HSPR1_DAT_HR_FIM" NomeLogico="DATA FINAL" DataType="DATE" id="A10002"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="STPG1_COD" NomeLogico="COD STATUS PROPRIEDADE GLEBA" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="HSPR1_COMENT" NomeLogico="COMENTARIO DA MUDANCA DE STATUS" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="HSPR1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STPG" NomeLogico="STATUS POSSIVEL PROPRIEDADE GLEBA" id="E1229">
      <Campos quantos="6">
        <Campo NomeFisico="STPG1_COD" NomeLogico="COD STATUS PROPRIEDADE GLEBA" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STPG1_NOM" NomeLogico="STATUS" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="STPG1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STPG1_COD_SGT2" NomeLogico="CODIGO SGT2" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="STPG1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="STPG1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PPRO" NomeLogico="PROPRIETARIO DA PROPRIEDADE" id="E1230">
      <Campos quantos="7">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PPRO1_COD_TIPO_ERP" NomeLogico="ID TIPO PROPRIETARIO ERP" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="PPRO1_TIPO_ERP" NomeLogico="TIPO PROPRIETARIO ERP" DataType="VARCHAR" Size="200" id="A10003"/>
        <Campo NomeFisico="PPRO1_DAT_HR_INI" NomeLogico="DATA DA CRIACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="PPRO1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="PPRO1_DP" NomeLogico="DOCUMENTOS PESSOAIS" DataType="VARCHAR" Size="8" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_ESCR" NomeLogico="ESCRITORIO" id="E1231">
      <Campos quantos="11">
        <Campo NomeFisico="ESCR1_COD" NomeLogico="ID DO ESCRITORIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="ESCR1_NOM" NomeLogico="NOME DO ESCRITORIO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="ESCR1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="USER2_COD_RESP" NomeLogico="CODIGO USUARIO RESPONSAVEL" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="ESCR1_GEOM" NomeLogico="POLIGONAL" DataType="POLYGON" id="A10004"/>
        <Campo NomeFisico="ESCR1_BB" NomeLogico="BOUNDING BOX" DataType="POLYGON" id="A10005"/>
        <Campo NomeFisico="ESCR1_ATI" NomeLogico="ATIVO" DataType="BIT" NotNull="true" id="A10006"/>
        <Campo NomeFisico="ESCR1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10007"/>
        <Campo NomeFisico="USER2_COD_GERE" NomeLogico="CODIGO USUARIO GERENTE" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="ESCR1_ABR" NomeLogico="ABREVIATURA" DataType="VARCHAR" Size="15" id="A10009"/>
        <Campo NomeFisico="ESCR1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10010"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PARQ" NomeLogico="PARQUE" id="E1232">
      <Campos quantos="18">
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="PARQ1_NOM" NomeLogico="NOME DO PARQUE" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="PARQ1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="ESCR1_COD" NomeLogico="ID DO ESCRITORIO" DataType="INTEGER" id="A10003"/>
        <Campo NomeFisico="COMPL1_COD" NomeLogico="CODIGO DO COMPLEXO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="PARQ1_GEOM" NomeLogico="GEOMETRIA DO LIMITE" DataType="POLYGON" id="A10005"/>
        <Campo NomeFisico="PARQ1_BB" NomeLogico="BOUNDING BOX" DataType="POLYGON" id="A10006"/>
        <Campo NomeFisico="PARQ1_GEOM_LIC" NomeLogico="GEOMETRIA LICENCIAMENTO" DataType="MULTIPOLYGON" id="A10007"/>
        <Campo NomeFisico="PARQ1_GEOM_EPE" NomeLogico="GEOMETRIA EPE" DataType="MULTIPOLYGON" id="A10008"/>
        <Campo NomeFisico="PARQ1_DAT_HR_MOD" NomeLogico="DATA DA ULTIMA MODIFICACAO" DataType="DATE" id="A10009"/>
        <Campo NomeFisico="PRIO1_COD_CESS" NomeLogico="PROPRIETARIO CESSIONARIO" DataType="INTEGER" id="A10010"/>
        <Campo NomeFisico="PARQ1_DAT_CLIE" NomeLogico="PRAZO CLIENTE" DataType="DATE" id="A10011"/>
        <Campo NomeFisico="PARQ1_DAT_REG" NomeLogico="PRAZO REGULARIZACAO" DataType="DATE" id="A10012"/>
        <Campo NomeFisico="PARQ1_DAT_CONQ" NomeLogico="PRAZO CONQUISTA" DataType="DATE" id="A10013"/>
        <Campo NomeFisico="PARQ1_ABR" NomeLogico="ABREVIATURA" DataType="VARCHAR" Size="15" id="A10014"/>
        <Campo NomeFisico="PARQ1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10015"/>
        <Campo NomeFisico="PARQ1_GD_ID" NomeLogico="GDRIVE ID" DataType="TEXT" id="A10016"/>
        <Campo NomeFisico="PARQ1_GEOM_ADA" NomeLogico="GEOMETRIA ADA" DataType="MULTIPOLYGON" id="A10017"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PRPQ" NomeLogico="PROPRIEDADE DO PARQUE" id="E1233">
      <Campos quantos="4">
        <Campo NomeFisico="PRPQ1_DAT_HR_INI" NomeLogico="DATA DA ENTRADA" DataType="DATE" id="A10000"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PRPQ1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PUES" NomeLogico="PERMISSAO USUARIO ESCRITORIO" id="E1234">
      <Campos quantos="7">
        <Campo NomeFisico="PUES2_EXIB" NomeLogico="EXIBIR" DataType="BIT" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PUES2_EDIT" NomeLogico="EDITAR" DataType="BIT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PUES2_ADMIN" NomeLogico="ADMINISTRAR" DataType="BIT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="ESCR1_COD" NomeLogico="ID DO ESCRITORIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PUES2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="PUES2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PUPR" NomeLogico="PERMISSAO USUARIO PROJETO" id="E1235">
      <Campos quantos="7">
        <Campo NomeFisico="PUPR2_EXIB" NomeLogico="EXIBIR" DataType="BIT" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PUPR2_EDIT" NomeLogico="EDITAR" DataType="BIT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PUPR2_ADMIN" NomeLogico="ADMINISTRAR" DataType="BIT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PUPR2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10005"/>
        <Campo NomeFisico="PUPR2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_PUPQ" NomeLogico="PERMISSAO USUARIO PARQUE" id="E1236">
      <Campos quantos="7">
        <Campo NomeFisico="PUPQ2_EXIB" NomeLogico="EXIBIR" DataType="BIT" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PUPQ2_EDIT" NomeLogico="EDITAR" DataType="BIT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PUPQ2_ADMIN" NomeLogico="ADMINISTRAR" DataType="BIT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10003"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10004"/>
        <Campo NomeFisico="PUPQ2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10005"/>
        <Campo NomeFisico="PUPQ2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10006"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_TELE" NomeLogico="TELEFONE" id="E1237">
      <Campos quantos="10">
        <Campo NomeFisico="TELE2_COD" NomeLogico="CODIGO DO TELEFONE" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="TELE2_NUM" NomeLogico="TELEFONE" DataType="VARCHAR" NotNull="true" Size="20" id="A10001"/>
        <Campo NomeFisico="TELE2_DDI" NomeLogico="DDI" DataType="VARCHAR" Size="4" id="A10002"/>
        <Campo NomeFisico="TELE2_DDD" NomeLogico="DDD" DataType="VARCHAR" Size="4" id="A10003"/>
        <Campo NomeFisico="TELE2_DESC" NomeLogico="DESCRICAO" DataType="VARCHAR" Size="75" id="A10004"/>
        <Campo NomeFisico="TELE2_WAPP" NomeLogico="WHATSAPP" DataType="BIT" id="A10005"/>
        <Campo NomeFisico="TELE2_DAT_ALO" NomeLogico="DATA DO ULTIMO ALO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="TELE2_INV" NomeLogico="INDICADOR DE TELEFONE INVALIDO" DataType="BIT" id="A10007"/>
        <Campo NomeFisico="TELE2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10008"/>
        <Campo NomeFisico="TELE2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB4_DOCU" NomeLogico="DOCUMENTO" id="E1238">
      <Campos quantos="6">
        <Campo NomeFisico="DOCU4_COD" NomeLogico="ID DO DOCUMENTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="DOCU4_TIT" NomeLogico="TITULO DO DOCUMENTO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="DOCU4_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="DOCU4_COD_REM" NomeLogico="CODIGO REMOTO" DataType="VARCHAR" NotNull="true" Size="150" id="A10003"/>
        <Campo NomeFisico="DOCU4_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="DOCU4_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB4_DOCO" NomeLogico="DOCUMENTO DO CONTRATO" id="E1239">
      <Campos quantos="4">
        <Campo NomeFisico="DOCU4_COD" NomeLogico="ID DO DOCUMENTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="DOCO4_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="DOCO4_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB4_DOPR" NomeLogico="DOCUMENTO DA PROPRIEDADE" id="E1240">
      <Campos quantos="4">
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10000"/>
        <Campo NomeFisico="DOCU4_COD" NomeLogico="ID DO DOCUMENTO" DataType="INTEGER" PrimaryKey="true" NotNull="true" id="A10001"/>
        <Campo NomeFisico="DOPR4_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="DOPR4_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10003"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_AERG" NomeLogico="AEROGERADOR" id="E1241">
      <Campos quantos="35">
        <Campo NomeFisico="AERG1_COD" NomeLogico="ID DO AEROGERADOR" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="AERG1_NOM" NomeLogico="NOME" DataType="VARCHAR" Size="400" id="A10001"/>
        <Campo NomeFisico="AERG1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="AERG1_MAQ" NomeLogico="MAQUINA" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="GLEP1_COD" NomeLogico="ID DA GLEBA" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="AERG1_GEOM" NomeLogico="LOCALIZACAO" DataType="POINT" NotNull="true" id="A10005"/>
        <Campo NomeFisico="AERG1_POT" NomeLogico="POTENCIA MW" DataType="DOUBLE PRECISION" id="A10006"/>
        <Campo NomeFisico="AERG1_ALT" NomeLogico="ALTURA TOTAL" DataType="DOUBLE PRECISION" id="A10007"/>
        <Campo NomeFisico="AERG1_TORRE" NomeLogico="ALTURA TORRE" DataType="DOUBLE PRECISION" id="A10008"/>
        <Campo NomeFisico="AERG1_DIAM" NomeLogico="DIAMETRO ROTOR" DataType="DOUBLE PRECISION" id="A10009"/>
        <Campo NomeFisico="AERG1_COTA" NomeLogico="COTA" DataType="DOUBLE PRECISION" id="A10010"/>
        <Campo NomeFisico="AERG1_FUSO" NomeLogico="FUSO" DataType="DOUBLE PRECISION" id="A10011"/>
        <Campo NomeFisico="PARQ1_COD" NomeLogico="ID DO PARQUE" DataType="INTEGER" id="A10012"/>
        <Campo NomeFisico="PROJ1_COD" NomeLogico="ID DO PROJETO" DataType="INTEGER" id="A10013"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" id="A10014"/>
        <Campo NomeFisico="STAG1_COD" NomeLogico="CODIGO STATUS" DataType="INTEGER" id="A10015"/>
        <Campo NomeFisico="VLOA1_COD" NomeLogico="CODIGO DA VERSAO" DataType="INTEGER" id="A10016"/>
        <Campo NomeFisico="AERG1_FC" NomeLogico="FATOR DE CAPACIDADE" DataType="DOUBLE PRECISION" id="A10017"/>
        <Campo NomeFisico="AERG1_LAYO" NomeLogico="LAYOUT" DataType="TEXT" id="A10018"/>
        <Campo NomeFisico="AERG1_TIPO_LAYO" NomeLogico="TIPO DE LAYOUT" DataType="TEXT" id="A10019"/>
        <Campo NomeFisico="AERG1_WM" NomeLogico="WIND MODEL" DataType="INTEGER" id="A10020"/>
        <Campo NomeFisico="AERG1_CENA" NomeLogico="CENARIO" DataType="INTEGER" id="A10021"/>
        <Campo NomeFisico="AERG1_PROJ" NomeLogico="PROJETO" DataType="TEXT" id="A10022"/>
        <Campo NomeFisico="AERG1_NET_YIELD" NomeLogico="NET YIELD" DataType="DOUBLE PRECISION" id="A10023"/>
        <Campo NomeFisico="AERG1_COMP_NAME" NomeLogico="NOME DO COMPLEXO ENGENHARIA" DataType="TEXT" id="A10024"/>
        <Campo NomeFisico="AERG1_COMP_COD" NomeLogico="CODIGO DO COMPLEXO ENGENHARIA" DataType="INTEGER" id="A10025"/>
        <Campo NomeFisico="AERG1_SITE_ID" NomeLogico="SITE ID" DataType="INTEGER" id="A10026"/>
        <Campo NomeFisico="AERG1_PORTFOLIO" NomeLogico="EH PORTFOLIO" DataType="BIT" id="A10027"/>
        <Campo NomeFisico="AERG1_DAT_HR_INI" NomeLogico="DATA DE CRIACAO" DataType="DATE" id="A10028"/>
        <Campo NomeFisico="AERG1_DAT_HR_MOD" NomeLogico="DATA DE MODIFICACAO" DataType="DATE" id="A10029"/>
        <Campo NomeFisico="AERG1_PARK_COD" NomeLogico="CODIGO PARQUE ENGENHARIA" DataType="INTEGER" id="A10030"/>
        <Campo NomeFisico="AERG1_PARK_NAME" NomeLogico="NOME PARQUE ENGENHARIA" DataType="VARCHAR" Size="100" id="A10031"/>
        <Campo NomeFisico="AERG1_PROJ_COD" NomeLogico="CODIGO PROJETO ENGENHARIA" DataType="INTEGER" id="A10032"/>
        <Campo NomeFisico="AERG1_LAYOUT_CHEIO" NomeLogico="LAYOUT CHEIO" DataType="BIT" id="A10033"/>
        <Campo NomeFisico="AERG1_PORTFOLIO_ID" NomeLogico="PORTFOLIO ID" DataType="INTEGER" id="A10034"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CART" NomeLogico="CARTORIO" id="E1242">
      <Campos quantos="21">
        <Campo NomeFisico="CART1_COD" NomeLogico="ID DO CARTORIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CART1_NOM" NomeLogico="NOME DO CARTORIO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CART1_MUNI" NomeLogico="MUNICIPIO DO CARTORIO" DataType="VARCHAR" Size="200" id="A10002"/>
        <Campo NomeFisico="CART1_UF" NomeLogico="UF DO CARTORIO" DataType="VARCHAR" Size="30" id="A10003"/>
        <Campo NomeFisico="MUNI1_COD" NomeLogico="CODIGO IBGE MUNICIPIO" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="COMA1_COD" NomeLogico="ID COMARCA" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="CART1_CNS" NomeLogico="CNS DO CARTORIO" DataType="VARCHAR" Size="15" id="A10006"/>
        <Campo NomeFisico="CART1_CNPJ" NomeLogico="CNPJ" DataType="VARCHAR" Size="30" id="A10007"/>
        <Campo NomeFisico="CART1_FANT" NomeLogico="FANTASIA" DataType="TEXT" id="A10008"/>
        <Campo NomeFisico="CART1_ENDE" NomeLogico="ENDERECO" DataType="TEXT" id="A10009"/>
        <Campo NomeFisico="CART1_BAI" NomeLogico="BAIRRO" DataType="TEXT" id="A10010"/>
        <Campo NomeFisico="CART1_CEP" NomeLogico="CEP" DataType="VARCHAR" Size="8" id="A10011"/>
        <Campo NomeFisico="CART1_SITE" NomeLogico="SITE" DataType="TEXT" id="A10012"/>
        <Campo NomeFisico="CART1_EMAIL" NomeLogico="EMAIL" DataType="TEXT" id="A10013"/>
        <Campo NomeFisico="CART1_TEL" NomeLogico="TELEFONE" DataType="TEXT" id="A10014"/>
        <Campo NomeFisico="CART1_ABRAN" NomeLogico="AREA ABRANGENCIA" DataType="TEXT" id="A10015"/>
        <Campo NomeFisico="CART1_ATRIB" NomeLogico="ATRIBUICOES" DataType="TEXT" id="A10016"/>
        <Campo NomeFisico="CART1_ENTR" NomeLogico="ENTRANCIA" DataType="TEXT" id="A10017"/>
        <Campo NomeFisico="CART1_NOM_CNS" NomeLogico="NOME VIA CNS" DataType="TEXT" id="A10018"/>
        <Campo NomeFisico="CART1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10019"/>
        <Campo NomeFisico="CART1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10020"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_STCT" NomeLogico="STATUS POSSIVEL CONTRATO" id="E1243">
      <Campos quantos="10">
        <Campo NomeFisico="STCT1_COD" NomeLogico="COD STATUS CONTRATO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="STCT1_NOM" NomeLogico="STATUS" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="STCT1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10002"/>
        <Campo NomeFisico="STCT1_COD_ERP" NomeLogico="CODIGO ERP" DataType="TEXT" id="A10003"/>
        <Campo NomeFisico="STPG1_COD" NomeLogico="COD STATUS PROPRIEDADE GLEBA" DataType="INTEGER" id="A10004"/>
        <Campo NomeFisico="STCT1_STATUS_BIG" NomeLogico="STATUS CONTRATO BIG" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="STCT1_GEO_BIG" NomeLogico="STATUS GEO BIG" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="STCT1_NOM_SAP" NomeLogico="NOME STATUS SAP" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="STCT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" id="A10008"/>
        <Campo NomeFisico="STCT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_HSCT" NomeLogico="HISTORICO STATUS CONTRATO" id="E1244">
      <Campos quantos="8">
        <Campo NomeFisico="HSCT1_COD" NomeLogico="ID HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="HSCT1_DAT_HR_INI" NomeLogico="DATA INICIO" DataType="DATE" NotNull="true" id="A10001"/>
        <Campo NomeFisico="HSCT1_DAT_HR_FIM" NomeLogico="DATA FINAL" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="STCT1_COD" NomeLogico="COD STATUS CONTRATO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10005"/>
        <Campo NomeFisico="HSCT1_COMENT" NomeLogico="COMENTARIO MUDANCA STATUS" DataType="TEXT" id="A10006"/>
        <Campo NomeFisico="HSCT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_VCTO" NomeLogico="VERSAO DO CONTRATO" id="E1245">
      <Campos quantos="10">
        <Campo NomeFisico="VCTO1_COD" NomeLogico="ID DA VERSAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="VCTO1_COD_ERP" NomeLogico="CODIGO ERP" DataType="VARCHAR" Size="100" id="A10001"/>
        <Campo NomeFisico="VCTO1_GODOCS" NomeLogico="CODIGO GODOCS" DataType="VARCHAR" Size="50" id="A10002"/>
        <Campo NomeFisico="VCTO1_COD_CVER" NomeLogico="CODIGO CVER" DataType="VARCHAR" Size="100" id="A10003"/>
        <Campo NomeFisico="CTRT1_COD" NomeLogico="ID DO CONTRATO" DataType="INTEGER" NotNull="true" id="A10004"/>
        <Campo NomeFisico="VCTO1_DES" NomeLogico="DESCRICAO" DataType="TEXT" id="A10005"/>
        <Campo NomeFisico="VCTO1_DAT_HR" NomeLogico="DATA DA VERSAO" DataType="DATE" id="A10006"/>
        <Campo NomeFisico="VCTO1_MOTIVO" NomeLogico="MOTIVO" DataType="TEXT" id="A10007"/>
        <Campo NomeFisico="STCT1_COD" NomeLogico="COD STATUS CONTRATO" DataType="INTEGER" id="A10008"/>
        <Campo NomeFisico="VCTO1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10009"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_PPRH" NomeLogico="PROPRIETARIO DA PROPRIEDADE HISTORICO" id="E1246">
      <Campos quantos="8">
        <Campo NomeFisico="PPRH1_DAT_HR" NomeLogico="DATA DA ALTERACAO" DataType="DATE" NotNull="true" id="A10000"/>
        <Campo NomeFisico="PROP1_COD" NomeLogico="ID DA PROPRIEDADE" DataType="INTEGER" NotNull="true" id="A10001"/>
        <Campo NomeFisico="PRIO1_COD" NomeLogico="ID DO PROPRIETARIO" DataType="INTEGER" NotNull="true" id="A10002"/>
        <Campo NomeFisico="PPRH1_COD" NomeLogico="CODIGO DO HISTORICO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10003"/>
        <Campo NomeFisico="PPRH1_COD_TIPO_ERP" NomeLogico="ID TIPO PROPRIETARIO ERP" DataType="VARCHAR" Size="50" id="A10004"/>
        <Campo NomeFisico="PPRH1_TIPO_ERP" NomeLogico="TIPO PROPRIETARIO ERP" DataType="VARCHAR" Size="200" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" id="A10006"/>
        <Campo NomeFisico="PPRH1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10007"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB2_CFUS" NomeLogico="CONFIGURACAO DO USUARIO" id="E1247">
      <Campos quantos="6">
        <Campo NomeFisico="CFUS2_COD" NomeLogico="CODIGO DA CONFIGURACAO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CFUS2_NOM" NomeLogico="NOME DA CONFIGURACAO" DataType="VARCHAR" NotNull="true" Size="400" id="A10001"/>
        <Campo NomeFisico="CFUS2_JSON" NomeLogico="JSON" DataType="TEXT" NotNull="true" id="A10002"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CFUS2_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10004"/>
        <Campo NomeFisico="CFUS2_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10005"/>
      </Campos>
    </Entidade>
    <Entidade NomeFisico="TB1_CPFT" NomeLogico="COMENTARIO PRAZO FT" id="E1248">
      <Campos quantos="7">
        <Campo NomeFisico="CPFT1_COD" NomeLogico="CODIGO COMENTARIO" DataType="INTEGER" PrimaryKey="true" NotNull="true" Sequence="1" id="A10000"/>
        <Campo NomeFisico="CPFT1_TXT" NomeLogico="COMENTARIO" DataType="TEXT" NotNull="true" id="A10001"/>
        <Campo NomeFisico="CPFT1_DAT_ORIG" NomeLogico="DATA ORIGINAL" DataType="DATE" NotNull="true" id="A10002"/>
        <Campo NomeFisico="CPFT1_DAT_HR_INI" NomeLogico="DATA CRIACAO" DataType="DATE" NotNull="true" id="A10003"/>
        <Campo NomeFisico="CPFT1_DAT_HR_MOD" NomeLogico="DATA MODIFICACAO" DataType="DATE" id="A10004"/>
        <Campo NomeFisico="DPFT1_COD" NomeLogico="CODIGO DEMANDA" DataType="INTEGER" NotNull="true" id="A10005"/>
        <Campo NomeFisico="USER2_COD" NomeLogico="CODIGO USUARIO" DataType="INTEGER" NotNull="true" id="A10006"/>
      </Campos>
    </Entidade>
  </Entidades>
  <Relacionamentos>
    <Relacionamento id="R30000" EntidadePai="TB6_ATCC" EntidadeFilho="TB6_LATC" CampoFilho="ATCC6_COD" CampoPai="ATCC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATCC6_COD" ChildName="ATCC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30001" EntidadePai="TB6_PROC" EntidadeFilho="TB6_PCTC" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30002" EntidadePai="TB6_CTRC" EntidadeFilho="TB6_PCTC" CampoFilho="CTRC6_COD" CampoPai="CTRC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CTRC6_COD" ChildName="CTRC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30003" EntidadePai="TB1_TCTB" EntidadeFilho="TB6_CTRC" CampoFilho="TCTB1_COD" CampoPai="TCTB1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TCTB1_COD" ChildName="TCTB1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30004" EntidadePai="TB2_USER" EntidadeFilho="TB6_CTRC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30005" EntidadePai="TB6_CTRC" EntidadeFilho="TB6_HCTC" CampoFilho="CTRC6_COD" CampoPai="CTRC6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CTRC6_COD" ChildName="CTRC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30006" EntidadePai="TB6_ATCC" EntidadeFilho="TB6_HCTC" CampoFilho="ATCC6_COD" CampoPai="ATCC6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ATCC6_COD" ChildName="ATCC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30007" EntidadePai="TB6_INTR" EntidadeFilho="TB6_HPEC" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30008" EntidadePai="TB6_INTR" EntidadeFilho="TB6_HPRC" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30009" EntidadePai="TB6_INTR" EntidadeFilho="TB6_HCTC" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30010" EntidadePai="TB6_PROC" EntidadeFilho="TB6_RESC" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30011" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_RESC" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30012" EntidadePai="TB6_INTR" EntidadeFilho="TB6_RESC" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30013" EntidadePai="TB6_TDAT" EntidadeFilho="TB6_ATRE" CampoFilho="TDAT6_COD" CampoPai="TDAT6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TDAT6_COD" ChildName="TDAT6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30014" EntidadePai="TB6_ATRE" EntidadeFilho="TB6_VARE" CampoFilho="ATRE6_COD" CampoPai="ATRE6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATRE6_COD" ChildName="ATRE6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30015" EntidadePai="TB6_RESC" EntidadeFilho="TB6_VARE" CampoFilho="RESC6_COD" CampoPai="RESC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="RESC6_COD" ChildName="RESC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30016" EntidadePai="TB6_ATRE" EntidadeFilho="TB6_LARE" CampoFilho="ATRE6_COD" CampoPai="ATRE6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATRE6_COD" ChildName="ATRE6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30017" EntidadePai="TB6_RESC" EntidadeFilho="TB6_HREC" CampoFilho="RESC6_COD" CampoPai="RESC6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="RESC6_COD" ChildName="RESC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30018" EntidadePai="TB6_ATRE" EntidadeFilho="TB6_HREC" CampoFilho="ATRE6_COD" CampoPai="ATRE6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="ATRE6_COD" ChildName="ATRE6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30019" EntidadePai="TB6_INTR" EntidadeFilho="TB6_HREC" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30020" EntidadePai="TB6_INTR" EntidadeFilho="TB6_VARE" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30021" EntidadePai="TB2_USER" EntidadeFilho="TB6_VAPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30022" EntidadePai="TB2_USER" EntidadeFilho="TB6_HPRC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30023" EntidadePai="TB2_USER" EntidadeFilho="TB6_HPEC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30024" EntidadePai="TB2_USER" EntidadeFilho="TB6_VAPC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30025" EntidadePai="TB2_USER" EntidadeFilho="TB6_VATI" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30026" EntidadePai="TB2_USER" EntidadeFilho="TB6_VACC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30027" EntidadePai="TB2_USER" EntidadeFilho="TB6_HCTC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30028" EntidadePai="TB2_USER" EntidadeFilho="TB6_VARE" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30029" EntidadePai="TB2_USER" EntidadeFilho="TB6_HREC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30030" EntidadePai="TB2_USER" EntidadeFilho="TB6_RESC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30031" EntidadePai="TB6_PROC" EntidadeFilho="TB6_INTE" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30032" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_INTE" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30033" EntidadePai="TB6_RESC" EntidadeFilho="TB6_INTE" CampoFilho="RESC6_COD" CampoPai="RESC6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="RESC6_COD" ChildName="RESC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30034" EntidadePai="TB2_USER" EntidadeFilho="TB6_INTE" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30035" EntidadePai="TB2_USER" EntidadeFilho="TB6_INTE" CampoFilho="USER2_COD_INTE" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="INTERVEIO" FraseInversa="PELO USUARIO">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_INTE"/>
    </Relacionamento>
    <Relacionamento id="R30036" EntidadePai="TB2_USER" EntidadeFilho="TB6_IMIN" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30037" EntidadePai="TB1_PRIO" EntidadeFilho="TB6_PCRM" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30038" EntidadePai="TB1_PROP" EntidadeFilho="TB1_RTPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30039" EntidadePai="TB2_USER" EntidadeFilho="TB1_RTPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30040" EntidadePai="TB6_INTR" EntidadeFilho="TB1_RTPR" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30041" EntidadePai="TB1_PROP" EntidadeFilho="TB1_CARP" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="2" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30042" EntidadePai="TB1_TIGC" EntidadeFilho="TB1_IGCA" CampoFilho="TIGC1_COD" CampoPai="TIGC1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TIGC1_COD" ChildName="TIGC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30043" EntidadePai="TB1_PROP" EntidadeFilho="TB1_CARP" CampoFilho="PROP1_COD0" CampoPai="PROP1_COD" Cardinality="0" Tipo="3" Frase="MAE DA" FraseInversa="FILHA DA">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD0"/>
    </Relacionamento>
    <Relacionamento id="R30044" EntidadePai="TB2_USER" EntidadeFilho="TB1_CARP" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30045" EntidadePai="TB2_USER" EntidadeFilho="TB1_IGCA" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30046" EntidadePai="TB1_SCAR" EntidadeFilho="TB1_HCAR" CampoFilho="SCAR1_COD" CampoPai="SCAR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="SCAR1_COD" ChildName="SCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30047" EntidadePai="TB1_TIGC" EntidadeFilho="TB1_HCAR" CampoFilho="TIGC1_COD" CampoPai="TIGC1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TIGC1_COD" ChildName="TIGC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30048" EntidadePai="TB2_USER" EntidadeFilho="TB1_HCAR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30049" EntidadePai="TB1_CARP" EntidadeFilho="TB1_HCAR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30050" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PCAR" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30051" EntidadePai="TB1_PCAR" EntidadeFilho="TB1_IGCA" CampoFilho="PCAR1_COD" CampoPai="PCAR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PCAR1_COD" ChildName="PCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30052" EntidadePai="TB1_PCAR" EntidadeFilho="TB1_HCAR" CampoFilho="PCAR1_COD" CampoPai="PCAR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PCAR1_COD" ChildName="PCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30053" EntidadePai="TB1_SCAR" EntidadeFilho="TB1_PCAR" CampoFilho="SCAR1_COD" CampoPai="SCAR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="SCAR1_COD" ChildName="SCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30054" EntidadePai="TB1_PCAR" EntidadeFilho="TB1_PRPC" CampoFilho="PCAR1_COD" CampoPai="PCAR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PCAR1_COD" ChildName="PCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30055" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PRPC" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30056" EntidadePai="TB2_USER" EntidadeFilho="TB1_PRPC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30057" EntidadePai="TB1_IGCA" EntidadeFilho="TB1_FICA" CampoFilho="IGCA1_COD" CampoPai="IGCA1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="IGCA1_COD" ChildName="IGCA1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30058" EntidadePai="TB1_PCAR" EntidadeFilho="TB1_TPPR" CampoFilho="PCAR1_COD" CampoPai="PCAR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PCAR1_COD" ChildName="PCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30059" EntidadePai="TB1_TPRC" EntidadeFilho="TB1_TPPR" CampoFilho="TPRC1_COD" CampoPai="TPRC1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TPRC1_COD" ChildName="TPRC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30060" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_GEORREF" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="RESPONSAVELGEORREFERENCIAMENTO" FraseInversa="DORESPONSAVELGEORREFERENCIAMENTO">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_GEORREF"/>
    </Relacionamento>
    <Relacionamento id="R30061" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_GOVFUND" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="GOVERNANCAFUNDIARIADO" FraseInversa="TEMGOVERNANCAFUNDIARIAPOR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_GOVFUND"/>
    </Relacionamento>
    <Relacionamento id="R30062" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_COORDFUND" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="COORDENADORFUNDIARIODO" FraseInversa="TEMCOORDENADORFUNDIARIPOR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_COORDFUND"/>
    </Relacionamento>
    <Relacionamento id="R30063" EntidadePai="TB1_CARP" EntidadeFilho="TB1_PCAP" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30064" EntidadePai="TB1_PCAR" EntidadeFilho="TB1_PCAP" CampoFilho="PCAR1_COD" CampoPai="PCAR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PCAR1_COD" ChildName="PCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30065" EntidadePai="TB1_ARQU_PROJ" EntidadeFilho="TB1_RRAPJ" CampoFilho="ARQU_PROJ1_COD" CampoPai="ARQU_PROJ1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ARQU_PROJ1_COD" ChildName="ARQU_PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30066" EntidadePai="TB1_ARQU_PARQ" EntidadeFilho="TB1_RRAPQ" CampoFilho="ARQU_PARQ1_COD" CampoPai="ARQU_PARQ1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ARQU_PARQ1_COD" ChildName="ARQU_PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30067" EntidadePai="TB1_ARQU_COMPL" EntidadeFilho="TB1_RRACM" CampoFilho="ARQU_COMPL1_COD" CampoPai="ARQU_COMPL1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ARQU_COMPL1_COD" ChildName="ARQU_COMPL1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30068" EntidadePai="TB1_MORR" EntidadeFilho="TB1_RRAPQ" CampoFilho="MORR1_COD" CampoPai="MORR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="MORR1_COD" ChildName="MORR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30069" EntidadePai="TB1_MORR" EntidadeFilho="TB1_RRACM" CampoFilho="MORR1_COD" CampoPai="MORR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="MORR1_COD" ChildName="MORR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30070" EntidadePai="TB1_MORR" EntidadeFilho="TB1_RRAPJ" CampoFilho="MORR1_COD" CampoPai="MORR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="MORR1_COD" ChildName="MORR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30071" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_ARQU_PARQ" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30072" EntidadePai="TB2_USER" EntidadeFilho="TB2_PEUS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30073" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_ARQU_COMPL" CampoFilho="COMPL1_COD" CampoPai="COMPL1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30074" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_ARQU_PROJ" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30075" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU_PARQ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30076" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU_COMPL" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30077" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU_PROJ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30078" EntidadePai="TB1_STVARQ" EntidadeFilho="TB1_ARQU_PARQ" CampoFilho="STVARQ1_COD" CampoPai="STVARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STVARQ1_COD" ChildName="STVARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30079" EntidadePai="TB1_STVARQ" EntidadeFilho="TB1_ARQU_COMPL" CampoFilho="STVARQ1_COD" CampoPai="STVARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STVARQ1_COD" ChildName="STVARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30080" EntidadePai="TB1_STVARQ" EntidadeFilho="TB1_ARQU_PROJ" CampoFilho="STVARQ1_COD" CampoPai="STVARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STVARQ1_COD" ChildName="STVARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30081" EntidadePai="TB1_PAAR" EntidadeFilho="TB1_ARQU_PARQ" CampoFilho="PAAR1_COD" CampoPai="PAAR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PAAR1_COD" ChildName="PAAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30082" EntidadePai="TB1_PAAR" EntidadeFilho="TB1_ARQU_COMPL" CampoFilho="PAAR1_COD" CampoPai="PAAR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PAAR1_COD" ChildName="PAAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30083" EntidadePai="TB1_PAAR" EntidadeFilho="TB1_ARQU_PROJ" CampoFilho="PAAR1_COD" CampoPai="PAAR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PAAR1_COD" ChildName="PAAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30084" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU_PARQ" CampoFilho="USER2_COD_VERIF" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="VERIFICOU" FraseInversa="VERIFICADO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_VERIF"/>
    </Relacionamento>
    <Relacionamento id="R30085" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU_COMPL" CampoFilho="USER2_COD_VERIF" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="VERIFICOU" FraseInversa="VERIFICADO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_VERIF"/>
    </Relacionamento>
    <Relacionamento id="R30086" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU_PROJ" CampoFilho="USER2_COD_VERIF" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="VERIFICOU" FraseInversa="VERIFICADO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_VERIF"/>
    </Relacionamento>
    <Relacionamento id="R30087" EntidadePai="TB1_STCC" EntidadeFilho="TB1_PCAR" CampoFilho="STCC1_COD" CampoPai="STCC1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STCC1_COD" ChildName="STCC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30088" EntidadePai="TB6_TIIN" EntidadeFilho="TB6_INTR" CampoFilho="TIIN6_COD" CampoPai="TIIN6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TIIN6_COD" ChildName="TIIN6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30089" EntidadePai="TB1_TPRC" EntidadeFilho="TB1_PPRC" CampoFilho="TPRC1_COD" CampoPai="TPRC1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TPRC1_COD" ChildName="TPRC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30090" EntidadePai="TB1_PCAR" EntidadeFilho="TB1_PPRC" CampoFilho="PCAR1_COD" CampoPai="PCAR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PCAR1_COD" ChildName="PCAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30091" EntidadePai="TB2_USER" EntidadeFilho="TB1_PPRC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1" Frase="ABRIU" FraseInversa="ABERTO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30092" EntidadePai="TB2_USER" EntidadeFilho="TB1_PPRC" CampoFilho="USER2_COD_CONC" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="CONCLUIU" FraseInversa="CONCLUIDO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_CONC"/>
    </Relacionamento>
    <Relacionamento id="R30093" EntidadePai="TB1_PROP" EntidadeFilho="TB1_HIPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30094" EntidadePai="TB2_USER" EntidadeFilho="TB1_HIPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30095" EntidadePai="TB1_STBE" EntidadeFilho="TB1_CTRT" CampoFilho="STBE1_COD" CampoPai="STBE1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STBE1_COD" ChildName="STBE1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30096" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU" CampoFilho="USER2_COD_VERIFGEO" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="VERIFICOU GEO" FraseInversa="VERIFICADO GEO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_VERIFGEO"/>
    </Relacionamento>
    <Relacionamento id="R30097" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_PROJ" CampoFilho="COMPL1_COD_UG" CampoPai="COMPL1_COD" Cardinality="0" Tipo="3" Frase="UG" FraseInversa="SOU UG">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD_UG"/>
    </Relacionamento>
    <Relacionamento id="R30098" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_PROJ" CampoFilho="COMPL1_COD_BOP" CampoPai="COMPL1_COD" Cardinality="0" Tipo="3" Frase="BOP" FraseInversa="SOU BOP">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD_BOP"/>
    </Relacionamento>
    <Relacionamento id="R30099" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_PROJ" CampoFilho="COMPL1_COD_XTRA" CampoPai="COMPL1_COD" Cardinality="0" Tipo="3" Frase="EXTRA" FraseInversa="SOU EXTRA">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD_XTRA"/>
    </Relacionamento>
    <Relacionamento id="R30100" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_PROJ" CampoFilho="COMPL1_COD_LI" CampoPai="COMPL1_COD" Cardinality="0" Tipo="3" Frase="LI" FraseInversa="SOU LI">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD_LI"/>
    </Relacionamento>
    <Relacionamento id="R30101" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_PROJ" CampoFilho="COMPL1_COD_LP" CampoPai="COMPL1_COD" Cardinality="0" Tipo="3" Frase="LP" FraseInversa="SOU LP">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD_LP"/>
    </Relacionamento>
    <Relacionamento id="R30102" EntidadePai="TB1_TAFT" EntidadeFilho="TB1_TDFT" CampoFilho="TAFT1_COD" CampoPai="TAFT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TAFT1_COD" ChildName="TAFT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30103" EntidadePai="TB1_PROP" EntidadeFilho="TB1_DPFT" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30104" EntidadePai="TB1_TDFT" EntidadeFilho="TB1_DPFT" CampoFilho="TDFT1_COD" CampoPai="TDFT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TDFT1_COD" ChildName="TDFT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30105" EntidadePai="TB2_USER" EntidadeFilho="TB1_DPFT" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30106" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_HIPQ" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30107" EntidadePai="TB2_NAJU" EntidadeFilho="TB2_PJUR" CampoFilho="NAJU2_COD" CampoPai="NAJU2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="NAJU2_COD" ChildName="NAJU2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30108" EntidadePai="TB2_USER" EntidadeFilho="TB1_HIPQ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30109" EntidadePai="TB2_SITU" EntidadeFilho="TB2_PJUR" CampoFilho="SITU2_COD" CampoPai="SITU2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="SITU2_COD" ChildName="SITU2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30110" EntidadePai="TB2_SIES" EntidadeFilho="TB2_PJUR" CampoFilho="SIES2_COD" CampoPai="SIES2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="SIES2_COD" ChildName="SIES2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30111" EntidadePai="TB1_MUNI" EntidadeFilho="TB2_PJUR" CampoFilho="MUNI1_COD" CampoPai="MUNI1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="MUNI1_COD" ChildName="MUNI1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30112" EntidadePai="TB2_PJUR" EntidadeFilho="TB2_SOAM" CampoFilho="PJUR2_CNPJ" CampoPai="PJUR2_CNPJ" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ"/>
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ"/>
    </Relacionamento>
    <Relacionamento id="R30113" EntidadePai="TB2_PFIS" EntidadeFilho="TB2_SOAM" CampoFilho="PFIS2_COD" CampoPai="PFIS2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PFIS2_COD" ChildName="PFIS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30114" EntidadePai="TB2_PFIS" EntidadeFilho="TB2_ENPF" CampoFilho="PFIS2_COD" CampoPai="PFIS2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PFIS2_COD" ChildName="PFIS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30115" EntidadePai="TB2_ENDE" EntidadeFilho="TB2_ENPF" CampoFilho="ENDE2_COD" CampoPai="ENDE2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ENDE2_COD" ChildName="ENDE2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30116" EntidadePai="TB2_TELE" EntidadeFilho="TB2_TLPF" CampoFilho="TELE2_COD" CampoPai="TELE2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TELE2_COD" ChildName="TELE2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30117" EntidadePai="TB1_MUNI" EntidadeFilho="TB2_ENDE" CampoFilho="MUNI1_COD" CampoPai="MUNI1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="MUNI1_COD" ChildName="MUNI1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30118" EntidadePai="TB2_PJUR" EntidadeFilho="TB2_TLPJ" CampoFilho="PJUR2_CNPJ" CampoPai="PJUR2_CNPJ" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ"/>
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ"/>
    </Relacionamento>
    <Relacionamento id="R30119" EntidadePai="TB2_TELE" EntidadeFilho="TB2_TLPJ" CampoFilho="TELE2_COD" CampoPai="TELE2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TELE2_COD" ChildName="TELE2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30120" EntidadePai="TB2_PFIS" EntidadeFilho="TB2_TLPF" CampoFilho="PFIS2_COD" CampoPai="PFIS2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PFIS2_COD" ChildName="PFIS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30121" EntidadePai="TB2_CNAE" EntidadeFilho="TB2_CNPJ" CampoFilho="CNAE2_COD" CampoPai="CNAE2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CNAE2_COD" ChildName="CNAE2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30122" EntidadePai="TB2_PJUR" EntidadeFilho="TB2_CNPJ" CampoFilho="PJUR2_SEQ" CampoPai="PJUR2_SEQ" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ"/>
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ"/>
    </Relacionamento>
    <Relacionamento id="R30123" EntidadePai="TB1_ESTA" EntidadeFilho="TB1_MUNI" CampoFilho="ESTA1_COD" CampoPai="ESTA1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="ESTA1_COD" ChildName="ESTA1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30124" EntidadePai="TB2_USER" EntidadeFilho="TB2_ARUS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30125" EntidadePai="TB2_PFIS" EntidadeFilho="TB2_EMPF" CampoFilho="PFIS2_COD" CampoPai="PFIS2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PFIS2_COD" ChildName="PFIS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30126" EntidadePai="TB2_PJUR" EntidadeFilho="TB2_EMPJ" CampoFilho="PJUR2_CNPJ" CampoPai="PJUR2_CNPJ" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ"/>
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ"/>
    </Relacionamento>
    <Relacionamento id="R30127" EntidadePai="TB2_PJUR" EntidadeFilho="TB2_SOPJ" CampoFilho="PJUR2_CNPJ" CampoPai="PJUR2_CNPJ" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ"/>
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ"/>
    </Relacionamento>
    <Relacionamento id="R30128" EntidadePai="TB2_PFIS" EntidadeFilho="TB2_SOPJ" CampoFilho="PFIS2_COD" CampoPai="PFIS2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PFIS2_COD" ChildName="PFIS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30129" EntidadePai="TB2_PJUR" EntidadeFilho="TB2_SOPJ" CampoFilho="PJUR2_CNPJ_SOCIO" CampoPai="PJUR2_CNPJ" Cardinality="0" Tipo="3" Frase="EH SOCIA PJ EM" FraseInversa="TEM COMO SOCIO A">
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ_SOCIO"/>
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ_SOCIO"/>
    </Relacionamento>
    <Relacionamento id="R30130" EntidadePai="TB2_SOPJ" EntidadeFilho="TB2_TSPJ" CampoFilho="SOPJ2_COD" CampoPai="SOPJ2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="SOPJ2_COD" ChildName="SOPJ2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30131" EntidadePai="TB2_TELE" EntidadeFilho="TB2_TSPJ" CampoFilho="TELE2_COD" CampoPai="TELE2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TELE2_COD" ChildName="TELE2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30132" EntidadePai="TB2_SOPJ" EntidadeFilho="TB2_ESPJ" CampoFilho="SOPJ2_COD" CampoPai="SOPJ2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="SOPJ2_COD" ChildName="SOPJ2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30133" EntidadePai="TB2_PJUR" EntidadeFilho="TB2_FUPJ" CampoFilho="PJUR2_CNPJ" CampoPai="PJUR2_CNPJ" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ"/>
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ"/>
    </Relacionamento>
    <Relacionamento id="R30134" EntidadePai="TB2_PFIS" EntidadeFilho="TB2_FUPJ" CampoFilho="PFIS2_COD" CampoPai="PFIS2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PFIS2_COD" ChildName="PFIS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30135" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_PRPR" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30136" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PRPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30137" EntidadePai="TB1_PROP" EntidadeFilho="TB1_GLEP" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30138" EntidadePai="TB1_GLEP" EntidadeFilho="TB1_HGGL" CampoFilho="GLEP1_COD" CampoPai="GLEP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="GLEP1_COD" ChildName="GLEP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30139" EntidadePai="TB1_PROP" EntidadeFilho="TB1_HGPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30140" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_CTRT" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30141" EntidadePai="TB1_GLEP" EntidadeFilho="TB1_HSGL" CampoFilho="GLEP1_COD" CampoPai="GLEP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="GLEP1_COD" ChildName="GLEP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30142" EntidadePai="TB1_PROP" EntidadeFilho="TB1_HSPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30143" EntidadePai="TB1_STPG" EntidadeFilho="TB1_PROP" CampoFilho="STPG1_COD" CampoPai="STPG1_COD" Cardinality="0" Tipo="3" Frase="EH O STATUS DA" FraseInversa="TEM O">
      <ColunaRel ParentName="STPG1_COD" ChildName="STPG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30144" EntidadePai="TB1_STPG" EntidadeFilho="TB1_HSPR" CampoFilho="STPG1_COD" CampoPai="STPG1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STPG1_COD" ChildName="STPG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30145" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PPRO" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30146" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PPRO" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30147" EntidadePai="TB1_STPG" EntidadeFilho="TB1_GLEP" CampoFilho="STPG1_COD" CampoPai="STPG1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STPG1_COD" ChildName="STPG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30148" EntidadePai="TB1_STPG" EntidadeFilho="TB1_HSGL" CampoFilho="STPG1_COD" CampoPai="STPG1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STPG1_COD" ChildName="STPG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30149" EntidadePai="TB2_PJUR" EntidadeFilho="TB1_PRIO" CampoFilho="PJUR2_CNPJ" CampoPai="PJUR2_CNPJ" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PJUR2_CNPJ" ChildName="PJUR2_CNPJ"/>
      <ColunaRel ParentName="PJUR2_SEQ" ChildName="PJUR2_SEQ"/>
    </Relacionamento>
    <Relacionamento id="R30150" EntidadePai="TB2_PFIS" EntidadeFilho="TB1_PRIO" CampoFilho="PFIS2_COD" CampoPai="PFIS2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PFIS2_COD" ChildName="PFIS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30151" EntidadePai="TB1_ESCR" EntidadeFilho="TB1_PARQ" CampoFilho="ESCR1_COD" CampoPai="ESCR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ESCR1_COD" ChildName="ESCR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30152" EntidadePai="TB1_ESCR" EntidadeFilho="TB1_PROJ" CampoFilho="ESCR1_COD" CampoPai="ESCR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ESCR1_COD" ChildName="ESCR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30153" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_CTRT" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30154" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_PRPQ" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30155" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PRPQ" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30156" EntidadePai="TB1_ESCR" EntidadeFilho="TB2_USER" CampoFilho="ESCR1_COD" CampoPai="ESCR1_COD" Cardinality="0" Tipo="3" Frase="EH BASE DO" FraseInversa="PERTENCE AO">
      <ColunaRel ParentName="ESCR1_COD" ChildName="ESCR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30157" EntidadePai="TB2_USER" EntidadeFilho="TB2_PUES" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30158" EntidadePai="TB1_ESCR" EntidadeFilho="TB2_PUES" CampoFilho="ESCR1_COD" CampoPai="ESCR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ESCR1_COD" ChildName="ESCR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30159" EntidadePai="TB2_USER" EntidadeFilho="TB2_PUPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30160" EntidadePai="TB1_PROJ" EntidadeFilho="TB2_PUPR" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30161" EntidadePai="TB1_PARQ" EntidadeFilho="TB2_PUPQ" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30162" EntidadePai="TB2_USER" EntidadeFilho="TB2_PUPQ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30163" EntidadePai="TB4_DOCU" EntidadeFilho="TB4_DOCO" CampoFilho="DOCU4_COD" CampoPai="DOCU4_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="DOCU4_COD" ChildName="DOCU4_COD"/>
    </Relacionamento>
    <Relacionamento id="R30164" EntidadePai="TB1_CTRT" EntidadeFilho="TB4_DOCO" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30165" EntidadePai="TB1_PROP" EntidadeFilho="TB4_DOPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30166" EntidadePai="TB4_DOCU" EntidadeFilho="TB4_DOPR" CampoFilho="DOCU4_COD" CampoPai="DOCU4_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="DOCU4_COD" ChildName="DOCU4_COD"/>
    </Relacionamento>
    <Relacionamento id="R30167" EntidadePai="TB1_CART" EntidadeFilho="TB1_PROP" CampoFilho="CART1_COD" CampoPai="CART1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="CART1_COD" ChildName="CART1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30168" EntidadePai="TB1_GLEP" EntidadeFilho="TB1_AERG" CampoFilho="GLEP1_COD" CampoPai="GLEP1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="GLEP1_COD" ChildName="GLEP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30169" EntidadePai="TB1_STCT" EntidadeFilho="TB1_CTRT" CampoFilho="STCT1_COD" CampoPai="STCT1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STCT1_COD" ChildName="STCT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30170" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_HSCT" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30171" EntidadePai="TB1_STCT" EntidadeFilho="TB1_HSCT" CampoFilho="STCT1_COD" CampoPai="STCT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STCT1_COD" ChildName="STCT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30172" EntidadePai="TB2_USER" EntidadeFilho="TB1_HSPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30173" EntidadePai="TB2_USER" EntidadeFilho="TB1_HSCT" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30174" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_VCTO" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30175" EntidadePai="TB1_ESCR" EntidadeFilho="TB1_PROP" CampoFilho="ESCR1_COD" CampoPai="ESCR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ESCR1_COD" ChildName="ESCR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30176" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_AERG" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30177" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_AERG" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30178" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PPRH" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30179" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PPRH" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30180" EntidadePai="TB1_PROP" EntidadeFilho="TB1_AERG" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30181" EntidadePai="TB2_USER" EntidadeFilho="TB2_CFUS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30182" EntidadePai="TB1_COMA" EntidadeFilho="TB1_PROP" CampoFilho="COMA1_COD" CampoPai="COMA1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="COMA1_COD" ChildName="COMA1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30183" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_GPCT" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30184" EntidadePai="TB1_GLEP" EntidadeFilho="TB1_GPCT" CampoFilho="GLEP1_COD" CampoPai="GLEP1_COD" Cardinality="2" Tipo="0">
      <ColunaRel ParentName="GLEP1_COD" ChildName="GLEP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30185" EntidadePai="TB2_USER" EntidadeFilho="TB1_DESE" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="2" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30186" EntidadePai="TB1_DESE" EntidadeFilho="TB1_PROP" CampoFilho="DESE1_COD" CampoPai="DESE1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="DESE1_COD" ChildName="DESE1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30187" EntidadePai="TB1_ESCR" EntidadeFilho="TB1_DESE" CampoFilho="ESCR1_COD" CampoPai="ESCR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ESCR1_COD" ChildName="ESCR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30188" EntidadePai="TB3_ATPT" EntidadeFilho="TB3_VAPT" CampoFilho="ATPT3_COD" CampoPai="ATPT3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATPT3_COD" ChildName="ATPT3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30189" EntidadePai="TB3_PTCM" EntidadeFilho="TB3_VAPT" CampoFilho="PTCM3_COD" CampoPai="PTCM3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PTCM3_COD" ChildName="PTCM3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30190" EntidadePai="TB3_CMPT" EntidadeFilho="TB3_PTCM" CampoFilho="CMPT3_COD" CampoPai="CMPT3_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="CMPT3_COD" ChildName="CMPT3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30191" EntidadePai="TB3_GRCM" EntidadeFilho="TB3_CMPT" CampoFilho="GRCM3_COD" CampoPai="GRCM3_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="GRCM3_COD" ChildName="GRCM3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30192" EntidadePai="TB3_CMPT" EntidadeFilho="TB3_ATPT" CampoFilho="CMPT3_COD" CampoPai="CMPT3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CMPT3_COD" ChildName="CMPT3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30193" EntidadePai="TB2_USER" EntidadeFilho="TB1_ESCR" CampoFilho="USER2_COD_RESP" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="EH RESPONSAVEL PELO" FraseInversa="TEM COMO RESPONSAVEL O">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_RESP"/>
    </Relacionamento>
    <Relacionamento id="R30194" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_ENG1" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="ENGENHEIROUM" FraseInversa="DOENGENHEIROUM">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_ENG1"/>
    </Relacionamento>
    <Relacionamento id="R30195" EntidadePai="TB1_STPG" EntidadeFilho="TB1_PROP" CampoFilho="STPG1_COD_ERP" CampoPai="STPG1_COD" Cardinality="2" Tipo="3" Frase="EH O STATUS NO ERP DA" FraseInversa="TEM NO ERP O">
      <ColunaRel ParentName="STPG1_COD" ChildName="STPG1_COD_ERP"/>
    </Relacionamento>
    <Relacionamento id="R30196" EntidadePai="TB3_CMLN" EntidadeFilho="TB3_LNCM" CampoFilho="CMLN3_COD" CampoPai="CMLN3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CMLN3_COD" ChildName="CMLN3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30197" EntidadePai="TB3_GRCM" EntidadeFilho="TB3_CMLN" CampoFilho="GRCM3_COD" CampoPai="GRCM3_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="GRCM3_COD" ChildName="GRCM3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30198" EntidadePai="TB3_LNCM" EntidadeFilho="TB3_VALN" CampoFilho="LNCM3_COD" CampoPai="LNCM3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="LNCM3_COD" ChildName="LNCM3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30199" EntidadePai="TB3_ATLN" EntidadeFilho="TB3_VALN" CampoFilho="ATLN3_COD" CampoPai="ATLN3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATLN3_COD" ChildName="ATLN3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30200" EntidadePai="TB3_CMLN" EntidadeFilho="TB3_ATLN" CampoFilho="CMLN3_COD" CampoPai="CMLN3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CMLN3_COD" ChildName="CMLN3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30201" EntidadePai="TB3_CMPL" EntidadeFilho="TB3_PLCM" CampoFilho="CMPL3_COD" CampoPai="CMPL3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CMPL3_COD" ChildName="CMPL3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30202" EntidadePai="TB3_PLCM" EntidadeFilho="TB3_VAPL" CampoFilho="PLCM3_COD" CampoPai="PLCM3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PLCM3_COD" ChildName="PLCM3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30203" EntidadePai="TB3_ATPL" EntidadeFilho="TB3_VAPL" CampoFilho="ATPL3_COD" CampoPai="ATPL3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATPL3_COD" ChildName="ATPL3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30204" EntidadePai="TB3_CMPL" EntidadeFilho="TB3_ATPL" CampoFilho="CMPL3_COD" CampoPai="CMPL3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CMPL3_COD" ChildName="CMPL3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30205" EntidadePai="TB3_GRCM" EntidadeFilho="TB3_CMPL" CampoFilho="GRCM3_COD" CampoPai="GRCM3_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="GRCM3_COD" ChildName="GRCM3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30206" EntidadePai="TB1_STAG" EntidadeFilho="TB1_AERG" CampoFilho="STAG1_COD" CampoPai="STAG1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STAG1_COD" ChildName="STAG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30207" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_PARQ" CampoFilho="COMPL1_COD" CampoPai="COMPL1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30208" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_LOAG" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30209" EntidadePai="TB1_MUNI" EntidadeFilho="TB1_CART" CampoFilho="MUNI1_COD" CampoPai="MUNI1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="MUNI1_COD" ChildName="MUNI1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30210" EntidadePai="TB1_COMA" EntidadeFilho="TB1_MUCO" CampoFilho="COMA1_COD" CampoPai="COMA1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="COMA1_COD" ChildName="COMA1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30211" EntidadePai="TB1_MUNI" EntidadeFilho="TB1_MUCO" CampoFilho="MUNI1_COD" CampoPai="MUNI1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="MUNI1_COD" ChildName="MUNI1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30212" EntidadePai="TB1_MUNI" EntidadeFilho="TB1_PROP" CampoFilho="MUNI1_COD" CampoPai="MUNI1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="MUNI1_COD" ChildName="MUNI1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30213" EntidadePai="TB1_STPG" EntidadeFilho="TB1_STCT" CampoFilho="STPG1_COD" CampoPai="STPG1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STPG1_COD" ChildName="STPG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30214" EntidadePai="TB1_PROP" EntidadeFilho="TB1_COPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30215" EntidadePai="TB2_USER" EntidadeFilho="TB2_ANUS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30216" EntidadePai="TB2_USER" EntidadeFilho="TB1_COPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30217" EntidadePai="TB2_USER" EntidadeFilho="TB1_GLEP" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30218" EntidadePai="TB2_USER" EntidadeFilho="TB1_HGGL" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30219" EntidadePai="TB2_PERM" EntidadeFilho="TB2_PEUS" CampoFilho="PERM2_COD" CampoPai="PERM2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PERM2_COD" ChildName="PERM2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30220" EntidadePai="TB2_USER" EntidadeFilho="TB2_HIPE" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30221" EntidadePai="TB2_USER" EntidadeFilho="TB2_PEUS" CampoFilho="USER2_COD_ATRIB" CampoPai="USER2_COD" Cardinality="0" Tipo="1" Frase="ATRIBUIU A" FraseInversa="FOI DADA POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_ATRIB"/>
    </Relacionamento>
    <Relacionamento id="R30222" EntidadePai="TB2_USER" EntidadeFilho="TB2_HIPE" CampoFilho="USER2_COD_ATRIB" CampoPai="USER2_COD" Cardinality="0" Tipo="1" Frase="ATRIBUIU" FraseInversa="FOI ATRIBUIDA POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_ATRIB"/>
    </Relacionamento>
    <Relacionamento id="R30223" EntidadePai="TB2_USER" EntidadeFilho="TB1_HGPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30224" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROP" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30225" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_COMPL" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30226" EntidadePai="TB2_USER" EntidadeFilho="TB2_PUCA" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30227" EntidadePai="TB3_CAMA" EntidadeFilho="TB2_PUCA" CampoFilho="CAMA3_COD" CampoPai="CAMA3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CAMA3_COD" ChildName="CAMA3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30228" EntidadePai="TB2_GRPM" EntidadeFilho="TB2_PEGR" CampoFilho="GRPM2_COD" CampoPai="GRPM2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="GRPM2_COD" ChildName="GRPM2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30229" EntidadePai="TB2_PERM" EntidadeFilho="TB2_PEGR" CampoFilho="PERM2_COD" CampoPai="PERM2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PERM2_COD" ChildName="PERM2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30230" EntidadePai="TB2_GRPM" EntidadeFilho="TB2_GPUS" CampoFilho="GRPM2_COD" CampoPai="GRPM2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="GRPM2_COD" ChildName="GRPM2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30231" EntidadePai="TB2_USER" EntidadeFilho="TB2_GPUS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30232" EntidadePai="TB2_GRPM" EntidadeFilho="TB2_PGCA" CampoFilho="GRPM2_COD" CampoPai="GRPM2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="GRPM2_COD" ChildName="GRPM2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30233" EntidadePai="TB3_CAMA" EntidadeFilho="TB2_PGCA" CampoFilho="CAMA3_COD" CampoPai="CAMA3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CAMA3_COD" ChildName="CAMA3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30234" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_POCO" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30235" EntidadePai="TB1_COMA" EntidadeFilho="TB1_CART" CampoFilho="COMA1_COD" CampoPai="COMA1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="COMA1_COD" ChildName="COMA1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30236" EntidadePai="TB2_USER" EntidadeFilho="TB2_FIUS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30237" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_PPCT" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30238" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PPCT" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30239" EntidadePai="TB1_LOAG" EntidadeFilho="TB1_VLOA" CampoFilho="LOAG1_COD" CampoPai="LOAG1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="LOAG1_COD" ChildName="LOAG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30240" EntidadePai="TB1_VLOA" EntidadeFilho="TB1_AERG" CampoFilho="VLOA1_COD" CampoPai="VLOA1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="VLOA1_COD" ChildName="VLOA1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30241" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PPCT" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30242" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_CSCT" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30243" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_CSCT" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30244" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_CTRT" CampoFilho="CTRT1_COD_SUC" CampoPai="CTRT1_COD" Cardinality="0" Tipo="3" Frase="FOI SUCEDIDO POR" FraseInversa="EH SUCESSOR DO">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD_SUC"/>
    </Relacionamento>
    <Relacionamento id="R30245" EntidadePai="TB2_USER" EntidadeFilho="TB1_MIDI" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30246" EntidadePai="TB2_ANUS" EntidadeFilho="TB1_MIDI" CampoFilho="ANUS2_COD" CampoPai="ANUS2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ANUS2_COD" ChildName="ANUS2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30247" EntidadePai="TB2_USER" EntidadeFilho="TB1_MIDI" CampoFilho="USER2_BLOQ" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="BLOQUEOU A" FraseInversa="FOI BLOQUEADA POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_BLOQ"/>
    </Relacionamento>
    <Relacionamento id="R30248" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_LOAG" CampoFilho="COMPL1_COD" CampoPai="COMPL1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30249" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_LOAG" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30250" EntidadePai="TB2_USER" EntidadeFilho="TB1_COCT" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30251" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_COCT" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30252" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PROP" CampoFilho="PROP1_COD_ORIG" CampoPai="PROP1_COD" Cardinality="0" Tipo="3" Frase="DESMEMBRADA NESTA" FraseInversa="DESMEMBRADAS DESTA">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD_ORIG"/>
    </Relacionamento>
    <Relacionamento id="R30253" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PROP" CampoFilho="PROP1_COD_REMEMB" CampoPai="PROP1_COD" Cardinality="0" Tipo="3" Frase="REMEMBRADA DESTA" FraseInversa="REMEMBRARAM NESTA">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD_REMEMB"/>
    </Relacionamento>
    <Relacionamento id="R30254" EntidadePai="TB1_PROP" EntidadeFilho="TB1_DEFU" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30255" EntidadePai="TB1_DEFU" EntidadeFilho="TB1_PRDF" CampoFilho="DEFU1_ORD" CampoPai="DEFU1_ORD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="DEFU1_ORD" ChildName="DEFU1_ORD"/>
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30256" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PRDF" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30257" EntidadePai="TB1_MUNI" EntidadeFilho="TB1_COMA" CampoFilho="MUNI1_COD" CampoPai="MUNI1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="MUNI1_COD" ChildName="MUNI1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30258" EntidadePai="TB1_CART" EntidadeFilho="TB1_PROP" CampoFilho="CART1_COD_ERP" CampoPai="CART1_COD" Cardinality="0" Tipo="3" Frase="DO ERP NA" FraseInversa="REGISTRADA NO ERP NO">
      <ColunaRel ParentName="CART1_COD" ChildName="CART1_COD_ERP"/>
    </Relacionamento>
    <Relacionamento id="R30259" EntidadePai="TB1_PROP" EntidadeFilho="TB0_LDOM" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30260" EntidadePai="TB2_USER" EntidadeFilho="TB0_LDOM" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30261" EntidadePai="TB1_PROP" EntidadeFilho="TB1_DEFU" CampoFilho="PROP1_COD_EXIST" CampoPai="PROP1_COD" Cardinality="0" Tipo="1" Frase="EXISTENTE EM" FraseInversa="NA EXISTENTE">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD_EXIST"/>
    </Relacionamento>
    <Relacionamento id="R30262" EntidadePai="TB2_USER" EntidadeFilho="TB2_CMCF" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30263" EntidadePai="TB2_USER" EntidadeFilho="TB2_CMCF" CampoFilho="USER2_COD_DEST" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="ENVIOU" FraseInversa="RECEBIDAS">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_DEST"/>
    </Relacionamento>
    <Relacionamento id="R30264" EntidadePai="TB3_CAMA" EntidadeFilho="TB3_SUCA" CampoFilho="CAMA3_COD" CampoPai="CAMA3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CAMA3_COD" ChildName="CAMA3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30265" EntidadePai="TB2_USER" EntidadeFilho="TB0_LGEO" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30266" EntidadePai="TB1_PROP" EntidadeFilho="TB0_LGEO" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30267" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PRGR" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30268" EntidadePai="TB1_GRPR" EntidadeFilho="TB1_PRGR" CampoFilho="GRPR1_COD" CampoPai="GRPR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="GRPR1_COD" ChildName="GRPR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30269" EntidadePai="TB2_USER" EntidadeFilho="TB1_TOCO" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="INCLUIU A" FraseInversa="INCLUIDA POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30270" EntidadePai="TB2_USER" EntidadeFilho="TB1_TOCO" CampoFilho="USER2_COD_REM" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="REMOVEU A" FraseInversa="REMOVIDA POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_REM"/>
    </Relacionamento>
    <Relacionamento id="R30271" EntidadePai="TB2_USER" EntidadeFilho="TB1_CTRT" CampoFilho="USER2_COD_AUTDIST" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_AUTDIST"/>
    </Relacionamento>
    <Relacionamento id="R30272" EntidadePai="TB2_USER" EntidadeFilho="TB1_PPRH" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30273" EntidadePai="TB2_USER" EntidadeFilho="TB1_MIDI" CampoFilho="USER2_COD_DEL" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="EXCLUIU" FraseInversa="FOI EXCLUIDA POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_DEL"/>
    </Relacionamento>
    <Relacionamento id="R30274" EntidadePai="TB1_STPJ" EntidadeFilho="TB1_PROJ" CampoFilho="STPJ1_COD" CampoPai="STPJ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STPJ1_COD" ChildName="STPJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30275" EntidadePai="TB1_STCP" EntidadeFilho="TB1_COMPL" CampoFilho="STCP1_COD" CampoPai="STCP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STCP1_COD" ChildName="STCP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30276" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_ENG2" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="ENGENHEIRODOIS" FraseInversa="DOENGENHEIRODOIS">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_ENG2"/>
    </Relacionamento>
    <Relacionamento id="R30277" EntidadePai="TB1_DESE" EntidadeFilho="TB1_PROJ" CampoFilho="DESE1_COD" CampoPai="DESE1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="DESE1_COD" ChildName="DESE1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30278" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_AMB" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="AMBIENTAL" FraseInversa="DOAMBIENTAL">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_AMB"/>
    </Relacionamento>
    <Relacionamento id="R30279" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_JUR" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="JURIDICO" FraseInversa="DOJURIDICO">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_JUR"/>
    </Relacionamento>
    <Relacionamento id="R30280" EntidadePai="TB1_PROP" EntidadeFilho="TB1_RFPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="2" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30281" EntidadePai="TB2_CARG" EntidadeFilho="TB2_USER" CampoFilho="CARG2_COD" CampoPai="CARG2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="CARG2_COD" ChildName="CARG2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30282" EntidadePai="TB1_RFPR" EntidadeFilho="TB1_CTRF" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30283" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_CTRF" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30284" EntidadePai="TB1_PROP" EntidadeFilho="TB1_SIGS" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30285" EntidadePai="TB1_PROP" EntidadeFilho="TB1_SNCS" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30286" EntidadePai="TB1_PROP" EntidadeFilho="TB1_CARS" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30287" EntidadePai="TB2_USER" EntidadeFilho="TB2_REPO" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30288" EntidadePai="TB2_USER" EntidadeFilho="TB2_NOTI" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30289" EntidadePai="TB2_USER" EntidadeFilho="TB2_NOTI" CampoFilho="USER2_COD_DEST" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="DESTINATARIO" FraseInversa="PARA">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_DEST"/>
    </Relacionamento>
    <Relacionamento id="R30290" EntidadePai="TB2_NOTI" EntidadeFilho="TB2_NOLI" CampoFilho="NOTI2_COD" CampoPai="NOTI2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="NOTI2_COD" ChildName="NOTI2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30291" EntidadePai="TB2_USER" EntidadeFilho="TB2_NOLI" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30292" EntidadePai="TB1_STCT" EntidadeFilho="TB1_VCTO" CampoFilho="STCT1_COD" CampoPai="STCT1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STCT1_COD" ChildName="STCT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30293" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_RESP" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="RESPONSAVEL PELO" FraseInversa="TEM RESPONSAVEL">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_RESP"/>
    </Relacionamento>
    <Relacionamento id="R30294" EntidadePai="TB1_PROP" EntidadeFilho="TB1_ARQU" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30295" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_ARQU" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30296" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_ARQU" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30297" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30298" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU" CampoFilho="USER2_COD_VERIF" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="VERIFICOU" FraseInversa="VERIFICADO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_VERIF"/>
    </Relacionamento>
    <Relacionamento id="R30299" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_RFCM" CampoFilho="COMPL1_COD" CampoPai="COMPL1_COD" Cardinality="2" Tipo="0">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30300" EntidadePai="TB2_USER" EntidadeFilho="TB1_RFCM" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30301" EntidadePai="TB2_USER" EntidadeFilho="TB1_ARQU" CampoFilho="USER2_COD_RECU" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="RECUSOU" FraseInversa="RECUSADO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_RECU"/>
    </Relacionamento>
    <Relacionamento id="R30302" EntidadePai="TB1_TIPR" EntidadeFilho="TB1_PROJ" CampoFilho="TIPR1_COD" CampoPai="TIPR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TIPR1_COD" ChildName="TIPR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30303" EntidadePai="TB2_USER" EntidadeFilho="TB1_ESCR" CampoFilho="USER2_COD_GERE" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="EH GERENTE" FraseInversa="TEM COMO GERENTE">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_GERE"/>
    </Relacionamento>
    <Relacionamento id="R30304" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PROJ" CampoFilho="PRIO1_COD_CESS" CampoPai="PRIO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD_CESS"/>
    </Relacionamento>
    <Relacionamento id="R30305" EntidadePai="TB2_USER" EntidadeFilho="TB1_COPJ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30306" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_COPJ" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30307" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PARQ" CampoFilho="PRIO1_COD_CESS" CampoPai="PRIO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD_CESS"/>
    </Relacionamento>
    <Relacionamento id="R30308" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_COPP" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30309" EntidadePai="TB2_USER" EntidadeFilho="TB1_COPP" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30310" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_CSCH" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30311" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_CSCH" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30312" EntidadePai="TB2_ENDE" EntidadeFilho="TB1_PRIO" CampoFilho="ENDE2_COD" CampoPai="ENDE2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ENDE2_COD" ChildName="ENDE2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30313" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PRIO" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="3" Frase="EH MORADIA DO" FraseInversa="MORA NA">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30314" EntidadePai="TB2_USER" EntidadeFilho="TB1_CTRT" CampoFilho="USER2_COD_CRIA" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="CRIOU" FraseInversa="CRIADO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_CRIA"/>
    </Relacionamento>
    <Relacionamento id="R30315" EntidadePai="TB1_PAAR" EntidadeFilho="TB1_ARQU" CampoFilho="PAAR1_COD" CampoPai="PAAR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PAAR1_COD" ChildName="PAAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30316" EntidadePai="TB2_TERM" EntidadeFilho="TB2_ACTM" CampoFilho="TERM2_COD" CampoPai="TERM2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TERM2_COD" ChildName="TERM2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30317" EntidadePai="TB2_USER" EntidadeFilho="TB2_ACTM" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30318" EntidadePai="TB1_CTRF" EntidadeFilho="TB1_CRFS" CampoFilho="CTRF1_COD" CampoPai="CTRF1_COD" Cardinality="0" Tipo="0" Frase="SUBSTITUI OUTRO EM" FraseInversa="TEM COMO SUBSTITUTO O">
      <ColunaRel ParentName="CTRF1_COD" ChildName="CTRF1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30319" EntidadePai="TB1_CTRF" EntidadeFilho="TB1_CRFS" CampoFilho="CTRF1_COD_SUBS" CampoPai="CTRF1_COD" Cardinality="0" Tipo="0" Frase="EH SUBSTITUIDO NO" FraseInversa="TEM COMO SUBSTITUIDO O">
      <ColunaRel ParentName="CTRF1_COD" ChildName="CTRF1_COD_SUBS"/>
    </Relacionamento>
    <Relacionamento id="R30320" EntidadePai="TB1_COMPL" EntidadeFilho="TB1_CTRF" CampoFilho="COMPL1_COD" CampoPai="COMPL1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="COMPL1_COD" ChildName="COMPL1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30321" EntidadePai="TB2_USER" EntidadeFilho="TB1_CRFS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30322" EntidadePai="TB2_USER" EntidadeFilho="TB1_RFPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30323" EntidadePai="TB2_USER" EntidadeFilho="TB1_CTRF" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30324" EntidadePai="TB2_BANC" EntidadeFilho="TB1_PRIO" CampoFilho="BANC2_COD" CampoPai="BANC2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="BANC2_COD" ChildName="BANC2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30325" EntidadePai="TB2_USER" EntidadeFilho="TB3_CJAN" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30326" EntidadePai="TB3_CJAN" EntidadeFilho="TB3_ANOT" CampoFilho="CJAN3_COD" CampoPai="CJAN3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CJAN3_COD" ChildName="CJAN3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30327" EntidadePai="TB3_ANOT" EntidadeFilho="TB3_HANO" CampoFilho="ANOT3_COD" CampoPai="ANOT3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="ANOT3_COD" ChildName="ANOT3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30328" EntidadePai="TB2_USER" EntidadeFilho="TB3_HANO" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30329" EntidadePai="TB2_USER" EntidadeFilho="TB2_EQUI" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30330" EntidadePai="TB2_EQUI" EntidadeFilho="TB2_USEQ" CampoFilho="EQUI2_COD" CampoPai="EQUI2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="EQUI2_COD" ChildName="EQUI2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30331" EntidadePai="TB2_USER" EntidadeFilho="TB2_USEQ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30332" EntidadePai="TB2_USER" EntidadeFilho="TB2_USEQ" CampoFilho="USER2_COD_ATRIB" CampoPai="USER2_COD" Cardinality="0" Tipo="1" Frase="ATRIBUIU O" FraseInversa="ATRIBUIDO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_ATRIB"/>
    </Relacionamento>
    <Relacionamento id="R30333" EntidadePai="TB3_CJAN" EntidadeFilho="TB3_CACA" CampoFilho="CJAN3_COD" CampoPai="CJAN3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CJAN3_COD" ChildName="CJAN3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30334" EntidadePai="TB3_CAMA" EntidadeFilho="TB3_CACA" CampoFilho="CAMA3_COD" CampoPai="CAMA3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CAMA3_COD" ChildName="CAMA3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30335" EntidadePai="TB3_CJAN" EntidadeFilho="TB3_CCUS" CampoFilho="CJAN3_COD" CampoPai="CJAN3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CJAN3_COD" ChildName="CJAN3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30336" EntidadePai="TB2_USER" EntidadeFilho="TB3_CCUS" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30337" EntidadePai="TB3_CJAN" EntidadeFilho="TB3_CCEQ" CampoFilho="CJAN3_COD" CampoPai="CJAN3_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CJAN3_COD" ChildName="CJAN3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30338" EntidadePai="TB2_EQUI" EntidadeFilho="TB3_CCEQ" CampoFilho="EQUI2_COD" CampoPai="EQUI2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="EQUI2_COD" ChildName="EQUI2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30339" EntidadePai="TB3_GRCA" EntidadeFilho="TB3_CAMA" CampoFilho="GRCA3_COD" CampoPai="GRCA3_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="GRCA3_COD" ChildName="GRCA3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30340" EntidadePai="TB2_PERM" EntidadeFilho="TB3_CAMA" CampoFilho="PERM2_COD" CampoPai="PERM2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PERM2_COD" ChildName="PERM2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30341" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_RLPR" CampoFilho="PRIO1_COD_PAI" CampoPai="PRIO1_COD" Cardinality="0" Tipo="0" Frase="EH PAI" FraseInversa="TEM COMO PAI">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD_PAI"/>
    </Relacionamento>
    <Relacionamento id="R30342" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_RLPR" CampoFilho="PRIO1_COD_FILHO" CampoPai="PRIO1_COD" Cardinality="0" Tipo="0" Frase="EH FILHO" FraseInversa="TEM COMO FILHO">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD_FILHO"/>
    </Relacionamento>
    <Relacionamento id="R30343" EntidadePai="TB1_TPRL" EntidadeFilho="TB1_RLPR" CampoFilho="TPRL1_COD" CampoPai="TPRL1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TPRL1_COD" ChildName="TPRL1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30344" EntidadePai="TB1_CONC" EntidadeFilho="TB1_PRCO" CampoFilho="CONC1_COD" CampoPai="CONC1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CONC1_COD" ChildName="CONC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30345" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PRCO" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30346" EntidadePai="TB2_USER" EntidadeFilho="TB1_CONC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30347" EntidadePai="TB2_USER" EntidadeFilho="TB1_PRCO" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30348" EntidadePai="TB2_USER" EntidadeFilho="TB1_RLPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30349" EntidadePai="TB5_PROJ" EntidadeFilho="TB5_COMP" CampoFilho="PROJ5_COD" CampoPai="PROJ5_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROJ5_COD" ChildName="PROJ5_COD"/>
    </Relacionamento>
    <Relacionamento id="R30350" EntidadePai="TB5_COMP" EntidadeFilho="TB5_PARK" CampoFilho="COMP5_COD" CampoPai="COMP5_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="COMP5_COD" ChildName="COMP5_COD"/>
    </Relacionamento>
    <Relacionamento id="R30351" EntidadePai="TB5_COMP" EntidadeFilho="TB5_SCEN" CampoFilho="COMP5_COD" CampoPai="COMP5_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="COMP5_COD" ChildName="COMP5_COD"/>
    </Relacionamento>
    <Relacionamento id="R30352" EntidadePai="TB5_SCEN" EntidadeFilho="TB5_SITE" CampoFilho="SCEN5_COD" CampoPai="SCEN5_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="SCEN5_COD" ChildName="SCEN5_COD"/>
    </Relacionamento>
    <Relacionamento id="R30353" EntidadePai="TB5_PARK" EntidadeFilho="TB5_SITE" CampoFilho="PARK5_COD" CampoPai="PARK5_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PARK5_COD" ChildName="PARK5_COD"/>
    </Relacionamento>
    <Relacionamento id="R30354" EntidadePai="TB1_STAG" EntidadeFilho="TB5_SITE" CampoFilho="STAG1_COD" CampoPai="STAG1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STAG1_COD" ChildName="STAG1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30355" EntidadePai="TB5_SITE" EntidadeFilho="TB5_TURB" CampoFilho="SITE5_COD" CampoPai="SITE5_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="SITE5_COD" ChildName="SITE5_COD"/>
    </Relacionamento>
    <Relacionamento id="R30356" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_PAIN" CampoFilho="CTRT1_COD_REL" CampoPai="CTRT1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD_REL"/>
    </Relacionamento>
    <Relacionamento id="R30357" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PAIN" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30358" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_PAIN" CampoFilho="CTRT1_COD_EFET" CampoPai="CTRT1_COD" Cardinality="0" Tipo="3" Frase="EFETIVOU" FraseInversa="EFETIVADA">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD_EFET"/>
    </Relacionamento>
    <Relacionamento id="R30359" EntidadePai="TB2_USER" EntidadeFilho="TB1_PAIN" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30360" EntidadePai="TB2_USER" EntidadeFilho="TB1_PAIN" CampoFilho="USER2_COD_EFET" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="EFETIVOU" FraseInversa="EFETIVADA POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_EFET"/>
    </Relacionamento>
    <Relacionamento id="R30361" EntidadePai="TB1_TIPR" EntidadeFilho="TB1_PAIN" CampoFilho="TIPR1_COD" CampoPai="TIPR1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TIPR1_COD" ChildName="TIPR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30362" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_PAIN" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30363" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PAIN" CampoFilho="PRIO1_COD_CESS" CampoPai="PRIO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD_CESS"/>
    </Relacionamento>
    <Relacionamento id="R30364" EntidadePai="TB1_PROJ" EntidadeFilho="TB1_PAIN" CampoFilho="PROJ1_COD" CampoPai="PROJ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROJ1_COD" ChildName="PROJ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30365" EntidadePai="TB1_STGEO" EntidadeFilho="TB1_PROP" CampoFilho="PROP1_GEO_STATUS" CampoPai="STGEO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STGEO1_COD" ChildName="PROP1_GEO_STATUS"/>
    </Relacionamento>
    <Relacionamento id="R30366" EntidadePai="TB1_TCTB" EntidadeFilho="TB1_CTRT" CampoFilho="TCTB1_COD" CampoPai="TCTB1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TCTB1_COD" ChildName="TCTB1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30367" EntidadePai="TB1_GTCT" EntidadeFilho="TB1_TCTB" CampoFilho="GTCT1_COD" CampoPai="GTCT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="GTCT1_COD" ChildName="GTCT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30368" EntidadePai="TB1_STAP" EntidadeFilho="TB1_PROP" CampoFilho="STAP1_COD" CampoPai="STAP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STAP1_COD" ChildName="STAP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30369" EntidadePai="TB1_STSE" EntidadeFilho="TB1_CTRT" CampoFilho="STSE1_COD" CampoPai="STSE1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STSE1_COD" ChildName="STSE1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30370" EntidadePai="TB1_GRTR" EntidadeFilho="TB1_TIRE" CampoFilho="GRTR1_COD" CampoPai="GRTR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30371" EntidadePai="TB1_STRF" EntidadeFilho="TB1_SRFP" CampoFilho="TIRE1_COD" CampoPai="TIRE1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TIRE1_COD" ChildName="TIRE1_COD"/>
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
      <ColunaRel ParentName="STRF1_COD" ChildName="STRF1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30372" EntidadePai="TB1_TIRE" EntidadeFilho="TB0_LSRP" CampoFilho="GRTR1_COD" CampoPai="GRTR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
      <ColunaRel ParentName="TIRE1_COD" ChildName="TIRE1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30373" EntidadePai="TB1_STRF" EntidadeFilho="TB0_LSRP" CampoFilho="STRF1_COD_ANT" CampoPai="STRF1_COD" Cardinality="0" Tipo="3" Frase="EH ANTERIOR" FraseInversa="TEM ANTERIOR">
      <ColunaRel ParentName="STRF1_COD" ChildName="STRF1_COD_ANT"/>
      <ColunaRel ParentName="TIRE1_COD" ChildName="TIRE1_COD"/>
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30374" EntidadePai="TB1_STRF" EntidadeFilho="TB0_LSRP" CampoFilho="GRTR1_COD" CampoPai="GRTR1_COD" Cardinality="0" Tipo="3" Frase="EH NOVO" FraseInversa="TEM NOVO">
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
      <ColunaRel ParentName="STRF1_COD" ChildName="STRF1_COD_NOVO"/>
      <ColunaRel ParentName="TIRE1_COD" ChildName="TIRE1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30375" EntidadePai="TB1_RFPR" EntidadeFilho="TB1_SRFP" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30376" EntidadePai="TB2_USER" EntidadeFilho="TB1_SRFP" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30377" EntidadePai="TB2_USER" EntidadeFilho="TB0_LSRP" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30378" EntidadePai="TB1_TIRE" EntidadeFilho="TB1_SRFP" CampoFilho="TIRE1_COD" CampoPai="TIRE1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TIRE1_COD" ChildName="TIRE1_COD"/>
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30379" EntidadePai="TB1_TIRE" EntidadeFilho="TB1_STRF" CampoFilho="TIRE1_COD" CampoPai="TIRE1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="TIRE1_COD" ChildName="TIRE1_COD"/>
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30380" EntidadePai="TB1_GRTR" EntidadeFilho="TB1_SRFP" CampoFilho="GRTR1_COD" CampoPai="GRTR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="GRTR1_COD" ChildName="GRTR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30381" EntidadePai="TB1_STVARQ" EntidadeFilho="TB1_ARQU" CampoFilho="STVARQ1_COD" CampoPai="STVARQ1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STVARQ1_COD" ChildName="STVARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30382" EntidadePai="TB1_STDOM" EntidadeFilho="TB1_PROP" CampoFilho="PROP1_DOMINIO" CampoPai="STDOM1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STDOM1_COD" ChildName="PROP1_DOMINIO"/>
    </Relacionamento>
    <Relacionamento id="R30383" EntidadePai="TB1_STCTB" EntidadeFilho="TB1_CTRT" CampoFilho="CTRT1_STATUS_CTRT" CampoPai="STCTB1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STCTB1_COD" ChildName="CTRT1_STATUS_CTRT"/>
    </Relacionamento>
    <Relacionamento id="R30384" EntidadePai="TB1_PROP" EntidadeFilho="TB1_REFU" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30385" EntidadePai="TB1_PROP" EntidadeFilho="TB1_REFU" CampoFilho="PROP1_COD_DEST" CampoPai="PROP1_COD" Cardinality="0" Tipo="0" Frase="EH DESTINO" FraseInversa="COM DESTINO NESTA">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD_DEST"/>
    </Relacionamento>
    <Relacionamento id="R30386" EntidadePai="TB3_GROF" EntidadeFilho="TB3_TIOF" CampoFilho="GROF3_COD" CampoPai="GROF3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="GROF3_COD" ChildName="GROF3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30387" EntidadePai="TB3_TIOF" EntidadeFilho="TB3_ORTO" CampoFilho="TIOF3_COD" CampoPai="TIOF3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TIOF3_COD" ChildName="TIOF3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30388" EntidadePai="TB3_CAMA" EntidadeFilho="TB3_ORTO" CampoFilho="CAMA3_COD" CampoPai="CAMA3_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="CAMA3_COD" ChildName="CAMA3_COD"/>
    </Relacionamento>
    <Relacionamento id="R30389" EntidadePai="TB0_TJOB" EntidadeFilho="TB0_JOBQ" CampoFilho="TJOB0_COD" CampoPai="TJOB0_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TJOB0_COD" ChildName="TJOB0_COD"/>
    </Relacionamento>
    <Relacionamento id="R30390" EntidadePai="TB1_PARQ" EntidadeFilho="TB1_PQSL" CampoFilho="PARQ1_COD" CampoPai="PARQ1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PARQ1_COD" ChildName="PARQ1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30391" EntidadePai="TB1_SECO" EntidadeFilho="TB1_CTRT" CampoFilho="SECO1_COD" CampoPai="SECO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="SECO1_COD" ChildName="SECO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30392" EntidadePai="TB1_PAAR" EntidadeFilho="TB1_MOPA" CampoFilho="PAAR1_COD" CampoPai="PAAR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PAAR1_COD" ChildName="PAAR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30393" EntidadePai="TB1_MORR" EntidadeFilho="TB1_MOPA" CampoFilho="MORR1_COD" CampoPai="MORR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="MORR1_COD" ChildName="MORR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30394" EntidadePai="TB1_ARQU" EntidadeFilho="TB1_RRAR" CampoFilho="ARQU1_COD" CampoPai="ARQU1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ARQU1_COD" ChildName="ARQU1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30395" EntidadePai="TB1_MORR" EntidadeFilho="TB1_RRAR" CampoFilho="MORR1_COD" CampoPai="MORR1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="MORR1_COD" ChildName="MORR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30396" EntidadePai="TB1_STIN" EntidadeFilho="TB1_CTRT" CampoFilho="STIN1_COD" CampoPai="STIN1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="STIN1_COD" ChildName="STIN1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30397" EntidadePai="TB2_USER" EntidadeFilho="TB1_EDIN" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30398" EntidadePai="TB1_GPCT" EntidadeFilho="TB1_EDIN" CampoFilho="GLEP1_COD" CampoPai="GLEP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="GLEP1_COD" ChildName="GLEP1_COD"/>
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30399" EntidadePai="TB1_PROP" EntidadeFilho="TB1_PCPR" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30400" EntidadePai="TB1_TPPC" EntidadeFilho="TB1_PCPR" CampoFilho="TPPC1_COD" CampoPai="TPPC1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="TPPC1_COD" ChildName="TPPC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30401" EntidadePai="TB1_STPC" EntidadeFilho="TB1_PCPR" CampoFilho="STPC1_COD" CampoPai="STPC1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STPC1_COD" ChildName="STPC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30402" EntidadePai="TB1_PROP" EntidadeFilho="TB1_HSPC" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30403" EntidadePai="TB1_PCPR" EntidadeFilho="TB1_HSPC" CampoFilho="PCPR1_COD" CampoPai="PCPR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PCPR1_COD" ChildName="PCPR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30404" EntidadePai="TB1_STPC" EntidadeFilho="TB1_HSPC" CampoFilho="STPC1_COD" CampoPai="STPC1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="STPC1_COD" ChildName="STPC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30405" EntidadePai="TB2_USER" EntidadeFilho="TB1_PCPR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30406" EntidadePai="TB2_USER" EntidadeFilho="TB1_HSPC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30407" EntidadePai="TB2_USER" EntidadeFilho="TB1_PCPR" CampoFilho="USER2_COD_STATUS" CampoPai="USER2_COD" Cardinality="0" Tipo="1" Frase="ALTEROU STATUS" FraseInversa="TEVE STATUS ALTERADO POR">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_STATUS"/>
    </Relacionamento>
    <Relacionamento id="R30408" EntidadePai="TB1_PCPR" EntidadeFilho="TB1_PTPC" CampoFilho="PCPR1_COD" CampoPai="PCPR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PCPR1_COD" ChildName="PCPR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30409" EntidadePai="TB1_PRIO" EntidadeFilho="TB1_PTPC" CampoFilho="PRIO1_COD" CampoPai="PRIO1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PRIO1_COD" ChildName="PRIO1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30410" EntidadePai="TB1_CONC" EntidadeFilho="TB1_PTPC" CampoFilho="CONC1_COD" CampoPai="CONC1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="CONC1_COD" ChildName="CONC1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30411" EntidadePai="TB1_PCPR" EntidadeFilho="TB1_CMPC" CampoFilho="PCPR1_COD" CampoPai="PCPR1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PCPR1_COD" ChildName="PCPR1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30412" EntidadePai="TB2_USER" EntidadeFilho="TB1_CMPC" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30413" EntidadePai="TB1_COND" EntidadeFilho="TB1_CNCT" CampoFilho="COND1_COD" CampoPai="COND1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="COND1_COD" ChildName="COND1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30414" EntidadePai="TB1_CTRT" EntidadeFilho="TB1_CNCT" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30415" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_ENG_NEG" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="ENGENHEIRONEGOCIO" FraseInversa="DOENGENHEIRONEGOCIO">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_ENG_NEG"/>
    </Relacionamento>
    <Relacionamento id="R30416" EntidadePai="TB2_USER" EntidadeFilho="TB1_PROJ" CampoFilho="USER2_COD_AMB_GEO" CampoPai="USER2_COD" Cardinality="0" Tipo="3" Frase="RESPONSAVELAMBGEO" FraseInversa="DORESPONSAVELAMBGEO">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD_AMB_GEO"/>
    </Relacionamento>
    <Relacionamento id="R30417" EntidadePai="TB6_INTR" EntidadeFilho="TB6_DOIN" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30418" EntidadePai="TB6_INTR" EntidadeFilho="TB6_IMIN" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30419" EntidadePai="TB6_ATPR" EntidadeFilho="TB6_VAPR" CampoFilho="ATPR6_COD" CampoPai="ATPR6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATPR6_COD" ChildName="ATPR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30420" EntidadePai="TB6_PROC" EntidadeFilho="TB6_VAPR" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30421" EntidadePai="TB6_INTR" EntidadeFilho="TB6_PEIN" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30422" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_PEIN" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30423" EntidadePai="TB6_PROC" EntidadeFilho="TB6_DOIN" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30424" EntidadePai="TB6_PROC" EntidadeFilho="TB6_IMIN" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30425" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_HPEC" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30426" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_VAPC" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30427" EntidadePai="TB6_ATPC" EntidadeFilho="TB6_VAPC" CampoFilho="ATPC6_COD" CampoPai="ATPC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATPC6_COD" ChildName="ATPC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30428" EntidadePai="TB6_PROC" EntidadeFilho="TB6_HPRC" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30429" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_DOIN" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30430" EntidadePai="TB6_ATPR" EntidadeFilho="TB6_LAPR" CampoFilho="ATPR6_COD" CampoPai="ATPR6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="ATPR6_COD" ChildName="ATPR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30431" EntidadePai="TB6_ATIN" EntidadeFilho="TB6_LAIN" CampoFilho="ATIN6_COD" CampoPai="ATIN6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="ATIN6_COD" ChildName="ATIN6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30432" EntidadePai="TB6_ATPC" EntidadeFilho="TB6_LATP" CampoFilho="ATPC6_COD" CampoPai="ATPC6_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="ATPC6_COD" ChildName="ATPC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30433" EntidadePai="TB6_INTR" EntidadeFilho="TB6_VAPC" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30434" EntidadePai="TB6_INTR" EntidadeFilho="TB6_VAPR" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30435" EntidadePai="TB6_ATPR" EntidadeFilho="TB6_HPRC" CampoFilho="ATPR6_COD" CampoPai="ATPR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ATPR6_COD" ChildName="ATPR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30436" EntidadePai="TB6_ATPC" EntidadeFilho="TB6_HPEC" CampoFilho="ATPC6_COD" CampoPai="ATPC6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="ATPC6_COD" ChildName="ATPC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30437" EntidadePai="TB6_PROC" EntidadeFilho="TB6_PCPR" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30438" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_PCPR" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30439" EntidadePai="TB6_TDAT" EntidadeFilho="TB6_ATIN" CampoFilho="TDAT6_COD" CampoPai="TDAT6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TDAT6_COD" ChildName="TDAT6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30440" EntidadePai="TB6_TDAT" EntidadeFilho="TB6_ATPC" CampoFilho="TDAT6_COD" CampoPai="TDAT6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TDAT6_COD" ChildName="TDAT6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30441" EntidadePai="TB6_TDAT" EntidadeFilho="TB6_ATPR" CampoFilho="TDAT6_COD" CampoPai="TDAT6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TDAT6_COD" ChildName="TDAT6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30442" EntidadePai="TB6_PCRM" EntidadeFilho="TB6_IMIN" CampoFilho="PCRM6_COD" CampoPai="PCRM6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PCRM6_COD" ChildName="PCRM6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30443" EntidadePai="TB6_INTR" EntidadeFilho="TB6_ACIN" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30444" EntidadePai="TB6_PJCQ" EntidadeFilho="TB6_FNCQ" CampoFilho="PJCQ6_COD" CampoPai="PJCQ6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PJCQ6_COD" ChildName="PJCQ6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30445" EntidadePai="TB6_FNCQ" EntidadeFilho="TB6_FNCQ" CampoFilho="FNCQ6_COD_PAI" CampoPai="FNCQ6_COD" Cardinality="0" Tipo="3" Frase="FILHO" FraseInversa="PAI">
      <ColunaRel ParentName="FNCQ6_COD" ChildName="FNCQ6_COD_PAI"/>
    </Relacionamento>
    <Relacionamento id="R30446" EntidadePai="TB6_FNCQ" EntidadeFilho="TB6_INTR" CampoFilho="FNCQ6_COD" CampoPai="FNCQ6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="FNCQ6_COD" ChildName="FNCQ6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30447" EntidadePai="TB6_ATIN" EntidadeFilho="TB6_VATI" CampoFilho="ATIN6_COD" CampoPai="ATIN6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATIN6_COD" ChildName="ATIN6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30448" EntidadePai="TB6_INTR" EntidadeFilho="TB6_VATI" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30449" EntidadePai="TB2_USER" EntidadeFilho="TB6_ACIN" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30450" EntidadePai="TB2_USER" EntidadeFilho="TB6_PJCQ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30451" EntidadePai="TB2_USER" EntidadeFilho="TB6_FNCQ" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30452" EntidadePai="TB2_USER" EntidadeFilho="TB6_INTR" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
    <Relacionamento id="R30453" EntidadePai="TB6_PROC" EntidadeFilho="TB6_PRIN" CampoFilho="PROC6_COD" CampoPai="PROC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="PROC6_COD" ChildName="PROC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30454" EntidadePai="TB6_INTR" EntidadeFilho="TB6_PRIN" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30455" EntidadePai="TB1_PROP" EntidadeFilho="TB6_PROC" CampoFilho="PROP1_COD" CampoPai="PROP1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="PROP1_COD" ChildName="PROP1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30456" EntidadePai="TB1_CTRT" EntidadeFilho="TB6_CTRC" CampoFilho="CTRT1_COD" CampoPai="CTRT1_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="CTRT1_COD" ChildName="CTRT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30457" EntidadePai="TB6_TDAT" EntidadeFilho="TB6_ATCC" CampoFilho="TDAT6_COD" CampoPai="TDAT6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="TDAT6_COD" ChildName="TDAT6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30458" EntidadePai="TB6_ATCC" EntidadeFilho="TB6_VACC" CampoFilho="ATCC6_COD" CampoPai="ATCC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="ATCC6_COD" ChildName="ATCC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30459" EntidadePai="TB6_CTRC" EntidadeFilho="TB6_VACC" CampoFilho="CTRC6_COD" CampoPai="CTRC6_COD" Cardinality="0" Tipo="0">
      <ColunaRel ParentName="CTRC6_COD" ChildName="CTRC6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30460" EntidadePai="TB6_INTR" EntidadeFilho="TB6_VACC" CampoFilho="INTR6_COD" CampoPai="INTR6_COD" Cardinality="0" Tipo="3">
      <ColunaRel ParentName="INTR6_COD" ChildName="INTR6_COD"/>
    </Relacionamento>
    <Relacionamento id="R30461" EntidadePai="TB1_DPFT" EntidadeFilho="TB1_CPFT" CampoFilho="DPFT1_COD" CampoPai="DPFT1_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="DPFT1_COD" ChildName="DPFT1_COD"/>
    </Relacionamento>
    <Relacionamento id="R30462" EntidadePai="TB2_USER" EntidadeFilho="TB1_CPFT" CampoFilho="USER2_COD" CampoPai="USER2_COD" Cardinality="0" Tipo="1">
      <ColunaRel ParentName="USER2_COD" ChildName="USER2_COD"/>
    </Relacionamento>
  </Relacionamentos>
</Modelo>
