<?
// set_time_limit(0);
$debug = true;
$initDir = $argv[1];
if (!$initDir) die("Favor especificar um diretório para gerar documentação sua documentação, como primeiro parâmetro.");
$initDir = realpath($initDir);
if (!$initDir) die("Não existe o diretório especificado no primeiro parâmetro.");

$docDir = $argv[2];
if (!$docDir) die("Favor espeficar o diretório de saída da documentação, como segundo parâmetro.");
$docDir = realpath($docDir);
if (!$initDir) die("Não existe o diretório especificado no segundo parâmetro.");

// Se chegou até aqui, os dirs tão OK.

$listaext = array(".inc", ".inc.php", ".php", ".php4");
$listaexttxt = implode($listaext, ", ");

echo "Gerando documentação para arquivos $listaexttxt:\nApartir do diretório $initDir (recursivamente).\nE colocar no $docDir.\n\n";

if ($arquivos = cataArquivos($initDir)) { 
	echo "done. \n\n".
	$listarq = implode($arquivos, " ");
	$comando = "cd $docDir; phpdoc -use -version -author -windowtitle MeuTitulo -docTitle MeuTituloDoc $listarq";
	if ($debug) echo "Vai executar: $comando\n";
	echo "Aguarde...\n\n";
	$output = `$comando`;
	echo "Resultado:\n$output\n";
} else {
	echo "Não encontrado nenhum arquivo com extensões $listaexttxt no diretório $initDir.\n";
}
/* Aqui vem as funções! 
 */

function cataArquivos($initDir) {
	global $debug;
	/* Primeiro cata os diretórios... vai entender... */
	$dirlist = RecurseDir($initDir);
	$dirlist[] = $initDir;
	/* Para cada diretório, cata os arquivos que "match" a listaext. */
	$files = null;
	foreach ($dirlist as $key=>$val) { 
	    if ($debug) echo "Processando ".$val."\n"; 
        $handle=opendir($val); 
        while ($fileo = readdir($handle)) { 
				$file = $val."/".$fileo;
				if ($debug) echo "Tentando: $file\n";
				if (is_file($file)) {
					if (matchExt($fileo)) {
						if ($debug) echo "Arquivo: $file\n";
						$files[] = $file;
					}
				}
        } 
        closedir($handle); 
	}
	return $files;
}

function matchExt($file) {
	global $listaext;
	foreach ($listaext as $ext) {
		if (substr_count($file, $ext) > 0) return true;
	}
	return false;
}

function RecurseDir($basedir, $AllDirectories=array()) { 
        #Create array for current directories contents 
        $ThisDir=array(); 
        #switch to the directory we wish to scan 
        chdir($basedir); 
        $current=getcwd(); 
        #open current directory for reading 
        $handle=opendir("."); 
        while ($file = readdir($handle)) { 
				echo ".";
                #Don't add special directories '..' or '.' to the list 
                if (($file!='..') & ($file!='.')) { 
                        if (is_dir($file)) { 
                                #build an array of contents for this directory 
                                array_push($ThisDir,$current.'/'.$file); 
                        } 
                } 
        } 
        closedir($handle); 
        #Loop through each directory,  run RecurseDir function on each one 
        foreach ($ThisDir as $key=>$var) { 
                array_push($AllDirectories, $var); 
                $AllDirectories=RecurseDir($var, $AllDirectories); 
        } 
        #make sure we go back to our origin 
        chdir($basedir); 
        return $AllDirectories; 
}




?>
