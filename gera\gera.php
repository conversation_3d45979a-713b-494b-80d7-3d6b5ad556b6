<?php
include_once('../codebase/function/func_geramodelos.inc');
include_once("function/function.inc");
$fileRoot=realpath('../userfiles/');
// echo $fileRoot."<br/>";
$jsonFiles = [];
$files=[];
$dirs=[];
if ($handle = opendir($fileRoot)) {
	while (false !== ($entry = readdir($handle))) {
		if ($entry == "." || $entry == "..") continue;
		if (is_dir($fileRoot.$entry)) $dirs[] = $entry; else $files[] = $entry;
	}
	closedir($handle);
}
$files=getModelFilesList();
// echo nl2br(print_r($files, true))."<br/>";
$msg='';
if (count($files)==0) $msg='Nenhum arquivo encontrado.';
$oFile=$_REQUEST['file'] ?? null;
?>
<SCRIPT language="JavaScript1.2">
function getPathSaida(campo) {
	var baseSaida='<?=str_replace('\\', '\\\\', uniformizaPath(getBaseSaida()))?>';
	var ds='/';
	if (baseSaida.indexOf(ds)==-1) ds='\\';
	document.getElementById("pathsaidaSpecified").value = baseSaida +ds + campo.value + ds;
	document.getElementById('pathSaidaSpecifiedRadio').checked=true;
}
function dirname(path){
	var ds='/';
	if (path.indexOf(ds)==-1) ds='\\';
	return path.split(ds).slice(0, -1).join(ds);
}
function basename(path){
	var ds='/';
	if (path.indexOf(ds)==-1) ds='\\';
	if (path.substr(-1)==ds) path=path.slice(0, -1);
	return path.split(ds).pop();
}
function uniformizaPath(path){
	var ds='/';
	if (path.indexOf(ds)==-1) ds='\\';
	path = path.replace(['\\', '/'], ds);
	while(path.indexOf(ds+ds) !== -1) path = path.replace(ds+ds, ds);
	if (path.substr(-1)==ds) path=path.slice(0, -1);
	return path;
}
function checkPathSaidaFile() {
	var userfile = document.getElementById("userfile").value;
	var dirUserfile = dirname(userfile);
	var saidaRadios=document.getElementsByName("pathsaida");
	// debugger;
	for (var i=0; i<saidaRadios.length; i++) {
		if (saidaRadios[i].value!='specified') {
			if (saidaRadios[i].value==dirUserfile) {
				saidaRadios[i].checked=true;
			}else if (uniformizaPath(saidaRadios[i].value)==uniformizaPath(dirname(dirUserfile))) {
				saidaRadios[i].checked=true;
			}
		}
	}
}
</SCRIPT>
<TABLE border="0" cellpadding="2" cellspacing="0">
	<form enctype="multipart/form-data" method="post" action="gogera.php">
		<input type="hidden" name="MAX_FILE_SIZE" value="10000000">
		<TR valign="middle">
			<td>Arquivo XML/JSON:
			</td>
			<td colspan="5">
<?
if (count($files)==0) {
	echo $msg;
} else {
	$dirsCandidatos=getCandidatosDirGera();
?>
	<select name='userfile' id='userfile' onchange="checkPathSaidaFile()">
<?
	foreach ($files as $file) {
		$fullPath=uniformizapath($file['dir'].DIRECTORY_SEPARATOR.$file['file']);
?>
		<option value="<?=$fullPath?>" <?=($fullPath==$oFile?'selected':'')?>><?=$fullPath?></option>
<?
	}
?>
	</select>
<?
}
?>
			</td>
			<td>
			<?=cospeHintHelp("Arquivo XML gerado pelo EmbarcXML. Upload.")?>
			</td>
		</TR>
		<TR>
			<td>Nome do Projeto:
			</td>
			<td><input  type="Text" size="20" id="projname" name="projname" value="nome_do_projeto" onchange="getPathSaida(this)" onkeypress="getPathSaida(this)" onkeyup="getPathSaida(this)">
			</td>
			<td>Sufixo:
			</td>
			<td><input  type="Text" size="20" id="sufixo" name="sufixo" value="_gt" >
			</td>
			<td>Schema:
			</td>
			<td><input  type="Text" size="20" id="schema" name="schema" value="app">
			</td>
			<td>
			<?=cospeHintHelp("Nome do projeto: será utilizado para compor o path (veja abaixo). Sufixo será usado para diferenciar o codebase. Schema é o schema do banco de dados.")?>
			</td>
		</TR>
		<TR>
			<td>Path final:
			</td>
			<td colspan="5"><input type="radio" name="pathsaida" id="pathSaidaSpecifiedRadio" value="specified" checked/><input type="Text" size="90" name="pathsaidaSpecified" id="pathsaidaSpecified" >
<?
		foreach ($dirsCandidatos as $dir) {
			echo '<br/><input type="radio" name="pathsaida" value="'.$dir.'"/> '.$dir.'';
		}
?>
			</td>
			<td valign="top">
			<?=cospeHintHelp("Path, na máquina onde está rodando o GeraClasses, onde serão colocados os resultados (classes, etc) para o XML especificado acima. Levar em consideração que esse diretório deve poder ser escrito pelo usuário que roda o Apache (nobody/nobody).")?>
			</td>
		</TR>
		<?
		if (getLinkSamba()) {
		?>
		<TR>
			<td>Acessível via:
			</td>
			<td colspan="5"><a target="_blank" href="<?=getLinkSamba()?>"><?=getLinkSamba()?></a>
			</td>
			<td>
			<?=cospeHintHelp("Good. Link para o Samba. We like.")?>
			</td>
		</TR>
		<?
		}
		?>
		
		<!--
		<TR>
			<td>Opções:
			</td>
			<td>
			<input type="Checkbox" name="targztoo" value="true" checked>Também me envie um .tar.gz com os resultados&nbsp;<?=cospeHintHelp("Dowload de um .tar.gz com as classes geradas. Feito para levar facilmente para outra máquina.")?><br>
			</td>
		</TR>
		-->
		<Tr>
			<td colspan="6" align="right"><input type="submit" style="font-weight: bold;" value="GeraClasses!">
			</td>
		</TR>
	</FORM>
</TABLE>
<SCRIPT language="JavaScript1.2">
getPathSaida(document.all.projname);
<?
if ($oFile) {
?>
checkPathSaidaFile();
<?
}
?>
</SCRIPT>
