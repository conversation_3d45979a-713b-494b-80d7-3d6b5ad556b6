<?php
include_once('../codebase/function/func_geramodelos.inc');
include('layout/init.inc');

// Get list of JSON files in userfiles directory
$fileRoot = realpath('../userfiles/');
$jsonFiles = [];

if ($handle = opendir($fileRoot)) {
    while (false !== ($entry = readdir($handle))) {
        if ($entry == "." || $entry == "..") continue;
        if (pathinfo($entry, PATHINFO_EXTENSION) == 'json') {
            $jsonFiles[] = $entry;
        }
    }
    closedir($handle);
}

// Handle form submission
$message = '';
$selectedFile = '';
$jsonContent = '';

if (isset($_POST['action']) && isset($_POST['file'])) {
    $selectedFile = $_POST['file'];
    $filePath = $fileRoot . '/' . $selectedFile;
    
    if (file_exists($filePath)) {
        $jsonContent = file_get_contents($filePath);
        $model = json_decode($jsonContent, true);
        
        if ($model === null) {
            $message = "Error: Invalid JSON in file $selectedFile";
        } else {
            switch ($_POST['action']) {
                case 'reorder_entities':
                    $model = reorderEntities($model);
                    $message = "Entities reordered alphabetically";
                    break;
                    
                case 'reorder_relationships':
                    $model = reorderRelationships($model);
                    $message = "Relationships reordered alphabetically";
                    break;
                    
                case 'reorder_all':
                    $model = reorderEntities($model);
                    $model = reorderRelationships($model);
                    $message = "All entities and relationships reordered alphabetically";
                    break;
            }
            
            // Save the modified JSON back to the file
            if ($message) {
                $jsonContent = json_encode($model, JSON_PRETTY_PRINT);
                file_put_contents($filePath, $jsonContent);
            }
        }
    } else {
        $message = "Error: File not found";
    }
}

// Function to reorder entities alphabetically
function reorderEntities($model) {
    // Check if we have the new format with 'entities' property
    if (isset($model['entities'])) {
        $entities = $model['entities'];
        ksort($entities);
        $model['entities'] = $entities;
    } else {
        // Old format where the model itself is the entities object
        ksort($model);
    }
    return $model;
}

// Function to reorder relationships alphabetically
function reorderRelationships($model) {
    // Process entities to reorder their columns and constraints
    $entities = isset($model['entities']) ? $model['entities'] : $model;
    
    foreach ($entities as $tableName => &$table) {
        // Reorder columns
        if (isset($table['columns'])) {
            ksort($table['columns']);
        }
        
        // Reorder constraints
        if (isset($table['constraints'])) {
            ksort($table['constraints']);
        }
        
        // Reorder parents (foreign keys)
        if (isset($table['parents'])) {
            ksort($table['parents']);
        }
    }
    
    // Update the model
    if (isset($model['entities'])) {
        $model['entities'] = $entities;
    } else {
        $model = $entities;
    }
    
    return $model;
}
?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1>JSON Model Maintenance</h1>
            <?php if ($message): ?>
                <div class="alert alert-info"><?php echo $message; ?></div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Select JSON File</h3>
                </div>
                <div class="panel-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="file">JSON File:</label>
                            <select name="file" id="file" class="form-control">
                                <?php foreach ($jsonFiles as $file): ?>
                                    <option value="<?php echo htmlspecialchars($file); ?>" <?php echo ($selectedFile == $file) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($file); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Maintenance Actions:</label>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="action" value="reorder_entities" checked>
                                    Reorder Entities Alphabetically
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="action" value="reorder_relationships">
                                    Reorder Relationships Alphabetically
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="action" value="reorder_all">
                                    Reorder All (Entities and Relationships)
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Apply</button>
                        <a href="/modela/modelo.php" class="btn btn-default">Open Model Viewer</a>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <?php if ($selectedFile && $jsonContent): ?>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">File Preview</h3>
                    </div>
                    <div class="panel-body">
                        <p><strong>File:</strong> <?php echo htmlspecialchars($selectedFile); ?></p>
                        <p><strong>Size:</strong> <?php echo number_format(strlen($jsonContent) / 1024, 2); ?> KB</p>
                        <p><strong>Last Modified:</strong> <?php echo date("Y-m-d H:i:s", filemtime($fileRoot . '/' . $selectedFile)); ?></p>
                        
                        <div class="form-group">
                            <label for="jsonPreview">JSON Preview (first 1000 characters):</label>
                            <textarea id="jsonPreview" class="form-control" rows="10" readonly><?php echo htmlspecialchars(substr($jsonContent, 0, 1000)); ?></textarea>
                        </div>
                        
                        <?php if (strlen($jsonContent) > 1000): ?>
                            <p class="text-muted">... (truncated)</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include('layout/end.inc'); ?>