<?

class JD {
	var $desc; // A descrição do JavaDoc.
	var $returns; // <PERSON>or de retorno, para um método.
	var $private=null; // Private?
	var $estatico=null; // Estico?
	var $type=null; // Tipo
	var $author=null; //autor?
	var $sees = array();// array de sees...
	var $params = array(); // array de params...
	var $publico=null; // publico?
	
	function get($indent=0) {
		global $lf;
		//if (!$this->autor) $this->autor = geraClassesConfigAutor();
		$s  = "/**".$lf."";
		$desclinhas2 = wordwrap($this->desc, 1120);
		$desclinhas = explode("".$lf."", $desclinhas2);
		foreach ($desclinhas as $linha) {
			$s .= " * ".$linha."".$lf."";
		}
		if (isset($this->returns)) $s .= " * @returns ".$this->returns."".$lf."";
		if (isset($this->private)) $s .= " * @private".$lf."";
		if (isset($this->publico)) $s .= " * @public".$lf."";
		if (isset($this->type)) $s .= " * @var ". str_replace(" ", "_", $this->type)."".$lf."";
		if (isset($this->estatico)) $s .= " * @static".$lf."";
		if (isset($this->autor)) $s .= " * <AUTHOR>
		foreach ($this->sees as $see) {
			$s .= " * @see ".$see."".$lf."";
		}
		foreach ($this->params as $param) {
			$s .= " * @param ".$param."".$lf."";
		}
		$s .= " */";
		if ($indent>0) {
			$linhas = explode("".$lf."", $s);
			$ret = null;
			foreach ($linhas as $linha) {
				$ret[] = str_repeat("\t", $indent).$linha;
			}
			$s = implode("".$lf."", $ret);
		}
		
		return $s."".$lf."";
	}
	
	function addSee($see) {
		$this->sees[] = $see;
	}
	
	function addParam($nomeparam, $descparam) {
		$nomeparam = "\$".$nomeparam;
		if (strlen($nomeparam) < 10) { $pad = str_repeat(" ", 10-strlen($nomeparam)); } else {$pad = " ";}
		$this->params[] = $nomeparam . $pad.$descparam;
	}
}

?>