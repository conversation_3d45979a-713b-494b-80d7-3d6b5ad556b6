{"entities": {"TABELA FÍSICO": {"logical": "TABELA LÓGICO", "columns": {"COLUNA FÍSICO": {"logical": "COLUNA LÓGICO", "physical": "COLUNA FÍSICO", "type": "TIPO DE DADOS", "pk": true, "sequence": true, "nullable": false, "size": 10, "fkrefs": ["FK FÍSICO"], "default": "VALOR DEFAULT (opcional)"}}, "constraints": {"CONSTRAINT FÍSICO (formato: [TABLE]_[ordered list of columns separated by underscore]_UK)": {"type": "unique", "columns": ["COLUNA FÍSICO"]}}, "indexes": {"INDICE FÍSICO (formato: [TABLE]_[ordered list of columns separated by underscore]_IX)": {"columns": ["COLUNA FÍSICO"]}}, "fkrefs": ["FK FÍSICO"], "position": [300, 9700]}}, "relationships": {"FK FÍSICO (formato: [sourceTable]_[targetTable]_[ordered list of target columns separated by underscore]_FK)": {"sourceTable": "TABELA REFERENCIADA FÍSICO", "targetTable": "ESTA TABELA FÍSICO", "columns": [{"parent": "COLUNA FÍSICO DA REFERENCIADA", "child": "COLUNA FÍSICO NESTA TABELA"}], "onDelete": "[on delete action - optional]", "onUpdate": "[on update action - optional]", "cardinality": "[number]", "reltype": "[number]", "phrase": "FRASE", "inverse": "INVERSO"}}}