["carreserva_new_gid_seq", "cnefeibge_ogc_fid_seq", "dnpm_gid_seq1", "el_aerog_gid_seq", "el_reginterf_gid_seq", "gdrive_files_file_cod_seq", "gdrive_folders_folder_cod_seq", "incra_sigef_uk_sigef_cod_seq", "incra_sigef_agg_sigef_agg_cod_seq", "table_logs_table_logs_id_seq", "trajetos_ogc_fid_seq", "tb1_prio_teste_prio1_cod_seq", "tb_bi_prop2aero2r2map_prop2aero2r2map_cod_seq", "tb_dataroom_files_cod_seq", "tb_dataroom_files_logs_dtrm_file_cod_seq", "tb_dataroom_folders_pasta_cod_seq", "tb_dataroom_pastas_projetos_projeto_dtr_cod_seq", "69227_con_gid_seq", "con69227_gid_seq", "tb1_crvt_crvt1_cod_seq", "tb1_etcr_etcr1_cod_seq", "tb1_spcr_spcr1_cod_seq", "tb1_sepc_sepc1_cod_seq", "tb1_secr_secr1_cod_seq", "tb1_crse_crse1_cod_seq", "tb1_epcr_epcr1_cod_seq", "tb1_oscv_oscv1_cod_seq", "tb1_aecr_aecr1_cod_seq", "tb1_aepc_aepc1_cod_seq", "tb1_sprc_sprc1_cod_seq", "tb1_stcv_stcv1_cod_seq", "tb1_spcv_spcv1_cod_seq", "tb1_ancv_ancv1_cod_seq", "tb1_anpc_anpc1_cod_seq", "tb1_athe_athe1_cod_seq", "tb1_cocv_cocv1_cod_seq", "tb1_ethe_ethe1_cod_seq", "tb1_hemp_hemp1_cod_seq", "tb1_anha_anha1_cod_seq", "tb1_cohe_cohe1_cod_seq", "tb1_hipo_hipo1_cod_seq", "assentamentos_gid_seq", "carimovel_new_gid_seq1", "tb1_proh_proh1_cod_seq"]