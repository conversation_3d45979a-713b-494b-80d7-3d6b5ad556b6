<?php
include_once('../codebase/function/func_geramodelos.inc');
include('layout/init.inc');

// Get list of JSON files in userfiles directory
$fileRoot = realpath('../userfiles/');
$jsonFiles = [];

if ($handle = opendir($fileRoot)) {
    while (false !== ($entry = readdir($handle))) {
        if ($entry == "." || $entry == "..") continue;
        if (pathinfo($entry, PATHINFO_EXTENSION) == 'json') {
            $jsonFiles[] = $entry;
        }
    }
    closedir($handle);
}
?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1>Data Model Viewer</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Available Models</h3>
                </div>
                <div class="panel-body">
                    <div class="list-group">
                        <?php foreach ($jsonFiles as $file): ?>
                            <a href="modelo.php?file=<?php echo urlencode('../userfiles/' . $file); ?>" class="list-group-item">
                                <h4 class="list-group-item-heading"><?php echo htmlspecialchars($file); ?></h4>
                                <p class="list-group-item-text">
                                    Last modified: <?php echo date("Y-m-d H:i:s", filemtime($fileRoot . '/' . $file)); ?>
                                </p>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Tools</h3>
                </div>
                <div class="panel-body">
                    <div class="list-group">
                        <a href="json_maintenance.php" class="list-group-item">
                            <h4 class="list-group-item-heading">JSON Maintenance</h4>
                            <p class="list-group-item-text">Reorder entities and relationships, manage JSON model files</p>
                        </a>
                        
                        <a href="../compara/index.php" class="list-group-item">
                            <h4 class="list-group-item-heading">Compare Models</h4>
                            <p class="list-group-item-text">Compare model with database or another model</p>
                        </a>
                        
                        <a href="../gera/index.php" class="list-group-item">
                            <h4 class="list-group-item-heading">Generate Code</h4>
                            <p class="list-group-item-text">Generate code from model</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?
include('layout/end.inc');
?>
