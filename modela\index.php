<?php
include_once('../codebase/function/func_geramodelos.inc');
include('layout/init.inc');

// Get list of JSON files in userfiles directory
$fileRoot = realpath('../userfiles/');
$jsonFiles = [];

if ($handle = opendir($fileRoot)) {
	while (false !== ($entry = readdir($handle))) {
		if ($entry == "." || $entry == "..") continue;
		if (pathinfo($entry, PATHINFO_EXTENSION) == 'json' && substr($entry, -9) != '.pos.json') {
			$jsonFiles[$fileRoot.DIRECTORY_SEPARATOR.$entry] = ['file' => $entry, 'dir'=>$fileRoot];
		}
	}
	closedir($handle);
}

foreach ($_pathModelosPlus as $path) {
	if ($handle = opendir($path)) {
		while (false !== ($entry = readdir($handle))) {
			if ($entry == "." || $entry == "..") continue;
			if (pathinfo($entry, PATHINFO_EXTENSION) == 'json' && substr($entry, -9) != '.pos.json') {
				$jsonFiles[$path.DIRECTORY_SEPARATOR.$entry] = ['file' => $entry, 'dir'=>$path];
			}
		}
		closedir($handle);
	}
}
$jsonFiles=getModelFilesList();
?>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<a href="/index.php"><i class="fa fa-home"></i> Home GeraModelos</a>
			<h1>Data Model Editor</h1>
		</div>
	</div>
	
	<div class="row">
		<div class="col-md-6">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">Available Models</h3>
				</div>
				<div class="panel-body">
					<div class="list-group">
						<?php foreach ($jsonFiles as $fullPath=>$def){
							$file=$def['file'];
							$dir=$def['dir'];
							if (substr($dir, 0, 1)=='.') $dir=realpath($dir);
							$data=filemtime($fullPath);
							?>
							<a href="modelo.php?file=<?php echo urlencode($fullPath); ?>" class="list-group-item">
								<h4 class="list-group-item-heading"><?php echo htmlspecialchars($file); ?></h4>
								<p class="list-group-item-text">
									Last modified: <?= '<b>'.getDecorrido($data).'</b> ('.date("Y-m-d H:i:s", $data).')' ?><br/>
									Directory: <b><?=htmlspecialchars($dir); ?></b>
								</p>
							</a>
						<?php } ?>
					</div>
				</div>
			</div>
		</div>
		
		<div class="col-md-6">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">Tools</h3>
				</div>
				<div class="panel-body">
					<div class="list-group">
						<a href="json_maintenance.php" class="list-group-item">
							<h4 class="list-group-item-heading">JSON Maintenance</h4>
							<p class="list-group-item-text">Reorder entities and relationships, manage JSON model files</p>
						</a>
						
						<a href="convert.php" class="list-group-item">
							<h4 class="list-group-item-heading">File conversion</h4>
							<p class="list-group-item-text">Convert XML to JSON and vice versa</p>
						</a>
						
						<a href="../compara/index.php" class="list-group-item">
							<h4 class="list-group-item-heading">Compare Models</h4>
							<p class="list-group-item-text">Compare model with database or another model</p>
						</a>
						
						<a href="../gera/index.php" class="list-group-item">
							<h4 class="list-group-item-heading">Generate Code</h4>
							<p class="list-group-item-text">Generate code from model</p>
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<?
include('layout/end.inc');
function getDecorrido($dat){
	$dif=time()-$dat;
	$decorrido='';
	if ($dif<60){
		$decorrido=$dif.' segundos';
	}else if ($dif<3600){
		$decorrido=round($dif/60).' minutos';
	}else if ($dif<86400){
		$decorrido=round($dif/3600).' horas';
		if ($decorrido=='1 horas') $decorrido='1 hora';
	}else{
		$diasBruto=($dif/86400);
		$dias=floor($diasBruto);
		$horasBruto=($diasBruto-$dias)*24;
		$horas=floor($horasBruto);
		$minutosBruto=($horasBruto-$horas)*60;
		$minutos=floor($minutosBruto);
		$decorrido=$dias.' dias';
		if ($horas>0) $decorrido.=' '.$horas.' horas';
		// if ($minutos>0) $decorrido.=' '.$minutos.' minutos';
		$decorrido=floor($dif/86400).' dias';
		if ($decorrido=='1 dias') $decorrido='1 dia';
	}
	return $decorrido;

}
?>
