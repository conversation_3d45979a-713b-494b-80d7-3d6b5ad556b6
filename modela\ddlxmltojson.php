<?php
// <PERSON>ript to parse outputddl.xml and convert to JSON format for modelo.js
// header('Content-Type: text/plain; charset=utf-8');

// Load the XML file
$xmlFile = 'outputddl.xml';
$outputFile = 'fullmodelo.js';

if (!file_exists($xmlFile)) {
    die("Error: XML file not found: $xmlFile");
}

$xml = simplexml_load_file($xmlFile);
if ($xml === false) {
    die("Error: Failed to parse XML file");
}

// Initialize the output array
$tbDefs = [];

// Process each entity in the XML
foreach ($xml->Entidades->Entidade as $entidade) {
    $tableName = (string)$entidade['NomeFisico'];
    $tableLogical = (string)$entidade['NomeLogico'];
    
    // Initialize table definition
    $tbDefs[$tableName] = [
        'logical' => $tableLogical,
        'columns' => [],
        'position' => generateRandomPosition()
    ];
    
    // Process each column
    if (isset($entidade->Campos)) {
        foreach ($entidade->Campos->Campo as $campo) {
            $columnName = (string)$campo['NomeFisico'];
            $columnLogical = (string)$campo['NomeLogico'];
            $dataType = strtolower((string)$campo['DataType']);
            
            // Initialize column definition
            $columnDef = [
                'logical' => $columnLogical,
                'physical' => $columnName,
                'type' => mapDataType($dataType),
                'pk' => false,
                'sequence' => false,
                'nullable' => !(isset($campo['NotNull']) && (string)$campo['NotNull'] === 'True')
            ];
            
            // Add size for varchar types
            if (isset($campo['DataLength'])) {
                $columnDef['size'] = (int)$campo['DataLength'];
            }
            
            // Check if it's a primary key and/or identity (sequence)
            if (isset($campo['Identity']) && (string)$campo['Identity'] === 'True') {
                $columnDef['sequence'] = true;
            }
            
            // Add SRID and dimension for geometry types
            if (isset($campo['SRID'])) {
                $columnDef['srid'] = (string)$campo['SRID'];
            }
            
            if (isset($campo['GeomDimension'])) {
                $columnDef['dimension'] = (string)$campo['GeomDimension'];
            }
            
            // Add column to table definition
            $tbDefs[$tableName]['columns'][$columnName] = $columnDef;
        }
    }
    
    // Process constraints to identify primary keys
    if (isset($entidade->Constraints)) {
        foreach ($entidade->Constraints->ConstraintPK as $constraint) {
            if ((string)$constraint['Tipo'] === 'PK') {
                foreach ($constraint->Coluna as $coluna) {
                    $colName = (string)$coluna['Nome'];
                    if (isset($tbDefs[$tableName]['columns'][$colName])) {
                        $tbDefs[$tableName]['columns'][$colName]['pk'] = true;
                    }
                }
            }
        }
    }
}

// Process relationships to add foreign keys
if (isset($xml->Relacionamentos)) {
    foreach ($xml->Relacionamentos->Relacionamento as $relacionamento) {
        $parentTable = (string)$relacionamento['ParentName'];
        $childTable = (string)$relacionamento['ChildName'];
        
        // Process each column relationship
        foreach ($relacionamento->ColunaRel as $colunaRel) {
            $parentColumn = (string)$colunaRel['ParentName'];
            $childColumn = (string)$colunaRel['ChildName'];
            
            // Add foreign key information to the child column
            if (isset($tbDefs[$childTable]) && isset($tbDefs[$childTable]['columns'][$childColumn])) {
                $tbDefs[$childTable]['columns'][$childColumn]['fk'] = [
                    'table' => $parentTable,
                    'column' => $parentColumn
                ];
                
                // Add phrase and inverse phrase if available
                if (isset($relacionamento['Frase'])) {
                    $tbDefs[$childTable]['columns'][$childColumn]['phrase'] = (string)$relacionamento['Frase'];
                }
                
                if (isset($relacionamento['FraseInversa'])) {
                    $tbDefs[$childTable]['columns'][$childColumn]['inverse'] = (string)$relacionamento['FraseInversa'];
                }
            }
        }
    }
}

// Convert to JavaScript format
$jsOutput = "var tbDefs=" . json_encode($tbDefs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

if (false){
	// Format the output to match the style in defmodelo.js
	$jsOutput = str_replace('"logical":', "\n\t\t\t\"logical\":", $jsOutput);
	$jsOutput = str_replace('"physical":', "\n\t\t\t\"physical\":", $jsOutput);
	$jsOutput = str_replace('"type":', "\n\t\t\t\"type\":", $jsOutput);
	$jsOutput = str_replace('"pk":', "\n\t\t\t\"pk\":", $jsOutput);
	$jsOutput = str_replace('"sequence":', "\n\t\t\t\"sequence\":", $jsOutput);
	$jsOutput = str_replace('"nullable":', "\n\t\t\t\"nullable\":", $jsOutput);
	$jsOutput = str_replace('"size":', "\n\t\t\t\"size\":", $jsOutput);
	$jsOutput = str_replace('"srid":', "\n\t\t\t\"srid\":", $jsOutput);
	$jsOutput = str_replace('"dimension":', "\n\t\t\t\"dimension\":", $jsOutput);
	$jsOutput = str_replace('"fk":', "\n\t\t\t\"fk\":", $jsOutput);
	$jsOutput = str_replace('"table":', "\n\t\t\t\t\"table\":", $jsOutput);
	$jsOutput = str_replace('"column":', "\n\t\t\t\t\"column\":", $jsOutput);
	$jsOutput = str_replace('"phrase":', "\n\t\t\t\"phrase\":", $jsOutput);
	$jsOutput = str_replace('"inverse":', "\n\t\t\t\"inverse\":", $jsOutput);
	$jsOutput = str_replace('"columns":', "\n\t\t\"columns\":{", $jsOutput);
	$jsOutput = str_replace('"position":', "\n\t\t},\"position\":", $jsOutput);
	$jsOutput = str_replace('},', "\n\t},", $jsOutput);
	$jsOutput = str_replace('}}', "\n\t}\n}", $jsOutput);
	$jsOutput = str_replace('"{', "{\n\t\t", $jsOutput);
	$jsOutput = str_replace('}"', "\n\t}", $jsOutput);
}
// Write to file
if (file_put_contents($outputFile, $jsOutput)) {
    echo "Successfully converted XML to JSON and saved to $outputFile";
} else {
    echo "Error: Failed to write to output file";
}

// Helper function to map XML data types to JS model data types
function mapDataType($xmlType) {
    $typeMap = [
        'integer' => 'integer',
        'varchar' => 'varchar',
        'text' => 'text',
        'date' => 'date',
        'datetime' => 'datetime',
        'bit' => 'bit',
        'float' => 'float',
        'polygon' => 'polygon',
        'multipolygon' => 'multipolygon',
        'multipolygonz' => 'multipolygonz',
        'geometry' => 'geometry',
        'pointz' => 'pointz',
        'char' => 'char',
        'nvarchar' => 'varchar'
    ];
    
    return isset($typeMap[strtolower($xmlType)]) ? $typeMap[strtolower($xmlType)] : $xmlType;
}

// Helper function to generate random positions for tables
function generateRandomPosition() {
    // Generate positions in a grid-like pattern
    static $x = 100;
    static $y = 100;
    static $col = 0;
    
    $pos = [$x, $y];
    
    // Move to next position
    $col++;
    if ($col > 4) {
        $col = 0;
        $y += 200;
        $x = 100;
    } else {
        $x += 200;
    }
    
    return $pos;
}
?>
