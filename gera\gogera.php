<?php
include_once('../codebase/function/func_geramodelos.inc');
include("function/function.inc");
include('init.inc');
?>
<div class="container" style="margin-bottom:99px;padding:20px;">
<?
$lf="\r\n";
$tipo='xml';
if (isset($_REQUEST['userfile']) && $arq=$_REQUEST['userfile']) {
	$arq = realpath($arq);
	if (!file_exists($arq)) die("Arquivo não encontrado: $arq");
	$ext=pathinfo($arq, PATHINFO_EXTENSION);
	switch($ext){
		case 'xml':
			$tipo='xml';
			if (!$doc = domxml_open_file($arq)) {
				die("Erro parseando XML ($arq)! Tem certeza que o que vc mandou é um XML?");
			}
			break;
		case 'json':
			$tipo='json';
			$json=trim(file_get_contents($arq, true));
			if (substr($json, 0, 1)!='{') {
				die("Erro parseando JSON ($arq)! Tem certeza que o que vc mandou é um JSON?");
			}
			$jsonViaXml=false;
			if ($jsonViaXml){
				$doc=generateXmlFromJSON($arq);
				$doc->dump_file(basename($arq).'.xml', false, true);
			}else{
				$doc=json_decode($json, true);
				if (!$doc) {
					die("Erro parseando JSON ($arq)! Tem certeza que o que vc mandou é um JSON?");
				}
			}
			break;
		case 'sql':
		case 'ddl':
			$tipo='ddl';
			$doc=generateXmlFromDDL($arq);
			break;
		default:
			die("Tipo de arquivo não suportado: $ext");
			break;
	}
}else{
	die("Erro ao tratar upload/arquivo");
}


// exit;
// Ok. Aqui temos:
// $doc = é o documento XML gerado/enviado.
// $projname = proj name, my friend.
// $targztoo = é para targziar?

if (false && !is_writable(getBaseSaida() )) {
	?>
	Infelizmente é impossível continuar.<br>
	O diretório <TT><?=getBaseSaida()?></TT> não oferece permissão de gravação ao webserver.<br>
	Por favor, logue-se como root na máquina, e execute um
	<code>chmod 777 <?=getBaseSaida()?></code> e tente novamente.<br>
	<?
	die();
}
$dirDoc=dirname($arq);
$paiDirDoc=basename($dirDoc);
// echo $dirDoc." dirDoc<br/>";
// echo $paiDirDoc." paiDirDoc<br/>";
$projname = $_REQUEST["projname"];
$pathsaida = $_REQUEST["pathsaida"];
if ($pathsaida=='specified') {
	$pathsaida = $_REQUEST["pathsaidaSpecified"];
}
$pathsaida = uniformizaPath($pathsaida);
// echo $pathsaida." orig<br/>";
$paiSaida=dirname($pathsaida);
if (file_exists($paiSaida)) {
	$pathsaidaProj = $paiSaida.DIRECTORY_SEPARATOR.$projname;
}
// echo file_exists($pathsaida)."<br/>";
// echo dirname($pathsaida)."<br/>";
// echo $pathsaida."<br/>";
// exit;
$sufixo='';
$namespace='';
$sufixo = $_REQUEST["sufixo"];
$schema = $_REQUEST["schema"];
$pathsaida = criaDiretorio($pathsaida);
// echo $pathsaida;
$pathsaida = realpath($pathsaida).DIRECTORY_SEPARATOR;
if ( ($pathsaida != getBaseSaida()."/")  ) {
	if ($pathsaida) {
		flush();
?>
<div id="infosGera">
	<h3>Gerando classes para o projeto <TT><?=$projname?></TT></h3>
	Arquivo: <TT><?=($arq)?></TT><br>
	Destino: <TT><?=realpath($pathsaida)?></TT><br>
	Sufixo: <TT><?=($sufixo)?></TT><br>
	Schema: <TT><?=($schema)?></TT><br>
</div>
<?
		// cospeProg();
		echo "Gerando... aguarde....".str_repeat(" ", 400)."<BR><UL>";
		flush();
		geraClassesInicial($doc, $pathsaida,['utf8'=>true, 'lf'=>"\r\n",'sufixo'=>$sufixo,'namespace'=>$namespace,'schema'=>$schema]);
		flush();
		// endProg();
		echo "</UL><BR>Gerado OK. Site montado em: <TT>$pathsaida</TT><BR>";
		if (false && getLinkSamba()) {
			echo "Acessível via: <a target='_blank' href='".getLinkSamba()."\\$projname"."'>".getLinkSamba()."\\$projname"."</a>";
		}
		if (true) {
			$pathtgz = realpath($pathsaida."../");
			$cd = $pathtgz;
			$linha = "cd $cd; chmod -R ugo+rw $projname;";
			$result = `$linha`;
		}
		
		global $listaArqs;
		if (count($listaArqs)>0) {
?>
		<div id="listaArquivosGerados">
			<h4>Arquivos gerados (<?=count($listaArqs)?>) <a href="javascript:void(0)" onclick="document.getElementById('listaArquivosGeradosUL').style.display=(document.getElementById('listaArquivosGeradosUL').style.display=='none'?'block':'none');">exibir/ocultar</a></h4>
			<ul id="listaArquivosGeradosUL" style="display:none;">
<?
			foreach ($listaArqs as $arq) {
				?><LI><?=$arq?></LI><?
			}
?>
			</ul>
		</div>
<?
		}
		if (@$targztoo) {
			$pathtgz = realpath($pathsaida."../");
			$nomegz = $projname.".tar.gz";
			$compgz = $pathtgz."/".$nomegz;
			$cd = $pathtgz;
			$linha = "cd $cd; tar czvf $nomegz $projname;";
			$result = `$linha`;
			?><IFRAME width="0" height="0" src="/gettgz.php?projname=<?=$projname?>"></IFRAME><?
		}
	}
} else {
	die("Erro com o path.");
}
?>

</div>
<?
include('end.inc');
function cospeProg() {
	?><?="\n"?><object classid='CLSID:35053A22-8589-11D1-B16A-00C0F0283628' width='100%' height='22' name='progbar' id='progbar' style='top: 0px; left: 0px;'></object><br><?="\n"?><?
	echo "\n".str_repeat(" ", 1250)."\n"; flush();
}

function initProgresso($max) {
	?>
	<script language="VBScript">
			on error resume next
			progbar.scrolling = 1
			progbar.min = 0
			progbar.max = <?=$max."\n"?>
	</script>
	<?
	echo str_repeat(" ", 1025)."\n"; flush();
}

function endProg() {

}


function incrProg($atual) {
	?>
	<script language="VBScript">
		progbar.value = <?=$atual?>
	</script>
	<?
	echo str_repeat(" ", 1025);
	flush();
}

?>
