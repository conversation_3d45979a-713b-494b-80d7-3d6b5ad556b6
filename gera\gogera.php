<?php
include_once('../codebase/function/func_geramodelos.inc');
include("function/function.inc");
?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
	<title>Vazio...</title>
	<link rel="stylesheet" type="text/css" href="/style/style.css">
</head>
<body bgcolor="whitesmoke">
<?
$lf="\r\n";
$tipo='xml';
if (isset($_FILES['userfile']) && is_uploaded_file($_FILES['userfile']['tmp_name'])) {
	$arq = $_FILES['userfile']['tmp_name'];
	if (!($_FILES['userfile']['type'] == "text/xml" || $_FILES['userfile']['type'] == "appliccation/json")) {
		die("Tipo do arquivo enviado não é text/xml ou json: ". $_FILES['userfile']['type'] );
	}
	if (!$doc = domxml_open_file($arq)) {
		die("Erro parseando XML ($arq)! Tem certeza que o que vc mandou é um XML?");
	}
} else if (isset($_REQUEST['userfile']) && $arq=$_REQUEST['userfile']) {
	$arq = realpath('../userfiles/'.$arq);
	if (!file_exists($arq)) die("Arquivo não encontrado: $arq");
	$ext=pathinfo($arq, PATHINFO_EXTENSION);
	switch($ext){
		case 'xml':
			$tipo='xml';
			if (!$doc = domxml_open_file($arq)) {
				die("Erro parseando XML ($arq)! Tem certeza que o que vc mandou é um XML?");
			}
			break;
		case 'json':
			$tipo='json';
			$json=trim(file_get_contents($arq, true));
			if (substr($json, 0, 1)!='{') {
				die("Erro parseando JSON ($arq)! Tem certeza que o que vc mandou é um JSON?");
			}
			$doc=generateXmlFromJSON($arq);
			$doc->dump_file(basename($arq).'.xml', false, true);
			break;
		case 'sql':
		case 'ddl':
			$tipo='ddl';
			$doc=generateXmlFromDDL($arq);
			break;
		default:
			die("Tipo de arquivo não suportado: $ext");
			break;
	}
}else{
	echo "Erro ao tratar upload/arquivo";
	die();
}


// exit;
// Ok. Aqui temos:
// $doc = é o documento XML gerado/enviado.
// $projname = proj name, my friend.
// $targztoo = é para targziar?

if (!is_writable(getBaseSaida() )) {
	?>
	Infelizmente é impossível continuar.<br>
	O diretório <TT><?=getBaseSaida()?></TT> não oferece permissão de gravação ao webserver.<br>
	Por favor, logue-se como root na máquina, e execute um
	<code>chmod 777 <?=getBaseSaida()?></code> e tente novamente.<br>
	<?
	die();
}
$pathsaida = criaDiretorio(getBaseSaida() ."/".$_REQUEST["projname"] . "/");
echo $pathsaida;
$sufixo='';
$namespace='';
$pathsaida = realpath($pathsaida)."/";
if ( ($pathsaida != getBaseSaida()."/")  ) {
	if ($pathsaida) {
		flush();
		cospeProg();
		echo "Gerando... aguarde....".str_repeat(" ", 400)."<BR><UL>";
		flush();
		geraClassesInicial($doc, $pathsaida,['utf8'=>true, 'lf'=>'\r\n','sufixo'=>$sufixo,'namespace'=>$namespace]);
		flush();
		endProg();
		echo "</UL><BR>Gerado OK. Site montado em: <TT>$pathsaida</TT><BR>";
		if (getLinkSamba()) {
			echo "Acessível via: <a target='_blank' href='".getLinkSamba()."\\$projname"."'>".getLinkSamba()."\\$projname"."</a>";
		}
		if (true) {
			$pathtgz = realpath($pathsaida."../");
			$cd = $pathtgz;
			$linha = "cd $cd; chmod -R ugo+rw $projname;";
			$result = `$linha`;
		}
		
		global $listaArqs;
		foreach ($listaArqs as $arq) {
			?><LI><?=$arq?></LI><?
		}
		
		if ($targztoo) {
			$pathtgz = realpath($pathsaida."../");
			$nomegz = $projname.".tar.gz";
			$compgz = $pathtgz."/".$nomegz;
			$cd = $pathtgz;
			$linha = "cd $cd; tar czvf $nomegz $projname;";
			$result = `$linha`;
			?><IFRAME width="0" height="0" src="/gettgz.php?projname=<?=$projname?>"></IFRAME><?
		}
	}
} else {
	die("Erro com o path.");
}

function cospeProg() {
	?><?="\n"?><object classid='CLSID:35053A22-8589-11D1-B16A-00C0F0283628' width='100%' height='22' name='progbar' id='progbar' style='top: 0px; left: 0px;'></object><br><?="\n"?><?
	echo "\n".str_repeat(" ", 1250)."\n"; flush();
}

function initProgresso($max) {
	?>
	<script language="VBScript">
			on error resume next
			progbar.scrolling = 1
			progbar.min = 0
			progbar.max = <?=$max."\n"?>
	</script>
	<?
	echo str_repeat(" ", 1025)."\n"; flush();
}

function endProg() {

}


function incrProg($atual) {
	?>
	<script language="VBScript">
		progbar.value = <?=$atual?>
	</script>
	<?
	echo str_repeat(" ", 1025);
	flush();
}

?>
</body>
</html>
