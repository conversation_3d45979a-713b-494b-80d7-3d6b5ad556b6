<?php
$_configModeloFile='fullmodelo.js';
$_configModeloPositions='positions.json';
$_configRelCardinality=array(
	"mandatory"=>array(
		'0' => '1 para 0 ou N',
		'1' => '1 para 1 ou N',
		'2' => '1 para 0 ou 1',
		'3' => '1 para N'
	),
	"optional"=>array(
		'0' => '0 para 0 ou N',
		'1' => '0 para 1 ou N',
		'2' => '0 para 0 ou 1',
		'3' => '0 para N'
	)
);
$_configRelTypes_en=array(
	'0' => 'Identifying',
	'1' => 'Non Identifying',
	'2' => 'Non specific',
	'3' => 'Non Identifying, optional'
);
$_configRelTypes_pt=array(
	'0' => 'Incluir na PK',
	'1' => 'Não incluir na PK, obrigatória',
	'2' => 'Não especificada',
	'3' => 'Não incluir na PK, opcional'
);
$_configRelTypes_sql=array(
	'0' => 'PK, not null',
	'1' => 'Not PK, not null',
	'2' => 'Not specified',
	'3' => 'Not PK, nullable'
);
$_configModeloTypes=array(
	// Numeric types
	'SMALLINT' => [],
	'INTEGER' => [],
	'BIGINT' => [],
	'DECIMAL' => ['precision' => 'integer', 'scale' => 'integer'],
	'NUMERIC' => ['precision' => 'integer', 'scale' => 'integer'],
	'REAL' => [],
	'FLOAT4' => [],
	'DOUBLE PRECISION' => [],
	'FLOAT8' => [],
	'SERIAL' => [],
	'SERIAL4' => [],
	'BIGSERIAL' => [],
	'SERIAL8' => [],
	'SMALLSERIAL' => [],
	'SERIAL2' => [],
	
	// Character types
	'CHARACTER' => ['size' => 'integer'],
	'CHAR' => ['size' => 'integer'],
	'CHARACTER VARYING' => ['size' => 'integer'],
	'VARCHAR' => ['size' => 'integer'],
	'TEXT' => [],
	
	// Date/Time types
	'DATE' => [],
	'TIME' => ['precision' => 'integer'],
	'TIME WITHOUT TIME ZONE' => ['precision' => 'integer'],
	'TIME WITH TIME ZONE' => ['precision' => 'integer'],
	'TIMETZ' => ['precision' => 'integer'],
	'TIMESTAMP' => ['precision' => 'integer'],
	'TIMESTAMP WITHOUT TIME ZONE' => ['precision' => 'integer'],
	'TIMESTAMP WITH TIME ZONE' => ['precision' => 'integer'],
	'TIMESTAMPTZ' => ['precision' => 'integer'],
	'INTERVAL' => ['fields' => 'string', 'precision' => 'integer'],
	
	// Boolean type
	'BOOLEAN' => [],
	'BOOL' => [],
	
	// Binary data
	'BYTEA' => [],
	
	// Network address types
	'CIDR' => [],
	'INET' => [],
	'MACADDR' => [],
	'MACADDR8' => [],
	
	// JSON types
	'JSON' => [],
	'JSONB' => [],
	
	// UUID type
	'UUID' => [],
	
	// XML type
	'XML' => [],
	
	// Money type
	'MONEY' => [],
	
	// Bit string types
	'BIT' => [],
	'BIT VARYING' => ['size' => 'integer'],
	'VARBIT' => ['size' => 'integer'],
	
	// Text search types
	'TSVECTOR' => [],
	'TSQUERY' => [],
	
	// Range types
	'INT4RANGE' => [],
	'INT8RANGE' => [],
	'NUMRANGE' => [],
	'TSRANGE' => [],
	'TSTZRANGE' => [],
	'DATERANGE' => [],
	
	// Transaction ID types
	// 'PG_LSN' => [],
	// 'PG_SNAPSHOT' => [],
	// 'TXID_SNAPSHOT' => [],
	
	// PostGIS types (extensions)
	'GEOMETRY' => ['srid' => 'integer', 'dimension' => 'integer'],
	'GEOGRAPHY' => ['srid' => 'integer', 'dimension' => 'integer'],
	'POINT' => ['srid' => 'integer', 'dimension' => 'integer'],
	'POINTZ' => ['srid' => 'integer', 'dimension' => 'integer'],
	'MULTIPOINT' => ['srid' => 'integer', 'dimension' => 'integer'],
	'MULTIPOINTZ' => ['srid' => 'integer', 'dimension' => 'integer'],
	'LINESTRING' => ['srid' => 'integer', 'dimension' => 'integer'],
	'LINESTRINGZ' => ['srid' => 'integer', 'dimension' => 'integer'],
	'MULTILINESTRING' => ['srid' => 'integer', 'dimension' => 'integer'],
	'MULTILINESTRINGZ' => ['srid' => 'integer', 'dimension' => 'integer'],
	'POLYGON' => ['srid' => 'integer', 'dimension' => 'integer'],
	'POLYGONZ' => ['srid' => 'integer', 'dimension' => 'integer'],
	'MULTIPOLYGON' => ['srid' => 'integer', 'dimension' => 'integer'],
	'MULTIPOLYGONZ' => ['srid' => 'integer', 'dimension' => 'integer'],
	
	// Other types
	'ENUM' => ['values' => 'array']
);
?>
