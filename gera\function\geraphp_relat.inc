<?
class PHP_GENERATOR_RELACIONAMENTO {

	function __construct(&$relat) {
	}
	
	function setForParent(&$relat) {
		// echo nl2br(print_r($relat, true))."<br/>";exit;
		$this->relat_parentid = $relat->get_Attribute("Parent"); // tabela pai, em id
		$this->relat_childid = $relat->get_Attribute("Child"); // sou eu mesmo, certo?! em id
		$this->relat_parentname = strtoupper($relat->get_Attribute("ParentName")); // tabela pai, nome
		$this->relat_childname = strtoupper($relat->get_Attribute("ChildName")); // eu mesmo, nome.
		$this->id_relat = $relat->get_Attribute("id");
		$this->frase = $relat->get_Attribute("Frase");
		$this->frase_inversa = $relat->get_Attribute("FraseInversa");
		$this->cardinal = $relat->get_Attribute("Cardinality");
		$this->tipo = 0;
		// echo "setForParent: ".$this->relat_parentname."<br/>";exit;
		$this->relat = &getEntidade($this->relat_parentname);
		if (empty($this->relat)) {
			echo nl2br(print_r($this, true))."<br/>";
			echo $this->relat_parentname."===============<br/>";
			echo nl2br(print_r($relat, true))."<br/>";
			exit;
		}
		$this->apoio = &getEntidade($this->relat_childname);
		// echo nl2br(print_r($this->relat, true))."<br/>";exit;
		$this->relat->relats_parent[$this->relat_parentname.$this->relat_childname.$this->frase.$this->frase_inversa] = &$this;
		$this->tipinho = "forParent";
		
		// precisa achar as colunas de um, para o outro.
		$jah_foi = null;
		$cols = $relat->get_Elements_By_TagName("ColunaRel");
		for ($w=0; $w < count($cols); $w++) {
			$col = $cols[$w];
			$col_parentname = strtoupper($col->get_Attribute("ParentName"));
			$col_childname = strtoupper($col->get_Attribute("ChildName"));
			$col_parentid = $col->get_Attribute("Parent");
			$col_childid = $col->get_Attribute("Child");
			if (@!$jah_foi[$col_parentname."-".$col_childname]) {
				$this->cols_relat[] =& getCampoEntidadeRef($this->relat, strtoupper($col_parentname));
				$this->cols_apoio[] =& getCampoEntidadeRef($this->apoio, strtoupper($col_childname)); /*//DFP
				$this->cols_relat[] =& getCampoEntidadeRef(&$this->relat, $col_parentname);
				$this->cols_apoio[] =& getCampoEntidadeRef(&$this->apoio, $col_childname);*/
				$jah_foi[$col_parentname."-".$col_childname] = "sim";
			}
		}
		/* Ordenar as colunas, mantendo a associação entre relat e apoio. Juntamos as duas num array intermediário... */
		$this->cols = null;
		for ($i = 0; $i < count($this->cols_relat); $i++) {
			$this->cols[] = array( "relat" => &$this->cols_relat[$i], "apoio" => &$this->cols_apoio[$i]);
		}
		
		/* ... ordenamos o array intermediário $this->cols ... */
		usort($this->cols, "ordenacaoConsistenteCamposRelatRelat");
		
		/* ... e separamos o intermediário novamente.... */
		$this->cols_relat = null; $this->cols_apoio = null;
		for ($i = 0; $i < count($this->cols); $i++) {
			$this->cols_relat[] =& $this->cols[$i]["relat"];
			$this->cols_apoio[] =& $this->cols[$i]["apoio"];
		}
		/* ... finalmente jogamos fora o intermediário. Hopefully tudo isso manteve as referências! (&'s) */
		unset($this->cols);
	}

	function setForChild(&$relat) {
		$this->relat_parentid = $relat->get_Attribute("Child");
		$this->relat_childid = $relat->get_Attribute("Parent");
		$this->relat_parentname = strtoupper($relat->get_Attribute("ChildName"));
		$this->relat_childname = strtoupper($relat->get_Attribute("ParentName")); 
		$this->id_relat = $relat->get_Attribute("id");
		$this->frase = $relat->get_Attribute("Frase");
		$this->frase_inversa = $relat->get_Attribute("FraseInversa");
		$this->cardinal = $relat->get_Attribute("Cardinality");
		$this->tipo = 0;
		$this->relat = &getEntidade($this->relat_parentname);
		$this->apoio = &getEntidade($this->relat_childname);
		$this->relat->relats_child[$this->relat_childname.$this->relat_parentname.$this->frase.$this->frase_inversa] = &$this;
		$this->tipinho = "forChild";
		
		// precisa achar as colunas de um, para o outro.
		$jah_foi = null;
		$cols = $relat->get_Elements_By_TagName("ColunaRel");
		for ($w=0; $w < count($cols); $w++) {
			$col = $cols[$w];
			$col_parentname = strtoupper($col->get_Attribute("ChildName"));
			$col_childname = strtoupper($col->get_Attribute("ParentName"));
			$col_parentid = $col->get_Attribute("Child");
			$col_childid = $col->get_Attribute("Parent");
			if (@!$jah_foi[$col_parentname."-".$col_childname]) {
				$this->cols_relat[] =& getCampoEntidadeRef($this->relat, strtoupper($col_parentname));
				$this->cols_apoio[] =& getCampoEntidadeRef($this->apoio, strtoupper($col_childname)); /*//DFP
				$this->cols_relat[] =& getCampoEntidadeRef(&$this->relat, $col_parentname);
				$this->cols_apoio[] =& getCampoEntidadeRef(&$this->apoio, $col_childname);*/
				$jah_foi[$col_parentname."-".$col_childname] = "sim";
			}
		}
		/* Ordenar as colunas, mantendo a associação entre relat e apoio. Juntamos as duas num array intermediário... */
		$this->cols = null;
		for ($i = 0; $i < count($this->cols_relat); $i++) {
			$this->cols[] = array( "relat" => &$this->cols_relat[$i], "apoio" => &$this->cols_apoio[$i]);
		}
		
		/* ... ordenamos o array intermediário $this->cols ... */
		usort($this->cols, "ordenacaoConsistenteCamposRelatApoio");
		
		/* ... e separamos o intermediário novamente.... */
		$this->cols_relat = null; $this->cols_apoio = null;
		for ($i = 0; $i < count($this->cols); $i++) {
			$this->cols_relat[] =& $this->cols[$i]["relat"];
			$this->cols_apoio[] =& $this->cols[$i]["apoio"];
		}
		/* ... finalmente jogamos fora o intermediário. Hopefully tudo isso manteve as referências! (&'s) */
		unset($this->cols);
	}

	function getDesc() {
		for ($i = 0; $i < count($this->cols_relat); $i++) {
			$relat = $this->cols_relat[$i];
			$apoio = $this->cols_apoio[$i];
			$campos[] = $this->relat->nome_fis.".".$relat->nome_fis. " = ". $this->apoio->nome_fis.".".$apoio->nome_fis;
		}
		$campos = implode(", ", $campos);
		$s = "Relacionamento ($this->tipinho) entre ".$this->relat->nome_log." e ".$this->apoio->nome_log . ($this->frase?(" (Fr: ".$this->frase.")"):""). ($this->frase_inversa?(" (FrInv: ".$this->frase_inversa.")"):""). " [Através de $campos]";
		return $s;
	}
	
	function getNomeFrase() {
		return $this->frase?"_".str_replace(" ", "_", strtoupper($this->frase)):"";
	}
	
	function getNomeFraseInversa() {
		return $this->frase_inversa?"_".str_replace(" ", "_", strtoupper($this->frase_inversa)):"";
	}
	
}
?>