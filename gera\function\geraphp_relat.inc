<?
class PHP_GENERATOR_RELACIONAMENTO {
	var $formatoOrigem='xml';

	function __construct(&$relat) {
		if (is_array($relat) && isset($relat['sourceTable'])){
			$this->formatoOrigem='json';
		}
	}
	
	function setForParent(&$relat) {
		// echo nl2br(print_r($relat, true))."------------------------------<br/>";exit;
		if ($this->formatoOrigem=='json'){
			// $this->relat_parentid = $relat['parent'];
			// $this->relat_childid = $relat['child'];
			$this->relat_parentname = $relat['sourceTable'];
			$this->relat_childname = $relat['targetTable'];
			// $this->id_relat = $relat['id'];
			$this->frase = $relat['phrase'];
			$this->frase_inversa = $relat['inverse'];
			$this->cardinal = $relat['cardinality'];
			$this->tipo = $relat['reltype'];
		}else{
			$this->relat_parentid = $relat->get_Attribute("Parent"); // tabela pai, em id
			$this->relat_childid = $relat->get_Attribute("Child"); // sou eu mesmo, certo?! em id
			$this->relat_parentname = strtoupper($relat->get_Attribute("ParentName")); // tabela pai, nome
			$this->relat_childname = strtoupper($relat->get_Attribute("ChildName")); // eu mesmo, nome.
			$this->id_relat = $relat->get_Attribute("id");
			$this->frase = $relat->get_Attribute("Frase");
			$this->frase_inversa = $relat->get_Attribute("FraseInversa");
			$this->cardinal = $relat->get_Attribute("Cardinality");
			$this->tipo = 0;
		}
		// echo "setForParent: ".$this->relat_parentname."<br/>";exit;
		$relatObj = &getEntidade($this->relat_parentname);
		if (empty($relatObj)) {
			echo nl2br(print_r($this, true))."<br/>";
			echo $this->relat_parentname."===============<br/>";
			echo nl2br(print_r($relatObj, true))."<br/>";
			exit;
		}
		$apoioObj = &getEntidade($this->relat_childname);
		// echo nl2br(print_r($relat, true))."<br/>";exit;
		$relatObj->relats_parent[$this->relat_parentname.$this->relat_childname.$this->frase.$this->frase_inversa] = &$this;
		$this->tipinho = "forParent";
		// precisa achar as colunas de um, para o outro.
		$jah_foi = null;
		if ($this->formatoOrigem=='json'){
			$cols = $relat['columns'] ?? [];
			foreach ($cols as $col) {
				// echo nl2br(print_r($col, true))."<br/>";exit;
				$col_parentname = strtoupper($col['parent']);
				$col_childname = strtoupper($col['child']);
				if (@!$jah_foi[$col_parentname."-".$col_childname]) {
					$this->cols_relat[] =& getCampoEntidadeRef($relatObj, strtoupper($col_parentname));
					$this->cols_apoio[] =& getCampoEntidadeRef($apoioObj, strtoupper($col_childname)); /*//DFP
					$this->cols_relat[] =& getCampoEntidadeRef(&$relat, $col_parentname);
					$this->cols_apoio[] =& getCampoEntidadeRef(&$apoioObj, $col_childname);*/
					$jah_foi[$col_parentname."-".$col_childname] = "sim";
				}
			}
		}else{
			$cols = $relat->get_Elements_By_TagName("ColunaRel");
			for ($w=0; $w < count($cols); $w++) {
				$col = $cols[$w];
				$col_parentname = strtoupper($col->get_Attribute("ParentName"));
				$col_childname = strtoupper($col->get_Attribute("ChildName"));
				$col_parentid = $col->get_Attribute("Parent");
				$col_childid = $col->get_Attribute("Child");
				if (@!$jah_foi[$col_parentname."-".$col_childname]) {
					$this->cols_relat[] =& getCampoEntidadeRef($relatObj, strtoupper($col_parentname));
					$this->cols_apoio[] =& getCampoEntidadeRef($apoioObj, strtoupper($col_childname)); /*//DFP
					$this->cols_relat[] =& getCampoEntidadeRef(&$relat, $col_parentname);
					$this->cols_apoio[] =& getCampoEntidadeRef(&$apoioObj, $col_childname);*/
					$jah_foi[$col_parentname."-".$col_childname] = "sim";
				}
			}
		}
		/* Ordenar as colunas, mantendo a associação entre relat e apoio. Juntamos as duas num array intermediário... */
		$this->cols = null;
		for ($i = 0; $i < count($this->cols_relat); $i++) {
			$this->cols[] = array( "relat" => &$this->cols_relat[$i], "apoio" => &$this->cols_apoio[$i]);
		}
		
		/* ... ordenamos o array intermediário $this->cols ... */
		usort($this->cols, "ordenacaoConsistenteCamposRelatRelat");
		
		/* ... e separamos o intermediário novamente.... */
		$this->cols_relat = null; $this->cols_apoio = null;
		for ($i = 0; $i < count($this->cols); $i++) {
			$this->cols_relat[] =& $this->cols[$i]["relat"];
			$this->cols_apoio[] =& $this->cols[$i]["apoio"];
		}
		/* ... finalmente jogamos fora o intermediário. Hopefully tudo isso manteve as referências! (&'s) */
		unset($this->cols);
	}

	function setForChild(&$relat) {
		if ($this->formatoOrigem=='json'){
			// $this->relat_parentid = $relat['parent'];
			// $this->relat_childid = $relat['child'];
			$this->relat_parentname = $relat['targetTable'];
			$this->relat_childname = $relat['sourceTable'];
			// $this->id_relat = $relat['id'];
			$this->frase = $relat['phrase'];
			$this->frase_inversa = $relat['inverse'];
			$this->cardinal = $relat['cardinality'];
			$this->tipo = $relat['reltype'];
		}else{
			$this->relat_parentid = $relat->get_Attribute("Child");
			$this->relat_childid = $relat->get_Attribute("Parent");
			$this->relat_parentname = strtoupper($relat->get_Attribute("ChildName"));
			$this->relat_childname = strtoupper($relat->get_Attribute("ParentName")); 
			$this->id_relat = $relat->get_Attribute("id");
			$this->frase = $relat->get_Attribute("Frase");
			$this->frase_inversa = $relat->get_Attribute("FraseInversa");
			$this->cardinal = $relat->get_Attribute("Cardinality");
			$this->tipo = 0;
		}
		$relatObj = &getEntidade($this->relat_parentname);
		$apoioObj = &getEntidade($this->relat_childname);
		$relatObj->relats_child[$this->relat_childname.$this->relat_parentname.$this->frase.$this->frase_inversa] = &$this;
		$this->tipinho = "forChild";
		
		// precisa achar as colunas de um, para o outro.
		$jah_foi = null;
		if ($this->formatoOrigem=='json'){
			$cols = $relat['columns'] ?? [];
			foreach ($cols as $col) {
				// echo nl2br(print_r($col, true))."<br/>";exit;
				$col_parentname = strtoupper($col['child']);
				$col_childname = strtoupper($col['parent']);
				if (@!$jah_foi[$col_parentname."-".$col_childname]) {
					$this->cols_relat[] =& getCampoEntidadeRef($relatObj, strtoupper($col_parentname));
					$this->cols_apoio[] =& getCampoEntidadeRef($apoioObj, strtoupper($col_childname)); /*//DFP
					$this->cols_relat[] =& getCampoEntidadeRef(&$relatObj, $col_parentname);
					$this->cols_apoio[] =& getCampoEntidadeRef(&$apoioObj, $col_childname);*/
					$jah_foi[$col_parentname."-".$col_childname] = "sim";
				}
			}
		}else{
			$cols = $relat->get_Elements_By_TagName("ColunaRel");
			for ($w=0; $w < count($cols); $w++) {
				$col = $cols[$w];
				$col_parentname = strtoupper($col->get_Attribute("ChildName"));
				$col_childname = strtoupper($col->get_Attribute("ParentName"));
				$col_parentid = $col->get_Attribute("Child");
				$col_childid = $col->get_Attribute("Parent");
				if (@!$jah_foi[$col_parentname."-".$col_childname]) {
					$this->cols_relat[] =& getCampoEntidadeRef($relatObj, strtoupper($col_parentname));
					$this->cols_apoio[] =& getCampoEntidadeRef($apoioObj, strtoupper($col_childname)); /*//DFP
					$this->cols_relat[] =& getCampoEntidadeRef(&$relatObj, $col_parentname);
					$this->cols_apoio[] =& getCampoEntidadeRef(&$apoioObj, $col_childname);*/
					$jah_foi[$col_parentname."-".$col_childname] = "sim";
				}
			}
		}
		/* Ordenar as colunas, mantendo a associação entre relat e apoio. Juntamos as duas num array intermediário... */
		$this->cols = null;
		for ($i = 0; $i < count($this->cols_relat); $i++) {
			$this->cols[] = array( "relat" => &$this->cols_relat[$i], "apoio" => &$this->cols_apoio[$i]);
		}
		
		/* ... ordenamos o array intermediário $this->cols ... */
		usort($this->cols, "ordenacaoConsistenteCamposRelatApoio");
		
		/* ... e separamos o intermediário novamente.... */
		$this->cols_relat = null; $this->cols_apoio = null;
		for ($i = 0; $i < count($this->cols); $i++) {
			$this->cols_relat[] =& $this->cols[$i]["relat"];
			$this->cols_apoio[] =& $this->cols[$i]["apoio"];
		}
		/* ... finalmente jogamos fora o intermediário. Hopefully tudo isso manteve as referências! (&'s) */
		unset($this->cols);
	}

	function getDesc() {
		$relatObj = &getEntidade($this->relat_parentname);
		$apoioObj = &getEntidade($this->relat_childname);
		for ($i = 0; $i < count($this->cols_relat); $i++) {
			$relat = $this->cols_relat[$i];
			$apoio = $this->cols_apoio[$i];
			$campos[] = $relatObj->nome_fis.".".$relat->nome_fis. " = ". $apoioObj->nome_fis.".".$apoio->nome_fis;
		}
		$campos = implode(", ", $campos);
		$s = "Relacionamento ($this->tipinho) entre ".$relatObj->nome_log." e ".$apoioObj->nome_log . ($this->frase?(" (Fr: ".$this->frase.")"):""). ($this->frase_inversa?(" (FrInv: ".$this->frase_inversa.")"):""). " [Através de $campos]";
		return $s;
	}
	
	function getNomeFrase() {
		return $this->frase?"_".str_replace(" ", "_", strtoupper($this->frase)):"";
	}
	
	function getNomeFraseInversa() {
		return $this->frase_inversa?"_".str_replace(" ", "_", strtoupper($this->frase_inversa)):"";
	}
	
}
?>