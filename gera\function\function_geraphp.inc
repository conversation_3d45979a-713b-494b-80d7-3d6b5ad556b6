<?
/* Utilit<PERSON>rias para pegar as entidades e campos por referência */
function &getEntidade($nome_fis) {
	global $array_ent, $array_ent_num;
	return $array_ent[$array_ent_num[$nome_fis]];
}

function &getCampoEntidade($nome_fis_ent, $nome_fis_campo) {
	$ent =& getEntidade($nome_fis_ent);
	for ($i = 0; $i < count($ent->campos); $i++) {
		$campo =& $ent->campos[$i];
		if ($campo->nome_fis == $nome_fis_campo) return $campo;
	}
	return null;
}

function &getCampoEntidadeRef(&$ent, $nome_fis_campo) {
	for ($i = 0; $i < count($ent->campos); $i++) {
		$campo =& $ent->campos[$i];
		if ($campo->nome_fis == $nome_fis_campo) return $campo;
	}
	return null;
}

set_time_limit(600);

/**************************************************************************************************************************
** Base do generator, contém arrays de todas as entidades, todas as classes geradas, testes de regressão, etc.
**************************************************************************************************************************/
class PHP_GENERATOR_BASE {
	var $doc; // O documento, para ser usado caso precise.
	var $bases; // Array de bases geradas;
	var $bases_list; // Array de bases geradas;
	var $rels; // Array de rels geradas;
	var $finals; // Array de finals geradas;
	var $sufixo=null; // Eventual sufixo para classes isoladas
	var $sufclasses=false;
	var $namespace=null; // Eventual namespace para classes isoladas
	var $schema=null; // Eventual schema para objetos do DDL (verificar necessidade de uso nas classes)
	var $lf="\n";
	var $base_class=null;
	var $base_list_class=null;
	var $finals_list=null;
	var $rels_list=null;
	var $entidades=null;
	var $ddls=null;
	var $config_postgresql=null;
	var $rodaregress_postgresql=null;
	var $config_oracle=null;
	var $rodaregress_oracle=null;
	var $config_mysql=null;
	var $rodaregress_mysql=null;
	var $initdb=null;
	var $classe_factory=null;
	var $include_all=null;
	var $prepend=null;
	var $append=null;
	var $httpd=null;
	var $meuphp=null;
	var $meuphpora=null;
	var $index_vazio=null;
	var $datapump=null;
	var $geraMsSqlServer=false;
	
	function init($dirbase="", $codebase_function, $codebase_function_classes, $codebase_function_classes_readonly) {
		$this->geraClasses($this->doc);
		$this->geraDDLs();
		$this->geraClassesBase();
		$this->geraFabrica($codebase_function, $codebase_function_classes, $codebase_function_classes_readonly);
		$this->geraIncludeAll($codebase_function_classes, $codebase_function_classes_readonly);
		$this->geraIndexVazio($dirbase);
		$this->geraPrepend($codebase_function);
		$this->geraConfApache($dirbase);
		$this->geraConfig();
		$this->geraInitDB();
		$this->geraDataPump();
	}
	
	function geraClasses($doc) {
		global $array_ent, $array_ent_num;
		$root = $doc->document_element();
		if ($root->tagname != "Modelo") {
			die("Elemento root deve ser um 'Modelo', erro.");
		}
		
		$entidades_element_array = $root->get_elements_by_tagname("Entidades");
		
		if (!$entidades_element = $entidades_element_array[0]) {
			die("Impossível achar elemento 'Entidades', erro.");
		} else {
			$entidades = $entidades_element->child_nodes();
			$array_ent = null; // mega-global hashtable
			$array_ent_num = null;
			$i = 0;
			foreach ($entidades as $entidade) {
				$class = new PHP_GENERATOR_ENTIDADE($entidade, $this->sufixo, $this->sufclasses, $this->lf, $this->geraMsSqlServer);
				$array_ent[$i] = $class;
				//$array_ent_num[$class->nome_fis] = $i;
				$i++;
			}
		}
		
		/* Ordenemos as entidades! */
		usort($array_ent, "ordenacaoConsistenteEntidades");
		$i = 0;
		foreach ($array_ent as $ent) {
			$array_ent_num[$ent->nome_fis] = $i;
			$i++;
		}
		
		
		
		
		
		$relats = $root->get_elements_by_tagname("Relacionamentos");
		echo nl2br(print_r($relats, true))."<br/>";exit;
		$relats = $relats[0];
		$relats = $relats->child_nodes();

		foreach ($relats as $relat) {
			$class1 = new PHP_GENERATOR_RELACIONAMENTO($relat);
				die(nl2br(print_r($relat, true)).' <a href="javascript:history.back();">Voltar</a>');
			$class1->setForParent($relat);
			$class2 = new PHP_GENERATOR_RELACIONAMENTO($relat);
			$class2->setForChild($relat);
		}
		initProgresso(  count($array_ent) );
		for ($i = 0; $i < count($array_ent); $i++) {
			$entidade             =& $array_ent[$i];
			$this->bases[]                =& $this->geraBase($entidade);
			$this->bases_list[]           =& $this->geraBaseList($entidade);
			$this->finals[]               =  $this->geraFinal($entidade);
			$this->finals_list[]          =  $this->geraFinalList($entidade);
			$this->rels[]                 =  $this->geraRel($entidade);
			$this->rels_list[]            =  $this->geraRelList($entidade);
			$this->entidades[]            =& $entidade;
			incrProg($i+1);
		}
	}
	
	// Gera DDLs, aggregando.
	
	function geraDDLs() {
		$this->ddls = null;
		$cls=DDL_GENERATOR::getGenerators();
		//echo nl2br(print_r($cls, true))."<br/>";
		foreach (DDL_GENERATOR::getGenerators() as $classe) {
			$destfinal = strtoupper("DDL_GENERATOR_".strtoupper($classe));
			$a = new $destfinal;
			$a->schema=$this->schema;
			$ddl = $a->init($this->entidades);
			$this->ddls[$classe]["obj"] = $a;
			$this->ddls[$classe]["ddl"] = $ddl;
		}
	}
	
	
	
	// Gera as Bases.
	function &geraBase(&$ent) {
		//aggregate($ent, "PHP_GENERATOR_ENTIDADE_ONE");
		$obj=$ent->geraBase();
		return $obj;
	}
	
	function &geraBaseList(&$ent) {
		//aggregate($ent, "PHP_GENERATOR_ENTIDADE_LIST");
		$obj=$ent->geraBaseList();
		return $obj;
	}
	
	function geraRel(&$ent) {
		//aggregate($ent, "PHP_GENERATOR_ENTIDADE_REL");
		return $ent->geraRel();
	}
	
	function geraRelList(&$ent) {
		//aggregate($ent, "PHP_GENERATOR_ENTIDADE_REL_LIST");
		return $ent->geraRelList();
	}
	
	function geraFinal(&$ent) {
		//aggregate($ent, "PHP_GENERATOR_ENTIDADE_FINAL");
		return $ent->geraFinal();
	}
	
	function geraFinalList(&$ent) {
		//aggregate($ent, "PHP_GENERATOR_ENTIDADE_FINAL_LIST");
		return $ent->geraFinalList();
	}
	
	
	function geraConfig($tipo="postgres") {
		$s = 
		"// Configurações de banco de dados do site...\n".
		"\$_configDbPers   = false;\n".
		"\$_configDbType   = \"zpostgres\"; // <MARCA DO BANCO> \n".
		"\$_configDbServer = \"localhost\"; // <SERVIDOR DO BANCO>\n".
		"\$_configDbUser   = \"ricardo\";   // <USUARIO DO BANCO>\n".
		"\$_configDbPass   = \"\";          // <SENHA DO USUARIO>\n".
		"\$_configDbName   = \"teste10\";   // <NOME DA BASE>\n".
		"\n".
		"// Configurações de log...\n".
		"\$_configLogPath = \"/var/log/site.log\";   // <PATH COMPLETO PARA O LOGFILE>\n".
		"\$_configLogEmail = \"root@localhost\";     // <EMAIL DO ADMINISTRADOR>\n".
		"\$_configLogEmailSubj = \"[erro no site]\"; // <SUBJECT DO EMAIL DE ERRO>\n".
		"\$_configNomeSite = \"develsite\";          // <NOME DESDE SITE>\n".
		"\n".
		"// Configurações de SMTP\n".
		"\$_configSMTPHost = \"localhost\"; // <HOST OU IP DO SMTP>\n".
		"\$_configSMTPPort = 25;\n".
		"\$_configSMTPHelo = \"develsite\"; // <HELO OU EHLO A SER UTILIZADO>\n";
		$this->config_postgresql = preparaSaidaPHP($s);
		$this->rodaregress_postgresql = 
		"echo \"******************************** REGRESSION TEST (POSTGRESQL) ************************************\"\n".
		"echo \"************************************ CREATING SCHEMA *********************************************\"\n".
		"dropdb teste10; createdb teste10; psql teste10 < codebase/install/ddl/postgresql.sql\n".
		"echo \"************************************* SCHEMA CREATED *********************************************\"\n".
		"echo \"************************************* REGRESSION (POSTGRESQL) ************************************\"\n".
		"./meuphp docroot/regression.php --with-config=config/config.postgresql.inc\n".
		"echo \"*************************************** DONE *****************************************************\"\n";
		
		$s = 
		"// Configurações de banco de dados do site...\n".
		"\$_configDbPers   = false;\n".
		"\$_configDbType   = \"zoracle\"; // <MARCA DO BANCO> \n".
		"\$_configDbServer = \"\"; // <SERVIDOR DO BANCO>\n".
		"\$_configDbUser   = \"cnet\";   // <USUARIO DO BANCO>\n".
		"\$_configDbPass   = \"cnet\";          // <SENHA DO USUARIO>\n".
		"\$_configDbName   = \"\";   // <NOME DA BASE>\n".
		"\n".
		"// Configurações de log...\n".
		"\$_configLogPath = \"/var/log/site.log\";   // <PATH COMPLETO PARA O LOGFILE>\n".
		"\$_configLogEmail = \"root@localhost\";     // <EMAIL DO ADMINISTRADOR>\n".
		"\$_configLogEmailSubj = \"[erro no site]\"; // <SUBJECT DO EMAIL DE ERRO>\n".
		"\$_configNomeSite = \"develsite\";          // <NOME DESDE SITE>\n".
		"\n".
		"// Configurações de SMTP\n".
		"\$_configSMTPHost = \"localhost\"; // <HOST OU IP DO SMTP>\n".
		"\$_configSMTPPort = 25;\n".
		"\$_configSMTPHelo = \"develsite\"; // <HELO OU EHLO A SER UTILIZADO>\n";
		$this->config_oracle = preparaSaidaPHP($s);
		$this->rodaregress_oracle = 
		"echo \"******************************** REGRESSION TEST (ORACLE) ****************************************\"\n".
		"echo \"************************************ CREATING SCHEMA *********************************************\"\n".
		"export NLS_LANG=AMERICAN_AMERICA.WE8ISO8859P1\n".
		"sqlplus sys/sys <<EOF\n".
		"connect sys/sys;\n".
		"drop user cnet cascade;\n".
		"create user cnet identified by cnet account unlock;\n".
		"grant connect, resource to cnet;\n".
		"connect cnet/cnet;\n".
		"@codebase/install/ddl/oracle.sql\n".
		"connect sys/sys;\n".
		"quit\n".
		"EOF\n".
		"echo \"************************************* SCHEMA CREATED *********************************************\"\n".
		"echo \"************************************* REGRESSION (ORACLE) ****************************************\"\n".
		"./meuphpora docroot/regression.php --with-config=config/config.oracle.inc\n".
		"echo \"*************************************** DONE *****************************************************\"\n";

		// MySQL
		$s = 
		"// Configurações de banco de dados do site...\n".
		"\$_configDbPers   = false;\n".
		"\$_configDbType   = \"zmysql\"; // <MARCA DO BANCO> \n".
		"\$_configDbServer = \"localhost\"; // <SERVIDOR DO BANCO>\n".
		"\$_configDbUser   = \"root\";   // <USUARIO DO BANCO>\n".
		"\$_configDbPass   = \"\";          // <SENHA DO USUARIO>\n".
		"\$_configDbName   = \"teste10\";   // <NOME DA BASE>\n".
		"\n".
		"// Configurações de log...\n".
		"\$_configLogPath = \"/var/log/site.log\";   // <PATH COMPLETO PARA O LOGFILE>\n".
		"\$_configLogEmail = \"root@localhost\";     // <EMAIL DO ADMINISTRADOR>\n".
		"\$_configLogEmailSubj = \"[erro no site]\"; // <SUBJECT DO EMAIL DE ERRO>\n".
		"\$_configNomeSite = \"develsite\";          // <NOME DESDE SITE>\n".
		"\n".
		"// Configurações de SMTP\n".
		"\$_configSMTPHost = \"localhost\"; // <HOST OU IP DO SMTP>\n".
		"\$_configSMTPPort = 25;\n".
		"\$_configSMTPHelo = \"develsite\"; // <HELO OU EHLO A SER UTILIZADO>\n";
		$this->config_mysql = preparaSaidaPHP($s);
		$this->rodaregress_mysql = 
		"echo \"******************************** REGRESSION TEST (MYSQL) *****************************************\"\n".
		"echo \"************************************ CREATING SCHEMA *********************************************\"\n".
		"mysqladmin -uroot -f drop teste10; mysqladmin -uroot create teste10; mysql -uroot teste10 < codebase/install/ddl/mysql.sql\n".
		"echo \"************************************* SCHEMA CREATED *********************************************\"\n".
		"echo \"************************************* REGRESSION (ORACLE) ****************************************\"\n".
		"./meuphp docroot/regression.php --with-config=config/config.mysql.inc\n".
		"echo \"*************************************** DONE *****************************************************\"\n";
	}


	function geraInitDB() {
		$s =
		"include('codebase/php/adodb/adodb.inc.php');\n".
		"ADOLoadCode(\$_configDbType);\n".
		"\$db = ADONewConnection();\n".
		"\n".
		"if (\$_configDbPers) {\n".
		"	if (! @\$db->PConnect(\$_configDbServer, \$_configDbUser, \$_configDbPass, \$_configDbName) ) {\n".
		"			echo \"Erro conectando ao DB.\";\n".
		"	        exit;\n".
		"	}\n".
		"} else {\n".
		"	if (! @\$db->Connect(\$_configDbServer, \$_configDbUser, \$_configDbPass, \$_configDbName) ) {\n".
		"			echo \"Erro conectando ao DB.\";\n".
		"	        exit;\n".
		"	}\n".
		"}\n".
		"if (@\$_configUTF_8===true){\n".
		"	switch(\$_configDbType){\n".
		"		case 'zpostgres':\n".
		"			\$db->Execute('set client_encoding to \"UTF-8\"');\n".
		"			break;\n".
		"	}\n".
		"}\n".
		"\$_oracle = (strpos(\$db->databaseType, \"oci8\")!==false || strpos(\$db->databaseType, \"oracle\")!==false);\n".
		"\$_mssql  = (strpos(\$db->databaseType, \"mssql\")!==false);\n".
		"//\$db->debug=true; // Ativa debugging da conexão ao DB. Quantidade absurda de output!\n";
		
		$this->initdb = preparaSaidaPHP($s);
	}


	function setDoc($xml) {
		$this->doc = &$xml;
	}

	function geraClassesBase() {
		// die('aqui ');
		$s  = "if (!class_exists('gc_base')) {\n";
		$s  .= "\tclass gc_base {\n\n";
		$s .= "\t}\n";
		$s .= "}\n\n";
		$this->base_class = preparaSaidaPHP($s);
		$s  = "if (!class_exists('gc_base_list')) {\n";
		$s .= "\tclass gc_base_list {\n";
		$s .= "\t\tfunction moveFirst(){\n";
		$s .= "\t\t\t@\$this->_rec->MoveFirst();\n";
		$s .= "\t\t}\n";
		$s .= "\t}\n";
		$s .= "}\n\n";
		$this->base_list_class = preparaSaidaPHP($s);
		
	}

	function geraFabrica($codebase_function, $codebase_function_classes, $codebase_function_classes_readonly) {
		$viaGetIncludes=true;
		$s = '';
		$s .= "@include_once(\"".$codebase_function."gc_base_class.inc"."\");\n";
		$s .= "@include_once(\"".$codebase_function."gc_base_list_class.inc"."\");\n";
		$s .= "if (!class_exists('gc_base')) {\n";
		$s .= "\tclass gc_base {}\n";
		$s .= "}\n";
		$s .= "if (!class_exists('gc_base_list')) {\n";
		$s .= "\tclass gc_base_list {\n";
		$s .= "\t\tfunction toArray(){\n";
		$s .= "\t\t\treturn [];\n";
		$s .= "\t\t}\n";
		$s .= "\t}\n";
		$s .= "}\n";
		$s .= "class FACTORY".$this->sufixo." {\n";
		if ($viaGetIncludes){
			$s .= "\tstatic function getIncludes(\$cl){\n";
			$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."\".\$cl.\"_base.inc\");\n";
			$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."\".\$cl.\"_rel.inc\");\n";
			$s .= "\t\tinclude_once(\"".$codebase_function_classes."\".\$cl.\".inc\");\n";
			$s .= "\t}\n";
			$s .= "\tstatic function getIncludesList(\$cl){\n";
			$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."\".\$cl.\"_base_list.inc\");\n";
			$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."\".\$cl.\"_rel_list.inc\");\n";
			$s .= "\t\tinclude_once(\"".$codebase_function_classes."\".\$cl.\"_list.inc\");\n";
			$s .= "\t}\n";
			$s .= "\tstatic function &get(\$cl){\n";
			$s .= "\t\t\$cl=strtoupper(\$cl);\n";
			$s .= "\t\t\$incl=strtolower(\$cl);\n";
			$s .= "\t\tif (strpos(\$incl,'_list')!==false) {\n";
			$s .= "\t\t\t\$incl=str_replace('_list','',\$incl);\n";
			$s .= "\t\t\tFACTORY_gt::getIncludesList(\$incl);\n";
			$s .= "\t\t}else{\n";
			$s .= "\t\t\tFACTORY_gt::getIncludes(\$incl);\n";
			$s .= "\t\t}\n";
			$s .= "\t\t\$item = new \$cl;\n";
			$s .= "\t\treturn \$item;\n";
			$s .= "\t}\n";
		}
		foreach ($this->entidades as $ent) {
			// um para final
			$s .= "\tstatic function &".$ent->nome_classe_final."() {\n";
			if ($this->geraMsSqlServer) $s .= "\t\tglobal \$_mssql;\n";
			if ($viaGetIncludes){
				$s .= "\t\tFACTORY".$this->sufixo."::getIncludes('".strtolower($ent->nome_classe_final)."');\n";
			}else{
				$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."".strtolower($ent->nome_classe_base).".inc"."\");\n";
				$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."".strtolower($ent->nome_classe_rel).".inc"."\");\n";
				$s .= "\t\tinclude_once(\"".$codebase_function_classes."".strtolower($ent->nome_classe_final).".inc"."\");\n";
			}
			//$s .= "\t\toverload('".$ent->nome_classe_final."');\n"; /* @TODO: overloading */
			$s .= "\t\t\$item = new ".$ent->nome_classe_final.";\n";
			if ($this->geraMsSqlServer) $s .= "\t\tif (@\$_mssql) @\$item->".$ent->nome_classe_rel."();\n";
			$s .= "\t\treturn \$item;\n";
			$s .= "\t}\n";

			// um para final_list.
			$s .= "\tstatic function &".$ent->nome_classe_final_list."() {\n";
			if ($this->geraMsSqlServer) $s .= "\t\tglobal \$_mssql;\n";
			if ($viaGetIncludes){
				$s .= "\t\tFACTORY".$this->sufixo."::getIncludesList('".strtolower($ent->nome_classe_final)."');\n";
			}else{
				$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."".strtolower($ent->nome_classe_base_list).".inc"."\");\n";
				$s .= "\t\tinclude_once(\"".$codebase_function_classes_readonly."".strtolower($ent->nome_classe_rel_list).".inc"."\");\n";
				$s .= "\t\tinclude_once(\"".$codebase_function_classes."".strtolower($ent->nome_classe_final_list).".inc"."\");\n";
			}
			//$s .= "\t\toverload('".$ent->nome_classe_final_list."');\n"; /* @TODO: overloading */
			$s .= "\t\t\$item = new ".$ent->nome_classe_final_list.";\n";
			if ($this->geraMsSqlServer) $s .= "\t\tif (@\$_mssql) @\$item->".$ent->nome_classe_rel_list."();\n";
			$s .= "\t\treturn \$item;\n";
			$s .= "\t}\n";
		}
		$s .= "}\n\n";
		$this->classe_factory = preparaSaidaPHP($s);
	}

	function geraIncludeAll($codebase_function_classes, $codebase_function_classes_readonly) {
		$s  = "";
		foreach ($this->entidades as $ent) {
			// um para final, um para final_list.
			$s .= "require(\"".$codebase_function_classes_readonly."/".strtolower($ent->nome_classe_base).".inc"."\");\n";
			$s .= "require(\"".$codebase_function_classes_readonly."/".strtolower($ent->nome_classe_rel).".inc"."\");\n";
			$s .= "require(\"".$codebase_function_classes."/".strtolower($ent->nome_classe_final).".inc"."\");\n";
			$s .= "require(\"".$codebase_function_classes_readonly."/".strtolower($ent->nome_classe_base_list).".inc"."\");\n";
			$s .= "require(\"".$codebase_function_classes_readonly."/".strtolower($ent->nome_classe_rel_list).".inc"."\");\n";
			$s .= "require(\"".$codebase_function_classes."/".strtolower($ent->nome_classe_final_list).".inc"."\");\n";
		}
		$s  .= "\n\nclass FACTORY_STATIC {\n";
		foreach ($this->entidades as $ent) {
			// um para final, um para final_list.
			$s .= "\tfunction ".$ent->nome_classe_final."() {\n";
			$s .= "\t\t\$item = new ".$ent->nome_classe_final.";\n";
			$s .= "\t\treturn \$item;\n";
			$s .= "\t}\n";
			$s .= "\tfunction ".$ent->nome_classe_final_list."() {\n";
			$s .= "\t\t\$item = new ".$ent->nome_classe_final_list.";\n";
			$s .= "\t\treturn \$item;\n";
			$s .= "\t}\n";
		}
		$s .= "}\n\n";

		$this->include_all = preparaSaidaPHP($s);
	}
	
	function geraPrepend($codebase_function) {
		$s = "// Arquivo que executa sempre antes de qualquer outra coisa.\n";
		$s .= "// Inclui: Configuração; Fabrica/IncludeAll; Inicializa DB.\n";
		$s .= "require(\"".$codebase_function."func_diversas.inc\"); // Funções diversas!\n";
		$s .= "// Seta o locale default (pode ser trocado na configuração).\n";
		$s .= "setlocale (\"LC_TIME\", \"pt_BR\");\n";
		$s .= "// Se estiver sendo executado via linha de comando, obtém o config a utilizar.\n";
		$s .= "\$conf = getCmdLine(\"with-config\");\n";
		$s .= "require(\$conf?\$conf:\"config/config.inc\"); // Carrega Configuração do sistema\n";
		$s .= "require(\"".$codebase_function."factory_dyn.inc\"); // Classes carregadas dinamicamente\n";
		$s .= "// require(\"".$codebase_function."factory_static.inc\"); // Classes carregadas estáticas\n";
		$s .= "require(\"".$codebase_function."initdb.inc\"); // Inicialização do DB\n";
		$this->prepend = preparaSaidaPHP($s);

		$s = "// Arquivo que executa sempre depois de qualquer outra coisa.\n";
		$s .= "// Não faz nada.\n";
		$this->append = preparaSaidaPHP($s);
	}
	
	function geraConfApache($basetotal=null) {
		$basetotal = realpath($basetotal);
		$ip_add = "<IP_DA_MAQ_AQUI>";
		$servername = "<NOME DNS DA MAQUINA AQUI>";
		$serveralias = "<OUTROS NOMES DESSA MAQUINA SEPARADOS POR ESPACO>";
		$s  = "<VirtualHost $ip_add>
	# Configuracao de nome de host!
	ServerName $servername
	ServerAlias $serveralias
	
	# Configuracoes necessarias do PHP.
	php_flag asp_tags on
	php_flag magic_quotes_gpc Off
	php_value auto_prepend_file \"codebase/function/auto_prepend.inc\"
	php_value auto_append_file  \"codebase/function/auto_append.inc\"
	php_value include_path \"$basetotal:.\"
	DirectoryIndex index.php index.html
	AddType application/x-httpd-php .php .html
	AddType application/x-httpd-php-source .phps
	
	DocumentRoot $basetotal/docroot
        <Directory $basetotal/docroot>
            Options Indexes Includes FollowSymLinks MultiViews
            AllowOverride All
            Order allow,deny
            Allow from all
        </Directory>
        <Directory />
            Options FollowSymLinks
            AllowOverride All
        </Directory>
	
	Alias /images $basetotal/codebase/images
	Alias /scripts $basetotal/codebase/scripts
	
	LogLevel warn
	CustomLog $basetotal/logs/access.log combined
	ErrorLog $basetotal/logs/error.log
	mod_gzip_on No
</VirtualHost>\n";
		$this->httpd = $s;
		
		
		$s  = "php4 -q -d include_path=$basetotal -d auto_append_file=codebase/function/auto_append.inc -d auto_prepend_file=codebase/function/auto_prepend.inc \$1 \$2 \$3 \$4 \$5 \$6 \$7 \$8 \$9\n";
		$this->meuphp = $s;

		$s  = "export NLS_LANG=AMERICAN_AMERICA.WE8ISO8859P1\n"."~/apache/php4/bin/php -q -d include_path=$basetotal -d auto_append_file=codebase/function/auto_append.inc -d auto_prepend_file=codebase/function/auto_prepend.inc \$1 \$2 \$3 \$4 \$5 \$6 \$7 \$8 \$9\n";
		$this->meuphpora = $s;
	}
	
	function geraIndexVazio() {
		$s = "?>
		<h3>Index Vazio!</h3>
		<a href='/regression.php'>Clique aqui</a> para rodar o regression test.<BR>
		Dados da configuração atual:<br>
		Persistência do Banco de Dados: <?=\$_configDbPers?><br>
		Tipo do Banco de dados: <?=\$_configDbType?><br>
		Servidor do Banco de Dados: <?=\$_configDbServer?><br>
		Usuário do Banco de Dados: <?=\$_configDbUser?><br>
		Senha do Banco: <?=\$_configDbPass?><br>
		Nome do Banco: <?=\$_configDbName?><br><br>
		Boa sorte e bom desenvolvimento!<?
		";
		$this->index_vazio	 = preparaSaidaPHP($s);
	}


	function geraDataPump() {
		$s  = "\n";
		$i = 1;
		foreach ($this->entidades as $ent) {
			/* Achar o campo IDENTITY, se tiver. */
			$seq = null;
			foreach ($ent->campos as $cc) {
				if ($cc->ident_campo) {
					$seq = $cc;
				}
			}
			if ($seq) { $seq = ", '".$seq->nome_fis."'"; } else { $seq = ", null"; }
			
			
			$s .= 'if ( ($from <= '.$i.') && ('.$i.' <= $to) ) { $didsomething = true; '."\n";
			$s .= '	if ($callback->shouldIPump("'.$ent->nome_fis.'")) { '."\n";
			$s .= '		$lista = &FACTORY'.$this->sufixo.'::'.$ent->nome_classe_final_list.'();' . "\n";
			$s .= '		$lista->getTodos();'."\n";
			$s .= '		$callback->preStartDumpEnt("'.$ent->nome_fis.'"'.$seq.', $lista->getQuantos());'."\n";
			$s .= '		while ($item = $lista->getNextItem()) {'."\n";
			$s .= '			$callback->preStartDumpRow("'.$ent->nome_fis.'");'."\n";
			$s .= '			$item->db =& $db_new;'."\n";
			$s .= '			if (!$item->hardInsert()) $callback->erro("Erro inserindo '.$ent->nome_classe_final.'.");'."\n";
			$s .= '			$callback->postEndDumpRow("'.$ent->nome_fis.'");'."\n";
			$s .= '		}'."\n";
			$s .= '		$callback->postEndDumpEnt("'.$ent->nome_fis.'"'.$seq.');'."\n";
			$s .= '		unset($lista); unset($item);'."\n";
			$s .= '	}'."\n";
			$s .= '}'."\n";
			$s .= ''."\n";
			$i++;
		}
		$s .= "\n\n";
		$this->datapump = preparaSaidaPHP($s);
	}
	
}

?>