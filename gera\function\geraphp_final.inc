<?php
/**************************************************************************************************************************
** Gera a classe final, para um row; Vazia.
**************************************************************************************************************************/
class PHP_GENERATOR_ENTIDADE_FINAL { 

	function geraFinal() {
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = 
					"Esta classe extende a <code>" . $this->nome_classe_rel . "</code> adicionando <b>toda a implementação de lógica de negócio relativa a ".$this->nome_log."</b>.<br>\n".
					"Por favor <b>modifique esta classe</b>. Não faça implementações nas classes <code>" . $this->nome_classe_base. "</code> e <code>".$this->nome_classe_rel."</code>.<br>\n".
					"Não instancie diretamente esta classe; ao invés disso utilize a classe <code>FACTORY</code>. Ex: <code>\$obj = ".$this->getComoInstanciarOne()."</code>.";
		$jd->autor = geraClassesConfigAutor();
		return 
		$jd->get().
		"class ". $this->nome_classe_final . " extends ". $this->nome_classe_rel . " { \n".
		"}\n";
	}
}

/**************************************************************************************************************************
** Gera a classe final para vários rows. Vazia.
**************************************************************************************************************************/
class PHP_GENERATOR_ENTIDADE_FINAL_LIST  { 

	function geraFinalList() {
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = 
					"Esta classe extende a <code>" . $this->nome_classe_rel_list . "</code> adicionando <b>toda a implementação de lógica de negócio relativa a uma lista de ".$this->nome_log."</b>.<br>\n".
					"Por favor <b>modifique esta classe</b>. Não faça implementações nas classes <code>" . $this->nome_classe_base_list. "</code> e <code>".$this->nome_classe_rel_list."</code>.<br>\n".
					"Não instancie diretamente esta classe; ao invés disso utilize a classe <code>FACTORY</code>. Ex: <code>\$objlist = ".$this->getComoInstanciarList()."</code>.";
		$jd->autor = geraClassesConfigAutor();
		return 
		$jd->get().
		"class ". $this->nome_classe_final_list . " extends ". $this->nome_classe_rel_list . " { \n".
		"}\n";
	}
}
?>