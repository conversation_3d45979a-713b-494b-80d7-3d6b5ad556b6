<?

/**************************************************************************************************************************
** Base do generator, contém arrays de todas as entidades, todas as classes geradas, testes de regressão, etc.
**************************************************************************************************************************/
class FORM_GENERATOR_BASE {
	var $doc; // O documento, para ser usado caso precise.
	var $bases; // Array de bases geradas;
	var $bases_list; // Array de bases geradas;
	var $rels; // Array de rels geradas;
	var $finals; // Array de finals geradas;
	var $forms; // Array de forms geradas;
	var $sufixo=null; // Eventual sufixo para classes isoladas
	var $namespace=null; // Eventual namespace para classes isoladas

	function init($dirbase="", $codebase_function, $codebase_function_classes, $codebase_function_classes_readonly, $options=null) {
		if (!$options) $this->options=array(); else $this->options=$options;
		if (isset($this->options['sufixo'])) $this->sufixo=$this->options['sufixo'];
		$this->geraClassesForms($this->doc);
	}

	function geraClassesForms($doc) {
		global $array_ent, $array_ent_num;
		$root = $doc->document_element();
		if ($root->tagname != "Modelo") {
			die("Elemento root deve ser um 'Modelo', erro.");
		}

		$entidades_element_array = $root->get_elements_by_tagname("Entidades");

		if (!$entidades_element = $entidades_element_array[0]) {
			die("Impossível achar elemento 'Entidades', erro.");
		} else {
			$entidades = $entidades_element->child_nodes();
			$array_ent = null; // mega-global hashtable
			$array_ent_num = null;

			$i = 0;
			foreach ($entidades as $entidade) {
				$class = new FORM_GENERATOR_ENTIDADE($entidade, $this->sufixo, $this->options);
				$array_ent[$i] = $class;
				//$array_ent_num[$class->nome_fis] = $i;
				$i++;
			}
		}

		/* Ordenemos as entidades! */
		usort($array_ent, "ordenacaoConsistenteEntidades");
		$i = 0;
		foreach ($array_ent as $ent) {
			$array_ent_num[$ent->nome_fis] = $i;
			$i++;
		}




		echo("******************************"."\n");
		$relats = $root->get_elements_by_tagname("Relacionamentos");
		$relats = $relats[0];
		$relats = $relats->child_nodes();

		foreach ($relats as $relat) {
			$class1 = new FORM_GENERATOR_RELACIONAMENTO($relat);
			$class1->setForParent($relat);
			$class2 = new FORM_GENERATOR_RELACIONAMENTO($relat);
			$class2->setForChild($relat);
		}

		initProgresso(  count($array_ent) );
		for ($i = 0; $i < count($array_ent); $i++) {
			$entidade             =& $array_ent[$i];
			$this->forms[]                = $this->geraForms($entidade);
			$this->entidades[]            =& $entidade;
			incrProg($i+1);
		}
	}

	// Gera DDLs, aggregando.

	function geraDDLs() {
		$this->dlls = null;
		foreach (DDL_GENERATOR::getGenerators() as $classe) {
			$destfinal = strtoupper("DDL_GENERATOR_".strtoupper($classe));
			$a = new $destfinal;
			$ddl = $a->init($this->entidades);
			$this->ddls[$classe]["obj"] = $a;
			$this->ddls[$classe]["ddl"] = $ddl;
		}
	}



	// Gera as Bases.
	function &geraBase(&$ent) {
		aggregate($ent, "PHP_GENERATOR_ENTIDADE_ONE");
		return $ent->geraBase();
	}

	function &geraBaseList(&$ent) {
		aggregate($ent, "PHP_GENERATOR_ENTIDADE_LIST");
		return $ent->geraBaseList();
	}

	function geraRel(&$ent) {
		aggregate($ent, "PHP_GENERATOR_ENTIDADE_REL");
		return $ent->geraRel();
	}

	function geraRelList(&$ent) {
		aggregate($ent, "PHP_GENERATOR_ENTIDADE_REL_LIST");
		return $ent->geraRelList();
	}

	function geraFinal(&$ent) {
		aggregate($ent, "PHP_GENERATOR_ENTIDADE_FINAL");
		return $ent->geraFinal();
	}

	function geraFinalList(&$ent) {
		aggregate($ent, "PHP_GENERATOR_ENTIDADE_FINAL_LIST");
		return $ent->geraFinalList();
	}

	/* Forms */
	function geraForms(&$ent){
		//aggregate($ent, "FORM_GENERATOR_ENTIDADE");
		return $ent->geraForms();
	}

	function setDoc($xml) {
		$this->doc = &$xml;
	}

}
?>
