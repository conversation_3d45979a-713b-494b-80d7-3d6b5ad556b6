<?php
/**************************************************************************************************************************
** Base do gerador de entidades; parseia tudo etc. O trabalho sujo ficam paras as classes que extendem essa.
**************************************************************************************************************************/
class PHP_GENERATOR_ENTIDADE {
	var $arGeomDtypes;
	var $nome_log;
	var $nome_fis;
	var $nome_classe_base;
	var $nome_classe_rel;
	var $nome_classe_final;
	var $nome_classe_base_list;
	var $nome_classe_rel_list;
	var $nome_classe_final_list;
	var $aster, $astercompleto;
	var $campos, $campos_pk, $campos_nao_pk; // array de campos
	var $parents;
	var $children;
	var $uniques;
	var $lista_funcs;
	var $sufixo='';
	var $sufclasses=false;
	var $namespace='';
	var $lf="\n";
	var $geraMsSqlServer=false;

	var $flash_nome_classe_final;
	var $flash_nome_classe_base;
	var $flash_nome_classe_final_full;
	var $flash_nome_classe_base_full;

	function __construct($ent, $sufx='', $sufclasses=false, $lf="\n", $fazMsSqlServer=false) {
		global $arGeomDtypes;
		$this->lf=$lf;
		$this->geraMsSqlServer=$fazMsSqlServer;
		$this->arGeomDtypes=$arGeomDtypes;
		$this->lista_funcs = array();
		$this->sufixo=$sufx;
		$this->sufclasses=$sufclasses;
		$this->fazNomes($ent);
		$this->populaXMLBase();
		$this->fazCampos($ent);
		$this->fazUniques($ent);
		$this->fazInversions($ent);
	}


	function populaXMLBase() {
		$root = &ndocGetRoot();
		$ndoc = &ndocGetDoc();
		$class = $ndoc->create_element("Classe");
		$class->set_attribute("nomefis", $this->nome_fis);
		$class->set_attribute("nomelog", $this->nome_classe_final);
		$root->append_child($class);
		ndocEmpilhaClass($this->nome_classe_final, $class);

		$classl = $ndoc->create_element("Classe");
		$classl->set_attribute("nomefis", $this->nome_fis);
		$classl->set_attribute("nomelog", $this->nome_classe_final_list);
		$root->append_child($classl);
		ndocEmpilhaClass($this->nome_classe_final_list, $classl);

	}

	function achaCampoDel() {
		foreach ($this->campos as $cc) {
			if ($cc->getStringTipagem() == "\"Bit\"") {
				if ( substr_count(strtoupper($cc->nome_fis), "_DEL") > 0) {
					return $cc;
				} else {
				}
			} else {
			}
		}
		return null;
	}

	function achaCampoDataIni() {
		foreach ($this->campos as $cc) {
			if ($cc->getStringTipagem() == "\"Date\"") {
				if ( substr_count(strtoupper($cc->nome_fis), "_DAT_HR_INI") > 0) {
					return $cc;
				} else {
				}
			} else {
			}
		}
		return null;
	}

	function achaCampoDataMod() {
		foreach ($this->campos as $cc) {
			if ($cc->getStringTipagem() == "\"Date\"") {
				if ( substr_count(strtoupper($cc->nome_fis), "_DAT_HR_MOD") > 0) {
					return $cc;
				} else {
				}
			} else {
			}
		}
		return null;
	}

	static function geraWarning($texto) {
		$cols = isset($_ENV["COLUMNS"])?$_ENV["COLUMNS"]:80;

		if (php_sapi_name() == "cli") {
			/* Desabilita essa frescura!
			$tmp = explode("".$this->lf."", wordwrap($texto, ($cols-5)));
			$i = 0;
			foreach ($tmp as $linha) {
				if ($i==0) {
					echo "**** $linha".$this->lf."";
				} else {
					echo "     $linha".$this->lf."";
				}
				$i++;
			}
			echo "".$this->lf."";
			 */
			echo "**** $texto\n";


		} else {
			echo nl2br("<LI>$texto</LI>");
		}
	}
	function fazNomes($ent) {
		$sufclasses=$this->sufclasses;
		//echo((int)$sufclasses."+++<br>");exit;
		$this->nome_log = strtoupper($ent->get_Attribute("NomeLogico"));
		$this->nome_fis = strtoupper($ent->get_Attribute("NomeFisico"));
		// nome da classe base...
		$this->nome_classe_base = $this->getNomePHP($this->nome_log)."_BASE".(@$sufclasses?$this->sufixo:'');
		$this->nome_classe_base_list = $this->getNomePHP($this->nome_log)."_BASE_LIST".(@$sufclasses?$this->sufixo:'');
		// nome da classe rel...
		$this->nome_classe_rel = $this->getNomePHP($this->nome_log)."_REL".(@$sufclasses?$this->sufixo:'');
		$this->nome_classe_rel_list = $this->getNomePHP($this->nome_log)."_REL_LIST".(@$sufclasses?$this->sufixo:'');
		// nome da final...
		$this->nome_classe_final = $this->getNomePHP($this->nome_log).(@$sufclasses?$this->sufixo:'');
		$this->nome_classe_final_list = $this->getNomePHP($this->nome_log)."_LIST".(@$sufclasses?$this->sufixo:'');
		// nome das coisas do flash.
		$this->flash_nome_classe_final = "gc".$this->getNomeFlash($this->nome_log);
		$this->flash_nome_classe_base = "gc".$this->getNomeFlash($this->nome_log)."_BASE";
		$this->flash_nome_classe_final_full = "gc.".$this->flash_nome_classe_final;
		$this->flash_nome_classe_base_full = "gc.ro.".$this->flash_nome_classe_base;
		$this->flash_nome_classe_peer_base = "gc".$this->getNomeFlash($this->nome_log."_PEER")."_BASE";
		$this->flash_nome_classe_peer_final = "gc".$this->getNomeFlash($this->nome_log."_PEER");
		$this->flash_nome_classe_peer_base_full = "gc.ro." . $this->flash_nome_classe_peer_base;
		$this->flash_nome_classe_peer_final_full = "gc.".$this->flash_nome_classe_peer_final;
		$this->flash_nome_classe_service_base = $this->getNomePHP($this->nome_log)."_SERVICE_BASE";
		$this->flash_nome_classe_service_final = $this->getNomePHP($this->nome_log)."_SERVICE";
		// flash, listas.
		$this->flash_nome_classe_list_final = "gc".$this->getNomeFlash($this->nome_log)."List";
		$this->flash_nome_classe_list_base = "gc".$this->getNomeFlash($this->nome_log)."_LIST_BASE";
		$this->flash_nome_classe_list_final_full = "gc.".$this->flash_nome_classe_list_final;
		$this->flash_nome_classe_list_base_full = "gc.ro.".$this->flash_nome_classe_list_base;
	}

	function fazCampos($ent) {
		// Itera pelos campos, gerando um array de objetos.
		$campos = $ent->get_elements_by_tagname("Campo");
		for ($q=0; $q < count($campos); $q++) {
			$campo = $campos[$q];
			$this->campos[] = new PHP_GENERATOR_CAMPO($campo, $this->nome_fis);
		}

		/* Ordenar $this->campos, utilizando ordenação consistente */
		usort($this->campos, "ordenacaoConsistenteCampos");

		/* alimenta o XML */
		$ndoc = &ndocGetDoc();
		$props = &ndocGetClassProps($this->nome_classe_final);
		foreach ($this->campos as $cam) {
			$prop = $ndoc->create_element("Prop");
			$prop->set_attribute("nomefis", $cam->nome_fis);
			$prop->set_attribute("nomelog", consertaEncoding($cam->nome_log));
			$prop->set_attribute("tipo", consertaEncoding($cam->getTipagemLimpo()));
			if ($cam->null_campo) $prop->set_attribute("NotNull", "true");
			if ($cam->ident_campo) $prop->set_attribute("AutoGen", "true");
			$props->append_child($prop);
		}


		/* Prepara uma string equivalente ao * do select.... */
		/* Vou isolar os campos geom também */
		$this->campos_geom = null;
		$this->aster = "";
		$this->astercompleto = "";
		$this->astercompletoalias = "";
		for ($i = 0; $i < count($this->campos); $i++) {
			if (in_array($this->campos[$i]->dt_campo, $this->arGeomDtypes)) {
				$this->campos_geom[] = &$this->campos[$i];
				continue;
			}
			$campo2 = &$this->campos[$i];
			if ($this->aster) $this->aster .= ", ";
			if ($this->astercompleto) $this->astercompleto .=", ";
			if ($this->astercompletoalias) $this->astercompletoalias .=", ";
			$this->aster .= $campo2->nome_fis;
			$this->astercompleto .= $this->nome_fis.".".$campo2->nome_fis;
			$this->astercompletoalias .= "\".\$alias.\".".$campo2->nome_fis;
		}

		/* Prepara uma string equivalente ao * do select, só que para o mssql - é para passar pela limitação de 255 para varchar no dblib */
		$this->asterMssql = "";
		$this->astercompletoMssql = "";
		$this->astercompletoaliasMssql = "";
		for ($i = 0; $i < count($this->campos); $i++) {
			if (in_array($this->campos[$i]->dt_campo, $this->arGeomDtypes)) continue;
			//if ($this->campos[$i]->dt_campo=='NVARCHAR')continue;
			$campo2 = &$this->campos[$i];
			if ($this->asterMssql) $this->asterMssql .= ", ";
			if ($this->astercompletoMssql) $this->astercompletoMssql .=", ";
			if ($this->astercompletoaliasMssql) $this->astercompletoaliasMssql .=", ";
			if ($campo2->dt_campo=='VARCHAR' && $campo2->dtl_campo>255){
				//echo($campo2->dt_campo."(".$campo2->dtl_campo.")".$this->lf."");
				$this->asterMssql .= "CAST(".$campo2->nome_fis." AS TEXT) AS ".$campo2->nome_fis;
				$this->astercompletoMssql .= "CAST(".$this->nome_fis.".".$campo2->nome_fis." AS TEXT) AS ".$campo2->nome_fis;
				$this->astercompletoaliasMssql .= "CAST("."\".\$alias.\".".$campo2->nome_fis." AS TEXT) AS ".$campo2->nome_fis;
			}else{
				$this->asterMssql .= $campo2->nome_fis;
				$this->astercompletoMssql .= $this->nome_fis.".".$campo2->nome_fis;
				$this->astercompletoaliasMssql .= "\".\$alias.\".".$campo2->nome_fis;
			}
		}

		// Busca Primary key. // Pode existir apenas uma primary key, com n (n>0) campos.
		$this->campos_pk = null;
		$this->campos_nao_pk = null;

		if ($pks = $ent->get_Elements_By_TagName("ConstraintPK")) {
			$pk = $pks[0];
			$campos = $pk->get_Elements_By_TagName("Coluna");
			for ($rr = 0; $rr < count($this->campos); $rr++) {
				$cc = &$this->campos[$rr];
				// para cada campo da tabela
				$ehpk = false;
				for ($q=0; $q < count($campos); $q++) {
					// procura se o tal campo é da pk ou não
					$campo = $campos[$q];
					$nomecampopk = $campo->get_Attribute("Nome");
					if (strtoupper($nomecampopk) == strtoupper($cc->nome_fis)) $ehpk = true;
				}
				if ($ehpk) {
					$this->campos_pk[] = &$cc;
				} else {
					$this->campos_nao_pk[] = &$cc;
				}
			}
		}

		/* Ordenar $this->campos_pk e campos_nao_pk, utilizando ordenação consistente */
		if ($this->campos_pk) usort($this->campos_pk, "ordenacaoConsistenteCampos");
		if ($this->campos_nao_pk) usort($this->campos_nao_pk, "ordenacaoConsistenteCampos");
	}

	function fazUniques(&$ent) {
		// Busca constraints de unique. podem existir n uniques, com w (w>0) campos cada.
		$this->uniques = null;
		if ($pks = $ent->get_Elements_By_TagName("ConstraintUnique")) {
			for ($q=0; $q < count($pks); $q++) {
				$pk = $pks[$q];
				$campos = null;
				/* @TODO: reescrever, de modo a iterar pelo $this->campos, e pegar os campos na ordem certa (ordem do $this->campos) */
				$campos = $pk->get_Elements_By_TagName("Coluna");
				$campo_obj=null;
				for ($w=0; $w < count($campos); $w++) {
					$campo = $campos[$w];
					$campo_id = $campo->get_Attribute("CampoID");
					$campo_nome = $campo->get_Attribute("Nome");
					$campo_obj[] =& getCampoEntidadeRef($this, strtoupper($campo_nome)); //DFP
					//$campo_obj[] =& getCampoEntidadeRef(&$this, $campo_nome);
				}
				// Ordenação.
				if ($campo_obj) usort($campo_obj, "ordenacaoConsistenteCampos");
				$this->uniques[] = $campo_obj;
			}
		}
	}

	function fazInversions(&$ent) {
		// Busca constraints de inversion. podem existir n uniques, com w (w>0) campos cada.
		$this->inversions = null;
		if ($pks = $ent->get_Elements_By_TagName("ConstraintInversion")) {
			for ($q=0; $q < count($pks); $q++) {
				$pk = $pks[$q];
				/* @TODO: reescrever, de modo a iterar pelo $this->campos, e pegar os campos na ordem certa (ordem do $this->campos) */
				$campos = $pk->get_Elements_By_TagName("Coluna");
				$campo_obj=null;
				$campo_obj["nome"]=$pk->get_Attribute("Nome");
				for ($w=0; $w < count($campos); $w++) {
					$campo = $campos[$w];
					$campo_id = $campo->get_Attribute("CampoID");
					$campo_nome = $campo->get_Attribute("Nome");
					$campo_obj["campos"][] =& getCampoEntidadeRef($this, strtoupper($campo_nome)); //DFP
					//$campo_obj["campos"][] =& getCampoEntidadeRef(&$this, $campo_nome);
				}
				// Ordenação.
				if ($campo_obj) usort($campo_obj["campos"], "ordenacaoConsistenteCampos");
				$this->inversions[] = $campo_obj;
			}
		}
	}

	function getNomePHP($nome) {
		return str_replace(" ", "_", str_replace("-", "_", $nome));
	}

	function getNomeFlash($nome) {
		/* Vejamos. Não gostei dos underscore. */
		$under = str_replace(" ", "_", str_replace("-", "_", $nome));
		$partes = explode("_", $under);
		$acumula = "";
		foreach ($partes as $parte) {
			$acumula .= ucwords(strtolower($parte));
		}
		return $acumula;
	}

	function getComoInstanciarOne() {
		return "&FACTORY".$this->sufixo."::" . $this->getNomePHP($this->nome_classe_final)."()";
	}

	function getComoInstanciarList() {
		return "&FACTORY".$this->sufixo."::" . $this->getNomePHP($this->nome_classe_final_list)."()";
	}


	function geraBase() {
		$jd = new JD();
		$jd->desc = "Representa uma linha (um registro) da tabela <code>". $this->nome_fis . "</code>.<br>".$this->lf."".
					"Serve para inserir, alterar, excluir ou selecionar uma linha dessa tabela, entre outros.<br>".$this->lf."".
					"Esta classe é extendida pela <code>" . $this->nome_classe_rel . "</code>, que é extendida pela <code>". $this->nome_classe_final. "</code>.<br>".$this->lf."".
					"Por favor <b>não modifique esta classe</b>. Faça implementações apenas na classe <code>" . $this->nome_classe_final. "</code>.<br>".$this->lf."".
					"Não instancie diretamente esta classe; ao invés disso utilize a classe <code>".$this->nome_classe_final."</code> através da <code>FACTORY</code>. Ex: <code>\$obj = ".$this->getComoInstanciarOne()."</code>.";
		$jd->autor = geraClassesConfigAutor();
		return
		$jd->get().
		"class " . $this->nome_classe_base . " extends gc_base {".$this->lf."".
		$this->geraDeclAsters().
		$this->geraDeclVars($this->campos_pk, "PK").
		$this->geraDeclVars($this->campos_nao_pk).
		$this->geraConstructor().
		$this->geraGetDB().
		$this->geraMethUpdate().
		$this->geraMethDelete().
		$this->geraMethLoadBySQL().
		$this->geraMethLoadFields().
		$this->geraMethLoadGisFields().
		$this->geraMethLoadByPK().
		$this->geraMethInsert().
		$this->geraMethHardInsert().
		/* $this->geraMethOverload().  // @TODO: overloading */
		"}".$this->lf."".
		"".$this->lf."";
	}

	function geraMethUpdate() {
		if (   (!is_array($this->campos_nao_pk))  |   (!is_array($this->campos_pk))  ) return "";

		/* @TODO: O que diabos é isso? */
		if (count($this->campos_nao_pk) == 1){
			if ($ccini = $this->achaCampoDataIni()){
				if (@$ccini->nome_fis == $this->campos_nao_pk[0]->nome_fis){
					return '';
				}
			}
		}
		/*
		if ((count($this->campos_nao_pk) == 1) & ($ccini = $this->achaCampoDataIni()) & (@$ccini->nome_fis == $this->campos_nao_pk[0]->nome_fis) ) {
			return "";
		}
		*/
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Atualiza o registro. As chaves primárias deverão estar definidas. Usa-se comumente após ter carregado o registro com o método load(). Retorna true se bem sucedido.";
		if ($ccmod = $this->achaCampoDataMod()) { $jd->desc .= " Note que este método trabalha automaticamente com o campo ".$ccmod->nome_log.", setando-o para a data/hora da execução do update()."; }
		$jd->returns = "boolean";
		$s .= $jd->get(1);

		/* XMLref */
		$meth = &ndocCreateMeth($this->nome_classe_final, "update", "boolean", "Atualiza o registro");

		$s .= "\tfunction update() {".$this->lf."";
		$s .= "\t\t\$db = &\$this->getDB();".$this->lf."";

		// Vamos achar o campo mod.
		if ($ccmod) {
			$s .= "\t\t".$ccmod->getStringThis(null, false)." = time();".$this->lf."";
		} else {
			//$this->geraWarning("Entidade $this->nome_log (tbl $this->nome_fis) não tem campo _DAT_HR_MOD.");
		}
		$s .= "\t\tif (function_exists('logadorClasses".$this->sufixo."')) \$orig=logadorClasses".$this->sufixo."('orig', \$this);".$this->lf."";
		$lista_campos = null;
		foreach ($this->campos_nao_pk as $cc) {
			if (in_array($cc->dt_campo, $this->arGeomDtypes)) continue;
			if ($cc->dt_campo=='NVARCHAR')continue;
			$lista_campos[] = "$cc->nome_fis = ?";
			$vals[] = array($cc->getStringTipagem(), "\$this->".$cc->getNome($this->nome_fis));
		}
		if (!$lista_campos) return '';
		$lista_campos = implode(",", $lista_campos);

		$lista_where = null;
		foreach ($this->campos_pk as $cc) {
			$lista_where[] = "$cc->nome_fis = ?";
			$vals[] = array($cc->getStringTipagem(), "\$this->".$cc->getNome($this->nome_fis));
		}
		$lista_where = implode(" AND ", $lista_where);

		$arrproc = null;
		foreach ($vals as $val) {
			$arrproc[] = "array(".$val[0].",".$val[1].")";
		}
		$arrvals = "array(".implode(",", $arrproc).")";

		$sql = "UPDATE $this->nome_fis SET $lista_campos WHERE $lista_where";

		$s .= "\t\t\$sql = \"$sql\";".$this->lf."";
		$s .= "\t\tif (!\$db->ExecuteBind(\$sql, $arrvals)) {".$this->lf."";
		$s .= "\t\t\t\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."";
		$s .= "\t\t\treturn false;".$this->lf."";
		$s .= "\t\t}".$this->lf."";
		$s .= "\t\tif (empty(\$orig)==false) logadorClasses".$this->sufixo."('update', \$this, \$orig);".$this->lf."";
		$s .= "\t\treturn true;".$this->lf."";
		$s .= "\t}".$this->lf."".$this->lf."";
		return $s;
	}



	function geraMethDelete() {
		if (    (!is_array($this->campos_pk))  ) return "";
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Exclui o registro. As chaves primárias deverão estar definidas. Retorna true se bem sucedido.";
		if ($ccdel = $this->achaCampoDel()) { $jd->desc .= " Note que este método trabalha automaticamente com o campo ".$ccdel->nome_log.", setando-o para a true na hora da execução do delete() e não excluindo o registro. Se definido o parametro \"force\" da função, o registro é excluído definitivamente."; }
		$jd->returns = "boolean";
		$s .= $jd->get(1);

		/* XMLref */
		$meth = &ndocCreateMeth($this->nome_classe_final, "delete", "boolean", "Exclui o registro ".($ccdel?'logicamente':'fisicamente'));

		$s .= "\tfunction delete(\$force=false) {".$this->lf."";
		$s .= "\t\t\$db = &\$this->getDB();".$this->lf."";
		$s .= "\t\tif (function_exists('logadorClasses".$this->sufixo."')) \$orig=logadorClasses".$this->sufixo."('orig', \$this);".$this->lf."";
		$lista_where = null;
		foreach ($this->campos_pk as $cc) {
			$lista_where[] = "$cc->nome_fis = ?";
			$vals[] = array($cc->getStringTipagem(), "\$this->".$cc->getNome($this->nome_fis));
		}
		$lista_where = implode(" AND ", $lista_where);

		$arrproc = null;
		//if ($ccdel) $arrproc[] = "array('Bit',true)";
		foreach ($vals as $val) {
			$arrproc[] = "array(".$val[0].",".$val[1].")";
		}
		$arrvals = implode(",", $arrproc);
		if ($ccdel){
			$s .= "\t\tif(\$force==true){".$this->lf."";
			$sql = "DELETE FROM $this->nome_fis WHERE $lista_where";
			$s .= "\t\t\t\$sql = \"$sql\";".$this->lf."";
			$s .= "\t\t\tif (!\$db->ExecuteBind(\$sql, array($arrvals))) {".$this->lf."";
			$s .= "\t\t\t\t\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."";
			$s .= "\t\t\t\treturn false;".$this->lf."";
			$s .= "\t\t\t}".$this->lf."";
			$s .= "\t\t}else{".$this->lf."";
			$sql = "UPDATE $this->nome_fis SET $ccdel->nome_fis = ? WHERE $lista_where";
			$s .= "\t\t\t\$sql = \"$sql\";".$this->lf."";
			$s .= "\t\t\tif (!\$db->ExecuteBind(\$sql, array(array('Bit',true), $arrvals))) {".$this->lf."";
			$s .= "\t\t\t\t\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."";
			$s .= "\t\t\t\treturn false;".$this->lf."";
			$s .= "\t\t\t}".$this->lf."";
			$s .= "\t\t}".$this->lf."";
		}else{
			$sql = "DELETE FROM $this->nome_fis WHERE $lista_where";
			$s .= "\t\t\$sql = \"$sql\";".$this->lf."";
			$s .= "\t\tif (!\$db->ExecuteBind(\$sql, array($arrvals))) {".$this->lf."";
			$s .= "\t\t\t\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."";
			$s .= "\t\t\treturn false;".$this->lf."";
			$s .= "\t\t}".$this->lf."";
		}
		$s .= "\t\tif (empty(\$orig)==false) logadorClasses".$this->sufixo."('delete', \$orig);".$this->lf."";
		$s .= "\t\treturn true;".$this->lf."";
		$s .= "\t}".$this->lf."".$this->lf."";
		return $s;
	}


	function geraMethLoadBySQL() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Carrega um registro, utilizando um SQL abitrario. Retorna true se foi possível carregar o registro; os valores foram parar nas propriedades do objeto.";
		$jd->addParam("sql", "obrigatorio. O SQL a ser executado. pode conter zero ou mais '?'");
		$jd->addParam("vals", "opcional; array dos valores. cada item é um array de tipo e valor.");
		$jd->returns = "boolean";
		$s .= $jd->get(1);
		/* XMLref */
		$meth = &ndocCreateMeth($this->nome_classe_final, "load", "boolean", "Carrega um registro, utilizando um SQL abitrario");
		ndocCreateParam($meth, "sql", "string", "obrigatorio. O SQL a ser executado. pode conter zero ou mais '?'");
		ndocCreateParam($meth, "vals", "array", "opcional; array dos valores. cada item é um array de tipo e valor.");
		$s .= "	function load(\$sql, \$vals=null, \$types=null) {".$this->lf."";
		$s .= "		\$db = &\$this->getDB();".$this->lf."";
		$s .= "		if (\$rec = &\$db->ExecuteBind(\$sql, \$vals, \$types)) {".$this->lf."";
		$s .= "			if (!\$rec->EOF) {".$this->lf."";
		$s .= "				\$this->loadFields(\$rec);".$this->lf."";
		/*
		$counter = 0;
		foreach ($this->campos as $cc) {
			if ($cc->dt_campo=='NVARCHAR')continue;
			$s .= "				\$this->" . $cc->getNome() . " = " . $cc->getStringTira("\$rec->fields[", "]", true, $counter) . ";".$this->lf."";
			$counter++;
		}
		*/
		$s .= "				return true;".$this->lf."";
		$s .= "			} else {".$this->lf."";
		$s .= "				return false;".$this->lf."";
		$s .= "			}".$this->lf."";
		$s .= "		} else {".$this->lf."";
		$s .= "			\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."";
		$s .= "			return false;".$this->lf."";
		$s .= "		}".$this->lf."";
		$s .= "	}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraMethLoadFields() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Carrega os valores de um registro para as propriedades do objeto";
		$jd->addParam("rec", "obrigatorio. o recordset de onde carregar os valores '?'");
		$jd->returns = "";
		$s .= $jd->get(1);
		/* XMLref */
		$meth = &ndocCreateMeth($this->nome_classe_final, "loadFields", "", "Carrega os valores de um registro");
		ndocCreateParam($meth, "rec", "recordset", "obrigatorio. O recordset a partir de onde carregar");
		$s .= "	function loadFields(&\$rec) {".$this->lf."";
		$s .= "\t\t\$db = &\$this->getDB();".$this->lf."";
		$counter = 0;
		foreach ($this->campos as $cc) {
			if (in_array($cc->dt_campo, $this->arGeomDtypes)) continue;
			$s .= "		\$this->" . $cc->getNome() . " = " . $cc->getStringTira("\$rec->fields[", "]", true, $counter) . ";".$this->lf."";
			$counter++;
		}
		$s .= "	}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraMethLoadGisFields() {
		$s  = "";
		foreach ($this->campos as $cc) {
			if (!in_array($cc->dt_campo, $this->arGeomDtypes)) continue;
			$nomeMeth='get_'.$cc->getNome();
			$nomeMethSet='set_'.$cc->getNome();
			$params = null;
			$paramsThis = null;
			$wh = null; $vals = null;
			$meth = &ndocCreateMeth($this->nome_classe_final, $nomeMeth, "boolean", "Carrega um valor de campo GIS a partir da chave primária");
			if (empty($this->campos_pk)) {
				echo nl2br(print_r($this, true))."<br/>";
			}
			foreach ($this->campos_pk as $ccpk) {
				$params[] = "\$".$ccpk->getNome($this->nome_fis);
				$paramsThis[] = "\$this->".$ccpk->getNome($this->nome_fis);
				$wh[] = $ccpk->nome_fis . " = ? ";
				$vals[] = array($ccpk->getStringTipagem(), "\$this->".$ccpk->getNome($this->nome_fis));
			}
			$wh = implode(" AND ", $wh);
			$ccmod = $this->achaCampoDataMod();
			$arrprocget = null;
			foreach ($vals as $val) {
				$arrprocget[] = "array(".$val[0].",".$val[1].")";
			}
			if (@$ccdel) $arrproc[] = "array('Bit',true)";
			$arrvalsget = "array(".implode(",", $arrprocget).")";
			$arrprocset = null;
			if ($ccmod) $arrprocset[] = "array('date',time())";
			foreach ($vals as $val) {
				$arrprocset[] = "array(".$val[0].",".$val[1].")";
			}
			$arrvalsset = "array(".implode(",", $arrprocset).")";

			$sql = "SELECT \".\$modifier.\" FROM " . $this->nome_fis . " WHERE $wh";

			// Vamos achar o campo mod.
			if ($ccmod) {
				$sqlSet = "UPDATE " . $this->nome_fis . " SET ".$cc->getNome()."=\".(is_null(\$geom)?'null':\$geom).\", ".$ccmod->getNome()."=? WHERE $wh";
			}else{
				$sqlSet = "UPDATE " . $this->nome_fis . " SET ".$cc->getNome()."=\".(is_null(\$geom)?'null':\$geom).\" WHERE $wh";
			}
			$params_test = null;
			foreach ($paramsThis as $paramThis) {
				$params_test[] = "isset(".$paramThis.")";
			}
			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Carrega um valor de campo GIS de uma instancia. Retorna true se foi possível carregar o valor; o valor vai parar na propriedade do objeto.";
			$jd->addParam("mod", "opcional. O modificador do formato do campo GIS");
			$jd->returns = "boolean";
			$s .= $jd->get(1);
			$s .= "	function ".$nomeMeth."(\$modifier='') {".$this->lf."";
			$s .= "\t\tglobal \$sqlCompleto;".$this->lf."";
			$s .= "		if (!(".implode(" && ", $params_test).")) return null;".$this->lf."";
			$s .= "		if (strpos(\$modifier, '?')!==false) \$modifier=str_replace('?', '".$cc->getNome()."', \$modifier); else \$modifier.='(".$cc->getNome().")';".$this->lf."";
			$s .= "		\$db = &\$this->getDB();".$this->lf."";
			$s .= "		\$sql = \"".$sql."\";".$this->lf."";
			$s .= "		if (!\$rs=\$db->ExecuteBind(\$sql, ".$arrvalsget.")){".$this->lf."";
			$s .= "			\$db->_raiseError(__FILE__, __LINE__, (@\$sqlCompleto?\$sqlCompleto:\$sql));".$this->lf."";
			$s .= "			return false;".$this->lf."";
			$s .= "		}".$this->lf."";
			$s .= "		\$this->".$cc->getNome()."=\$rs->fields[0];".$this->lf."";
			$s .= "		return true;".$this->lf."";
			$s .= "	}".$this->lf."".$this->lf."";

			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Insere um valor de campo GIS de uma instancia. Retorna true se foi possível inserir o valor.";
			$jd->returns = "boolean";
			$s .= "	function ".$nomeMethSet."(\$geom) {".$this->lf."";
			$s .= "		global \$sqlCompleto;".$this->lf."";
			$s .= "		if (!(".implode(" && ", $params_test).")) return null;".$this->lf."";
			$s .= "		\$db = &\$this->getDB();".$this->lf."";
			$s .= "		if (function_exists('logadorClasses".$this->sufixo."')) \$orig=logadorClasses".$this->sufixo."('geom@".$cc->getNome()."', \$this);".$this->lf."";
			$s .= "		\$sql = \"".$sqlSet."\";".$this->lf."";
			$s .= "		if (!\$db->ExecuteBind(\$sql, ".$arrvalsset.")){".$this->lf."";
			$s .= "			\$db->_raiseError(__FILE__, __LINE__, (@\$sqlCompleto?\$sqlCompleto:\$sql));".$this->lf."";
			$s .= "			return false;".$this->lf."";
			$s .= "		}".$this->lf."";
			$s .= "		if (empty(\$orig)==false) logadorClasses".$this->sufixo."('updategeom@".$cc->getNome()."', \$this, \$orig);".$this->lf."";
			$s .= "		return true;".$this->lf."";
			$s .= "	}".$this->lf."".$this->lf."";

		}

		return $s;
	}

	function geraMethLoadByPK() {
		if ((!is_array($this->campos_pk))) return "";
		$params = null;
		$ccdel = $this->achaCampoDel();
		$wh = null; $vals = null;
		$meth = &ndocCreateMeth($this->nome_classe_final, "loadByPk", "boolean", "Carrega um registro a partir da chave primária");
		foreach ($this->campos_pk as $cc) {
			$params[] = "\$".$cc->getNome($this->nome_fis);
			ndocCreateParam($meth, $cc->getNome($this->nome_fis), $cc->getTipagemLimpo(), $cc->nome_log);
			$wh[] = $cc->nome_fis . " = ? ";
			$vals[] = array($cc->getStringTipagem(), "\$".$cc->getNome($this->nome_fis));
		}
		$wh = implode(" AND ", $wh);

		$arrproc = null;
		foreach ($vals as $val) {
			$arrproc[] = "array(".$val[0].",".$val[1].")";
		}
		if ($ccdel) $arrproc[] = "array('Bit',true)";
		$arrvals = "array(".implode(",", $arrproc).")";

		$sql = "SELECT \".\$this->getAster().\" FROM " . $this->nome_fis . " WHERE $wh";
		if ($ccdel) $sql.= " AND (".$ccdel->nome_fis." <> ? OR ".$ccdel->nome_fis." IS NULL)";
		$params_test = null;
		foreach ($params as $param) {
			$params_test[] = "isset(".$param.")";
		}
		$params = implode(",", $params);




		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Carrega um registro a partir da chave primária. Retorna true se foi possível carregar o registro; os valores vão parar nas propriedades do objeto.";
		if ($ccdel) {
			$jd->desc .= " Note que este método trabalha automaticamente com o campo ".$ccdel->nome_log.", carregando apenas os não deletados (".$ccdel->nome_log." <> true)";
			$jd->desc .= " Se passado o segundo parâmetro opcional como true, carrega qualquer registro";
		}
		foreach ($this->campos_pk as $cc) {
			$jd->addParam($cc->getNome($this->nome_fis), $cc->nome_log);
		}
		$jd->returns = "boolean";
		$s .= $jd->get(1);
		$s .= "	function loadByPk(".$params.($ccdel?", \$loadDeleted=false":"").") {".$this->lf."";
		$s .= "		if (!(".implode(" && ", $params_test).")) return null;".$this->lf."";
		$s .= "		\$db = &\$this->getDB();".$this->lf."";
		$s .= "		return \$this->load(\"".$sql."\", $arrvals);".$this->lf."";
		$s .= "	}".$this->lf."".$this->lf."";
		return $s;
	}


	function geraMethOverload() {
		$s  = "";
		$s .= "	function __get(\$prop_name, &\$prop_value) {".$this->lf."";
		//$s .= "	echo \"get na '".$this->nome_log."',\$prop_name <BR>\".$this->lf."\";".$this->lf."";
		$s .= "		switch (\$prop_name) {".$this->lf."";
		foreach ($this->campos as $cc) {
			if ($cc->isNativo($this->nome_fis)) {
				$s .= "			case '".$cc->getNomeCurto()."':".$this->lf."";
				$s .= "				\$prop_value =& \$this"."->".$cc->getNome()."; return true; ".$this->lf."";
				$s .= "				break;".$this->lf."";
			}
		}
		$s .= "		}".$this->lf."";
		$s .= "		\$prop_value =& \$this->\$prop_name;".$this->lf."";
		$s .= "		return true;".$this->lf."";
		$s .= "	}".$this->lf."";
		return $s;
	}

	function geraMethInsert() {
		if ((!is_array($this->campos_pk))) return "";
		$descini='';
		$descmod='';
		// Vamos achar o campo ini.
		$s='';
		if ($ccini = $this->achaCampoDataIni()) {
			$s .= "\t\t".$ccini->getStringThis(null, false)." = time();".$this->lf."";
			$descini = "Atenção: Este método altera automaticamente o campo ".$ccini->nome_log." para a data atual.";
		} else {
			//$this->geraWarning("Entidade $this->nome_log (tbl $this->nome_fis) não tem campo _DAT_HR_INI.");
		}
		if ($ccmod = $this->achaCampoDataMod()) {
			$s .= "\t\t".$ccmod->getStringThis(null, false)." = time();".$this->lf."";
			$descmod = "Atenção: Este método altera automaticamente o campo ".$ccmod->nome_log." para a data atual.";
		} else {
			//$this->geraWarning("Entidade $this->nome_log (tbl $this->nome_fis) não tem campo _DAT_HR_INI.");
		}

		// para o caso de banco A: omitir o campo autonumber, retornar o insert id
		$lista_campos = null; $lista_valores = null; $vals=null; $types=null; $campos_geom_insert = null;
		$preGeom="\t\t\$camposGeom='';".$this->lf."";
		$preGeom.="\t\t\$valsGeom='';".$this->lf."";
		$sqlGeomFields="";
		$sqlGeomValues="";
		foreach ($this->campos as $cc) {
			if (in_array($cc->dt_campo, $this->arGeomDtypes)) {
				//echo (print_r($cc, true))."<br/>";
				if (@$cc->null_campo) {
					$campos_geom_insert[] = $cc->nome_fis;
				}
				$preGeom.="\t\tif (empty(".$cc->getStringThis(null, false).")==false){".$this->lf."";
				$preGeom.="\t\t\t\$camposGeom.=', GEOP1_GEOM';".$this->lf."";
				$preGeom.="\t\t\t\$valsGeom.=', '.".$cc->getStringThis(null, false).";".$this->lf."";
				$preGeom.="\t\t}".$this->lf."";
				continue;
			}
			if ($cc->dt_campo=='NVARCHAR')continue;
			if (!$cc->ident_campo) {
				$lista_campos[] = $cc->nome_fis;
				$lista_valores[] = "?";
				$vals[] = $cc->getStringThis(null, false);  //$vals[] = $cc->getStringMandaSQL(null, false);
				$types[] = $cc->getStringTipagem();
			}
		}
		$lista_campos = implode(", ", $lista_campos);
		$lista_valores = implode(", ", $lista_valores);
		if (is_null($campos_geom_insert)){
			$sqla = "INSERT INTO ".$this->nome_fis." "."(".$lista_campos.")"."VALUES"."(".$lista_valores.")";
		}else{
			$sqla = "INSERT INTO ".$this->nome_fis." "."(".$lista_campos."\".\$camposGeom.\")"."VALUES"."(".$lista_valores."\".\$valsGeom.\")";
		}
		$valsa = "array(" . implode (",", $vals).")";
		$typesa = "array(" . implode (",", $types).")";

		// para o caso de banco B: Obter sequencia, retornar sequencia
		// aproveitamos para pegar os campos geometria e geografia, para inserir junto caso estejam preenchidos e sejam not null
		$lista_campos = null; $lista_valores = null;$seq = null; $vals=null; $types = null;
		foreach ($this->campos as $cc) {
			if (in_array($cc->dt_campo, $this->arGeomDtypes)) continue;
			if ($cc->dt_campo=='NVARCHAR')continue;
			$lista_campos[] = $cc->nome_fis;
			if ($cc->ident_campo) {
				$seq = $cc;
				$seqthis = $cc->getStringThis(null, false);
				$vals[] = "\$seq";
				$types[] = "\"Integer\"";
			} else {
				$vals[] = $cc->getStringThis(null, false); //$cc->getStringMandaSQL(null, false);
				$types[] = $cc->getStringTipagem();
			}
			$lista_valores[] = "?";
		}
		$lista_campos = implode(", ", $lista_campos); $lista_valores = implode(", ", $lista_valores);
		if (is_null($campos_geom_insert)) {
			$preGeom='';
		}else{
			$sqlGeomFields="\".\$camposGeom.\"";
			$sqlGeomValues="\".\$valsGeom.\"";
		}
		if (false && $this->nome_fis=='TB1_GEOP') {
			var_dump($preGeom);
		}
		$sqlb = "INSERT INTO ".$this->nome_fis.""."(".$lista_campos.$sqlGeomFields.")"."VALUES"."(".$lista_valores.$sqlGeomValues.")";
		$valsb = "array(" . implode (",", $vals).")";
		$typesb = "array(" . implode (",", $types).")";
		$meth = &ndocCreateMeth($this->nome_classe_final, "insert", "boolean", "Insere um novo registro");


		if (!$seq) {
			// se não achamos sequencia, vai ser sempre o caso A.
			$s  = "";
			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Insere um novo registro. Retorna true se bem sucedido. $descini $descmod";
			$jd->returns = "boolean";
			$s .= $jd->get(1);
			$s .= "\tfunction insert() {".$this->lf."";
			$s .= "\t\tglobal \$sqlCompleto;".$this->lf."";
			$s .= "\t\t\$db = &\$this->getDB();".$this->lf."";
			if ($ccini)	$s .= "\t\t".$ccini->getStringThis(null, false)." = time();".$this->lf."";
			if ($ccmod)	$s .= "\t\t".$ccmod->getStringThis(null, false)." = time();".$this->lf."";
			$s .= $preGeom;
			$s .=
			"\t\t\$sql = \"".$sqla. "\";".$this->lf."".
			"\t\tif (\$db->ExecuteBind(\$sql, $valsa, $typesa)) {".$this->lf."".

			"\t\tif (function_exists('logadorClasses".$this->sufixo."')) logadorClasses".$this->sufixo."('insert', \$this);".$this->lf."".

			"\t\t\treturn true;".$this->lf."".
			"\t\t} else {".$this->lf."".
			"\t\t\t\$db->_raiseError(__FILE__, __LINE__, (@\$sqlCompleto?\$sqlCompleto:\$sql));".$this->lf."".
			"\t\t\treturn false;".$this->lf."".
			"\t\t}".$this->lf."";
		} else {
			// se achamos, vai ter os dois.
			$s  = "";
			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Insere um novo registro. Retorna o valor do campo ".$seq->nome_log." do novo registro se bem sucedido. $descini $descmod";
			$jd->returns = "int";
			$s .= $jd->get(1);
			$s .= "\tfunction insert() {".$this->lf."";
			$s .= "\t\tglobal \$sqlCompleto;".$this->lf."";
			$s .= "\t\t\$db = &\$this->getDB();".$this->lf."";
			if ($ccini)	$s .= "\t\t".$ccini->getStringThis(null, false)." = time();".$this->lf."";
			if ($ccmod)	$s .= "\t\t".$ccmod->getStringThis(null, false)." = time();".$this->lf."";
			$s .= $preGeom;
			$s .=
			"\t\tif (\$db->usaSeq()) {".$this->lf."".
				"\t\t\t\$seq = \$db->getSeq(\"".$seq->tabela_nativa."\", \"".$seq->nome_fis."\");".$this->lf."".
				"\t\t\t\$sql = \"".$sqlb. "\";".$this->lf."".
				"\t\t\tif (\$db->ExecuteBind(\$sql, $valsb, $typesb)) {".$this->lf."".
				"\t\t\t\t$seqthis = \$seq;".$this->lf."". // Feed into myself.
				"\t\t\t\tif (function_exists('logadorClasses".$this->sufixo."')) logadorClasses".$this->sufixo."('insert', \$this);".$this->lf."".
				"\t\t\t\treturn \$seq;".$this->lf."".
				"\t\t\t} else {".$this->lf."".
				"\t\t\t\t\$db->_raiseError(__FILE__, __LINE__, (@\$sqlCompleto?\$sqlCompleto:\$sql));".$this->lf."".
				"\t\t\t\treturn false;".$this->lf."".
				"\t\t\t}".$this->lf."".
			"\t\t} else {"."".$this->lf."".
				"\t\t\t\$sql = \"".$sqla. "\";".$this->lf."".
				"\t\t\tif (\$db->ExecuteBind(\$sql, $valsa, $typesa)) {".$this->lf."".
				"\t\t\t\t$seqthis = \$db->Insert_ID();".$this->lf."". // Feed into myself
				"\t\t\t\tif (function_exists('logadorClasses".$this->sufixo."')) logadorClasses".$this->sufixo."('insert', \$this);".$this->lf."".
				"\t\t\t\treturn $seqthis;".$this->lf."".
				"\t\t\t} else {".$this->lf."".
				"\t\t\t\t\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."".
				"\t\t\t\treturn false;".$this->lf."".
				"\t\t\t}".$this->lf."".
			"\t\t}"."".$this->lf."";
		}
		$s .= "\t}".$this->lf."".$this->lf."";
		if (false && $this->nome_fis=='TB1_GEOP') {
			echo ($s)."<br/>";
			die('aqui '.$this->nome_fis);
		}
		return $s;
	}

	/* hardInsert() é utilizado para inserir 'na marra', sem considerar sequências. */
	function geraMethHardInsert() {
		if ((!is_array($this->campos_pk))) return "";

		// para o caso de banco B: Obter sequencia, retornar sequencia
		$lista_campos = null; $lista_valores = null;$seq = null; $vals=null; $types = null;
		foreach ($this->campos as $cc) {
			if (in_array($cc->dt_campo, $this->arGeomDtypes)) continue;
			if ($cc->dt_campo=='NVARCHAR')continue;
			$lista_campos[] = $cc->nome_fis;
			$vals[] = $cc->getStringThis(null, false); //$cc->getStringMandaSQL(null, false);
			$types[] = $cc->getStringTipagem();
			$lista_valores[] = "?";
		}
		$lista_campos = implode(", ", $lista_campos); $lista_valores = implode(",", $lista_valores);
		$sqlb = "INSERT INTO ".$this->nome_fis.""."(".$lista_campos.")"."VALUES"."(".$lista_valores.")";
		$valsb = "array(" . implode (", ", $vals).")";
		$typesb = "array(" . implode (",", $types).")";
		$meth = &ndocCreateMeth($this->nome_classe_final, "hardInsert", "boolean", "Insere diretamente um novo registro");

		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Insere diretamente um novo registro, sem tratamentos especiais. Retorna true se bem sucedido.";
		$jd->returns = "int";
		$s .= $jd->get(1);
		$s .= "\tfunction hardInsert() {".$this->lf."";
		$s .= "\t\t\$db = &\$this->getDB();".$this->lf."";
		$s .=
			"\t\t\$sql = \"".$sqlb. "\";".$this->lf."".
			"\t\tif (\$db->ExecuteBind(\$sql, $valsb, $typesb)) {".$this->lf."".
			"\t\t\tif (function_exists('logadorClasses".$this->sufixo."')) logadorClasses".$this->sufixo."('insert', \$this);".$this->lf."".
			"\t\t\treturn true;".$this->lf."".
			"\t\t} else {".$this->lf."".
			"\t\t\t\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."".
			"\t\t\treturn false;".$this->lf."".
			"\t\t}"."".$this->lf."";
		$s .= "\t}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraBaseList() {
		$jd = new JD();
		$jd->desc = "Representa um conjunto linhas (de 0 a n registros) da tabela <code>". $this->nome_fis . "</code>.<br>".$this->lf."".
					"Esta classe é extendida pela <code>".$this->nome_classe_rel_list."</code> que é extendida pela <code>".$this->nome_classe_final_list."</code>, que é onde deverão ser feitas implementações pelos desenvolvedores.<br>".$this->lf."".
					"<b>Por favor não instancie esta classe!</b> Ao invés disso, utilize a classe <code>".$this->nome_classe_final_list."</code>, através de algo como <code>\$objlist = ".$this->getComoInstanciarList()."</code>.";
		$jd->autor = geraClassesConfigAutor();

		$jde = new JD();
		$jde->desc = "Indica que a lista está no final.";
		$jde->type = "boolean";
		$jde->publico = true;

		$jdc = new JD();
		$jdc->desc = "Indica a quantidade de registros na lista.";
		$jdc->type = "int";
		$jdc->publico = true;

		$jdn = new JD();
		$jdn->desc = "Indica a posição atual na lista.";
		$jdn->type = "int";
		$jdn->publico = true;

		return
		$jd->get().
		"class ". $this->nome_classe_base_list . " extends gc_base_list { ".$this->lf."".
		$jdc->get(1).
		"\tvar \$quantos = null;".$this->lf."".
		$jde->get(1).
		"\tvar \$EOF = true;".$this->lf."".
		$jdn->get(1).
		"\tvar \$n_atual = null;".$this->lf."".
		$this->geraDeclAsters().
		//$this->geraDeclVars($this->campos_pk).
		//$this->geraDeclVars($this->campos_nao_pk).
		"".$this->lf."".
		$this->geraConstructorList().
		$this->geraGetDB().
		//$this->geraMethMoveTo().
		//$this->geraMethMoveNext().
		$this->geraMethGetNextItem().
		$this->geraMethLoadBySQL_list().
		$this->geraMethGetTodos().
		$this->geraMethGetQuantos().
		$this->geraMethGetPar().
		"}".$this->lf."";
	}

	function geraMethGetTodos() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Obtém *todos* os registros da tabela ". $this->nome_fis. "; retorna false se a tabela estiver vazia.";
		$jd->addParam("orderby", "trecho SQL para ordenação da lista; deve conter <code>ORDER BY xxx</code>");
		$jd->addParam("rowsbypage", "Paginação - se presente, ativa paginação; representa o número de registros por página");
		$jd->addParam("pageatual", "Paginação - indica a página atual");
		$jd->returns = "boolean";
		$s .= $jd->get(1);
		$meth = &ndocCreateMeth($this->nome_classe_final_list, "getTodos", "boolean", "Obtém todos os registros da tabela ". $this->nome_fis. "");
		ndocCreateParam($meth, "orderby", "string", "trecho SQL para ordenação da lista");
		ndocCreateParam($meth, "rowsbypage", "integer", "Paginação - se presente, ativa paginação; representa o número de registros por página");
		ndocCreateParam($meth, "pageatual", "integer", "Paginação - indica a página atual");
		return
		$s.
		"	function getTodos(\$orderby=null, \$rowsbypage=null, \$pageatual=null) {".$this->lf."".
		"		return \$this->loadBySQL(\"SELECT \".\$this->getAster().\" FROM ".$this->nome_fis."\".(\$orderby?' '.\$orderby:''), null, null, \$rowsbypage, \$pageatual);".$this->lf."".
		"	}".$this->lf."".$this->lf."";
	}

	function geraMethLoadBySQL_list() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Carrega uma lista de registros, utilizando um SQL abitrário. Retorna true se foi possível carregar a lista.";
		$jd->addParam("sql", "obrigatório. O SQL a ser executado. pode conter zero ou mais '?'");
		$jd->addParam("vals", "opcional; array dos valores. cada item é um array de tipo e valor.");
		$jd->addParam("types", "deprecated optional; passar null se necessário.");
		$jd->addParam("rowsbypage", "optional; ativa paginação; representa o número de registros por página");
		$jd->addParam("pageatual", "optional; indica a página atual");
		$jd->returns = "boolean";
		$s .= $jd->get(1);
		$meth = &ndocCreateMeth($this->nome_classe_final_list, "loadBySQL", "boolean", "Carrega uma lista de registros, utilizando um SQL abitrario");
		ndocCreateParam($meth, "sql", "string", "obrigatorio. O SQL a ser executado. pode conter zero ou mais '?'");
		ndocCreateParam($meth, "vals", "array", "opcional; array dos valores. cada item é um array de tipo e valor.");
		ndocCreateParam($meth, "types", "array", "deprecated optional; passar null se necessário.");
		ndocCreateParam($meth, "rowsbypage", "integer", "optional; ativa paginação; representa o número de registros por página");
		ndocCreateParam($meth, "pageatual", "integer", "optional; indica a página atual");

		$s  .=
		"	function loadBySQL(\$sql, \$vals=null, \$types=null, \$rowsbypage=null, \$pageatual=null) {".$this->lf."".
		"		\$db = &\$this->getDB();".$this->lf."".
		"		if (\$rowsbypage) {".$this->lf."".
		"			if (\$this->_rec = &\$db->PageExecuteBind(\$sql, \$rowsbypage, \$pageatual, \$vals, \$types)) {".$this->lf."".
		"				\$this->EOF = \$this->_rec->EOF;".$this->lf."".
		"				\$this->paginado = true;".$this->lf."".
		"				\$this->n_atual = 0;".$this->lf."".
		"				return true;".$this->lf."".
		"			} else {".$this->lf."".
		"				\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."".
		"				\$this->n_atual = 0;".$this->lf."".
		"				return false;".$this->lf."".
		"			}".$this->lf."".
		"		} else {".$this->lf."".
		"			if (\$this->_rec = &\$db->ExecuteBind(\$sql, \$vals, \$types)) {".$this->lf."".
		"				\$this->EOF = \$this->_rec->EOF;".$this->lf."".
		"				\$this->paginado = false;".$this->lf."".
		"				\$this->n_atual = 0;".$this->lf."".
		"				return true;".$this->lf."".
		"			} else {".$this->lf."".
		"				\$db->_raiseError(__FILE__, __LINE__, \$sql);".$this->lf."".
		"				\$this->n_atual = 0;".$this->lf."".
		"				return false;".$this->lf."".
		"			}".$this->lf."".
		"		}".$this->lf."".
		"	}".$this->lf."";
		return $s;
	}

	function getQuantos() {
		return $this->_rec->RecordCount();
	}

	function geraMethGetQuantos() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Retorna o número de registros atualmente carregados na lista.";
		$jd->returns = "integer";
		$s .= $jd->get(1);
		$meth = &ndocCreateMeth($this->nome_classe_final_list, "getQuantos", "integer", "Retorna o número de registros atualmente carregados na lista");
		$s .=
		"".$this->lf."\tfunction getQuantos() {".$this->lf."".
		"\t\tif (!\$this->_rec) return -1;".$this->lf."".
		"\t\treturn \$this->_rec->RecordCount();".$this->lf."".
		"\t}".$this->lf."".
		"";
		return $s;
	}

	function geraMethGetPar() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Retorna 'Par' ou 'Impar', conforme a posição sequencial na lista (incrementada por ::getNextItem()).";
		$jd->returns = "string";
		$s .= $jd->get(1);
		$meth = &ndocCreateMeth($this->nome_classe_final_list, "getPar", "string", "Retorna Par ou Impar, conforme a posicao sequencial na lista");
		$s .=
		"".$this->lf."\tfunction getPar() {".$this->lf."".
		"\t\treturn !(\$this->n_atual%2==0)?'Par':'Impar';".$this->lf."".
		"\t}".$this->lf."".
		"";
		return $s;
	}

	function geraMethGetNextItem() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Obtém o objeto ".$this->nome_classe_final." para o próximo item da lista; se chegou ao final, retorna false.";
		$jd->returns = $this->nome_classe_final;
		$s .= $jd->get(1);
		$meth = &ndocCreateMeth($this->nome_classe_final_list, "getNextItem", $this->nome_classe_final, "Obtem o objeto ".$this->nome_classe_final." para o proximo item da lista; se chegou ao final, retorna false.");
		$s .=
		"\tfunction getNextItem() {".$this->lf."".
		"\t\t\$db = &\$this->getDB();".$this->lf."".
		"\t\tif (\$this->_rec->EOF) return false;".$this->lf."".
		"\t\t\$item = ".$this->getComoInstanciarOne().";".$this->lf."";
		$s .= "		\$item->loadFields(\$this->_rec);".$this->lf."";
		/*
		$counter=0;
		foreach ($this->campos as $c) {
			if ($c->dt_campo=='NVARCHAR')continue;
			$s .= "\t\t\$item->".$c->getNome($this->nome_fis)." = ".$c->getStringTira("\$this->_rec->fields[", "]", true, $counter).";".$this->lf."";
			//$s .= "\t\t\$item->".$c->getNome($this->nome_fis)." = ".$c->getStringTira("\$this->_rec->fields[\"", "\"]", true, $c->nome_fis).";".$this->lf."";
			$counter++;
		}
		*/
		$s .=
		"\t\t\$this->_rec->MoveNext();".$this->lf."".
		"\t\t\$this->n_atual++;".$this->lf."".
		"\t\t\$this->EOF = \$this->_rec->EOF;".$this->lf."".
		"\t\treturn \$item;".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
		return $s;
	}


	function geraDeclVars($arr, $maiscoment=null) {
		if (!is_array($arr) )  return null;
		$s = "";
		foreach ($arr as $c) {
			if ($maiscoment=="PK") $maiscoment = "integra a chave primária";
			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "campo ".$c->nome_log."; ".(!$c->null_campo?"pode ser null":"não pode ser null". ($maiscoment?"; $maiscoment":""));
			$jd->type = $c->dt_campo;
			//$jd->type = $c->getTipagemLimpo();
			$jd->type = $c->getTipagemPhpDocs();
			$jd->publico = true;
			$s .= $jd->get(1);
			$s .= "\tvar \$". $c->getNome($this->nome_fis)." = null;"."".$this->lf."";
		}
		return $s;
	}

	function geraConstructor(){
		if ($this->geraMsSqlServer==false) return '';
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Constructor da classe ".$this->nome_classe_base;
		$jd->returns = $this->nome_classe_base;
		$s .= $jd->get(1);
		return
		$s.
		//"\tfunction ".$this->nome_classe_base."() {".$this->lf."".
		"\tfunction __construct() {".$this->lf."".
		"\t\tglobal \$_mssql;".$this->lf."".
		"\t\tif (\$_mssql) {".$this->lf."".
		"\t\t\t\$this->aster = \"".$this->asterMssql."\" ;"."".$this->lf."".
		"\t\t\t\$this->astercompleto = \"".$this->astercompletoMssql."\" ;"."".$this->lf."".
		"\t\t}".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraConstructorList(){
		if ($this->geraMsSqlServer==false) return '';
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Constructor da classe ".$this->nome_classe_base_list;
		$jd->returns = $this->nome_classe_base_list;
		$s .= $jd->get(1);
		return
		$s.
		//"\tfunction ".$this->nome_classe_base_list."() {".$this->lf."".
		"\tfunction __construct() {".$this->lf."".
		"\t\tglobal \$_mssql;".$this->lf."".
		"\t\tif (\$_mssql) {".$this->lf."".
		"\t\t\t\$this->aster = \"".$this->asterMssql."\" ;"."".$this->lf."".
		"\t\t\t\$this->astercompleto = \"".$this->astercompletoMssql."\" ;"."".$this->lf."".
		"\t\t}".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraDeclAsters() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Obtém uma string equivalente ao aster (*) de um SELECT desta tabela.";
		$jd->returns = "varchar";
		$s .= $jd->get(1);
		$s .="\tfunction getAster() {".$this->lf."".
		"\t\treturn \"".$this->aster."\" ;"."".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Obtém uma string equivalente ao aster (*) de um SELECT desta tabela, completo (para uso com múltiplas tabelas na cláusula FROM.";
		$jd->returns = "varchar";
		$s .= $jd->get(1);
		$s .="\tfunction getAsterCompleto(\$alias='$this->nome_fis') {".$this->lf."".
		"\t\treturn \"".$this->astercompletoalias."\" ;"."".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
		/* JavaDoc */
		if (@$this->campos_pk){
			foreach ($this->campos_pk as $cc) {
				$params[] = "'".$cc->getNome($this->nome_fis)."'";
				$types[]  = $cc->getStringTipagem();
				//$wh[] = $cc->nome_fis . " = ? ";
			}
		}else{
			$params=array();
			$types=array();
		}
		if (count($params)>0){
			$jd = new JD();
			$jd->desc = "Define um array com as chaves primarias desta tabela, na ordem usada pelo loadByPk.";
			$jd->type = "array de varchar";
			$jd->publico = true;
			$s .= $jd->get(1);
			$s .= "\tvar \$pks = array(".implode(',',$params).");"."".$this->lf."";
		}
		if (count($types)>0){
			$jd = new JD();
			$jd->desc = "Define um array com os tipos de dados dos campos chave primaria desta tabela, na ordem usada pelo loadByPk.";
			$jd->type = "array de varchar";
			$jd->publico = true;
			$s .= $jd->get(1);
			$s .= "\tvar \$types = array(".implode(',',$types).");"."".$this->lf."";
		}
		$jd = new JD();
		$jd->desc = "Define o nome da tabela associada a classe.";
		$jd->type = "varchar";
		$jd->publico = true;
		$s .= $jd->get(1);
		$s .= "\tvar \$tab = '".$this->nome_fis."';".$this->lf."";
		if (@$this->campos_geom){
			$ds='';
			
			foreach($this->campos_geom as $cc){
				$ds.=($ds==''?'':',')."'".$cc->getNome($this->nome_fis)."'=>'".$cc->getTipagemLimpo()."'";
			}
			$jd = new JD();
			$jd->desc = "Define um array com os campos geo.";
			$jd->type = "array de campos e tipos";
			$jd->publico = true;
			$s .= $jd->get(1);
			$ds=
			$s .= "\tvar \$geoms = array(".$ds.");"."".$this->lf."";

		}
		return $s;
	}

	function geraGetDB() {
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Obtém uma conexão ao banco de dados. Para definir uma conexão para ser utilizada pelo objeto, defina a propriedade \$db deste objeto. Caso contrário, retorna a conexão global.";
		$jd->returns = "ADODBConnection";
		$s .= $jd->get(1);
		return
		$s.
		"\tfunction &getDB() {".$this->lf."".
		"\t\tglobal \$db".$this->sufixo.";".$this->lf."".
		"//\t\tif (isset(\$this->db)) return \$this->db;".$this->lf."".
		"\t\treturn \$db".$this->sufixo.";".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
	}

	function padTo($string, $len) {
		return $string . str_repeat(" ",$len - strlen($string));
	}



	function geraRel() {
		if (isset($this->relats_parent)) {
			ksort($this->relats_parent);
			//echo (print_r(array_keys($this->relats_parent), true)."\r".$this->lf."");exit;
		}
		if (isset($r) && $r->apoio->relats_parent) ksort($r->apoio->relats_parent);
		if (isset($this->relats_child)) ksort($this->relats_child);
		if (isset($r) && $r->apoio->relats_child) ksort($r->apoio->relats_child);

		$jd = new JD();
		$jd->desc =
					"Esta classe é extendida pela <code>" . $this->nome_classe_final . "</code>, e extende a <code>". $this->nome_classe_base. "</code>, adicionando metodos de relacionamento com outras classes.<br>".$this->lf."".
					"Por favor <b>não modifique esta classe</b>. Faça implementações apenas na classe <code>" . $this->nome_classe_final. "</code>.<br>".$this->lf."".
					"Não instancie diretamente esta classe; ao invés disso utilize a classe <code>".$this->nome_classe_final."</code> através da <code>FACTORY</code>. Ex: <code>\$obj = ".$this->getComoInstanciarOne()."</code>.";
		$jd->autor = geraClassesConfigAutor();
		return
		$jd->get().
		"class ". $this->nome_classe_rel . " extends ". $this->nome_classe_base . "{".$this->lf."".$this->lf."".
		($this->geraMsSqlServer?$this->geraConstructorRel():'').
		$this->geraMethDeleteCascade().
		$this->geraGetByUnique().
		$this->geraGetParents(). // para obter os pais.
		$this->geraGetChildren(). // para obter os filhos!!! catzo!!!
		$this->geraGetTwoDeep().
		"}".$this->lf."";
	}

	function geraConstructorRel(){
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Constructor da classe ".$this->nome_classe_rel;
		$jd->returns = $this->nome_classe_rel;
		$s .= $jd->get(1);
		return
		$s.
		//"\tfunction ".$this->nome_classe_rel."() {".$this->lf."".
		"\tfunction __construct() {".$this->lf."".
		//"\t\t@\$this->".$this->nome_classe_base."();".$this->lf."".
		"\t\tparent::__construct();".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraMethDeleteCascade() {
		if (    (!is_array($this->campos_pk))  ) return "";
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Exclui o registro e todas as suas dependências. As chaves primárias deverão estar definidas. Retorna true se bem sucedido.";
		$jd->returns = "boolean";
		$s .= $jd->get(1);

		/* XMLref */
		$meth = &ndocCreateMeth($this->nome_classe_final, "deleteCascade", "boolean", "Exclui o registro e suas dependências");

		$s .= "\tfunction deleteCascade(\$force=false) {".$this->lf."";

		if (isset($this->relats_parent)) {
			ksort($this->relats_parent);
			foreach ($this->relats_parent as $r) {
				$nom_ext = $r->getNomeFraseInversa();
				if ($r->cardinal != 2) {
					$s .= "\t\tif (\$list = &\$this->get".$r->apoio->nome_classe_final_list.$r->getNomeFraseInversa()."()) {".$this->lf."";
					$s .= "\t\t\twhile (\$item = \$list->getNextItem()) {".$this->lf."";
					$s .= "\t\t\t\tif (!\$item->deleteCascade(\$force)) return false;".$this->lf."";
					$s .= "\t\t\t}".$this->lf."";
					$s .= "\t\t}".$this->lf."";
				} else {
					$s .= "\t\tif (\$item = \$this->get".$r->apoio->nome_classe_final.$r->getNomeFraseInversa()."()) {".$this->lf."";
					$s .= "\t\t\tif (!\$item->deleteCascade(\$force)) return false;".$this->lf."";
					$s .= "\t\t}".$this->lf."";
				}
			}
		}

		$s .= "\t\tif (!\$this->delete(\$force)) return false;".$this->lf."";
		$s .= "\t\treturn true;".$this->lf."";
		$s .= "\t}".$this->lf."".$this->lf."";
		return $s;
	}


	/**************************************************************************************************************************
	** GeraGetByUnique (gera métodos de get via os campos unique)
	**************************************************************************************************************************/
	function geraGetByUnique() {
		if (!$this->uniques) return null; // nao tem.... sorry...
		$s = "\t// ------------------------------[Obter-me via meus campos unique]--------------------------------".$this->lf."";
		foreach ($this->uniques as $un) {
			$ccnome = null; foreach ($un as $cc) { $ccnome[] = UCwords(strtolower(str_replace("-", "_", str_replace(" ", "_", $cc->nome_log))));} $ccnome = implode("And", $ccnome);
			$nomefunc = "getBy$ccnome";
			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, "boolean", "Obtém um registro através de campo(s) unique da tabela");

			$params = null;	$wh = null; $vals = null; foreach ($un as $cc) {
				$params[] = "\$".$cc->getNome($this->nome_fis);
				$wh[] = $cc->nome_fis . " = ? ";
				$vals[] = array($cc->getStringTipagem(), "\$".$cc->getNome($this->nome_fis));
				ndocCreateParam($meth, $cc->getNome($this->nome_fis), $cc->getTipagemLimpo(), $cc->nome_log);
			} $wh = implode(" AND ", $wh); $params = implode(",", $params);
			$arrproc = null; foreach ($vals as $val) {$arrproc[] = "array(".$val[0].",".$val[1].")";} $arrvals = "array(".implode(",", $arrproc).")";
			$sql = "SELECT \".\$this->getAster().\" FROM " . $this->nome_fis . " WHERE $wh";

			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Obtém um registro através de campo(s) unique da tabela.";
			$jd->returns = "boolean";
			$s .= $jd->get(1);
			$s .= "";
			$s .= "	function $nomefunc(".$params.") {".$this->lf."";
			$s .= "		\$db = &\$this->getDB();".$this->lf."";
			$s .= "		return \$this->load(\"".$sql."\", $arrvals);".$this->lf."";
			$s .= "	}".$this->lf."".$this->lf."";
		}
		return $s;
	}


	function geraGetTwoDeep() {
		$s = "";

		/* Pega os Pais do Meu Pai */
		if (isset($this->relats_parent)) {
			ksort($this->relats_parent);
			$s .= "\t// --------------------------------- geraGetTwoDeep - geraGetTwoDeepParentParent -------------- ".$this->lf."";
			foreach ($this->relats_parent as $k=>$r) {
				//echo $k."\r".$this->lf."";
				//echo $k." ==> ".print_r($r, true)."\r".$this->lf."";exit;
				if (isset($r->apoio->relats_parent)) {
					ksort($r->apoio->relats_parent);
					foreach ($r->apoio->relats_parent as $r2) {
						if ($r->apoio->nome_fis == $r2->relat->nome_fis) {
							$s .= $this->geraGetTwoDeepParentParent($r, $r2);
						}
					}
				}
			}
		}

		/* Pega Filhos do Meu Pai, que não sejam eu. */
		if (isset($this->relats_parent)) {
			$s .= "\t// --------------------------------- geraGetTwoDeep - geraGetTwoDeepParentChild -------------- ".$this->lf."";
			foreach ($this->relats_parent as $r) {
				if ($r->apoio->relats_child) {
					ksort($r->apoio->relats_child);
					foreach ($r->apoio->relats_child as $r2) {
						if ($r->apoio->nome_fis == $r2->relat->nome_fis) {
							if ($r2->apoio->nome_fis != $this->nome_fis) { /* que não sejam eu mesmo */
								$s .= $this->geraGetTwoDeepParentChild($r, $r2);
							}
						}
					}
				}
			}
		}

		/* Pega os filhos dos meus filhos. */
		if (isset($this->relats_child)) {
			ksort($this->relats_child);
			$s .= "// --------------------------------- geraGetTwoDeep - geraGetTwoDeepChildChild---------------- ".$this->lf."";
			foreach ($this->relats_child as $r) {
				if (isset($r->apoio->relats_child)) {
					ksort($r->apoio->relats_child);
					foreach ($r->apoio->relats_child as $r2) {
						$s .= $this->geraGetTwoDeepChildChild($r, $r2);
					}
				}
			}
		}
		return $s;
	}

	/**************************************************************************************************************************
	** geraGetTwoDeepParent (get ZZZ via WWWW)....
	**************************************************************************************************************************/
	function geraGetTwoDeepParentParent($r, $r2) {
		if (!is_array($r->cols_relat)) return;
		if (!is_array($r->cols_apoio)) return;
		if (!is_array($r2->cols_relat)) return;
		if (!is_array($r2->cols_apoio)) return;
		// Faz o join do r2 com o r1.
		$campos = null;
		for ($i = 0; $i < count($r2->cols_relat); $i++) {
			$relat = $r2->cols_apoio[$i];
			$apoio = $r2->cols_relat[$i];
			$campos[] = $r2->apoio->nome_fis.".".$relat->nome_fis. " = ". /*$r2->relat->nome_fis*/ "A".".".$apoio->nome_fis;
		}
		$campos = implode(" AND ", $campos);
		// Faz o where do r1 comigo.
		$pks = null;
		foreach ($r->cols_apoio as $col) {
			$pks[] = /*$r->apoio->nome_fis*/"A".".$col->nome_fis = ?";
		}
		$pks = implode(" AND ", $pks);

		$vals = null;
		foreach ($r->cols_relat as $col) {
			$vals[] = array($col->getStringTipagem(), "\$this->".$col->getNome($this->nome_fis));
		}

		$arrproc = null;
		foreach ($vals as $val) {
			$arrproc[] = "array(".$val[0].",".$val[1].")";
		}
		$ccdel=$r->relat->achaCampoDel();
		//$ccdel2=$r2->apoio->achaCampoDel();
		$s='';
		if ($r2->cardinal == 2) {
			$sql  = "SELECT \".\$item->getAsterCompleto().\" FROM " . $r->apoio->nome_fis . " A, " . $r2->apoio->nome_fis;
			$sql .= " WHERE ";
			$sql .= $campos . " AND ";
			$sql .= $pks;
			if ($ccdel) {
				$sql .= " \".(\$loadDeleted?\" AND (A.".$ccdel->nome_fis." <> ? OR A.".$ccdel->nome_fis." IS NULL)\":\"\").\"";
				//$arrproc[] = "(\$loadDeleted?array('Bit', true):\"null\"";
			}
			/* não vai
			if ($ccdel2) {
				$sql .= " AND ".$r2->apoio->nome_fis.".".$ccdel2->nome_fis." <> ? ";
				$arrproc[] = "array('Bit', true)";
			}
			*/
			$arrvals = "array(".implode(",", $arrproc).")";
			$nomefunc = "get" .$r2->apoio->nome_classe_final . $r2->getNomeFrase(). "via". $r->apoio->nome_classe_final.$r->getNomeFrase()."(\$loadDeleted=false)";
			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $r2->apoio->nome_classe_final, "Magica 1");
			if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetTwoDeepParent() (2, na ".$this->nome_classe_final.") está gerando função com nome repetido ($nomefunc).")) return "";
			$jd = new JD();
			$jd->desc = "geraGetTwoDeepParentParent (cardinality 2)";
			$jd->desc .= "".$this->lf."R: " . $r->getDesc();
			$jd->desc .= "".$this->lf."R2: " . $r2->getDesc();
			$jd->returns = $r2->apoio->nome_classe_final;
			$s .= $jd->get(1);
			$s .=
			"\tfunction &$nomefunc {".$this->lf."".
			"\t\t\$item = ".$r2->apoio->getComoInstanciarOne().";".$this->lf."".
			"\t\t\$params = $arrvals;".$this->lf."".
			"\t\tif (\$loadDeleted==true) \$params[]=array('Bit', true);".$this->lf."".
			"\t\tif (!\$item->load(\"$sql\", \$params)) return null;".$this->lf."".
			"\t\treturn \$item;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";
		} else {
			$sql  = "SELECT \".\$list->getAsterCompleto().\" FROM " . $r->apoio->nome_fis . " A, " . $r2->apoio->nome_fis;
			$sql .= " WHERE ";
			$sql .= $campos . " AND ";
			$sql .= $pks;
			if ($ccdel) {
				$sql .= " \".(\$loadDeleted?\" AND (A.".$ccdel->nome_fis." <> ? OR A.".$ccdel->nome_fis." IS NULL) \":\"\").\"";
			}
			/* não vai
			if ($ccdel2) {
				$sql .= " AND ".$r2->apoio->nome_fis.".".$ccdel2->nome_fis." <> ? ";
				$arrproc[] = "array('Bit', true)";
			}
			*/
			$arrvals = "array(".implode(",", $arrproc).")";
			$nomefunc = "get" .$r2->apoio->nome_classe_final_list . $r->getNomeFrase() . "via". $r->apoio->nome_classe_final. $r2->getNomeFrase() . "";
			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $r2->apoio->nome_classe_final_list, "Magica 2");
			if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetTwoDeepParent() (na ".$this->nome_classe_final.") está gerando função com nome repetido ($nomefunc) (frases: ".$r->frase.",".$r2->frase.")".(isset($nom_ext)?" (nom_ext: ".$nom_ext.")":'').".")) return "";
			$jd = new JD();
			$jd->desc = "geraGetTwoDeepParentParent (no cardinality)";
			$jd->desc .= "".$this->lf."R: " . $r->getDesc();
			$jd->desc .= "".$this->lf."R2: " . $r2->getDesc();
			$jd->addParam("orderby", "trecho SQL para ordenação da lista; deve conter <code>ORDER BY xxx</code>");
			$jd->returns = $r2->apoio->nome_classe_final_list;
			$s .= $jd->get(1);
			$s .=
			"\tfunction &$nomefunc (\$orderby=null, \$loadDeleted=false){".$this->lf."".
			"\t\t\$list = ".$r2->apoio->getComoInstanciarList().";".$this->lf."".
			"\t\t\$params = $arrvals;".$this->lf."".
			"\t\tif (\$loadDeleted==true) \$params[]=array('Bit', true);".$this->lf."".
			"\t\t\$list->loadBySQL(\"$sql\".(\$orderby?' '.\$orderby:''), \$params);".$this->lf."".
			"\t\treturn \$list;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";
		}
		return $s;
	}

	/**************************************************************************************************************************
	** Gera um get XXX via YYY.
	**************************************************************************************************************************/
	function geraGetTwoDeepParentChild($r, $r2) {
		if (!is_array($r->cols_relat)) return;
		if (!is_array($r->cols_apoio)) return;
		if (!is_array($r2->cols_relat)) return;
		if (!is_array($r2->cols_apoio)) return;
		// Faz o join do r2 com o r1.
		$campos = null;
		$s='';
		for ($i = 0; $i < count($r2->cols_relat); $i++) {
			$relat = $r2->cols_apoio[$i];
			$apoio = $r2->cols_relat[$i];
			$campos[] = $r2->apoio->nome_fis.".".$relat->nome_fis. " = ". /*$r2->relat->nome_fis*/ "A".".".$apoio->nome_fis;
		}
		$campos = implode(" AND ", $campos);
		// Faz o where do r1 comigo.
		$pks = null;
		foreach ($r->cols_apoio as $col) {
			$pks[] = /*$r->apoio->nome_fis*/ "A".".$col->nome_fis = ?";
		}
		$pks = implode(" AND ", $pks);

		$vals = null;
		foreach ($r->cols_relat as $col) {
			$vals[] = array($col->getStringTipagem(), "\$this->".$col->getNome($this->nome_fis));
		}

		$arrproc = null;
		foreach ($vals as $val) {
			$arrproc[] = "array(".$val[0].",".$val[1].")";
		}
		$arrvals = "array(".implode(",", $arrproc).")";

		$ccdel=$r->apoio->achaCampoDel();
		//$ccdel2=$r2->apoio->achaCampoDel();
		if ($r->cardinal == 2) {
			$sql  = "SELECT \".\$item->getAsterCompleto().\" FROM " . $r->apoio->nome_fis . " A, " . $r2->apoio->nome_fis;
			$sql .= " WHERE ";
			$sql .= $campos . " AND ";
			$sql .= $pks;

			$nomefunc = "get" .$r2->apoio->nome_classe_final . $r2->getNomeFrase(). "via". $r->apoio->nome_classe_final. $r->getNomeFrase()."()";

			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $r2->apoio->nome_classe_final, "Magica 3");
			if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetTwoDeepChild() (2, na ".$this->nome_classe_final.") está gerando função com nome repetido ($nomefunc).")) return "";
			$jd = new JD();
			$jd->desc = "geraGetTwoDeepParentChild (cardinality 2)";
			$jd->desc .= "".$this->lf."R: " . $r->getDesc();
			$jd->desc .= "".$this->lf."R2: " . $r2->getDesc();
			$jd->returns = $r2->apoio->nome_classe_final;
			$s .= $jd->get(1);
			$s .=
			"\tfunction &$nomefunc {".$this->lf."".
			"\t\t\$item = ".$r2->apoio->getComoInstanciarOne().";".$this->lf."".
			"\t\tif (!\$item->load(\"$sql\", $arrvals)) \$item=null;".$this->lf."".
			"\t\treturn \$item;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";
		} else {
			$sql  = "SELECT \".\$list->getAsterCompleto().\" FROM " . $r->apoio->nome_fis . " A, " . $r2->apoio->nome_fis;
			$sql .= " WHERE ";
			$sql .= $campos . " AND ";
			$sql .= $pks;
			if ($ccdel) {
				$sql .= " \".(\$loadDeleted?\" AND (A.".$ccdel->nome_fis." <> ? OR A.".$ccdel->nome_fis." IS NULL)\":\"\").\"";
			}


			$nomefunc = "get" .$r2->apoio->nome_classe_final_list . $r2->getNomeFrase(). "via". $r->apoio->nome_classe_final. $r->getNomeFrase()."";

			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $r2->apoio->nome_classe_final_list, "Magica 4");
			if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetTwoDeepChild() (na ".$this->nome_classe_final.") está gerando função com nome repetido ($nomefunc).")) return "";
			$jd = new JD();
			$jd->desc = "geraGetTwoDeepParentChild (no cardinality)";
			$jd->desc .= "".$this->lf."R: " . $r->getDesc();
			$jd->desc .= "".$this->lf."R2: " . $r2->getDesc();
			$jd->addParam("orderby", "trecho SQL para ordenação da lista; deve conter <code>ORDER BY xxx</code>");
			$jd->returns = $r2->apoio->nome_classe_final_list;
			$s .= $jd->get(1);
			$s .=
			"\tfunction &$nomefunc (\$orderby=null".($ccdel?", \$loadDeleted=false":"")."){".$this->lf."".
			"\t\t\$list = ".$r2->apoio->getComoInstanciarList().";".$this->lf."".
			"\t\t\$params = $arrvals;".$this->lf."".
			($ccdel?"\t\tif (\$loadDeleted==true) \$params[]=array('Bit', true);".$this->lf."":"").
			"\t\t\$list->loadBySQL(\"$sql\".(\$orderby?' '.\$orderby:''), \$params);".$this->lf."".
			"\t\treturn \$list;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";
		}
		return $s;
	}

	/**************************************************************************************************************************
	** Gera um get XXX via YYY.
	**************************************************************************************************************************/
	function geraGetTwoDeepChildChild($r, $r2) {
		if (!is_array($r->cols_relat)) return;
		if (!is_array($r->cols_apoio)) return;
		if (!is_array($r2->cols_relat)) return;
		if (!is_array($r2->cols_apoio)) return;
		// Faz o join do r2 com o r1.
		$campos = null;
		$s='';
		for ($i = 0; $i < count($r2->cols_relat); $i++) {
			$relat = $r2->cols_apoio[$i];
			$apoio = $r2->cols_relat[$i];
			$campos[] = $r2->apoio->nome_fis.".".$relat->nome_fis. " = ". /*$r2->relat->nome_fis*/ "A".".".$apoio->nome_fis;
		}
		$campos = implode(" AND ", $campos);


		// Faz o where do r1 comigo.
		$pks = null;
		foreach ($r->cols_apoio as $col) {
			$pks[] = /*$r->apoio->nome_fis*/ "A".".$col->nome_fis = ?";
		}
		$pks = implode(" AND ", $pks);

		$vals = null;
		foreach ($r->cols_relat as $col) {
			$vals[] = array($col->getStringTipagem(), "\$this->".$col->getNome($this->nome_fis));
		}
		$arrproc = null;
		foreach ($vals as $val) {
			$arrproc[] = "array(".$val[0].",".$val[1].")";
		}
		$arrvals = "array(".implode(",", $arrproc).")";

		$ccdel=$r->apoio->achaCampoDel();
		//$ccdel2=$r2->apoio->achaCampoDel();
		if  (($r->cardinal == 2) | ($r2->cardinal == 2)) {
			$sql  = "SELECT \".\$item->getAsterCompleto().\" FROM " . $r->apoio->nome_fis . " A, " . $r2->apoio->nome_fis;
			$sql .= " WHERE ";
			$sql .= $campos . " AND ";
			$sql .= $pks;
			/* Novamente uma loucura */
			$nomefunc = "get" .$r2->apoio->nome_classe_final . $r2->getNomeFraseInversa(). "via". $r->apoio->nome_classe_final. $r->getNomeFraseInversa()."()";


			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $r2->apoio->nome_classe_final, "Magica 5");
			if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetTwoDeepChildChild() (2, na ".$this->nome_classe_final.") está gerando função com nome repetido ($nomefunc).")) return "";
			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Obtém a <code>".$r2->apoio->nome_classe_final."</code> através da entidade de ligação . ".$r->apoio->nome_classe_final;
			$jd->returns = $r2->apoio->nome_classe_final;
			$s .= $jd->get(1);
			$s .=
			"\tfunction &$nomefunc {".$this->lf."".
			"\t\t\$item = ".$r2->apoio->getComoInstanciarOne().";".$this->lf."".
			"\t\tif (!\$item->load(\"$sql\", $arrvals)) \$item=null;".$this->lf."".
			"\t\treturn \$item;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";
		} else {
			$sql  = "SELECT \".\$list->getAsterCompleto().\" FROM " . $r->apoio->nome_fis . " A, " . $r2->apoio->nome_fis;
			$sql .= " WHERE ";
			$sql .= $campos . " AND ";
			$sql .= $pks;
			if ($ccdel) {
				$sql .= " \".(\$loadDeleted?\" AND (A.".$ccdel->nome_fis." <> ? OR A.".$ccdel->nome_fis." IS NULL)\":\"\").\"";
			}
			/* Mais madness! */
			$nomefunc = "get" .$r2->apoio->nome_classe_final_list . $r2->getNomeFraseInversa(). "via". $r->apoio->nome_classe_final. $r->getNomeFraseInversa()."";

			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $r2->apoio->nome_classe_final_list, "Magica 6");
			if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetTwoDeepChildChild() (na ".$this->nome_classe_final.")  está gerando função com nome repetido ($nomefunc).")) return "";
			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Obtém a <code>".$r2->apoio->nome_classe_final_list."</code> através da entidade de ligação " . $r->apoio->nome_classe_final_list;
			$jd->desc .= "".$this->lf."R: " . $r->getDesc();
			$jd->desc .= "".$this->lf."R2: " . $r2->getDesc();
			$jd->addParam("orderby", "trecho SQL para ordenação da lista; deve conter <code>ORDER BY xxx</code>");
			$jd->returns = $r2->apoio->nome_classe_final_list;
			$s .= $jd->get(1);
			$s .=
			"\tfunction &$nomefunc (\$orderby=null".($ccdel?", \$loadDeleted=false":"")."){".$this->lf."".
			"\t\t\$list = ".$r2->apoio->getComoInstanciarList().";".$this->lf."".
			"\t\t\$params = $arrvals;".$this->lf."".
			($ccdel?"\t\tif (\$loadDeleted==true) \$params[]=array('Bit', true);".$this->lf."":"").
			"\t\t\$list->loadBySQL(\"$sql\".(\$orderby?' '.\$orderby:''), \$params);".$this->lf."".
			"\t\treturn \$list;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";
		}
		return $s;
	}


	/**************************************************************************************************************************
	** GeraGetParents (obtém uma entidade pai minha)
	**************************************************************************************************************************/
	function geraGetParents() {
		if (!isset($this->relats_child)) return null;
		$tot = "\t// ---------------------------------------[Obter meus pais]---------------------------------------".$this->lf."";
		foreach ($this->relats_child as $relat) {
			if (!is_array($relat->cols_apoio)) continue;
			$ccdel=$relat->apoio->achaCampoDel();
			$nom_ext = $relat->frase?"_".str_replace(" ", "_", strtoupper($relat->frase)):"";
			$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext."(\$loadDeleted=false)";
			$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $relat->apoio->nome_classe_final, "Parent 1");
			if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetParents() está gerando função com nome repetido ($nomefunc). Por favor nomeie o relacionamento (".$relat->getDesc().") usando verbos **diretos**!")) continue;
			$pks = null;
			foreach ($relat->cols_relat as $col) {
				$pks[] = "\$this->".$col->getNome();
			}
			$pks = implode(",", $pks);
			$s  = "";
			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Obtém a <code>".$relat->apoio->nome_classe_final."</code> do item atual.";
			$jd->returns = $relat->apoio->nome_classe_final;
			$s .= $jd->get(1);
			$s .=
			"\tfunction &".$nomefunc." {".$this->lf."".
			"\t\t\$item = ".$relat->apoio->getComoInstanciarOne().";".$this->lf."".
			"\t\tif (!\$item->loadByPk($pks)) \$item=null;".$this->lf."".
			($ccdel?"\t\tif (\$item && \$loadDeleted!=true && \$item->".strtolower($ccdel->nome_fis)."==true) \$item=null;".$this->lf."":"").
			"\t\treturn \$item;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";
			$tot = $tot . $s;
		}
		return $tot;
	}

	/**************************************************************************************************************************
	** GeraGetChildren (obtém entidade filha minha, conforme cardinalidade será uma lista ou um item)
	**************************************************************************************************************************/
	function geraGetChildren() {
		if (!isset($this->relats_parent)) return null;
		$tot = "\t// --------------------------------------[Obter meus filhos DIRETOS]--------------------------------------".$this->lf."";
		foreach ($this->relats_parent as $relat) {
			if (!is_array($relat->cols_relat)) continue;
			if (!is_array($relat->cols_apoio)) continue;
			$ccdel=$relat->apoio->achaCampoDel();
			$pks = null; foreach ($relat->cols_apoio as $col) { $pks[] = "$col->nome_fis = ?"; } $pks = implode(" AND ", $pks);
			$vals = null;foreach ($relat->cols_relat as $col) {	$vals[] = array($col->getStringTipagem(), "\$this->".$col->getNome($this->nome_fis));}
			$arrproc = null; foreach ($vals as $val) { $arrproc[] = "array(".$val[0].",".$val[1].")"; }	$arrvals = "array(".implode(",", $arrproc).")";

			$nom_ext = $relat->frase_inversa?"_".str_replace(" ", "_", strtoupper($relat->frase_inversa)):"";
			if ($relat->cardinal == 2) {
				$sql = "SELECT \".\$item->getAsterCompleto().\" FROM ".$relat->apoio->nome_fis." WHERE $pks";
				if ($ccdel) $sql.= " \".(\$loadDeleted==true?\"\":\" AND (".$relat->apoio->nome_fis.".".$ccdel->nome_fis." = ? OR ".$relat->apoio->nome_fis.".".$ccdel->nome_fis." IS NULL)\").\"";
				$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext."(\$loadDeleted=false)";
				$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $relat->apoio->nome_classe_final, "Children 1");
				if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetChildren() está gerando função com nome repetido ($nomefunc). Por favor nomeie o relacionamento (".$relat->getDesc().") usando verbos diretos e **inversos**!")) continue;
				$s  = "";
				/* JavaDoc */
				$jd = new JD();
				$jd->desc = "Obtém a <code>".$relat->apoio->nome_classe_final."</code> do item atual.";
				$jd->returns = $relat->apoio->nome_classe_final;
				$s .= $jd->get(1);
				$s .=
				"\tfunction &$nomefunc {".$this->lf."".
				"\t\t\$item = ".$relat->apoio->getComoInstanciarOne().";".$this->lf."".
				"\t\t\$params = $arrvals;".$this->lf."".
				($ccdel?"\t\tif (\$loadDeleted!=true) \$params[]=array('Bit', false);".$this->lf."":"").
				"\t\tif (!\$item->load(\"$sql\", \$params)) \$item=null;".$this->lf."".
				"\t\treturn \$item;".$this->lf."".
				"\t}".$this->lf."".$this->lf."";
			} else {
				$sql = "SELECT \".\$list->getAsterCompleto().\" FROM ".$relat->apoio->nome_fis." WHERE $pks ";
				if ($ccdel) $sql.= " \".(\$loadDeleted==true?\"\":\" AND (".$relat->apoio->nome_fis.".".$ccdel->nome_fis." = ? OR ".$relat->apoio->nome_fis.".".$ccdel->nome_fis." IS NULL)\").\"";
				$nomefunc = "get".$relat->apoio->nome_classe_final_list.$nom_ext."";
				$meth = &ndocCreateMeth($this->nome_classe_final, $nomefunc, $relat->apoio->nome_classe_final_list, "Children 2");
				if (testaJaExiste($this->nome_log, $nomefunc, __FILE__, __LINE__, "geraGetChildren() está gerando função com nome repetido ($nomefunc). Por favor nomeie o relacionamento (".$relat->getDesc().") usando verbos diretos e **inversos**!")) continue;
				$s  = "";
				/* JavaDoc */
				$jd = new JD();
				$jd->desc = "Obtém uma lista de <code>".$relat->apoio->nome_classe_final."</code> do item atual.";
				$jd->addParam("orderby", "trecho SQL para ordenação da lista; deve conter <code>ORDER BY xxx</code>");
				$jd->returns = $relat->apoio->nome_classe_final_list;
				$s .= $jd->get(1);
				$s .=
				"\tfunction &$nomefunc (\$orderby=null".($ccdel?", \$loadDeleted=false":"")."){".$this->lf."".
				"\t\t\$list = ".$relat->apoio->getComoInstanciarList().";".$this->lf."".
				"\t\t\$params = $arrvals;".$this->lf."".
				($ccdel?"\t\tif (\$loadDeleted!=true) \$params[]=array('Bit', false);".$this->lf."":"").
				"\t\t\$list->loadBySQL(\"$sql\".(\$orderby?' '.\$orderby:''), \$params);".$this->lf."".
				"\t\treturn \$list;".$this->lf."".
				"\t}".$this->lf."".$this->lf."";
			}
			$tot = $tot . $s;
		}
		return $tot;
	}

	function geraFinal() {
		/* JavaDoc */
		$jd = new JD();
		$jd->desc =
					"Esta classe extende a <code>" . $this->nome_classe_rel . "</code> adicionando <b>toda a implementação de lógica de negócio relativa a ".$this->nome_log."</b>.<br>".$this->lf."".
					"Por favor <b>modifique esta classe</b>. Não faça implementações nas classes <code>" . $this->nome_classe_base. "</code> e <code>".$this->nome_classe_rel."</code>.<br>".$this->lf."".
					"Não instancie diretamente esta classe; ao invés disso utilize a classe <code>FACTORY</code>. Ex: <code>\$obj = ".$this->getComoInstanciarOne()."</code>.";
		$jd->autor = geraClassesConfigAutor();
		return
		$jd->get().
		"class ". $this->nome_classe_final . " extends ". $this->nome_classe_rel . " { ".$this->lf."".
		"}".$this->lf."";
	}

	function geraFinalList() {
		/* JavaDoc */
		$jd = new JD();
		$jd->desc =
					"Esta classe extende a <code>" . $this->nome_classe_rel_list . "</code> adicionando <b>toda a implementação de lógica de negócio relativa a uma lista de ".$this->nome_log."</b>.<br>".$this->lf."".
					"Por favor <b>modifique esta classe</b>. Não faça implementações nas classes <code>" . $this->nome_classe_base_list. "</code> e <code>".$this->nome_classe_rel_list."</code>.<br>".$this->lf."".
					"Não instancie diretamente esta classe; ao invés disso utilize a classe <code>FACTORY</code>. Ex: <code>\$objlist = ".$this->getComoInstanciarList()."</code>.";
		$jd->autor = geraClassesConfigAutor();
		return
		$jd->get().
		"class ". $this->nome_classe_final_list . " extends ". $this->nome_classe_rel_list . " { ".$this->lf."".
		"}".$this->lf."";
	}

/**************************************************************************************************************************
** Gera a classe rel para varios rows.
**************************************************************************************************************************/
	function geraRelList() {
		$jd = new JD();
		$jd->desc =
					"Esta classe é extendida pela <code>".$this->nome_classe_final_list."</code> e extende a <code>".$this->nome_classe_base_list."</code>, adicionando *absolutamente nada por enquanto*.<br>".$this->lf."".
					"<b>Por favor não instancie nem modifique esta classe!</b> Ao invés disso, utilize a classe <code>".$this->nome_classe_final_list."</code>, através de algo como <code>\$objlist = ".$this->getComoInstanciarList()."</code>.";
		$jd->autor = geraClassesConfigAutor();
		return
		$jd->get(0).
		"class ". $this->nome_classe_rel_list . " extends ". $this->nome_classe_base_list . " { ".$this->lf."".
		($this->geraMsSqlServer?$this->geraConstructorRelList():'').
		$this->geraGetByInverse().
		"}".$this->lf."";
	}


	function geraConstructorRelList(){
		$s  = "";
		/* JavaDoc */
		$jd = new JD();
		$jd->desc = "Constructor da classe ".$this->nome_classe_rel_list;
		$jd->returns = $this->nome_classe_rel_list;
		$s .= $jd->get(1);
		return
		$s.
		//"\tfunction ".$this->nome_classe_rel_list."() {".$this->lf."".
		"\tfunction __construct() {".$this->lf."".
		//"\t\t@\$this->".$this->nome_classe_base_list."();".$this->lf."".
		"\t\tparent::__construct();".$this->lf."".
		"\t}".$this->lf."".$this->lf."";
		return $s;
	}

	/**************************************************************************************************************************
	** GeraGetByUnique (gera métodos de get via os campos unique)
	**************************************************************************************************************************/
	function geraGetByInverse() {
		if (!$this->inversions) return null; // nao tem.... sorry...
		$s = "\t// ------------------------------[Obter-me via meus campos unique]--------------------------------".$this->lf."";
		foreach ($this->inversions as $un) {
			$ccnome = nomeniza($un["nome"]);
			$nomefunc = "getBy$ccnome";
			$meth = &ndocCreateMeth($this->nome_classe_final_list, $nomefunc, "boolean", "Inversion-entry get");

			$params = null;	$wh = null; $vals = null; foreach ($un["campos"] as $cc) {
				$params[] = "\$".$cc->getNome($this->nome_fis); $wh[] = $cc->nome_fis . " = ? "; $vals[] = array($cc->getStringTipagem(), "\$".$cc->getNome($this->nome_fis));
				ndocCreateParam($meth, $cc->getNome($this->nome_fis), $cc->getTipagemLimpo(), $cc->nome_log);
			} $wh = implode(" AND ", $wh); $params = implode(",", $params);
			$arrproc = null; foreach ($vals as $val) {$arrproc[] = "array(".$val[0].",".$val[1].")";} $arrvals = "array(".implode(",", $arrproc).")";
			$sql = "SELECT \".\$this->getAster().\" FROM " . $this->nome_fis . " WHERE $wh";

			/* JavaDoc */
			$jd = new JD();
			$jd->desc = "Obtém um registro através de campo(s) que foram designados para uma inversion entry (indexados).";
			$jd->returns = "boolean";
			$s .= $jd->get(1);
			$s .= "";
			$s .= "	function $nomefunc(".$params.") {".$this->lf."";
			$s .= "		return \$this->loadBySQL(\"".$sql."\", $arrvals);".$this->lf."";
			$s .= "	}".$this->lf."".$this->lf."";
		}
		return $s;
	}

	function geraFlashBase_deprecado() {
		$s  = "// READ-ONLY! Não modifique. Isto *vai* ser sobrescrito!.".$this->lf."".$this->lf."";
		$s .= "class ".$this->flash_nome_classe_base_full." {".$this->lf."";
		//$s .= $this->flashCriaConstructor();
		$s .= $this->geraDeclVarsFlash($this->campos);
		$s .= $this->geraTraceVarsFlash($this->campos);
		$s .= $this->flashBaseCriaGetParents();
		$s .= $this->flashBaseCriaGetChildren();
		$s .= "}".$this->lf."";
		return $s;
	}

	function geraDeclVarsFlash_deprecado($arr) {
		if (!is_array($arr) )  return null;
		$s = "";
		foreach ($arr as $c) {
			$s .= "\tpublic var ".$c->getNomeFlash().":".$c->getTipoFlash()." = null;"."".$this->lf."";
		}
		return $s;
	}

	function geraTraceVarsFlash_deprecado($arr) {
		if (!is_array($arr) )  return null;
		$s = "".$this->lf."";
		$s .= "\tfunction trace():Void {".$this->lf."";
		$s .= "\t\t". 'trace("/-[begin]--=['.$this->flash_nome_classe_final_full.']=----");' . "".$this->lf."";
		foreach ($arr as $c) {
			$s .= "\t\t". 'trace("|-['.$c->getNomeFlash().' ('.$c->getTipoFlash().'): " + '.$c->getNomeFlash().');' . "".$this->lf."";
		}
		$s .= "\t\t". 'trace("\\\\-[ end ]--=['.$this->flash_nome_classe_final_full.']=----");' . "".$this->lf."";
		$s .= "\t}".$this->lf."";
		return $s;
	}

	function flashBaseCriaGetParents_deprecado() {
		if (!isset($this->relats_child)) return null;
		$s  = "";
		foreach ($this->relats_child as $relat) {
			if (!is_array($relat->cols_apoio)) continue;
			$nom_ext = $relat->frase?"_".str_replace(" ", "_", strtoupper($relat->frase)):"";
			$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext."";
			$s .= "\t"."function $nomefunc(handler):Void {"."".$this->lf."";
			$s .= "\t\t"."var onePeer:".$this->flash_nome_classe_peer_final_full." = new ".$this->flash_nome_classe_peer_final_full."();"."".$this->lf."";
			$s .= "\t\t"."onePeer.onArrived = handler;"."".$this->lf."";
			$s .= "\t\t"."onePeer.$nomefunc(this);"."".$this->lf."";
			//$s .= "\t\t".""."".$this->lf."";
			$s .= "\t"."}"."".$this->lf."".$this->lf."";
		}
		return $s;
	}


	function flashBaseCriaGetChildren_deprecado() {
		if (!isset($this->relats_parent)) return null;
		$s = "";
		foreach ($this->relats_parent as $relat) {
			if (!is_array($relat->cols_relat)) continue;
			if (!is_array($relat->cols_apoio)) continue;
			$nom_ext = $relat->frase_inversa?"_".str_replace(" ", "_", strtoupper($relat->frase_inversa)):"";
			if ($relat->cardinal == 2) {
				$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext;
				$s .= "\t"."function $nomefunc(handler):Void {"."".$this->lf."";
				$s .= "\t\t"."var onePeer:".$this->flash_nome_classe_peer_final_full." = new ".$this->flash_nome_classe_peer_final_full."();"."".$this->lf."";
				$s .= "\t\t"."onePeer.onArrived = handler;"."".$this->lf."";
				$s .= "\t\t"."onePeer.$nomefunc(this);"."".$this->lf."";
				$s .= "\t"."}"."".$this->lf."".$this->lf."";
			} else {
				$nomefunc = "get".$relat->apoio->nome_classe_final_list.$nom_ext;
				$s .= "\t"."function $nomefunc(handler):Void {"."".$this->lf."";
				$s .= "\t\t"."var onePeer:".$this->flash_nome_classe_peer_final_full." = new ".$this->flash_nome_classe_peer_final_full."();"."".$this->lf."";
				$s .= "\t\t"."onePeer.onArrived = handler;"."".$this->lf."";
				$s .= "\t\t"."onePeer.$nomefunc(this);"."".$this->lf."";
				$s .= "\t"."}"."".$this->lf."".$this->lf."";
			}
		}
		return $s;
	}

	function geraFlashFinal_deprecado() {
		$s = "// Whee! ".$this->flash_nome_classe_final." extends ".$this->flash_nome_classe_base." ".$this->lf."".$this->lf."";
		$s .= "class ".$this->flash_nome_classe_final_full." extends ".$this->flash_nome_classe_base_full." {".$this->lf."";
		$s .= "}".$this->lf."";
		return $s;
	}

	/* AS1
	function geraFlashFinal() {
		$s = "// Whee! ".$this->flash_nome_classe_final." extends ".$this->flash_nome_classe_base." ".$this->lf."".$this->lf."";
		$s .= $this->flashCriaConstructorFinal();
		$s .= $this->flashCospeObjectRegisterClass();
		return $s;
	}

	function flashCriaConstructorFinal() {
		$s  = "";
		$s .= "_global.".$this->flash_nome_classe_final." = function () {".$this->lf."";
		$s .= "}".$this->lf."";
		$s .= ""."_global.".$this->flash_nome_classe_final.".prototype = new ".$this->flash_nome_classe_base."();"."".$this->lf."".$this->lf."";
		return $s;
	}


	function flashCospeObjectRegisterClass() {
		$s = "Object.registerClass(\"".$this->flash_nome_classe_final."Class\", ".$this->flash_nome_classe_final.");".$this->lf."".$this->lf."";
		return $s;
	}
	*/

	function geraFlashPeerBase_deprecado() {
		$s  = "// READ-ONLY! Não modifique. Isto *vai* ser sobrescrito!.".$this->lf."".$this->lf."";
		$s .= "class ".$this->flash_nome_classe_peer_base_full." extends gc.ro.BaseServicePeer {".$this->lf."";
		$s .= $this->flashPeerCriaCreateMyService();
		$s .= $this->flashPeerCriaGetByPK();
		$s .= $this->flashPeerLISTCriaGetTodos();
		$s .= $this->flashPeerCriaGetParents();
		$s .= $this->flashPeerCriaGetChildren();
		$s .= "}".$this->lf."";
		return $s;
	}


	function flashPeerCriaCreateMyService_deprecado() {
		$s  = "\t"."function createMyService():mx.remoting.NetServiceProxy {".$this->lf."";
		$s .= "\t\t"."return this.createService(\"".$this->flash_nome_classe_service_final."\");".$this->lf."";
		$s .= "\t"."}".$this->lf."".$this->lf."";
		return $s;
	}

	function flashPeerCriaGetByPK_deprecado() {
		if (!isset($this->campos_pk)) return "";
		$s  = "";
		$defstr = null;
		$defstrsemtipo = null;
		foreach ($this->campos_pk as $c) {
			$defstr[] = $c->getNomeFlash().":".$c->getTipoFlash();
			$defstrsemtipo[] = $c->getNomeFlash();
		}
		$s .= "\t"."function getByPK(".implode(",", $defstr)."):Void {"."".$this->lf."";
		$s .= "\t\t"."var myService:mx.remoting.NetServiceProxy = this.createMyService();"."".$this->lf."";
		$s .= "\t\t"."this.clearResult();"."".$this->lf."";
		$s .= "\t\t"."myService.getByPK(this, ".implode(",", $defstrsemtipo).");"."".$this->lf."";
		$s .= "\t\t".""."".$this->lf."";
		$s .= "\t"."}"."".$this->lf."";
		return $s;
	}


	function flashPeerLISTCriaGetTodos_deprecado() {
		if (!isset($this->campos_pk)) return "";
		$s  = "";
		$s .= "\t"."function getTodosList():Void {"."".$this->lf."";
		$s .= "\t\t"."var myService:mx.remoting.NetServiceProxy = this.createMyService();"."".$this->lf."";
		$s .= "\t\t"."this.clearResult();"."".$this->lf."";
		$s .= "\t\t"."myService.getTodosList(this, null);"."".$this->lf."";
		$s .= "\t"."}"."".$this->lf."";
		return $s;
	}

	function flashPeerCriaGetParents_deprecado() {
		if (!isset($this->relats_child)) return null;
		$s  = "";
		foreach ($this->relats_child as $relat) {
			if (!is_array($relat->cols_apoio)) continue;
			$nom_ext = $relat->frase?"_".str_replace(" ", "_", strtoupper($relat->frase)):"";
			$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext."";
			$s .= "\t"."function $nomefunc(caller):Void {"."".$this->lf."";
			$s .= "\t\t"."var myService:mx.remoting.NetServiceProxy = this.createMyService();"."".$this->lf."";
			$s .= "\t\t"."this.clearResult();"."".$this->lf."";
			$s .= "\t\t"."myService.$nomefunc(this, caller);"."".$this->lf."";
			$s .= "\t"."}"."".$this->lf."".$this->lf."";
		}
		return $s;
	}

	function flashPeerCriaGetChildren_deprecado() {
		if (!isset($this->relats_parent)) return null;
		$s = "";
		foreach ($this->relats_parent as $relat) {
			if (!is_array($relat->cols_relat)) continue;
			if (!is_array($relat->cols_apoio)) continue;
			$nom_ext = $relat->frase_inversa?"_".str_replace(" ", "_", strtoupper($relat->frase_inversa)):"";
			if ($relat->cardinal == 2) {
				$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext;
			} else {
				$nomefunc = "get".$relat->apoio->nome_classe_final_list.$nom_ext;
			}
			$s .= "\t"."function $nomefunc(caller):Void {"."".$this->lf."";
			$s .= "\t\t"."var myService:mx.remoting.NetServiceProxy = this.createMyService();"."".$this->lf."";
			$s .= "\t\t"."this.clearResult();"."".$this->lf."";
			$s .= "\t\t"."myService.$nomefunc(this, caller);"."".$this->lf."";
			$s .= "\t"."}"."".$this->lf."".$this->lf."";
		}
		return $s;
	}

	function geraFlashPeerFinal_deprecado() {
		$s = "// Whee! ".$this->flash_nome_classe_peer_final." extends ".$this->flash_nome_classe_peer_base." ".$this->lf."".$this->lf."";
		$s .= "class ".$this->flash_nome_classe_peer_final_full." extends ".$this->flash_nome_classe_peer_base_full." {".$this->lf."";
		$s .= "}".$this->lf."";
		return $s;
	}

	var $meths = null;

	function geraFlashServiceBase_deprecado() {
		$this->meths = null;
		$this->meths[] = $this->geraFlashServiceGetByPKMethod();
		$this->meths[] = $this->geraFlashServiceLISTGetTodos();
		$this->geraFlashServiceGetParents($this->meths);
		$this->geraFlashServiceGetChildren($this->meths);
		$s  = "";
		$s .= "include_once(\"codebase/flash/services/ro/BaseService.php\");".$this->lf."".$this->lf."";
		$s .= "class " . $this->flash_nome_classe_service_base . " extends gcBASE_SERVICE { ".$this->lf."";
		$s .= $this->geraFlashServiceBaseConstructor($this->meths);
		foreach ($this->meths as $meth) {
			if ($meth) $s .= $meth->codigo;
		}

		$s .= "}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraFlashServiceBaseConstructor_deprecado($meths) {
		$s  = "";
		$s .= "\t"."function " . $this->flash_nome_classe_service_base . "() {"."".$this->lf."";
		$s .= "\t\t"."\$this->gcBASE_SERVICE();"."".$this->lf."";
		foreach ($meths as $meth) {
			if ($meth) $s .= $meth->getRegisterService();
		}
		$s .= "\t"."}"."".$this->lf."".$this->lf."";
		return $s;
	}

	function geraFlashServiceGetByPKMethod_deprecado() {
		if (!isset($this->campos_pk)) return null;
		$meth = new FLASH_GENERATOR_SERVICE_METHOD();
		$meth->name = "getByPK";
		$meth->description = '(geraFlashServiceGetByPKMethod) Retorna um '.$this->nome_classe_final.' dada sua chave primária';
		$meth->access = 'remote';
		$meth->roles = '';
		$meth->arguments = null;
		foreach ($this->campos_pk as $c) {
			$meth->arguments[] = '"'.$c->getNome().'"';
			$args[] = '$'.$c->getNome();
		}
		$meth->returns = $this->nome_classe_final;

		$s  = "";
		$s .= "\t"."function ".$meth->name."(".implode(",", $args).") {"."".$this->lf."";
		$s .= "\t\t"."\$obj = ".$this->getComoInstanciarOne().";"."".$this->lf."";
		$s .= "\t\t"."\$obj->loadByPk(".implode(",", $args).");"."".$this->lf."";
		$s .= "\t\t"."return \$this->preparaObjeto(\$obj);"."".$this->lf."";
		$s .= "\t"."}"."".$this->lf."".$this->lf."";
		$meth->codigo = $s;
		return $meth;
	}

	function geraFlashServiceLISTGetTodos_deprecado() {
		if (!isset($this->campos_pk)) return null;
		$meth = new FLASH_GENERATOR_SERVICE_METHOD();
		$meth->name = "getTodosList";
		$meth->description = '(geraFlashServiceLISTGetTodos) Retorna uma '.$this->nome_classe_final_list.' contendo todos os registros';
		$meth->access = 'remote';
		$meth->roles = '';
		$meth->arguments = null;
		$meth->returns = $this->nome_classe_final_list;

		$s  = "";
		$s .= "\t"."function ".$meth->name."() {"."".$this->lf."";
		$s .= "\t\t"."\$obj = ".$this->getComoInstanciarList().";"."".$this->lf."";
		$s .= "\t\t"."\$obj->getTodos();"."".$this->lf."";
		$s .= "\t\t"."return \$this->preparaLista(\$obj);"."".$this->lf."";
		$s .= "\t"."}"."".$this->lf."".$this->lf."";
		$meth->codigo = $s;
		return $meth;
	}

	/**************************************************************************************************************************
	** GeraGetParents (obtém uma entidade pai minha)
	**************************************************************************************************************************/
	function geraFlashServiceGetParents_deprecado(&$meths) {
		if (!isset($this->relats_child)) return null;
		foreach ($this->relats_child as $relat) {
			if (!is_array($relat->cols_apoio)) continue;
			$nom_ext = $relat->frase?"_".str_replace(" ", "_", strtoupper($relat->frase)):"";
			$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext."";
			$pks = null; foreach ($relat->cols_relat as $col) {	$pks[] = "\$obj->".$col->getNome(); } $pks = implode(",", $pks);

			$meth = new FLASH_GENERATOR_SERVICE_METHOD();
			$meth->name = $nomefunc;
			$meth->description = "(geraFlashServiceGetParents) Obtém a ".$relat->apoio->nome_classe_final." do ".$this->nome_classe_final." passado.";
			$meth->access = 'remote';
			$meth->roles = '';
			$meth->arguments = array('"'."obj".'"');
			$meth->returns = (isset($this->apoio)? $this->apoio->nome_classe_final:'');

			$s  = "";
			$s .=
			"\tfunction &".$nomefunc."(\$obj) {".$this->lf."".
			"\t\tif (\$item = \$obj->$nomefunc()) return \$this->preparaObjeto(\$item);".$this->lf."".
			"\t\treturn null;".$this->lf."".
			"\t}".$this->lf."".$this->lf."";

			$meth->codigo = $s;
			$meths[] = $meth;
		}
	}

	/**************************************************************************************************************************
	** GeraGetChildren (obtém entidade filha minha, conforme cardinalidade será uma lista ou um item)
	**************************************************************************************************************************/
	function geraFlashServiceGetChildren_deprecado(&$meths) {
		if (!isset($this->relats_parent)) return null;
		foreach ($this->relats_parent as $relat) {
			if (!is_array($relat->cols_relat)) continue;
			if (!is_array($relat->cols_apoio)) continue;
			$nom_ext = $relat->frase_inversa?"_".str_replace(" ", "_", strtoupper($relat->frase_inversa)):"";
			$meth = new FLASH_GENERATOR_SERVICE_METHOD();
			if ($relat->cardinal == 2) {
				$nomefunc = "get".$relat->apoio->nome_classe_final.$nom_ext;
				$meth->name = $nomefunc;
				$meth->description = "(geraFlashServiceGetChildren) Obtém a ".$relat->apoio->nome_classe_final." do ".$this->nome_classe_final." passado.";
				$meth->access = 'remote';
				$meth->roles = '';
				$meth->arguments = array('"'."obj".'"');
				$meth->returns = $relat->apoio->nome_classe_final;
				$s =
				"\tfunction &".$nomefunc."(\$obj) {".$this->lf."".
				"\t\tif (\$item = \$obj->$nomefunc()) return \$this->preparaObjeto(\$item);".$this->lf."".
				"\t\treturn null;".$this->lf."".
				"\t}".$this->lf."".$this->lf."";
			} else {
				$nomefunc = "get".$relat->apoio->nome_classe_final_list.$nom_ext;
				$meth->name = $nomefunc;
				$meth->description = "(geraFlashServiceGetChildren) Obtém uma lista de ".$relat->apoio->nome_classe_final." do ".$this->nome_classe_final." passado.";
				$meth->access = 'remote';
				$meth->roles = '';
				$meth->arguments = array('"'."obj".'"');
				$meth->returns = $relat->apoio->nome_classe_final_list;
				$s =
				"\tfunction &".$nomefunc."(\$obj) {".$this->lf."".
				"\t\tif (\$lista = \$obj->$nomefunc()) return \$this->preparaLista(\$lista);".$this->lf."".
				"\t\treturn null;".$this->lf."".
				"\t}".$this->lf."".$this->lf."";
			}
			$meth->codigo = $s;
			$meths[] = $meth;
		}
	}

 	function geraFlashServiceFinal_deprecado() {
		$s  = "";
		$s .= "include_once(\"codebase/flash/services/ro/".$this->flash_nome_classe_service_base.".php\");".$this->lf."".$this->lf."";
		$s .= "class " . $this->flash_nome_classe_service_final . " extends ".$this->flash_nome_classe_service_base." { ".$this->lf."";
		$s .= $this->geraFlashServiceFinalConstructor();
		$s .= "}".$this->lf."".$this->lf."";
		return $s;
	}

	function geraFlashServiceFinalConstructor_deprecado() {
		$s  = "";
		$s .= "\t"."function " . $this->flash_nome_classe_service_final . "() {"."".$this->lf."";
		$s .= "\t\t"."\$this->".$this->flash_nome_classe_service_base."();"."".$this->lf."";
		$s .= "\t\t"."// Remember to enable services here; the services defined in the base class are *not* enabled by default for security reasons."."".$this->lf."";

		/* @TODO: Desabilitar e deixar apenas como comentários. */
		foreach ($this->meths as $meth) {
			if ($meth) $s .= "\t\t"."\$this->enableMethod('". $meth->name  ."');"."".$this->lf."";
		}
		$s .= "\t\t"."\$this->generateMethodTable();"."".$this->lf."";
		$s .= "\t"."}"."".$this->lf."";
		return $s;
	}

	function geraFlashListBase_deprecado() {
		$s  = "// READ-ONLY! Não modifique. Isto *vai* ser sobrescrito!.".$this->lf."".$this->lf."";
		$s .= "class ".$this->flash_nome_classe_list_base_full." {".$this->lf."";
		$s .= "\tpublic var EOF:Boolean = null;".$this->lf."";
		$s .= "\tpublic var quantos:Number = null;".$this->lf."";
		$s .= "\tpublic var recs:Array = null;".$this->lf."";
		$s .= "\tpublic var n_atual:Number = null;".$this->lf."";
		$s .= $this->geraTraceVarsFlashList();
		$s .= $this->geraFlashGetNextItem();
		$s .= "}".$this->lf."";
		return $s;
	}
	function geraTraceVarsFlashList_deprecado() {
		$s = "".$this->lf."";
		$s .= "\tfunction trace():Void {".$this->lf."";
		$s .= "\t\t". 'trace("/-[begin]--=['.$this->flash_nome_classe_list_final_full.']=----");' . "".$this->lf."";
		$s .= "\t\t". 'trace("|-[EOF (Boolean): " + EOF);' . "".$this->lf."";
		$s .= "\t\t". 'trace("|-[quantos (Number): " + quantos);' . "".$this->lf."";
		$s .= "\t\t". 'trace("|-[n_atual (Number): " + n_atual);' . "".$this->lf."";
		$s .= "\t\t". 'trace("\\\\-[ end ]--=['.$this->flash_nome_classe_list_final_full.']=----");' . "".$this->lf."";
		$s .= "\t}".$this->lf."";
		return $s;
	}

	function geraFlashGetNextItem_deprecado() {
		$s = "".$this->lf."";
		$s .= "\tfunction getNextItem():" . $this->flash_nome_classe_final_full . " {".$this->lf."";
		$s .= "\t\tif (this.EOF) return null;".$this->lf."";
		$s .= "\t\tthis.n_atual++;".$this->lf."";
		$s .= "\t\tif (this.n_atual > this.quantos-1) this.EOF = true;".$this->lf."";
		$s .= "\t\treturn this.recs[this.n_atual-1];".$this->lf."";
		$s .= "\t}".$this->lf."";
		return $s;
	}

	function geraFlashListFinal_deprecado() {
		$s = "// Whee! ".$this->flash_nome_classe_list_final." extends ".$this->flash_nome_classe_list_base." ".$this->lf."".$this->lf."";
		$s .= "class ".$this->flash_nome_classe_list_final_full." extends ".$this->flash_nome_classe_list_base_full." {".$this->lf."";
		$s .= "}".$this->lf."";
		return $s;
	}
}
?>
