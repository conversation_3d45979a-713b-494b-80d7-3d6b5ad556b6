<?php
include_once('../codebase/function/func_geramodelos.inc');
/*
$d=$aqui->myDire->dire3_txt;
$d='/statmkt/jaca';
$d='/';
$p=substr($d,0,strpos($d.'/','/',1));
echo $p."<br/>";
*/
require('func_comparabanco.inc');
set_time_limit(0);
$debug=false;
$viaAjax=true;
if ($_REQUEST['debug']=='true') $debug=true;
$action=$_REQUEST['ati'];
switch ($action){
	case 'compare_go':
		if ($viaAjax) comparaAjax(); else compara();
		break;
	case 'doalter':
	case 'alter':
		if ($_REQUEST['go']=='true'){
			executaAlter();
		}else{
			mostraAlter();
		}
		break;
	case 'alter_go':
		executaAlter();
		break;
	default:
		include('layout/init.inc');
		cospeCabecalhoGera();
		cospeForm();
		include('layout/end.inc');
		break;
}
function comparaAjax(){
	include_once('codebase/function/func_ajax.inc');
	cospeJSAjaxMulti();
	cospeCabecalhoCompara();
	$trava=$_REQUEST['trava'];
	$doFks=($_REQUEST['do_fks']=='true');
	$doIdx=($_REQUEST['do_idx']=='true');
	$doSeq=($_REQUEST['do_seq']=='true');
	$soPublic=($_REQUEST['soPublic']=='true');

	$tipo1=$_REQUEST['tipo1'];
	$host1=$_REQUEST['host1'];
	$user1=$_REQUEST['user1'];
	$pass1=$_REQUEST['pass1'];
	$options1=$_REQUEST['options1'];
	$base1=$_REQUEST['base1'];
	$schema1=$_REQUEST['schema1'];

	$tipo2=$_REQUEST['tipo2'];
	$host2=$_REQUEST['host2'];
	$user2=$_REQUEST['user2'];
	$pass2=$_REQUEST['pass2'];
	$options2=$_REQUEST['options2'];
	$base2=$_REQUEST['base2'];
	$schema2=$_REQUEST['schema2'];
	$qs='trava='.$trava.'&doFks='.$doFks.'&doIdx='.$doIdx.'&doSeq='.$doSeq.'&soPublic='.$soPublic;
	$qs.='&tipo1='.$tipo1.'&host1='.$host1.'&user1='.$user1.'&pass1='.$pass1.'&base1='.$base1.'&schema1='.$schema1.'&options1='.$options1;
	$qs.='&tipo2='.$tipo2.'&host2='.$host2.'&user2='.$user2.'&pass2='.$pass2.'&base2='.$base2.'&schema2='.$schema2.'&options2='.$options2;

	global $_pathModelos;
	if (false && $host1=='json'){
		$umDB1=(object)null;
		$umDB1->configDbType='json';
		$umDB1->configDbPers=false;
		$umDB1->configDbServer='';
		$umDB1->configDbUser='';
		$umDB1->configDbPass='';
		$umDB1->configDbName='';
		$umDB1->nome=$_pathModelos.$base1;
		if (!$db1=getConexao($umDB1)){
			echo "Não foi possível conectar-se à base 1";exit;
		}
		$db1->nome=$umDB1->nome;
		$db1->schema=$_REQUEST['schema1'];
		if (empty($db1->schema)) $db1->schema='public';

		$umDB2=(object)null;
		$umDB2->configDbType=$_REQUEST['tipo2'];
		$umDB2->configDbPers=false;
		$umDB2->configDbServer=$_REQUEST['host2'];
		$umDB2->configDbUser=$_REQUEST['user2'];
		$umDB2->configDbPass=$_REQUEST['pass2'];
		$umDB2->configDbOptions=$_REQUEST['options2'];
		$umDB2->configDbName=$base2;
		$umDB2->nome=$umDB2->configDbServer.' '.$umDB2->configDbName;
		if (!($umDB2->configDbType && $umDB2->configDbUser && $umDB2->configDbPass)){//$umDB2->configDbServer&&
			echo("Dados incompletos para a base 2<br>");
			return;
		}
		if (!$db2=getConexao($umDB2)){
			echo "Não foi possível conectar-se à base 2";exit;
		}
		
		$db2->nome=$umDB2->nome;
		$db2->schema=$_REQUEST['schema2'];
		if (empty($db2->schema)) $db2->schema='public';
		$schema1=$db1->schema;
		$schema2=$db2->schema;
		$ora1=isOracle($db1);
		$ora2=isOracle($db2);
		$ms1=isMSSqlServer($db1);
		$ms2=isMSSqlServer($db2);
		ob_start();
		$tabs=getArrayTabelas($db1, false, true, $soPublic, $schema1);
		$tabs2=getArrayTabelas($db2, false, true, $soPublic, $schema2);
		$html=ob_get_clean();
		foreach($tabs as $nome=>$tab){ //para cada tabela da local
			if (empty($schema1)==false){
				if ($tab->schema!=$schema1) {
					//echo $tab->schema.' - '.$nome."<br/>";
					continue;
				}
			}
			$fks1=MetaForeignKeys($db1, $nome);
			$fks2=MetaForeignKeys($db2, $nome);
			foreach ($fks1 as $nomefk=>$dados){
				asort($dados);
				foreach($fks2 as $nomefk2=>$dados2){
					asort($dados2);
					if ($nomefk2==$nomefk){
						echo "ACHEI via nome: ".$nomefk2." - ".print_r($dados2, true)."<br/>IGUAL: ".$nomefk." - ".print_r($dados, true)."<br/>";
					}elseif(in_arrayi($dados[0], $dados2)){
						echo "ACHEI via dados: ".$nomefk2." - ".print_r($dados2, true)."<br/>IGUAL: ".$nomefk." - ".print_r($dados, true)."<br/>";
					}
				}
			}
			echo $nome.' - '. nl2br(print_r($fks1, true))."<br/>";
			echo $nome.' - '. nl2br(print_r($fks2, true))."<br/>";
			exit;
		}
	}
?>

<script language="JavaScript">
var bases=new Array();
var b1={};
b1.tipo='<?=$tipo1?>';
b1.pers='false';
b1.serv='<?=$host1?>';
b1.user='<?=$user1?>';
b1.pass='<?=$pass1?>';
b1.options='<?=$options1?>';
b1.name='<?=$base1?>';
b1.schema='<?=$schema1?>';
bases.push(b1);
var b2={};
b2.tipo='<?=$tipo2?>';
b2.pers='false';
b2.serv='<?=$host2?>';
b2.user='<?=$user2?>';
b2.pass='<?=$pass2?>';
b2.options='<?=$options2?>';
b2.name='<?=$base2?>';
b2.schema='<?=$schema2?>';
bases.push(b2);
var qs='<?=$qs?>';
var showErro=true;
//debugger;
callCnetMulti('compara', qs, null, null, true, 'ajax_cb.php');
var divprog=document.getElementById("progresso");
function showTools(tool){
	var div=document.getElementById('toolsCompara');
	if (div.style.display=='none'){
		div.style.display='';
	}
	var h=div.innerHTML;
	if (h!='') h+=' | ';
	h+=tool;
	div.innerHTML=h;
}
function progresso(msg){
	if (divprog==null) return;
	divprog.style.display='';
	divprog.innerHTML=msg;
}
function umHtml(h){
	document.appendChild(h);
}
var divprog=document.getElementById("progresso");
function progresso(msg){
	if (divprog==null) return;
	divprog.style.display='';
	divprog.innerHTML=msg;
}
function addTr(html, bg){
	var tr=document.createElement('tr');
	if (typeof bg !='undefined') tr.style.backgroundColor=bg;
	tr.innerHTML=html;
	document.getElementById('tabCompara').appendChild(tr);
}
</script>
<style>
table.borda {
	border-top:solid 1px #c0c0c0;;
	border-left:solid 1px #c0c0c0;
}

table.borda TD{
	border-bottom:solid 1px #c0c0c0;
	border-right:solid 1px #c0c0c0;
	font-family: arial;
	font-size: 9pt;
}
.bloq{
	background-color:whitesmoke;
	color:gray;
}
.drop{
	background-color:#ffdbdb;
}
.primeira{
	border:solid 1px red;
}
.sequencia{
	/*border:solid 1px green;*/
}
.segunda{
	border:solid 1px blue;
}
.ausente{
	background-color:#ffff00;
}
.tit-tabela{
	border:solid 1px black;
}
</style>
<table id="tabCompara" cellpadding="2" cellspacing="0" class="borda">
	<tr>
		<td colspan="4">Comparação da <b><?=$host1.' '.$base1?></b> com <b><?=$host2.' '.$base2?></b></td>
	</tr>
	<tr bgcolor="silver">
		<td><b>Coluna</b></td>
		<td><b><?=$host1.' '.$base1?></b></td>
		<td><b><?=$host2.' '.$base2?></b></td>
		<td><b>Ação</b></td>
	</tr>

</table>
<?
}

?>
