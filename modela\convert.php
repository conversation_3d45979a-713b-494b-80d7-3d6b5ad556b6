<?php
include_once('../codebase/function/func_geramodelos.inc');
include("../gera/function/function.inc");
include('layout/init.inc');

if (isset($_POST['modelFile'])) {
	$sourceFile = $_POST['modelFile'];
	$targetFile = '';
	$sourceExt = pathinfo($sourceFile, PATHINFO_EXTENSION);
	if ($sourceExt == 'json') {
		$targetFile = str_replace('.json', '.xml', $sourceFile);
		$targetExt = 'xml';
	} else if ($sourceExt == 'xml') {
		$targetFile = str_replace('.xml', '.json', $sourceFile);
		$targetExt = 'json';
	}
	if (file_exists($targetFile)) {
		echo "Target file already exists: $targetFile";
		exit;
	}
	if ($sourceExt == 'json') {
		$json = file_get_contents($sourceFile);
		$xml = generateXmlFromJSON($json);
		$xml->dump_file($targetFile, false, true);
	} else if ($sourceExt == 'xml') {
		$json = generateJsonFromXml($sourceFile);
		file_put_contents($targetFile, $json);
	}
	echo "Conversion completed: $sourceFile -> $targetFile";
	exit;
}

// Get list of JSON files 
$jsonFiles = getModelFilesList();
$xmlFiles = getModelFilesList('xml', false);
$files = array_merge($jsonFiles, $xmlFiles);
// echo nl2br(print_r($xmlFiles, true))."<br/>";
// Show form to select file to convert
?>
<div class="container">
	<div class="row">
		<div class="col-md-12">
			<h1>Convert Files</h1>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">Convert JSON to XML or vice versa</h3>
				</div>
				<div class="panel-body">
					<form method="post" action="convert.php">
						<div class="form-group">
							<label for="modelFile">JSON/XML File:</label>
							<select name="modelFile" id="modelFile" class="form-control">
								<?php foreach ($files as $fullPath=>$def): ?>
									<option value="<?php echo htmlspecialchars($fullPath); ?>">
										<?php echo htmlspecialchars($fullPath); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
						<button type="submit" class="btn btn-primary">Convert</button>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<?php
include('layout/end.inc');
