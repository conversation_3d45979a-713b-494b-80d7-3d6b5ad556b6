<?php

/* Para ordenação de campos dentro das entidades */

function ordenacaoConsistenteCampos($a, $b) {
	if ($a->ident_campo) return -1;
	if ($b->ident_campo) return 1;
	return strnatcmp($a->nome_fis, $b->nome_fis);
}

function ordenacaoConsistenteCamposRelatRelat($a1, $b1) {
	$a =& $a1["relat"];
	$b =& $b1["relat"];
	if ($a->ident_campo) return -1;
	if ($b->ident_campo) return 1;
	return strnatcmp($a->nome_fis, $b->nome_fis);
}

function ordenacaoConsistenteCamposRelatApoio($a1, $b1) {
	$a =& $a1["apoio"];
	$b =& $b1["apoio"];
	if ($a->ident_campo) return -1;
	if ($b->ident_campo) return 1;
	return strnatcmp($a->nome_fis, $b->nome_fis);
}

/* Para ordenação das entidades */
function ordenacaoConsistenteEntidades($a, $b) {
	return strnatcmp($a->nome_log, $b->nome_log);
}



/**************************************************************************************************************************
** Representa um campo.
**************************************************************************************************************************/
class PHP_GENERATOR_CAMPO {
	var $nome_log;
	var $nome_fis;
	var $default=null;
	var $identity;
	
	function __construct($xml, $nome_fis) {
		$this->tabela_nativa = $nome_fis;
		$this->nome_fis =  strtoupper($xml->get_Attribute("NomeFisico"));
		$this->nome_log =  strtoupper($xml->get_Attribute("NomeLogico"));
		$this->dt_campo   =  $xml->get_Attribute("DataType");
		$this->dtl_campo   =  $xml->get_Attribute("DataLength");
		$this->ident_campo = $xml->get_Attribute("Identity");
		$this->null_campo  = $xml->get_Attribute("NotNull");
		if ($xml->has_attribute('default')) {
			$this->default = $xml->get_attribute("default");
		}
	}
	
	function getNome($onde=null) {
		return strtolower($this->nome_fis);
	}
	
	function getNomeFlash() {
		return strtolower($this->nome_fis);
	}
	
	function getNomeColecao($onde=null) {
		return strtolower($this->nome_fis)."s";
	}
	
	function getStringMandaSQL($paraquem=null, $escapa=true) {
		switch ($this->dt_campo) {
			case 'VARCHAR':
				$func = "mandaVarchar";
				break;
			case 'BIGINT':
			case 'INTEGER':
				$func = "mandaInteger";
				break;
			case 'FLOAT':
			case 'DOUBLE PRECISION':
				$func = "mandaDouble";
				break;
			case 'TEXT':
				$func = "mandaText";
				break;
			case 'DATE':
				$func = "mandaDate";
				break;
			case 'BIT':
				$func = "mandaBit";
				break;
			default:
				$func = "mandaALGO" . $this->dt_campo;
				break;
		}
		return "".($escapa?"\".":"")."\$db->$func(\$this->".$this->getNome($paraquem).")".($escapa?".\"":"")."";
	}
	
	function getStringThis($paraquem=null, $escapa=true) {
		return "".($escapa?"\".":"")."\$this->".$this->getNome($paraquem)."".($escapa?".\"":"")."";
	}
	
	function getApresenta() {
		return $this->getApresentaPuro($this->nome_log);
	}
	
	function getStringTipagem() {
		$dt=$this->dt_campo;
		$dtl=$this->dtl_campo;
		switch ($dt) {
			case 'VARCHAR':
				$func = "Varchar";
				break;
			case 'BIGINT':
			case 'INTEGER':
				$func = "Integer";
				break;
			case 'FLOAT':
			case 'DOUBLE PRECISION':
				$func = "Double";
				break;
			case 'TEXT':
				$func = "Text";
				break;
			case 'DATE':
				$func = "Date";
				break;
			case 'BIT':
				$func = "Bit";
				break;
			case 'POINT':
			case 'POINTM':
			case 'POINTZ':
			case 'MULTIPOINT':
			case 'MULTIPOINTM':
			case 'MULTIPOINTZ':
			case 'LINESTRING':
			case 'LINESTRINGM':
			case 'LINESTRINGZ':
			case 'MULTILINESTRING':
			case 'MULTILINESTRINGM':
			case 'MULTILINESTRINGZ':
			case 'POLYGON':
			case 'POLYGONM':
			case 'POLYGONZ':
			case 'MULTIPOLYGON':
			case 'MULTIPOLYGONM':
			case 'MULTIPOLYGONZ':
			case 'GEOMETRY':
			case 'GEOGRAPHY':
				$func = $dt;
				break;
			case 'IMAGE/LONG BINARY':
				$dtl='3';
			case 'NVARCHAR':
				switch($dtl){
					case '1':
						$func='MULTIPOINTM';
						break;
					case '12':
						$func='POINT';
						break;
					case '13':
						$func='POINTM';
						break;
					case '14':
						$func='POINTZ';
						break;
					case '112':
						$func='MULTIPOINT';
						break;
					case '113':
						$func='MULTIPOINTM';
						break;
					case '114':
						$func='MULTIPOINTZ';
						break;
					case '2':
						$func='MULTILINESTRINGM';
						break;
					case '22':
						$func='LINESTRING';
						break;
					case '23':
						$func='LINESTRINGM';
						break;
					case '24':
						$func='LINESTRINGZ';
						break;
					case '122':
						$func='MULTILINESTRING';
						break;
					case '123':
						$func='MULTILINESTRINGM';
						break;
					case '124':
						$func='MULTILINESTRINGZ';
						break;
					case '3':
						$func='MULTIPOLYGONM';
						break;
					case '32':
						$func='POLYGON';
						break;
					case '33':
						$func='POLYGONM';
						break;
					case '34':
						$func='POLYGONZ';
						break;
					case '132':
						$func='MULTIPOLYGON';
						break;
					case '133':
						$func='MULTIPOLYGONM';
						break;
					case '134':
						$func='MULTIPOLYGONZ';
						break;
					case '100':
						$func='GEOMETRY';
						break;
					case '360':
						$func='GEOGRAPHY';
						break;
					default:
						$func = "ALGOGEOMETRIA" . $this->dt_campo;
						break;
				}
				break;
			default:
				$func = "ALGO" . $this->dt_campo;
				break;
		}
		if (empty($func)) die('aqui '.$dt.'-'.$dtl);
		return "\"$func\"";
	}

	function getTipoFlash() {
		switch ($this->dt_campo) {
			case 'VARCHAR':
				$func = "String";
				break;
			case 'BIGINT':
			case 'FLOAT':
			case 'INTEGER':
			case 'DOUBLE PRECISION':
				$func = "Number";
				break;
			case 'TEXT':
				$func = "String";
				break;
			case 'DATE':
				$func = "Date";
				break;
			case 'BIT':
				$func = "Boolean";
				break;
			default:
				$func = "ALGO" . $this->dt_campo;
				break;
		}
		return "$func";
	}
	
	function getTipagemLimpo() {
		$st = $this->getStringTipagem();
		return str_replace("\"", "", trim($st));
	}
	
	function getTipagemPhpDocs(){
		$dt=$this->dt_campo;
		$dtl=$this->dtl_campo;
		switch ($dt) {
			case 'TEXT':
			case 'VARCHAR':
				$func = "string";
				break;
			case 'BIGINT':
			case 'INTEGER':
				$func = "integer";
				break;
			case 'FLOAT':
			case 'DOUBLE PRECISION':
				$func = "float";
				break;
			case 'DATE':
				$func = "date";
				break;
			case 'BIT':
				$func = "boolean";
				break;
			case 'POINT':
			case 'POINTM':
			case 'POINTZ':
			case 'MULTIPOINT':
			case 'MULTIPOINTM':
			case 'MULTIPOINTZ':
			case 'LINESTRING':
			case 'LINESTRINGM':
			case 'LINESTRINGZ':
			case 'MULTILINESTRING':
			case 'MULTILINESTRINGM':
			case 'MULTILINESTRINGZ':
			case 'POLYGON':
			case 'POLYGONM':
			case 'POLYGONZ':
			case 'MULTIPOLYGON':
			case 'MULTIPOLYGONM':
			case 'MULTIPOLYGONZ':
			case 'GEOMETRY':
			case 'GEOGRAPHY':
				$func = $dt;
				break;
			case 'IMAGE/LONG BINARY':
				$dtl='3';
			case 'NVARCHAR':
				switch($dtl){
					case '1':
						$func='MULTIPOINTM';
						break;
					case '12':
						$func='POINT';
						break;
					case '13':
						$func='POINTM';
						break;
					case '14':
						$func='POINTZ';
						break;
					case '112':
						$func='MULTIPOINT';
						break;
					case '113':
						$func='MULTIPOINTM';
						break;
					case '114':
						$func='MULTIPOINTZ';
						break;
					case '2':
						$func='MULTILINESTRINGM';
						break;
					case '22':
						$func='LINESTRING';
						break;
					case '23':
						$func='LINESTRINGM';
						break;
					case '24':
						$func='LINESTRINGZ';
						break;
					case '122':
						$func='MULTILINESTRING';
						break;
					case '123':
						$func='MULTILINESTRINGM';
						break;
					case '124':
						$func='MULTILINESTRINGZ';
						break;
					case '3':
						$func='MULTIPOLYGONM';
						break;
					case '32':
						$func='POLYGON';
						break;
					case '33':
						$func='POLYGONM';
						break;
					case '34':
						$func='POLYGONZ';
						break;
					case '132':
						$func='MULTIPOLYGON';
						break;
					case '133':
						$func='MULTIPOLYGONM';
						break;
					case '134':
						$func='MULTIPOLYGONZ';
						break;
					case '100':
						$func='GEOMETRY';
						break;
					case '360':
						$func='GEOGRAPHY';
						break;
					default:
						$func = "ALGOGEOMETRIA" . $this->dt_campo;
						break;
				}
				break;
			default:
				$func = "ALGO" . $this->dt_campo;
				break;
		}
		if (empty($func)) die('aqui '.$dt.'-'.$dtl);
		return $func;
	}
	
	function geraTesteReg() {
		switch ($this->dt_campo) {
			case 'VARCHAR':
				$base = str_repeat("áéíóú'ãõôâÁÉ'ÍÓÚÃÕÂÔ", 100);
				$text = substr($base, 0, $this->dtl_campo);
				$func = "\"$text\"";
				break;
			case 'BIGINT':
			case 'INTEGER':
				$func = rand(1, 64000);
				break;
			case 'FLOAT':
			case 'DOUBLE PRECISION':
				$func = rand(1, 64000).".".(double)microtime()*1000000;
				break;
			case 'TEXT':
				$base = str_repeat("áéíó'úãõôâÁÉÍÓ'ÚÃÕÂÔ", 10000);
				$func = "\"$base\"";
				break;
			case 'DATE':
				$func = mktime (10,31,31,10,15,1980);
				break;
			case 'BIT':
				$func = "true";
				break;
			default:
				$func = "mandaALGO" . $this->dt_campo;
				break;
		}
		return $func;
	}
	
	function getStringMandaSQLArb($paraquem) {
		switch ($this->dt_campo) {
			case 'VARCHAR':
				$func = "mandaVarchar";
				break;
			case 'BIGINT':
			case 'INTEGER':
				$func = "mandaInteger";
				break;
			case 'FLOAT':
			case 'DOUBLE PRECISION':
				$func = "mandaDouble";
				break;
			case 'TEXT':
				$func = "mandaText";
				break;
			case 'DATE':
				$func = "mandaDate";
				break;
			case 'BIT':
				$func = "mandaBit";
				break;
			default:
				$func = "mandaALGO" . $this->dt_campo;
				break;
		}
		return "\".\$db->$func(".$paraquem.").\"";
	}
	
	function getStringTira($quem=null, $quem2=null, $fis=true, $over=null) {
		switch ($this->dt_campo) {
			case 'VARCHAR':
				$func = "tiraVarchar";
				break;
			case 'BIGINT':
			case 'INTEGER':
				$func = "tiraInteger";
				break;
			case 'FLOAT':
			case 'DOUBLE PRECISION':
				$func = "tiraDouble";
				break;
			case 'TEXT':
				$func = "tiraText";
				break;
			case 'DATE':
				$func = "tiraDate";
				break;
			case 'BIT':
				$func = "tiraBit";
				break;
			default:
				$func = "tiraALGO" . $this->dt_campo;
				break;
		}
		if (!is_null($over)) {
			return "\$db->$func($quem".$over."$quem2)";
		} else {
			return "\$db->$func($quem".strtoupper($this->nome_fis)."$quem2)";
		}
	}
	
	function isNativo($nome_fis) {
		$partes = explode("_", $this->nome_fis,2);
		$int = $partes[0];
		$nat = "TB".$int[4]."_".$int[0].$int[1].$int[2].$int[3];
		if ($nat == $this->tabela_nativa) return true;
	}
	
	function getNomeCurto() {
		$partes = explode("_", $this->nome_fis,2);
		return strtolower($partes[1]);
	}
}

?>