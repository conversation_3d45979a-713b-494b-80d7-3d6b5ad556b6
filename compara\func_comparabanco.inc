<?php
include_once('../codebase/function/func_geramodelos.inc');
include_once('../codebase/config.inc');
// echo nl2br(print_r($_pathModelosPlus, true))."<br/>";
// exit;
//require('func_allsites.inc');
require_once('config.inc');
function getArrayBases($qual=null){
	global $_arrayBases, $_arrayBases1, $_arrayBases2;
	if (!$qual && isset($_arrayBases)) return $_arrayBases;
	if ($qual==1){
		if (isset($_arrayBases1)) return $_arrayBases1; else return $_arrayBases;
	}
	if ($qual==2){
		if (isset($_arrayBases2)) return $_arrayBases2; else return $_arrayBases;
	}
	return array();
}
function getConexao($umDB){
	if ($umDB->configDbType=='json'){
		return getConexaoJson($umDB->nome);
	}
	ADOLoadCode($umDB->configDbType);
	$dbr = ADONewConnection();
	global $_pg_ssl_options;
	if (!empty($umDB->configDbOptions)) $_pg_ssl_options=$umDB->configDbOptions;
	if ($umDB->configDbPers) {
		if (! @$dbr->PConnect($umDB->configDbServer, $umDB->configDbUser, $umDB->configDbPass, $umDB->configDbName) ) {
				echo "Erro conectando ao DB.";
				return false;
		}
	} else {
		if (! $dbr->Connect($umDB->configDbServer, $umDB->configDbUser, $umDB->configDbPass, $umDB->configDbName) ) {
				echo "Erro conectando ao DB.";
				return false;
		}
	}
	return $dbr;
}
function getConexaoJson($jsonFile){
	include_once('codebase/function/jsonConnection.inc');
	return new JSON_CONNECTION($jsonFile);
}
function mandaHtmlAjax($html){
	if (empty(trim($html)))return;
	//echo '========================'.$html.'========================';
	//echo '========================'.json_encode($html).'========================';
	$toSend=json_encode($html);
	if (empty(trim($toSend)))return;
	echo 'umHtml('.$toSend.');#_#';
}
function comparaViaAjax(){
	global $db, $debug, $db1, $db2, $schema1, $schema2, $cospeDebug, $temComenta, $temCreate, $temCreateIgnore, $temSequencias, $temSequenciasIgnore, $temColunas, $temColunasIgnore, $temRenameIdx, $_pathModelos;
	$temComenta=0;
	$temCreate=0;
	$temCreateIgnore=0;
	$temSequencias=0;
	$temSequenciasIgnore=0;
	$temRenameIdx=0;
	set_time_limit(0);
	//cospeCabecalhoCompara();
	@ob_end_clean();
	$cospeDebug=false;
	if ($_REQUEST['saidaDebug']=='true') $cospeDebug=true;
	if ($cospeDebug) header('Content-type:text/plain; charset=UTF-8;');
	global $trava;
	$trava=$_REQUEST['trava'];
	$doFks=($_REQUEST['doFks']=='1');
	$doIdx=($_REQUEST['doIdx']=='1');
	$doSeq=($_REQUEST['doSeq']=='1');
	$soPublic=($_REQUEST['soPublic']=='1');
	if ($base1=$_REQUEST['baselista1']){
		die('aqui '.$base1);
		if ($base1=='json'){
			$umDB1=(object)null;
			$umDB1->configDbType='json';
			$umDB1->configDbPers=false;
			$umDB1->configDbServer='';
			$umDB1->configDbUser='';
			$umDB1->configDbPass='';
			$umDB1->configDbName='';
			$umDB1->nome=$_pathModelos.'fullmodelo.js';
		}else{
			$umDB1=$dbs[$base1];
		}
	}else{
		if ($base1=$_REQUEST['base1']){
			$host1=$_REQUEST['host1'];
			if ($host1=='json'){
				$umDB1=(object)null;
				$umDB1->configDbType='json';
				$umDB1->configDbPers=false;
				$umDB1->configDbServer='';
				$umDB1->configDbUser='';
				$umDB1->configDbPass='';
				$umDB1->configDbName='';
				$umDB1->nome=$base1;
			}else{
				$umDB1=(object)null;
				$umDB1->configDbType=$_REQUEST['tipo1'];
				$umDB1->configDbPers=false;
				$umDB1->configDbServer=$_REQUEST['host1'];
				$umDB1->configDbUser=$_REQUEST['user1'];
				$umDB1->configDbPass=$_REQUEST['pass1'];
				$umDB1->configDbOptions=$_REQUEST['options1'];
				$umDB1->configDbName=$base1;
				$umDB1->nome=$umDB1->configDbServer.' '.$umDB1->configDbName;
				if (!($umDB1->configDbType && $umDB1->configDbUser && $umDB1->configDbPass)){// && $umDB1->configDbServer
					echo("Dados incompletos para a base 1<br>");
					return;
				}
			}
		}else{
			echo("Não foi especificada a base 1<br>");
			return;
		}
	}
	if (!$db1=getConexao($umDB1)){
		echo "Não foi possível conectar-se à base 1";exit;
	}
	$db1->nome=$umDB1->nome;
	$db1->schema=$_REQUEST['schema1'];
	if (empty($db1->schema)) $db1->schema='public';
	// die('aqui '.$db1->schema);
	cospeMsgProgressoAjax("Conectado à base ".$umDB1->nome);
	if ($base2=$_REQUEST['baselista2']){
		$umDB2=$dbs[$base2];
	}else{
		if ($base2=$_REQUEST['base2']){
			$umDB2=(object)null;
			$umDB2->configDbType=$_REQUEST['tipo2'];
			$umDB2->configDbPers=false;
			$umDB2->configDbServer=$_REQUEST['host2'];
			$umDB2->configDbUser=$_REQUEST['user2'];
			$umDB2->configDbPass=$_REQUEST['pass2'];
			$umDB2->configDbOptions=$_REQUEST['options2'];
			$umDB2->configDbName=$base2;
			$umDB2->nome=$umDB2->configDbServer.' '.$umDB2->configDbName;
			if (!($umDB2->configDbType && $umDB2->configDbUser && $umDB2->configDbPass)){//$umDB2->configDbServer&&
				echo("Dados incompletos para a base 2<br>");
				return;
			}
		}else{
			echo("Não foi especificada a base 2 - comparando com base local<br>");
		}
	}
	// echo nl2br(print_r($umDB2, true));exit;
	if (!$db2=getConexao($umDB2)){
		echo "Não foi possível conectar-se à base 2";exit;
	}
	@ob_end_clean();
	$db2->nome=$umDB2->nome;
	$db2->schema=$_REQUEST['schema2'];
	if (empty($db2->schema)) $db2->schema='public';
	$schema1=$db1->schema;
	$schema2=$db2->schema;
	cospeMsgProgressoAjax("Conectado à base ".$umDB2->nome);
	$mesmoDbType=($db1->databaseType==$db2->databaseType);
	$ora1=isOracle($db1);
	$ora2=isOracle($db2);
	$ms1=isMSSqlServer($db1);
	$ms2=isMSSqlServer($db2);
	if (!($ora1 || $ms1)) $db1->version=getDbVersion($db1);
	if (!($ora2 || $ms2)) $db2->version=getDbVersion($db2);
	$tabs=getArrayTabelas($db1, false, true, $soPublic, $schema1);
	$tabs2=getArrayTabelas($db2, false, true, $soPublic, $schema2);
	if ($doSeq) {
		$seqs1=getArraySeqs($db1);
		$seqs2=getArraySeqs($db2);
	}
	if (false){
		?>

		<table cellpadding="2" cellspacing="0" id="borda">
			<tr>
				<td colspan="4"><b>Comparação da <?=$umDB1->nome?> com <?=$umDB2->nome?></b></td>
			</tr>
			<tr bgcolor="silver">
				<td><b>Coluna</b></td>
				<td><b><?=$umDB1->nome?></b></td>
				<td><b><?=$umDB2->nome?></b></td>
				<td><b>Ação</b></td>
			</tr>
		<?
	}
	$numTabs=count($tabs);
	$repSchema=false;
	if (empty($schema1)==false && empty($schema2)==false && $schema1!=$schema2) $repSchema=true;
	$cont=0;
	$html=ob_get_clean();
	mandaHtmlAjax($html);
	$ignoreTabs=getIgnoreTables();
	$ignoreSeqs=getIgnoreSeqs();
	$ignoreCols=getIgnoreCols();
	// echo nl2br(print_r(array_keys($tabs), true))."<br/>";exit;
	foreach($tabs as $nome=>$tab){ //para cada tabela da local
		if (connection_aborted()) {
			if ($debug) echo "Aborted"."<br/>";
			exit;
		}
		// if ($nome=='app.tb1_pcar') die('aqui ');
		if ($soPublic && strpos($nome, 'public.')===false) {
			//echo $tabs;
			continue;
		}
		if (in_array($nome, $ignoreTabs)) {
			cospeTrTabelaIgnoradaAjax($nome);
			continue; // se a tabela está marcada para ignorar, pula
		}
		if (strpos($nome, 'public.')!==false){
			//echo $nome."<br/>";exit;
		}
		if (empty($schema1)==false){
			if ($tab->schema!=$schema1) {
				//echo $tab->schema.' - '.$nome."<br/>";
				continue;
			}
		}else{

		}
		$nomeRep=$nome;
		$nomeSem=str_replace($schema1.'.','',$nome);
		if ($repSchema){
			$nomeRep=str_replace($schema1.'.',$schema2.'.',$nome);
			//die('aqui '.$nome.' - '.$nomeRep);
		}
		$cont++;
		//if ($cont>10) die('aqui 10 itens');; //////////////////////////////////////////////////////////////////////////////////////////////
		//echo $cont."...<br/>";
		$mostrouTabela=false;
		//echo nl2br(print_r($tab, true))."<br/>";
		//echo nl2br(print_r($tabs2[$nome], true))."<br/>";exit;
		if ($tab2=$tabs2[$nomeRep]){ // EXISTE NAS DUAS BASES DE COMPARAÇÃO
			if ($nome!=$nomeRep){
				$txtCompTable=$nome.' &lt;=&gt; '.$nomeRep;
			}else{
				$txtCompTable=$nome;
			}
			cospeMsgProgressoAjax("Comparando:  ".$txtCompTable.' ('.$cont.'/'.$numTabs.')');
			if ($debug) echo("Comparando tabela ".$nome."<br>");
			//if ($nome!='tb2_arat') continue;
			if ($tab->comment!=$tab2->comment) {
				if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
				cospeDifComentarioTabela($tab, $tab2);
			}
			foreach($tab->cols as $nomeCol=>$col){ // para cada coluna da local
				cospeMsgProgressoAjax("Comparando:  ".$txtCompTable." - ".$nomeCol);
				if (in_array($nome.'.'.$nomeCol, $ignoreCols)) {
					if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
					cospeTrColunaIgnoradaAjax($nome, $nomeCol);
					continue; // se a coluna está marcada para ignorar, pula
				}
				if ($col2=$tabs2[$nomeRep]->cols[$nomeCol]){ // se existe na remota
					if ($debug) echo($nomeCol." existe nas duas<br>");
					//$metaType1=$db->MetaType($col1->type, $col1->max_length);
					//$metaType2=$db->MetaType($col2->type, $col2->max_length);
					$tiposIguais=false;
					if ($ora2){
						$tiposIguais=comparaOracle($col, $col2);
					}else if ($ms2){
						$tiposIguais=comparaMSSqlServer($col, $col2);
					}else{
						$tiposIguais=($col->type==$col2->type && ($col->type=='geometry'?$col->formated==$col2->formated: $col->max_length==$col2->max_length));
					}
					$comentIgual=true;
					if ($col->comment!=$col2->comment) $comentIgual=false;
					if (!($comentIgual && $tiposIguais && $col->not_null==$col2->not_null && ($col->has_default==$col2->has_default && $col->default_value==$col2->default_value && @$col->primary_key==@$col2->primary_key))){ // se há diferença

						// echo "alert('".$col->max_length.' - '.$col2->max_length."');";
						// if ($col->type=='geometry') {
						// 	echo nl2br(print_r($col, true))."<br/>";
						// 	echo nl2br(print_r($col2, true))."<br/>";
						// 	exit;
						// }
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
						cospeDifColsAjax($tab, $tab2, $col, $col2, $ora1, $ms1, $ora2, $ms2); // mostra a diferença
						// exit;
					}
				}else{ // não existe na remota
					if ($debug) echo("não existe na segunda<br>");
					if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
					cospeDifColsAjax($tab, $tab2, $col, null, $ora1, $ms1, $ora2, $ms2); // mostra a ausência na remota
				}
			} // foreach colunas locais
			foreach ($tab2->cols as $nomeCol=>$col2){ // para cada coluna da remota
				if (!$col=$tabs[$nome]->cols[$nomeCol]) { // não existe na local
					if (in_array($nome.'.'.$nomeCol, $ignoreCols)) {
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
						cospeTrColunaIgnoradaAjax($nome, $nomeCol);
						continue; // se a coluna está marcada para ignorar, pula
					}
						if ($debug) echo("não existe na primeira<br>");
					if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
					cospeDifColsAjax($tab, $tab2, null, $col2, $ora1, $ms1, $ora2, $ms2); // mostra a ausência na local
				}
			} // foreach colunas remotas
			if ($doFks){
				if (connection_aborted()) exit;
				/*
				$fks=$db->MetaForeignKeys($nome);
				$fks2=$db2->MetaForeignKeys($nome);
				*/
				//foreignKeys:
				$fks=array();
				$fks2=array();
				/*
				$fks=$db->MetaForeignKeys($nome);
				$fks2=$db2->MetaForeignKeys($nome);
				*/
				$fks=MetaForeignKeys($db1, $nome);
				$fks2=MetaForeignKeys($db2, $nomeRep);
				//echo nl2br(print_r($fks, true))."<br/>";exit;
				if ($debug && sizeof($fks)>0) {echo nl2br(print_r($fks, true))."++++ FK1<br>";}
				if ($debug && sizeof($fks2)>0) {echo nl2br(print_r($fks2, true))."++++ FK2<br>";}
				$fksMostradas=array();
				$fksMostradas2=array();
				if ($debug) echo("Vai comparar FKs<br>");
				//echo (nl2br(print_r($fks, true))."<br>");
				if ($fks){
					foreach ($fks as $nomefk=>$dados){
						cospeMsgProgressoAjax("Comparando: ".$nome." FK:  ".$nomefk);
						asort($dados);
						if ($debug) echo("Tabela relacionada: ".$nomefk." - colunas (nesta=relacionada):".print_r($dados, true)."<br>");

						if (isset($fks2[$nomefk])) {
							if ($debug) echo(print_r($fks2[$nomefk], true)."+++++++++++++++<br>");
							$dados2=$fks2[$nomefk];
							asort($dados2);
							$mostrouTabela=cospeDifFkAjax($nome, $nomeRep, $nomefk, $dados, $nomefk, $dados2, $mostrouTabela);
							$fksMostradas[$nomefk]=$nomefk;
							unset($fks2[$nomefk]);
						}else{
							$nomefk2=null;
							if ($fks2){
								foreach($fks2 as $nomefk2=>$dados2){
									asort($dados2);
									if ($nomefk2==$nomefk){
										// echo "ACHEI via nome: ".$nomefk2." - ".print_r($dados2, true)."<br/>IGUAL: ".$nomefk." - ".print_r($dados, true)."<br/>";
									}elseif(in_arrayi($dados[0], $dados2)){
										// echo "ACHEI via dados: ".$nomefk2." - ".print_r($dados2, true)."<br/>IGUAL: ".$nomefk." - ".print_r($dados, true)."<br/>";
									}
								}
							}
							if ($nomefk2){
								$dados2=$fks2[$nomefk2];
								asort($dados2);
								$mostrouTabela=cospeDifFkAjax($nome, $nomeRep, $nomefk, $dados, $nomefk2, $dados2, $mostrouTabela);
								$fksMostradas[$nomefk2]=$nomefk2;
								unset($fks2[$nomefk2]);
							}else{
								if ($debug) echo("não tinha a fk remota<br>");
								//echo("não tinha a fk remota<br>");
								if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
								//echo nl2br(print_r($fks2, true))."<br>";exit;
								cospeDifFkAjax($nome, $nomeRep, $nomefk, $dados, null, null);
							}
						}
						//break;
					}
				}
				if ($fks2){
					//echo (nl2br(print_r($fks2, true))."<br>");exit;
					foreach ($fks2 as $nomefk=>$dados){
						cospeMsgProgressoAjax("Comparando: ".$nome." FK:  ".$nomefk);
						asort($dados);
						if ($debug) echo("Tabela relacionada: ".$nomefk." - colunas (nesta=relacionada):".print_r($dados, true)."<br>");
						if (!isset($fks[$nomefk])) {
							if ($debug) echo("não tinha a fk local<br>");
							//echo("não tinha a fk local<br>");
							if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
							cospeDifFkAjax($nome, $nomeRep, null, null, $nomefk, $dados);
						}
					}
				}
				//if ($cont>5) exit;
			}
			if ($doIdx){
				//echo '+++++++++++++++++++++++'.$nome."<br/>";
				$idx=MetaIndexes($db1, $nome, $tab);
				$idx2=MetaIndexes($db2, $nomeRep, $tab2);
				// echo nl2br(print_r($idx, true))."<br/>";
				// echo nl2br(print_r($idx2, true))."<br/>";
				// exit;
				$tabl=$nome;
				$esq=null;
				$dir=null;
				if (strpos($nome, '.')!==false ){
					$nacos=explode('.',$nome);
					$tabl=$nacos[1];
					$esq=$nacos[0];
				}
				if (strpos($nomeRep, '.')!==false ){
					$nacos=explode('.',$nomeRep);
					$tabl2=$nacos[1];
					$dir=$nacos[0];
				}
				//echo(count($idx)." - ".count($idx2)."<br>");
				if ($debug) echo("Vai comparar índices<br>");
				$arTemNasDuas=array();
				$arTemNasDuasNomeDif=array();
				$arTemNasDuasOrdemDif=array();
				foreach($idx as $nomeidx=>$def){
					if ($esq){
						if (strpos($def, $esq.'.'.$tabl)!==false) {
							$defcom=$def;
							$defsem=str_replace($esq.'.','',$def);
						}else{
							$defsem=$def;
							$defcom=str_replace(' '.$tabl.' ',' '.$esq.'.'.$tabl.' ',$def);
						}
					}else{
						$defcom=$def;
						$defsem=$def;
					}
					if ($debug) echo("Índice: ".$nomeidx." - definição:".print_r($def, true)."<br>");
					$defSemNome=str_replace([' ','\t','\n','\r'], '', str_replace(strtolower($nomeidx),'',strtolower($defsem)));
					$nomeidx2=null;
					$colunas1=extraiColunasIdx($def);
					//echo nl2br(print_r($idx2, true))."<br/>";
					//echo $defsem."<br/>";
					foreach($idx2 as $nome2=>$def2){
						if ($dir) $def2sem=str_replace($dir.'.','',$def2); else $def2sem=$def2;
						$defSemNome2=str_replace([' ','\t','\n','\r'], '', str_replace(strtolower($nome2),'',strtolower($def2sem)));
						if ($defsem==$def2sem){
							$nomeidx2=$nome2;
							break;
						}
						// echo "POR NOME: $nomeidx - $nome2<br/>";
						// echo "POR NOME DEFS: $defSemNome - $defSemNome2<br/>";
						if ($defSemNome==$defSemNome2){
							if ($debug) echo "ACHEI POR NOME DIFERENTE: $nomeidx - $nome2<br/>";
							// echo "ACHEI POR NOME DIFERENTE: $nomeidx - $nome2";exit;
							$nomeidx2=$nome2;
							$arTemNasDuasNomeDif[]=$nomeidx2;
							break;
						}
						$colunas2=extraiColunasIdx($def2);
						if (count(array_diff(array_merge($colunas1, $colunas2), array_intersect($colunas1, $colunas2))) === 0){
							if ($debug) echo "ACHEI POR ORDEM DIFERENTE: $nomeidx - $nome2<br/>";
							// echo "ACHEI POR ORDEM DIFERENTE: $nomeidx - $nome2";exit;
							$nomeidx2=$nome2;
							$arTemNasDuasOrdemDif[]=$nomeidx2;
							break;
						}
					}
					//if ($nomeidx2=array_search($def, $idx2)){
					if ($nomeidx2){
						if (in_array($nomeidx2, $arTemNasDuasNomeDif)){ // tem com nome diferente - vamos cuspir um rename to
							if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
							if ($debug) echo "Tem índice $nomeidx ($nomeidx2) com nome diferente nas duas "."<br/>";
							$defSchema=$def;
							if ($esq!=$dir) $defSchema=str_replace(' '.($esq?$esq.'.'.$tabl:$tabl).' ',' '.($dir?$dir.'.'.$tabl:$tabl).' ', $def);
							// exit;
							cospeDifIdxAjax($nomeidx, $defSchema, $nomeidx2, $def2, $tab, $tab2);
						}elseif (in_array($nomeidx2, $arTemNasDuasOrdemDif)){ // tem com ordem de colunas diferente ignorar
						}else{
							$arTemNasDuas[]=$nomeidx2;
						}
					}else{
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
						if ($debug) echo "Tem índice $nomeidx na primeira e não na segunda "."<br/>";
						$defSchema=$def;
						if ($esq!=$dir) $defSchema=str_replace(' '.($esq?$esq.'.'.$tabl:$tabl).' ',' '.($dir?$dir.'.'.$tabl:$tabl).' ', $def);
						cospeDifIdxAjax($nomeidx, $defSchema, null, null, $tab, $tab2);
						// TODO: IDENTIFICAR SE O ÍNDICE É UMA PRIMARY KEY
					}
				}
				// exit;
				foreach($idx2 as $nomeidx2=>$def2){
					if (in_array($nomeidx2, $arTemNasDuas)) continue;
					if (in_array($nomeidx2, $arTemNasDuasNomeDif)) continue;
					if (in_array($nomeidx2, $arTemNasDuasOrdemDif)) continue;
					if ($dir){
						if (strpos($def2, $dir.'.'.$tabl)!==false) {
							$def2com=$def2;
							$def2sem=str_replace($dir.'.','',$def2);
						}else{
							$def2sem=$def2;
							$def2com=str_replace(' '.$tabl.' ',' '.$dir.'.'.$tabl.' ',$def2);
						}
					}else{
						$def2com=$def2;
						$def2sem=$def2;
					}
					if ($debug) echo("Índice: ".$nomeidx2." - definição:".print_r($def2, true)."<br>");
					$nomeidx=null;
					foreach($idx as $umNome=>$def){
						if ($esq) $defsem=str_replace($esq.'.','',$def); else $defsem=$def;
						if ($defsem==$def2sem){
							$nomeidx=$umNome;
							break;
						}
					}
					//if ((!array_search($nomeidx2, $idx)) && !isset($idx[$nomeidx2])){
					if (empty($nomeidx)){
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
						if ($debug) echo "Tem índice na $nomeidx2 na segunda e não na primeira"."<br/>";
						$defSchema=$def2;
						if ($esq!=$dir) $defSchema=str_replace(' '.($dir?$dir.'.'.$tabl:$tabl).' ',' '.($esq?$esq.'.'.$tabl:$tabl).' ', $def);
						//echo $def." - não na primeira sem schema<br/>";
						//echo $defSchema." - não na primeira com schema<br/>";exit;
						cospeDifIdxAjax(null, null, $nomeidx2, $def2, $tab, $tab2);
						// TODO: IDENTIFICAR SE O ÍNDICE É UMA PRIMARY KEY
					}
				}
				//die('aqui '.$nome);
				//die('aqui ');
			}
			//echo '=============================='.$nome.'<br/>';
			//if ($nome=='public.tb1_csct') die('aqui '.$nome);
		}else{// nao existe a tabela na remota
			if (getTableOrView($db1, $nome)=='v'){
				cospeTrViewAjax($nome, true); // mostra view ausente na remota
				cospeTrCriarViewAjax($db1, $nome, false, $ora2, $ms2);
			}else{
				if ($nome=='app.tb1_pcar--'){
					echo nl2br(print_r($tab, true))."<br/>";
					die('aqui '.$nome);
				}
				cospeTrTabelaAjax($nome, true); // mostra tabela ausente na remota
				//echo ((int)$ora2)." chamada<br>";
				cospeTrCriarTabelaAjax($tab, false, $ora2, $ms2);
			}
		}
	}// para cada tabela local
	foreach ($tabs2 as $nome=>$tab2){ // para cada tabela remota
		if (connection_aborted()) exit;
		$nomeRep=$nome;
		$nomeSem=str_replace($schema2.'.','',$nome);
		if ($repSchema){
			$nomeRep=str_replace($schema2.'.',$schema1.'.',$nome);
			//die('aqui '.$nome.' - '.$nomeRep);
		}
		if (in_array($nomeRep, $ignoreTabs)) {
			cospeTrTabelaIgnoradaAjax($nome);
			continue; // se a tabela está marcada para ignorar, pula
		}
		cospeMsgProgressoAjax("Comparando:  ".$nome);
		$mostrouTabela=false;
		if (!$tab=$tabs[$nomeRep]){ // se não existe na local
			if ($debug) echo "Não existe a tabela ".$tab." na primeira<br>";
			cospeTrTabelaAjax($nome, true, true); // mostra tabela ausente na local
			cospeTrCriarTabelaAjax($tab2, true, $ora1, $ms1);
		}else{
			if ($debug) echo "Existe nas duas: ".$nome." <br>";
		}
	}
	$foi=[];
	if (@$seqs1){
		$html='<td colspan="4" id="sequences"><b>Sequências</b></td>';
		echo 'addTr(\''.$html.'\');';
		foreach ($seqs1 as $seq1){ // para cada sequencia 1
			if (connection_aborted()) exit;
			cospeMsgProgressoAjax("Comparando seq.: ".$seq1);
			$mostrouTabela=false;
			if (in_array($seq1, $ignoreSeqs)) {
				cospeTrSequenciaIgnoradaAjax($seq1);
				$foi[]=$seq1;
				continue; // se a sequencia está marcada para ignorar, pula
			}
			if (!in_array($seq1, $seqs2)){ // se não existe na 2
				cospeTrSequenciaAjax($seq1, true, false); // mostra sequencia ausente na 2
				cospeTrCriarSequenciaAjax($seq1, false, $ora1, $ms1);
			}
		}
		if (false && $seq1=='tb1_hipq_hipq1_cod_seq') {
			echo nl2br(print_r($foi, true))."<br/>";
			die('aqui '.$seq1);
		}
		foreach ($seqs2 as $seq2){ // para cada sequencia 2
			if (connection_aborted()) exit;
			cospeMsgProgressoAjax("Comparando seq.: ".$seq2);
			$mostrouTabela=false;
			if (in_array($seq2, $ignoreSeqs)) {
				if (!in_array($seq2, $foi)) cospeTrSequenciaIgnoradaAjax($seq2);
				$foi[]=$seq2;
				continue; // se a sequencia está marcada para ignorar, pula
			}
			if (!in_array($seq2, $seqs1)){ // se não existe na 1
				cospeTrSequenciaAjax($seq2, true, true); // mostra sequencia ausente na 2
				cospeTrCriarSequenciaAjax($seq2, true, $ora1, $ms1);
			}
		}
	}
?>
	showTools('<a href="#tabCompara">Topo</a>');
	showTools('<a href="#sequences">Sequências</a>');
<?
	if ($temComenta>0){
		?>
		showTools('<a href="javascript:doAllComenta()">Rodar comentários (<?=$temComenta?>)</a>');
		<?
	}
	if ($temCreate>0){
		?>
		showTools('<a href="javascript:doAllCreate()">Rodar criações (<?=$temCreate?>)</a>');
		<?
	}
	if ($temRenameIdx>0){
		?>
		showTools('<a href="javascript:doAllRenameIdx()">Renomear índices (<?=$temRenameIdx?>)</a>');
		<?
	}
	if ($temCreate>0 || $temCreateIgnore>0){
		?>
		showTools('<span id="linkIgnoreTables"><a href="javascript:setIgnoreTables(this)">Atualizar tabelas ignoradas</a></span>');
		<?
	}
	if ($temSequencias>0 || $temSequenciasIgnore>0){
		?>
		showTools('<span id="linkIgnoreSequences"><a href="javascript:setIgnoreSequences(this)">Atualizar sequências ignoradas</a></span>');
		<?
	}
	if ($temColunas>0 || $temColunasIgnore>0){
		?>
		showTools('<span id="linkIgnoreColumns"><a href="javascript:setIgnoreColumns(this)">Atualizar colunas ignoradas</a></span>');
		<?
	}
	cospeMsgProgressoAjax("Comparação concluída!");
}
function extraiColunasIdx($def){
	$partes=explode('(', $def);
	$partes2=explode(')', $partes[1]);
	$colunas=explode(',', str_replace(' ', '', $partes2[0]));
	return $colunas;
}
function cospeTrTabelaIgnoradaAjax($nome){
	global $trava, $temCreateIgnore;
	$txt="Tabela ".$nome.' <input type="checkbox" class="ignore_tab" name="ignore_tab[]" value="'.$nome.'" checked> marcada para ignorar';
	$bgc='silver';
	$html='<td colspan="4" class="tit-tabela tabela-ignorada"><b>'.$txt.'</b></td>';
	echo 'addTr(\''.$html.'\',\''.$bgc.'\');#_#';
	$temCreateIgnore++;
	return true;
}
function cospeTrSequenciaIgnoradaAjax($nome){
	global $trava, $temSequenciasIgnore;
	$txt="Sequência ".$nome.' <input type="checkbox" class="ignore_seq" name="ignore_seq[]" value="'.$nome.'" checked> marcada para ignorar';
	$bgc='silver';
	$html='<td colspan="4" class="tit-tabela sequencia-ignorada"><b>'.$txt.'</b></td>';
	echo 'addTr(\''.$html.'\',\''.$bgc.'\');#_#';
	$temSequenciasIgnore++;
	return true;
}
function cospeTrColunaIgnoradaAjax($nome){
	global $trava, $temColunasIgnore;
	$txt="Coluna ".$nome.' <input type="checkbox" class="ignore_col" name="ignore_col[]" value="'.$nome.'" checked> marcada para ignorar';
	$bgc='lightyellow';
	$html='<td colspan="4" class="coluna-ignorada"><b>'.$txt.'</b></td>';
	echo 'addTr(\''.$html.'\',\''.$bgc.'\');#_#';
	$temColunasIgnore++;
	return true;
}
function cospeTrIndiceIgnoradoAjax($nome){
	global $trava, $temColunasIgnore;
	$txt="Coluna ".$nome.' <input type="checkbox" class="ignore_col" name="ignore_col[]" value="'.$nome.'" checked> marcada para ignorar';
	$bgc='lightyellow';
	$html='<td colspan="4" class="coluna-ignorada"><b>'.$txt.'</b></td>';
	echo 'addTr(\''.$html.'\',\''.$bgc.'\');#_#';
	$temColunasIgnore++;
	return true;
}
function compara(){
	global $db, $debug;
	set_time_limit(0);
	cospeCabecalhoCompara();
	global $trava;
	$trava=$_REQUEST['trava'];
	$doFks=($_REQUEST['do_fks']=='true');
	$doIdx=($_REQUEST['do_idx']=='true');
	$doSeq=($_REQUEST['do_seq']=='true');
	$dbs=getArrayBases();
	if ($base1=$_REQUEST['baselista1']){
		$umDB1=$dbs[$base1];
	}else{
		if ($base1=$_REQUEST['base1']){
			$umDB1=(object)null;
			$umDB1->configDbType=$_REQUEST['tipo1'];
			$umDB1->configDbPers=false;
			$umDB1->configDbServer=$_REQUEST['host1'];
			$umDB1->configDbUser=$_REQUEST['user1'];
			$umDB1->configDbPass=$_REQUEST['pass1'];
			$umDB1->configDbName=$base1;
			$umDB1->nome=$umDB1->configDbServer.' '.$umDB1->configDbName;
			if (!($umDB1->configDbType && $umDB1->configDbUser && $umDB1->configDbPass)){// && $umDB1->configDbServer
				echo("Dados incompletos para a base 1<br>");
				return;
			}
		}else{
			echo("Não foi especificada a base 1<br>");
			return;
		}
	}
	if (!$db1=getConexao($umDB1)){
		echo "Não foi possível conectar-se à base 1";exit;
	}
	$db1->nome=$umDB1->nome;
	cospeMsgProgresso("Conectado à base ".$umDB1->nome);
	if ($base2=$_REQUEST['baselista2']){
		$umDB2=$dbs[$base2];
	}else{
		if ($base2=$_REQUEST['base2']){
			$umDB2=(object)null;
			$umDB2->configDbType=$_REQUEST['tipo2'];
			$umDB2->configDbPers=false;
			$umDB2->configDbServer=$_REQUEST['host2'];
			$umDB2->configDbUser=$_REQUEST['user2'];
			$umDB2->configDbPass=$_REQUEST['pass2'];
			$umDB2->configDbName=$base2;
			$umDB2->nome=$umDB2->configDbServer.' '.$umDB2->configDbName;
			if (!($umDB2->configDbType && $umDB2->configDbUser && $umDB2->configDbPass)){//$umDB2->configDbServer&&
				echo("Dados incompletos para a base 2<br>");
				return;
			}
		}else{
			echo("Não foi especificada a base 2 - comparando com base local<br>");
		}
	}
	//echo nl2br(print_r($umDB2, true));exit;
	if (!$db2=getConexao($umDB2)){
		echo "Não foi possível conectar-se à base 2";exit;
	}
?>
<script language="JavaScript">
var bases=new Array();
var b1={};
b1.tipo='<?=$umDB1->configDbType?>';
b1.pers='<?=$umDB1->configDbPers?>';
b1.serv='<?=$umDB1->configDbServer?>';
b1.user='<?=$umDB1->configDbUser?>';
b1.pass='<?=$umDB1->configDbPass?>';
b1.name='<?=$umDB1->configDbName?>';
bases.push(b1);
var b2={};
b2.tipo='<?=$umDB2->configDbType?>';
b2.pers='<?=$umDB2->configDbPers?>';
b2.serv='<?=$umDB2->configDbServer?>';
b2.user='<?=$umDB2->configDbUser?>';
b2.pass='<?=$umDB2->configDbPass?>';
b2.name='<?=$umDB2->configDbName?>';
bases.push(b2);
</script>
<?
	$db2->nome=$umDB2->nome;
	cospeMsgProgresso("Conectado à base ".$umDB2->nome);

	/*
	$base=$_REQUEST['base'];
	$umDB=$dbs[$base];
	$db2=getConexao($umDB);
	*/
	$mesmoDbType=($db1->databaseType==$db2->databaseType);
	$ora1=isOracle($db1);
	$ora2=isOracle($db2);
	$ms1=isMSSqlServer($db1);
	$ms2=isMSSqlServer($db2);
	$tabs=getArrayTabelas($db1, false);
	$tabs2=getArrayTabelas($db2, false);
	if ($doSeq) {
		$seqs1=getArraySeqs($db1);
		$seqs2=getArraySeqs($db2);
	}
	//echo nl2br(print_r($seqs1, true))."<br/>";
	//echo($db2->databaseType." ---<br>");
	//ob_end_flush();
	/*
	echo(print_r($tabs, true)."<br>");
	echo("---------------------------------------------------<br>");
	echo(print_r($tabs2, true)."<br>");
	echo("---------------------------------------------------<br>");
	echo((int)(print_r($tabs, true)==print_r($tabs2, true))."<br>");
	*/
?>
<style>
table#borda {
	border-top:solid 1px #c0c0c0;;
	border-left:solid 1px #c0c0c0;
}

table#borda TD{
	border-bottom:solid 1px #c0c0c0;
	border-right:solid 1px #c0c0c0;
	font-family: arial;
	font-size: 9pt;
}

</style>
<table cellpadding="2" cellspacing="0" id="borda">
	<tr>
		<td colspan="4"><b>Comparação da <?=$umDB1->nome?> com <?=$umDB2->nome?></b></td>
	</tr>
	<tr bgcolor="silver">
		<td><b>Coluna</b></td>
		<td><b><?=$umDB1->nome?></b></td>
		<td><b><?=$umDB2->nome?></b></td>
		<td><b>Ação</b></td>
	</tr>
<?
	$numTabs=count($tabs);
	$cont=0;
	foreach($tabs as $nome=>$tab){ //para cada tabela da local
		if (connection_aborted()) {
			if ($debug) echo "Aborted"."<br/>";
			exit;
		}
		$cont++;
		$mostrouTabela=false;
		if ($tab2=$tabs2[$nome]){ //se existe na remota
			cospeMsgProgresso("Comparando:  ".$nome.' ('.$cont.'/'.$numTabs.')');
			if ($debug) echo("Comparando tabela ".$nome."<br>");
			//if ($nome!='tb2_arat') continue;
			foreach($tab->cols as $nomeCol=>$col){ // para cada coluna da local
				cospeMsgProgresso("Comparando:  ".$nome." - ".$nomeCol);
				if ($col2=$tabs2[$nome]->cols[$nomeCol]){ // se existe na remota
					if ($debug) echo($nomeCol." existe nas duas<br>");
					//$metaType1=$db->MetaType($col1->type, $col1->max_length);
					//$metaType2=$db->MetaType($col2->type, $col2->max_length);
					$tiposIguais=false;
					if ($ora2){
						$tiposIguais=comparaOracle($col, $col2);
					}else if ($ms2){
						$tiposIguais=comparaMSSqlServer($col, $col2);
					}else{
						$tiposIguais=($col->type==$col2->type && $col->max_length==$col2->max_length);
					}
					if (!($tiposIguais && $col->not_null==$col2->not_null && ($col->has_default==$col2->has_default && $col->default_value==$col2->default_value && @$col->primary_key==@$col2->primary_key))){ // se há diferença
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
						cospeDifCols($tab, $tab2, $col, $col2, $ora1, $ms1, $ora2, $ms2); // mostra a diferença
					}
				}else{ // não existe na remota
					if ($debug) echo("não existe na segunda<br>");
					if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
					cospeDifCols($tab, $tab2, $col, null, $ora1, $ms1, $ora2, $ms2); // mostra a ausência na remota
				}
			} // foreach colunas locais
			foreach ($tab2->cols as $nomeCol=>$col2){ // para cada coluna da remota
				if (!$col=$tabs[$nome]->cols[$nomeCol]) { // não existe na local
					if ($debug) echo("não existe na primeira<br>");
					if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
					cospeDifCols($tab, $tab2, null, $col2, $ora1, $ms1, $ora2, $ms2); // mostra a ausência na local
				}
			} // foreach colunas remotas
			if ($doFks){
				if (connection_aborted()) exit;
				/*
				$fks=$db->MetaForeignKeys($nome);
				$fks2=$db2->MetaForeignKeys($nome);
				*/
				//foreignKeys:
				$fks=array();
				$fks2=array();
				/*
				$fks=$db->MetaForeignKeys($nome);
				$fks2=$db2->MetaForeignKeys($nome);
				*/
				$fks=MetaForeignKeys($db1, $nome);
				$fks2=MetaForeignKeys($db2, $nome);
				if ($debug && sizeof($fks)>0) {echo nl2br(print_r($fks, true))."++++ FK1<br>";}
				if ($debug && sizeof($fks2)>0) {echo nl2br(print_r($fks2, true))."++++ FK2<br>";}
				$fksMostradas=array();
				$fksMostradas2=array();
				if ($debug) echo("Vai comparar FKs<br>");
				//echo (nl2br(print_r($fks, true))."<br>");
				if ($fks){
					foreach ($fks as $nomefk=>$dados){
						cospeMsgProgresso("Comparando FKs:  ".$nomefk);
						asort($dados);
						if ($debug) echo("Tabela relacionada: ".$nomefk." - colunas (nesta=relacionada):".print_r($dados, true)."<br>");
						if (isset($fks2[$nomefk])) {
							if ($debug) echo(print_r($fks2[$nomefk], true)."+++++++++++++++<br>");
							$dados2=$fks2[$nomefk];
							asort($dados2);
							$mostrouTabela=cospeDifFk($nome, $nomefk, $dados, $nomefk, $dados2, $mostrouTabela);
							$fksMostradas[$nomefk]=$nomefk;
							unset($fks2[$nomefk]);
						}else{
							if ($debug) echo("não tinha a fk remota<br>");
							//echo("não tinha a fk remota<br>");
							if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
							//echo nl2br(print_r($fks2, true))."<br>";exit;
							cospeDifFk($nome, $nomefk, $dados, null, null);
						}
						//break;
					}
				}
				if ($fks2){
					//echo (nl2br(print_r($fks2, true))."<br>");exit;
					foreach ($fks2 as $nomefk=>$dados){
						cospeMsgProgresso("Comparando FKs:  ".$nomefk);
						asort($dados);
						if ($debug) echo("Tabela relacionada: ".$nomefk." - colunas (nesta=relacionada):".print_r($dados, true)."<br>");
						if (!isset($fks[$nomefk])) {
							if ($debug) echo("não tinha a fk local<br>");
							//echo("não tinha a fk local<br>");
							if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
							cospeDifFk($nome, null, null, $nomefk, $dados);
						}
					}
				}
				//if ($cont>5) exit;
			}
			if ($doIdx){
				$idx=MetaIndexes($db1, $nome);
				$idx2=MetaIndexes($db2, $nome);
				$tabl=$nome;
				$esq=null;
				if (strpos($nome, '.')!==false ){
					$nacos=explode('.',$nome);
					$tabl=$nacos[1];
					$esq=$nacos[0];
				}
				//echo(count($idx)." - ".count($idx2)."<br>");
				if ($debug) echo("Vai comparar índices<br>");
				$arTemNasDuas=array();
				foreach($idx as $nomeidx=>$def){
					if ($esq){
						if (strpos($def, $esq.'.'.$tabl)!==false) {
							$defcom=$def;
							$defsem=str_replace($esq.'.','',$def);
						}else{
							$defsem=$def;
							$defcom=str_replace(' '.$tabl.' ',' '.$esq.'.'.$tabl.' ',$def);
						}
					}else{
						$defcom=$def;
						$defsem=$def;
					}
					if ($debug) echo("Índice: ".$nomeidx." - definição:".print_r($def, true)."<br>");
					if ($debug) echo '=======&gt; ';$defsem.' || '.$defcom.'<br/>';
					$nomeidx2=null;
					foreach($idx2 as $nome2=>$def2){
						if ($esq) $def2sem=str_replace($esq.'.','',$def2); else $def2sem=$def2;
						if ($defsem==$def2sem){
							$nomeidx2=$nome2;
							break;
						}
					}
					//if ($nomeidx2=array_search($def, $idx2)){
					if ($nomeidx2){
						$arTemNasDuas[]=$nomeidx2;
						/*
						//if ($nomeidx2==$nomeidx) continue;
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
						if ($debug) echo "Tem índice na $nomeidx nas duas??"."<br/>";
						cospeDifIdx($nomeidx, $def, $nomeidx2, $idx2[$nomeidx2], $tab, $tab2);
					}else if (isset($idx2[$nome])){//tratar este caso!!!
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
						if ($debug) echo "Não sei porque mostra $nomeidx "." OOOOOOOOOOOOOOOOOOO<br/>";
						cospeDifIdx($nomeidx, $def, $nomeidx, $idx2[$nomeidx], $tab, $tab2);
						*/
					}else{
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
						if ($debug) echo "Tem índice na $nomeidx na primeira e não na segunda "."<br/>";
						cospeDifIdx($nomeidx, $def, null, null, $tab, $tab2);
					}
				}
				foreach($idx2 as $nomeidx2=>$def2){
					if (in_array($nomeidx2, $arTemNasDuas)) continue;
					if ($esq){
						if (strpos($def2, $esq.'.'.$tabl)!==false) {
							$def2com=$def2;
							$def2sem=str_replace($esq.'.','',$def2);
						}else{
							$def2sem=$def2;
							$def2com=str_replace(' '.$tabl.' ',' '.$esq.'.'.$tabl.' ',$def2);
						}
					}else{
						$def2com=$def2;
						$def2sem=$def2;
					}
					if ($debug) echo("Índice: ".$nomeidx2." - definição:".print_r($def2, true)."<br>");
					$nomeidx=null;
					foreach($idx as $nome=>$def){
						if ($esq) $defsem=str_replace($esq.'.','',$def); else $defsem=$def;
						if ($defsem==$def2sem){
							$nomeidx=$nome;
							break;
						}
					}
					//if ((!array_search($nomeidx2, $idx)) && !isset($idx[$nomeidx2])){
					if (empty($nomeidx)){
						if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
						if ($debug) echo "Tem índice na $nomeidx2 na segunda e não na primeira"."<br/>";
						cospeDifIdx(null, null, $nomeidx2, $def2, $tab, $tab2);
					}
				}
			}
		}else{// nao existe a tabela na remota
			if (getTableOrView($db1, $nome)=='v'){
				cospeTrView($nome, true); // mostra view ausente na remota
				cospeTrCriarView($db1, $nome, false, $ora2, $ms2);
			}else{
				cospeTrTabela($nome, true); // mostra tabela ausente na remota
				//echo ((int)$ora2)." chamada<br>";
				cospeTrCriarTabela($tab, false, $ora2, $ms2);
			}
		}
	}// para cada tabela local
	foreach ($tabs2 as $nome=>$tab2){ // para cada tabela remota
		if (connection_aborted()) exit;
		cospeMsgProgresso("Comparando:  ".$nome);
		$mostrouTabela=false;
		if (!$tab=$tabs[$nome]){ // se não existe na local
			if ($debug) echo "Não existe a tabela ".$tab." na primeira<br>";
			cospeTrTabela($nome, true, true); // mostra tabela ausente na local
			cospeTrCriarTabela($tab2, true, $ora1, $ms1);
		}else{
			if ($debug) echo "Existe nas duas: ".$nome." <br>";
		}
	}
	if (@$seqs1){
		foreach ($seqs1 as $seq1){ // para cada sequencia 1
			if (connection_aborted()) exit;
			cospeMsgProgresso("Comparando:  ".$seq1);
			$mostrouTabela=false;
			if (!in_array($seq1, $seqs2)){ // se não existe na 2
				cospeTrSequencia($seq1, true, false); // mostra sequencia ausente na 2
				cospeTrCriarSequencia($seq1, false, $ora1, $ms1);
			}
		}
		foreach ($seqs2 as $seq2){ // para cada sequencia 2
			if (connection_aborted()) exit;
			cospeMsgProgresso("Comparando:  ".$seq2);
			$mostrouTabela=false;
			if (!in_array($seq2, $seqs1)){ // se não existe na 1
				cospeTrSequencia($seq2, true, true); // mostra sequencia ausente na 2
				cospeTrCriarSequencia($seq2, true, $ora1, $ms1);
			}
		}
	}
?>
</table>
<?
	cospeRodapeCompara();
}
function isOracle(&$db){
	return in_array($db->databaseType, array("oracle", "Oracle", "oci8", "oci8po", "zoracle"));
}

function isMSSqlServer(&$db){
	return in_array($db->databaseType, array("mssql", "mssqlpo"));
}
function executaAlter(){
	$umDB=(object)null;
	$umDB->configDbType=$_REQUEST['type'];
	$umDB->configDbPers=false;
	$umDB->configDbServer=(empty($_REQUEST['host'])?$_REQUEST['dbhost']:$_REQUEST['host']);
	$umDB->configDbUser=(empty($_REQUEST['user'])?$_REQUEST['dbuser']:$_REQUEST['user']);
	$umDB->configDbPass=$_REQUEST['pw'];
	$umDB->configDbName=$_REQUEST['db'];
	if (!($umDB->configDbType && $umDB->configDbUser && $umDB->configDbPass)){// && $umDB->configDbServer
		echo("Dados incompletos para a base<br>");
		return;
	}
	if (!$db=getConexao($umDB)){
		echo "Não foi possível conectar-se à base";exit;
	}else{
		// echo nl2br(print_r($_REQUEST, true))."<br/>";exit;
		if (is_array($_REQUEST['sql'])){
			$sql=$_REQUEST['sql'][0];
			$sql=str_replace(array('&gt;','&lt;'),array('>','<'), $sql);
			$sqlUser=$_REQUEST['sql'][1];
			$sql=$_REQUEST['sql'];
		}else{
			$sql=$_REQUEST['sql'];
		}
		//echo nl2br(print_r($sql, true))."<br/>";
		if ((!is_array($sql)) && strpos($sql, '<user>')!==false){
			echo 'não é array'."<br/>";
			$sql=explode('<user>', $sql);
			$sql=array($sql[0],$sqlUser,$sql[1]);
		}
		$semErro=true;
		// echo nl2br(print_r($sql, true))."<br/>";exit;
		if (is_array($sql)){
			foreach($sql as $s){
				$s=trim($s);
				if ($s=='' || $s==';')continue;
				$s=str_replace(array('&gt;','&lt;'),array('>','<'), $s);
				echo nl2br(print_r($s, true))."<br/>";//exit;
				if (strpos($s, '<alt>')!==false){
					$altSql=explode('<alt>', $s);
					echo nl2br(print_r($altSql, true))."<br/>";//exit;
					$altPassa=false;
					$msg="";
					$msgSuccess="";
					foreach($altSql as $k=>$oSql){
						if ($resp=$db->ExecuteBind($oSql, array())){
							$altPassa=true;
							$msgSuccess.="sql ".($k+1).": ".$db->ErrorMsg()."<br/>";
						}else{
							$msg.="sql ".($k+1).": ".$db->ErrorMsg()."<br/>";
						}
					}
					if ($altPassa){
						echo "Pelo menos um rodou: <br/>".$msgSuccess.'<br/>'.$msg;
					}else{
						echo "Nenhum rodou: <br/>".$msg;
					}
				}else{
					echo nl2br(print_r($s, true))."<br/>";//exit;
					if (!$resp=$db->ExecuteBind($s, array())){
						$db->_raiseError(__LINE__, __FILE__, $s);
						$semErro=false;
					}else{
						echo "Rodou: ".$s."<br>";
					}
				}
			}
		}else{
			if (strpos($sql, '<alt>')!==false){
				$altSql=explode('<alt>', $sql);
				$altPassa=false;
				$msg="";
				$msgSuccess="";
				foreach($altSql as $k=>$sql){
					if ($resp=$db->ExecuteBind($sql, array())){
						$altPassa=true;
						$msgSuccess.="sql ".($k+1).": ".$db->ErrorMsg()."<br/>";
					}else{
						$msg.="sql ".($k+1).": ".$db->ErrorMsg()."<br/>";
					}
				}
				if ($altPassa){
					echo "Pelo menos um rodou: <br/>".$msgSuccess;
				}
			}else{
				if (!$resp=$db->ExecuteBind($sql, array())){
					$db->_raiseError(__LINE__, __FILE__, $sql);
					$semErro=false;
				}else{
					echo "Rodou: ".$sql."<br>";
				}
			}
		}
		if ($semErro==true && $_REQUEST['go']==true){
			?><script language="Javascript">window.close();</script><?
		} else{
			?><a href="javascript:window.close()">Fechar</a><?
		}
	}

}
function mostraAlter(){
	if (stripos($_REQUEST['sql'], ';&lt;user&gt;')!==false){
		$arSql=explode(';&lt;user&gt;', $_REQUEST['sql']);
	}else{
		$arSql=explode('<user>', $_REQUEST['sql']);
	}

	$host=(empty($_REQUEST['host'])?$_REQUEST['dbhost']:$_REQUEST['host']);
	$user=(empty($_REQUEST['user'])?$_REQUEST['dbuser']:$_REQUEST['user']);
?>
Por favor, confirme a execução do comando:<br><br>
<form method="POST" action="" name="alterador">
<?
$x=0;
//echo nl2br(print_r($arSql, true))."<br>";exit;
foreach($arSql as $umSql){
	?><input type="hidden" name="sql[<?=$x?>]" value="<?=str_replace('"', '&quot;',$umSql)?>">
<b><?=$umSql?></b><br>
	Adicione um comando sql adicional, se necessário:<br>
	<textarea cols=40 rows=4 name=sql[<?=($x+1)?>]></textarea><br>
	<?
	$x+=2;
}
?><br><br>
No host <?=$host?>, base <?=$_REQUEST['db']?>, usuário <?=$user?>
	<input type="hidden" name="ati" value="alter_go">
	<input type="hidden" name="type" value="<?=$_REQUEST['type']?>">
	<input type="hidden" name="host" value="<?=$host?>">
	<input type="hidden" name="user" value="<?=$user?>">
	<input type="hidden" name="db" value="<?=$_REQUEST['db']?>">
	<input type="hidden" name="pw" value="<?=$_REQUEST['pw']?>">
	<input type="button" name="butCancela" value="Cancelar" onclick="window.close()">&nbsp
	<input type="submit" name="butSubmete" value="Executar">
</FORM>
<?
}
function cospeCabecalhoGera(){
	?>
	<div>
	<a href="/index.php"><i class="fa fa-home"></i>&nbsp;GeraModelos</a>
	</div>
	<?
}
function cospeForm(){
	global $PHP_SELF, $secret, $_pathModelos;
	$dbs=getArrayBases();
	/*
	echo nl2br(print_r($dbs, true))."<br/>";
	$jsonDb=(object)null;
	$jsonDb->scalar='';
	$jsonDb->nome='json';
	$jsonDb->configDbType='json';
	$jsonDb->configDbPers=false;
	$jsonDb->configDbServer='json';
	$jsonDb->configDbUser='';
	$jsonDb->configDbPass='';
	$jsonDb->configDbSchema='public';
	$jsonDb->configDbName='json';
	$jsonDb->nome='json: fullmodelo.js';
	$dbs['json']=$jsonDb;
	*/
	// Get list of JSON files in userfiles directory
	$root=getcwd();
	$jsonFiles = getModelFilesList();
	// echo nl2br(print_r($jsonFiles, true))."<br/>";exit;
	foreach ($jsonFiles as $def) {
		$fullPath=$def['dir'].DIRECTORY_SEPARATOR.$def['file'];
		// echo $fullPath."<br/>";
		$jsonDb=(object)null;
		$jsonDb->scalar='';
		$jsonDb->nome=$fullPath;
		$jsonDb->configDbType='json';
		$jsonDb->configDbPers=false;
		$jsonDb->configDbServer='json';
		$jsonDb->configDbUser='';
		$jsonDb->configDbPass='';
		$jsonDb->configDbSchema='public';
		$jsonContent=trim(file_get_contents($fullPath, true));
		$jsonLimpo=str_replace('var tbDefs=', '', $jsonContent);
		if (substr($jsonLimpo, -1)==';') $jsonLimpo=substr($jsonLimpo, 0, -1);
		$jsonData=json_decode($jsonLimpo, true);
		if (isset($jsonData['modelDefs']['modelSchema'])) $jsonDb->configDbSchema=$jsonData['modelDefs']['modelSchema'];
		$jsonDb->configDbName=$fullPath;
		$jsonDb->nome='JSON: '.$def['file'];
		$dbs[$fullPath]=$jsonDb;
		// echo nl2br(print_r($jsonDb, true))."<br/>";
		// echo nl2br(print_r($jsonData, true))."<br/>";exit;
	}



	// echo nl2br(print_r($dbs, true))."<br/>";exit;
?>
<script>
var bases=<?=json_encode($dbs)?>;
function setBase(sel, onde){
	var qual=sel.options[sel.selectedIndex].value;
	if (qual==''){

	}else{
		// debugger;
		var db=bases[qual];
		document.getElementById('host'+onde).value=db['configDbServer'];
		document.getElementById('tipo'+onde).value=db['configDbType'];
		document.getElementById('db'+onde).value=db['configDbName'].replaceAll('\\', '/');
		document.getElementById('user'+onde).value=db['configDbUser'];
		document.getElementById('pass'+onde).value=db['configDbPass'];
		document.getElementById('schema'+onde).value=db['configDbSchema'];
	}
	//alert(bases[qual]['configDbName']);
}
</script>
<?
	$base=$_REQUEST['base'];
	$ops='<option value="">-- Selecione a base --</option>';
	foreach ($dbs as $chave=>$umaBase){
		$ops.='<option value="'.$chave.'"'.($base==$chave?' selected':'').'>'.$umaBase->nome.' - '.$umaBase->configDbName.'</option>';
	}
	$host1=$_REQUEST['host1'];
	$tipo1=$_REQUEST['tipo1'];
	$base1=$_REQUEST['base1'];
	$schema1=$_REQUEST['schema1'];
	$user1=$_REQUEST['user1'];
	$pass1=$_REQUEST['pass1'];
	$options1=$_REQUEST['options1'];
	$host2=$_REQUEST['host2'];
	$tipo2=$_REQUEST['tipo2'];
	$base2=$_REQUEST['base2'];
	$schema2=$_REQUEST['schema2'];
	$user2=$_REQUEST['user2'];
	$pass2=$_REQUEST['pass2'];
	$options2=$_REQUEST['options2'];
	$trava=$_REQUEST['trava'];
	$soPublic=($_REQUEST['soPublic']=='true');
	$baselista1=$_REQUEST['baselista1'];
	$baselista2=$_REQUEST['baselista2'];

	if (empty($schema1)) $schema1='app';
	if (empty($schema2)) $schema2='app';
?>
<style>
table{
	padding:2px;
}
table td{
	padding:2px;
}
</style>
<div class="box">
	<div class="box-header with-border">
		<h4><b>Comparação de bases</b></h4>
	</div>
	<div class="box-body">
	<table>
<form action="<?=$PHP_SELF?>" method="post" name="formMultibase" id="formMultibase">
<?=montaHidden()?>
<input type="hidden" name="secret" value="<?=$secret?>"/>
	<input type="hidden" name="ati" value="compare_go">
	<input type="hidden" name="NO_LAYOUT" value="true">
	<input type="hidden" name="NO_GZ" value="true">
	<tr>
		<td>Base 1 (lista)</td>
		<td>Base 2 (lista)</td>
	</tr>
	<tr>
		<td><select name="baselista1" id="base" onchange="setBase(this, 1)">
<?
	$ops='<option value="">-- Selecione a base --</option>';
	foreach ($dbs as $chave=>$umaBase){
		$ops.='<option value="'.$chave.'"'.($baselista1==$chave?' selected':'').'>'.$umaBase->nome.' - '.$umaBase->configDbName.'</option>';
	}
	reset($dbs);
?>
		<?=$ops?>
		</select></td>
		<td><select name="baselista2" id="base2" onchange="setBase(this, 2)">
		<?
	$ops='<option value="">-- Selecione a base --</option>';
	foreach ($dbs as $chave=>$umaBase){
		$ops.='<option value="'.$chave.'"'.($baselista2==$chave?' selected':'').'>'.$umaBase->configDbName.' - '.$umaBase->nome.'</option>';
	}
	reset($dbs);
?>
		<?=$ops?>
		</select></td>
	</tr>
	<tr>
		<td><b>Base 1</b></td>
		<td><b>Base 2</b></td>
	</tr>
	<tr>
		<td>
			<table cellpadding="2" cellspacing="0" border="0">
				<tr>
					<td>Host 1</td>
					<td><input type="text" name="host1" id="host1" value="<?=$host1?>"></td>
				</tr>
				<tr>
					<td>Tipo 1</td>
					<td><select name="tipo1" id="tipo1">
						<option value="zpostgres"<?=(@$tipo1=='zpostgres'?' selected':'')?>>PostgreSQL</option>
						<option value="zoracle"<?=(@$tipo1=='zoracle'?' selected':'')?>>Oracle</option>
						<option value="zsqlserver"<?=(@$tipo1=='zsqlserver'?' selected':'')?>>SQL Server</option>
						<option value="zmysql"<?=(@$tipo1=='zmysql'?' selected':'')?>>MySQL</option>
					</select></td>
				</tr>
				<tr>
					<td>Base 1</td>
					<td><input type="text" name="base1" id="db1" value="<?=$base1?>"></td>
				</tr>
				<tr>
					<td>Schema 1</td>
					<td><input type="text" name="schema1" id="schema1" value="<?=$schema1?>"></td>
				</tr>
				<tr>
					<td>Usuário 1</td>
					<td><input type="text" name="user1" id="user1" value="<?=$user1?>"></td>
				</tr>
				<tr>
					<td>Senha 1</td>
					<td><input type="text" name="pass1" id="pass1" value="<?=$pass1?>"></td>
				</tr>
				<tr>
					<td>Options 1</td>
					<td><input type="text" name="options1" id="options1" value="<?=$options1?>"></td>
				</tr>
			</table>
		</td>
		<td>
			<table cellpadding="2" cellspacing="0" border="0">
				<tr>
					<td>Host 2</td>
					<td><input type="text" name="host2" id="host2" value="<?=$host2?>"></td>
				</tr>
				<tr>
					<td>Tipo 2</td>
					<td><select name="tipo2" id="tipo2">
						<option value="zpostgres"<?=(@$tipo2=='zpostgres'?' selected':'')?>>PostgreSQL</option>
						<option value="zoracle"<?=(@$tipo2=='zoracle'?' selected':'')?>>Oracle</option>
						<option value="zsqlserver"<?=(@$tipo2=='zsqlserver'?' selected':'')?>>SQL Server</option>
						<option value="zmysql"<?=(@$tipo2=='zmysql'?' selected':'')?>>MySQL</option>
					</select></td>
				</tr>
				<tr>
					<td>Base 2</td>
					<td><input type="text" name="base2" id="db2" value="<?=$base2?>"></td>
				</tr>
				<tr>
					<td>Schema 2</td>
					<td><input type="text" name="schema2" id="schema2" value="<?=$schema2?>"></td>
				</tr>
				<tr>
					<td>Usuário 2</td>
					<td><input type="text" name="user2" id="user2" value="<?=$user2?>"></td>
				</tr>
				<tr>
					<td>Senha 2</td>
					<td><input type="text" name="pass2" id="pass2" value="<?=$pass2?>"></td>
				</tr>
				<tr>
					<td>Options 2</td>
					<td><input type="text" name="options2" id="options2" value="<?=$options2?>"></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td><label><input type="radio" name="trava" value="1"<?=($trava=='1'?' checked':'')?>>&nbsp;&nbsp;Bloquear alterações nesta</label></td>
		<td><label><input type="radio" name="trava" value="2"<?=($trava=='2'?' checked':'')?>>&nbsp;&nbsp;Bloquear alterações nesta</label></td>
	</tr>
	<tr>
		<td><label><input type="radio" name="trava" value="0"<?=($trava!='1' & $trava!='2'?' checked':'')?>>&nbsp;&nbsp;Liberar ambas</label></td>
		<td>Não havendo seleção para a base 2 a comparação será feita com a base local (deste site)<br/>
	</tr>
	<tr>
		<td style="vertical-align:top;"><b>Só para PostgreSQL:</b><br>
		<label><input type="checkbox" name="do_fks" value="true"<?=(empty($_REQUEST['do_fks']) || $_REQUEST['do_fks']=='true'?' checked':'')?>>Comparar Foreign Keys</label><br>
		<label><input type="checkbox" name="do_idx" value="true"<?=(empty($_REQUEST['do_idx']) || $_REQUEST['do_idx']=='true'?' checked':'')?>>Comparar Índices</label><br>
		<label><input type="checkbox" name="do_seq" value="true"<?=(empty($_REQUEST['do_seq']) || $_REQUEST['do_seq']=='true'?' checked':'')?>>Comparar Sequências</label><br>
		</td>
		<td style="vertical-align:top;"><b>Só para PostgreSQL:</b><br>
		<label><input type="checkbox" name="soPublic" value="true"<?=($soPublic?' checked':'')?>>Somente schema public</label><br>
		</td>
	</tr>
	<tr>
		<td colspan="2"><input type="submit" name="submeter" value="Comparar bases"></td>
	</tr>
</form>
</table>
	</div>
</div>
<?
}
function cospeForm_old(){
	global $PHP_SELF;
	$dbs=getArrayBases();
	$base=$_REQUEST['base'];
	$ops='<option value="">-- Selecione a base --</option>';
	foreach ($dbs as $chave=>$umaBase){
		$ops.='<option value="'.$chave.'"'.($base==$chave?' selected':'').'>'.$umaBase->configDbName.' - '.$umaBase->nome.'</option>';
	}
	$host1=$_REQUEST['host1'];
	$tipo1=$_REQUEST['tipo1'];
	$base1=$_REQUEST['base1'];
	$user1=$_REQUEST['user1'];
	$pass1=$_REQUEST['pass1'];
	$host2=$_REQUEST['host2'];
	$tipo2=$_REQUEST['tipo2'];
	$base2=$_REQUEST['base2'];
	$user2=$_REQUEST['user2'];
	$pass2=$_REQUEST['pass2'];
	$trava=$_REQUEST['trava'];
?>
<table>
<form action="<?=$PHP_SELF?>" method="get" name="formMultibase" id="formMultibase">
	<tr>
		<td valign="top"><b>Comparação de bases</b></td>
		<td><b>Só para PostgreSQL:</b><br>
		<label><input type="checkbox" name="do_fks" value="true"<?=($_REQUEST['do_fks']=='true'?' checked':'')?>>Comparar Foreign Keys</label><br>
		<label><input type="checkbox" name="do_idx" value="true"<?=($_REQUEST['do_idx']=='true'?' checked':'')?>>Comparar Índices</label><br>
		<label><input type="checkbox" name="do_seq" value="true"<?=($_REQUEST['do_seq']=='true'?' checked':'')?>>Comparar Sequências</label><br>
		</td>
	</tr>
	<tr>
		<td colspan="2">Base 1 (lista)</td>
	</tr>
	<?=montaHidden()?>
	<input type="hidden" name="ati" value="compare_go">
	<input type="hidden" name="NO_LAYOUT" value="true">
	<input type="hidden" name="NO_GZ" value="true">
	<tr>
		<td colspan="2"><select name="baselista1" id="base">
		<?=$ops?>
		</select></td>
	</tr>
	<tr>
		<td colspan="2">Base 2 (lista)</td>
	</tr>
	<tr>
		<td colspan="2"><select name="baselista2" id="base2">
		<?=$ops?>
		</select></td>
		<td>&nbsp;
		</td>
	</tr>
	<tr>
		<td><b>Base 1</b></td>
		<td><b>Base 2</b></td>
	</tr>
	<tr>
		<td>
			<table cellpadding="2" cellspacing="0" border="0">
				<tr>
					<td>Host 1</td>
					<td><input type="text" name="host1" value="<?=$host1?>"></td>
				</tr>
				<tr>
					<td>Tipo 1</td>
					<td><select name="tipo1">
						<option value="zpostgres"<?=(@$tipo1=='zpostgres'?' selected':'')?>>PostgreSQL</option>
						<option value="zoracle"<?=(@$tipo1=='zoracle'?' selected':'')?>>Oracle</option>
						<option value="zsqlserver"<?=(@$tipo1=='zsqlserver'?' selected':'')?>>SQL Server</option>
						<option value="zmysql"<?=(@$tipo1=='zmysql'?' selected':'')?>>MySQL</option>
					</select></td>
				</tr>
				<tr>
					<td>Base 1</td>
					<td><input type="text" name="base1" value="<?=$base1?>"></td>
				</tr>
				<tr>
					<td>Usuário 1</td>
					<td><input type="text" name="user1" value="<?=$user1?>"></td>
				</tr>
				<tr>
					<td>Senha 1</td>
					<td><input type="text" name="pass1" value="<?=$pass1?>"></td>
				</tr>
			</table>
		</td>
		<td>
			<table cellpadding="2" cellspacing="0" border="0">
				<tr>
					<td>Host 2</td>
					<td><input type="text" name="host2" value="<?=$host2?>"></td>
				</tr>
				<tr>
					<td>Tipo 2</td>
					<td><select name="tipo2">
						<option value="zpostgres"<?=(@$tipo2=='zpostgres'?' selected':'')?>>PostgreSQL</option>
						<option value="zoracle"<?=(@$tipo2=='zoracle'?' selected':'')?>>Oracle</option>
						<option value="zsqlserver"<?=(@$tipo2=='zsqlserver'?' selected':'')?>>SQL Server</option>
						<option value="zmysql"<?=(@$tipo2=='zmysql'?' selected':'')?>>MySQL</option>
					</select></td>
				</tr>
				<tr>
					<td>Base 2</td>
					<td><input type="text" name="base2" value="<?=$base2?>"></td>
				</tr>
				<tr>
					<td>Usuário 2</td>
					<td><input type="text" name="user2" value="<?=$user2?>"></td>
				</tr>
				<tr>
					<td>Senha 2</td>
					<td><input type="text" name="pass2" value="<?=$pass2?>"></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td colspan="2">Não havendo seleção para a base 2 a comparação será feita com a base local (deste site)<br/>
		<label><input type="radio" name="trava" value="1"<?=($trava=='1'?' checked':'')?>>Travar a primeira</label>&nbsp;&nbsp;
		<label><input type="radio" name="trava" value="2"<?=($trava=='2'?' checked':'')?>>Travar a segunda</label>&nbsp;&nbsp;
		<label><input type="radio" name="trava" value="0"<?=($trava!='1' & $trava!='2'?' checked':'')?>>Liberar ambas</label><br/>
		<br><input type="submit" name="submeter" value="Comparar bases">
		</td>
	</tr>
</form>
</table>
<?
}
function cospeCabecalhoCompara(){
	$host1=$_REQUEST['host1'];
	$tipo1=$_REQUEST['tipo1'];
	$base1=$_REQUEST['base1'];
	$schema1=$_REQUEST['schema1'];
	$user1=$_REQUEST['user1'];
	$pass1=$_REQUEST['pass1'];
	$host2=$_REQUEST['host2'];
	$tipo2=$_REQUEST['tipo2'];
	$base2=$_REQUEST['base2'];
	$schema2=$_REQUEST['schema2'];
	$user2=$_REQUEST['user2'];
	$pass2=$_REQUEST['pass2'];
	$trava=$_REQUEST['trava'];
	$soPublic=$_REQUEST['soPublic'];
	$baselista1=$_REQUEST['baselista1'];
	$baselista2=$_REQUEST['baselista2'];

	if (empty($schema1)) $schema1='public';
	if (empty($schema2)) $schema2='public';
	$qs='base1='.$_REQUEST['base1'].'&host1='.$_REQUEST['host1'].'&user1='.$_REQUEST['user1'].'&pass1='.$_REQUEST['pass1'].'&baselista1='.$_REQUEST['baselista1'].'&schema1='.$_REQUEST['schema1'].'&base2='.$_REQUEST['base2'].'&host2='.$_REQUEST['host2'].'&user2='.$_REQUEST['user2'].'&pass2='.$_REQUEST['pass2'].'&baselista2='.$_REQUEST['baselista2'].'&schema2='.$_REQUEST['schema2'].'&do_idx='.$_REQUEST['do_idx'].'&do_fks='.$_REQUEST['do_fks'].'&do_seq='.$_REQUEST['do_seq'].'&trava='.$_REQUEST['trava'].'&soPublic='.$_REQUEST['soPublic'];
	$title="Comparação de bases";
	include_once('layout/init.inc');
?>
<style>
body{
	font-family:verdana,arial,sans-serif;
	font-size:14px;
	margin:10px;
}
</style>
<body style="font-family:verdana,arial,sans-serif;font-size:14px;">
	<?cospeCabecalhoGera();?>
	<script type="text/javascript" src="/scripts/prototype/prototype.js"></script>
	<script type="text/javascript" src="comparaBanco.js"></script>
	<div id="cabecalho" style="position:sticky;top:0;background-color:white;">
	<form action="" method="GET" name="newComp">
		<input type="hidden" name="base1" value="<?=$base1?>"/>
		<input type="hidden" name="host1" value="<?=$host1?>"/>
		<input type="hidden" name="user1" value="<?=$user1?>"/>
		<input type="hidden" name="pass1" value="<?=$pass1?>"/>
		<input type="hidden" name="baselista1" value="<?=$baselista1?>"/>
		<input type="hidden" name="schema1" value="<?=$schema1?>"/>
		<input type="hidden" name="base2" value="<?=$base2?>"/>
		<input type="hidden" name="host2" value="<?=$host2?>"/>
		<input type="hidden" name="user2" value="<?=$user2?>"/>
		<input type="hidden" name="pass2" value="<?=$pass2?>"/>
		<input type="hidden" name="baselista2" value="<?=$baselista2?>"/>
		<input type="hidden" name="schema2" value="<?=$schema2?>"/>
		<input type="hidden" name="soPublic" value="<?=$soPublic?>"/>
		<input type="submit" value="Nova comparação..."/> &nbsp;<a href="<?=$_SERVER['PHP_SELF'].'?'.$qs?>">Nova comparação</a>
	</form>
	<div id="progresso" style="padding:10px;border:solid 1px gray;display:none;"></div>
	<div id="toolsCompara"></div>
</div>
<?
}
function cospeDifComentarioTabela($tab, $tab2) {
    global $trava, $PHP_SELF, $temComenta;
    $pode1=(!empty($tab->comment));
	$pode2=(!empty($tab2->comment));
    $sqlComment1 = "COMMENT ON TABLE " . $tab->name . " IS '" . addslashes($tab->comment) . "';";
    $sqlComment2 = "COMMENT ON TABLE " . $tab2->name . " IS '" . addslashes($tab2->comment) . "';";

	// $sqlComment1 = "COMMENT ON TABLE " . $tab->name . " IS JACA;";
    // $sqlComment2 = "COMMENT ON TABLE " . $tab2->name . " IS PITOMBA;";
    
    $html = '<td>' . $tab->name . ' (comentário)</td>';
    $html .= '<td>' . $tab->comment . '</td>';
    $html .= '<td>' . $tab2->comment . '</td>';
    $html .= '<td>';
    if ($trava != '2' && $pode1) {
		$temComenta++;
        $html .= '<a class="comenta" style="cursor:pointer;" onclick="sqlGo(1, \\\'' . addslashes(addslashes($sqlComment1)) . '\\\', this);" title="' . addslashes($sqlComment1) . '">Copiar para direita</a>&nbsp;';
    }
    
    if ($trava != '1' && $pode2) {
		$temComenta++;
        $html .= '<a class="comenta" style="cursor:pointer;" onclick="sqlGo(0, \\\'' . addslashes(addslashes($sqlComment2)) . '\\\', this);" title="'.addslashes($sqlComment2).'">Copiar para esquerda</a>';
    }
    
    $html .= '</td>';
    
    echo 'addTr(\'' . $html . '\');';
    return true;
}
function cospeRodapeCompara(){
?>
<script language="JavaScript1.2" type="text/javascript">
if (divprog!=null) divprog.style.display='none';
</script>
</body>
</html>
<?
}
function cospeMsgProgresso($txt){
	global $_funcProgCuspida;
	if (@!$_funcProgCuspida){
?>
<script language="JavaScript1.2" type="text/javascript">
var divprog=document.getElementById("progresso");
function progresso(msg){
	if (divprog==null) return;
	divprog.style.display='';
	divprog.innerHTML=msg;
}
</script>
<?
		$_funcProgCuspida=true;
	}
?>
<script language="JavaScript1.2" type="text/javascript">
progresso('<?=$txt?>');
</script>
<?
	@flush();
}
function cospeMsgProgressoAjax($txt){
?>
progresso('<?=$txt?>');#_#
<?
	@flush();
}
function getTableOrView($dbt, $nome){
	global $sqlCompleto;
	$nomeTab=$nome;
	$schema=null;
	if (strpos($nomeTab, '.')!==false){
		$nomeTab=substr($nomeTab, strpos($nomeTab, '.')+1);
		$schema=substr($nome, 0, strpos($nome, '.'));
	}
	if ($dbt->databaseType=='json') return 'r';
	$sql="select c.relkind
	from pg_class c LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
	where n.nspname='".$schema."'
	AND c.relname='".$nomeTab."'";
	if (!$rs=$dbt->Execute($sql)){
		$dbt->_raiseError(__LINE__, __FILE__, $sqlCompleto);
		exit;
	}
	if (!$rs->EOF){
		return $rs->fields(0);
	}
	return 'r';
}
function getSqlDropSeq($seq, $ora, $ms){
	$sql="DROP SEQUENCE ".$seq." ;";
	return $sql;
}
function getSqlCreateSeq($seq, $ora, $ms){
	$sql="CREATE SEQUENCE ".$seq." INCREMENT 1  MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;";
	return $sql;
}
function cospeTrCriarSequenciaAjax($seq, $primeira=false, $ora, $ms){
	global $PHP_SELF, $trava, $schema1, $schema2, $temCreate;
	$sqlCreate1=getSqlCreateSeq($schema1.'.'.$seq, $ora, $ms);
	$sqlCreateJs1=preg_replace('/\s/', ' ', $sqlCreate1);
	$sqlDrop1=getSqlDropSeq($schema1.'.'.$seq, $ora, $ms);
	$sqlDropJs1=preg_replace('/\s/', ' ', $sqlDrop1);
	$sqlCreate2=getSqlCreateSeq($schema2.'.'.$seq, $ora, $ms);
	$sqlCreateJs2=preg_replace('/\s/', ' ', $sqlCreate2);
	$sqlDrop2=getSqlDropSeq($schema2.'.'.$seq, $ora, $ms);
	$sqlDropJs2=preg_replace('/\s/', ' ', $sqlDrop2);
	if ($primeira){
		$html='<td colspan=2 class="primeira sequencia"><b>Criar na primeira</b><br/>'.nl2br($sqlCreate1).'<br/></td>';
		$html.='<td colspan=2 class="drop segunda sequencia"><b>DROPAR na segunda</b><br/>'.nl2br($sqlDrop2).'<br/></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan=2 class="primeira sequencia">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$temCreate++;
			$html.='<form name="create1'.$seq.'" action="index.php" method="POST" target="_blank" style="margin:0;">';
			$html.='<input type="submit" name="submete" value="Criar">&nbsp;<a class="gocreate" style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sqlCreateJs1).'\\\', this);" title="'.$sqlCreateJs1.'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.$sqlCreateJs1.'"><input type="hidden" name="ati" value="alter">';
			$html.='<input type="hidden" name="type" value="'.$_REQUEST['tipo1'].'"><input type="hidden" name="host" value="'.$_REQUEST['host1'].'">';
			$html.='<input type="hidden" name="user" value="'.$_REQUEST['user1'].'"><input type="hidden" name="pw" value="'.$_REQUEST['pass1'].'">';
			$html.='<input type="hidden" name="db" value="'.$_REQUEST['base1'].'"></form>';
		}
		$html.='</td>';
		$html.='<td colspan=2 class="segunda sequencia">';
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<form name="create2'.$seq.'" action="index.php" method="POST" target="_blank" style="margin:0;">';
			$html.='<input type="submit" name="submete" value="Dropar">&nbsp;<a class="godrop" style="cursor:pointer;" onclick="if (confirm(\\\'Tem certeza de que quer DROPAR  a sequência '.$seq.'?\\\'))sqlGo(1, \\\''.addslashes($sqlDropJs2).'\\\', this);" title="'.$sqlDropJs2.'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.$sqlDropJs2.'"><input type="hidden" name="ati" value="alter">';
			$html.='<input type="hidden" name="type" value="'.$_REQUEST['tipo2'].'"><input type="hidden" name="host" value="'.$_REQUEST['host2'].'">';
			$html.='<input type="hidden" name="user" value="'.$_REQUEST['user2'].'"><input type="hidden" name="pw" value="'.$_REQUEST['pass2'].'">';
			$html.='<input type="hidden" name="db" value="'.$_REQUEST['base2'].'"></form>';
		}
		$html.='</td>';
		echo 'addTr(\''.$html.'\');';
	}else{
		$html='<td colspan=2 class="drop primeira sequencia"><b>Dropar na primeira</b><br/>'.nl2br($sqlDrop1).'<br/></td>';
		$html.='<td colspan=2 class="segunda sequencia"><b>Criar na segunda</b><br/>'.nl2br($sqlCreate2).'<br/></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan=2 class="primeira sequencia">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<form name="create1'.$seq.'" action="index.php" method="POST" target="_blank">';
			$html.='<input type="submit" name="submete" value="Dropar">&nbsp;<a class="godrop" style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqlDropJs1).'\\\', this);" title="'.$sqlCreateJs1.'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.$sqlDropJs1.'"><input type="hidden" name="ati" value="alter">';
			$html.='<input type="hidden" name="type" value="'.$_REQUEST['tipo2'].'"><input type="hidden" name="host" value="'.$_REQUEST['host2'].'">';
			$html.='<input type="hidden" name="user" value="'.$_REQUEST['user2'].'"><input type="hidden" name="pw" value="'.$_REQUEST['pass2'].'">';
			$html.='<input type="hidden" name="db" value="'.$_REQUEST['base2'].'"></form>';
		}
		$html.='</td>';
		$html.='<td colspan=2 class="segunda sequencia">';
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$temCreate++;
			$html.='<form name="create2'.$seq.'" action="index.php" method="POST" target="_blank" style="margin:0;">';
			$html.='<input type="submit" name="submete" value="Criar">&nbsp;<a class="gocreate" style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqlCreateJs2).'\\\', this);" title="'.$sqlCreateJs2.'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.$sqlCreateJs2.'"><input type="hidden" name="ati" value="alter">';
			$html.='<input type="hidden" name="type" value="'.$_REQUEST['tipo2'].'"><input type="hidden" name="host" value="'.$_REQUEST['host2'].'">';
			$html.='<input type="hidden" name="user" value="'.$_REQUEST['user2'].'"><input type="hidden" name="pw" value="'.$_REQUEST['pass2'].'">';
			$html.='<input type="hidden" name="db" value="'.$_REQUEST['base2'].'"></form>';
		}
		$html.='</td>';
		echo 'addTr(\''.$html.'\');';
	}

}
function cospeTrCriarSequencia($seq, $primeira=false, $ora, $ms){
	global $PHP_SELF, $trava;
	$sqlCreate=getSqlCreateSeq($seq, $ora, $ms);
	$sqlCreateJs=preg_replace('/\s/', ' ', $sqlCreate);
	if ($primeira){
?>
	<form name="create1<?=$seq?>" action="<?=$PHP_SELF?>" method="POST" target="_blank">
	<tr>
		<td colspan=4><b>Criar na primeira</b><br/>
		<?=nl2br($sqlCreate)?><br/>
		</td>
	</tr>
	<tr>
		<td colspan=4><?if ($trava=='1'){?>Alteração bloqueada<?}else{?>
		<input type="submit" name="submete" value="Criar">&nbsp;
		<a style="cursor:pointer;" onclick="sqlGo(0, '<?=addslashes($sqlCreateJs)?>', this);" title="<?=$sqlCreate?>">Go!</a>&nbsp;
		<?}?>
		</td>
	</tr>
		<input type="hidden" name="sql" value="<?=$sqlCreate?>">
		<input type="hidden" name="ati" value="alter">
		<input type="hidden" name="type" value="<?=$_REQUEST['tipo1']?>">
		<input type="hidden" name="host" value="<?=$_REQUEST['host1']?>">
		<input type="hidden" name="user" value="<?=$_REQUEST['user1']?>">
		<input type="hidden" name="pw" value="<?=$_REQUEST['pass1']?>">
		<input type="hidden" name="db" value="<?=$_REQUEST['base1']?>">
		<?=montaHidden()?>
	</form>
<?
	}else{
?>
	<form name="create1<?=$seq?>" action="<?=$PHP_SELF?>" method="POST" target="_blank">
	<tr>
		<td colspan=4><b>Criar na segunda</b><br/>
		<?=nl2br($sqlCreate)?><br/>
		</td>
	</tr>
	<tr>
		<td colspan=4><?if ($trava=='2'){?>Alteração bloqueada<?}else{?>
		<input type="submit" name="submete" value="Criar">&nbsp;
		<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sqlCreateJs)?>', this);" title="<?=$sqlCreate?>">Go!</a>&nbsp;
		<?}?>
		</td>
	</tr>
		<input type="hidden" name="sql" value="<?=$sqlCreate?>">
		<input type="hidden" name="ati" value="alter">
		<input type="hidden" name="type" value="<?=$_REQUEST['tipo2']?>">
		<input type="hidden" name="host" value="<?=$_REQUEST['host2']?>">
		<input type="hidden" name="user" value="<?=$_REQUEST['user2']?>">
		<input type="hidden" name="pw" value="<?=$_REQUEST['pass2']?>">
		<input type="hidden" name="db" value="<?=$_REQUEST['base2']?>">
		<?=montaHidden()?>
	</form>
<?
	}

}
function cospeTrCriarTabelaAjax($tab, $primeira=false, $ora, $ms){
	global $PHP_SELF, $trava, $temCreate;
	$sqlCreate=getSqlCreate($tab, $primeira, $ora, $ms);
	$sqlCreateJs=preg_replace('/\s/', ' ', $sqlCreate);
	//$sqlCreateJs=str_replace("'", '&apos;', $sqlCreateJs);
	//$sqlCreateJs=addslashes($sqlCreateJs);
	$sqlDrop=getSqlDrop($tab, $ora, $ms);
	$sqlDropJs=preg_replace('/\s/', ' ', $sqlDrop);
	if ($primeira){
		$html='<td colspan=2 class="primeira tabela"><b>Criar na primeira</b><br/>'.str_replace("\t",'',str_replace("\n",'',str_replace("\r",'',nl2br(addslashes($sqlCreate))))).'<br/></td>';
		$html.='<td style="vertical-align:top;" colspan=2 class="drop segunda tabela"><b>DROPAR na segunda</b><br/>'.str_replace("\t",'',str_replace("\n",'',str_replace("\r",'',nl2br($sqlDrop)))).'<br/></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan=2 class="primeira tabela">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$temCreate++;
			$html.='<form name="create1'.$tab->name.'" action="index.php" method="POST" target="_blank">';
			$html.='<input type="submit" name="submete" value="Criar">&nbsp;<a class="gocreate" style="cursor:pointer;" onclick="sqlGo(0, \\\''.str_replace('\\','\\\\\\',addslashes($sqlCreateJs)).'\\\', this);" title="'.addslashes($sqlCreateJs).'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.addslashes($sqlCreateJs).'">';
			$html.='<input type="hidden" name="ati" value="alter"><input type="hidden" name="type" value="'.$_REQUEST['tipo1'].'">';
			$html.='<input type="hidden" name="host" value="'.$_REQUEST['host1'].'"><input type="hidden" name="user" value="'.$_REQUEST['user1'].'">';
			$html.='<input type="hidden" name="pw" value="'.$_REQUEST['pass1'].'"><input type="hidden" name="db" value="'.$_REQUEST['base1'].'">';
			$html.='</form>';
		}
		$html.='</td>';
		$html.='<td colspan=2 class="segunda tabela">';
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<form name="create2'.$tab->name.'" action="index.php" method="POST" target="_blank">';
			$html.='<input type="submit" name="submete" value="Dropar">&nbsp;<a style="cursor:pointer;" onclick="if (confirm(\\\'Tem certeza de que quer DROPAR a tabela '.$tab->name.'?\\\')){sqlGo(1, \\\''.addslashes($sqlDropJs).'\\\', this);}" title="'.addslashes($sqlDropJs).'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.addslashes($sqlDropJs).'">';
			$html.='<input type="hidden" name="ati" value="alter"><input type="hidden" name="type" value="'.$_REQUEST['tipo2'].'">';
			$html.='<input type="hidden" name="host" value="'.$_REQUEST['host2'].'"><input type="hidden" name="user" value="'.$_REQUEST['user2'].'">';
			$html.='<input type="hidden" name="pw" value="'.$_REQUEST['pass2'].'"><input type="hidden" name="db" value="'.$_REQUEST['base2'].'">';
			$html.='</form>';
		}
		$html.='</td>';
		echo 'addTr(\''.$html.'\');';
	}else{
		$html='<td colspan=2 class="drop primeira tabela"><b>Dropar na primeira</b><br/>'.str_replace("\t",'',str_replace("\n",'',str_replace("\r",'',nl2br($sqlDrop)))).'<br/></td>';
		$html.='<td colspan=2 class="segunda tabela"><b>Criar na segunda</b><br/>'.str_replace("\t",'',str_replace("\n",'',str_replace("\r",'',nl2br(addslashes($sqlCreate))))).'<br/></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan=2 class="primeira tabela">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<form name="create1'.$tab->name.'" action="index.php" method="POST" target="_blank">';
			$html.='<input type="submit" name="submete" value="Dropar">&nbsp;<a style="cursor:pointer;" onclick="if (confirm(\\\'Tem certeza de que quer DROPAR a tabela '.$tab->name.'?\\\')){sqlGo(0, \\\''.addslashes($sqlDropJs).'\\\', this);}" title="'.addslashes($sqlDropJs).'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.addslashes($sqlDropJs).'">';
			$html.='<input type="hidden" name="ati" value="alter"><input type="hidden" name="type" value="'.$_REQUEST['tipo1'].'">';
			$html.='<input type="hidden" name="host" value="'.$_REQUEST['host1'].'"><input type="hidden" name="user" value="'.$_REQUEST['user1'].'">';
			$html.='<input type="hidden" name="pw" value="'.$_REQUEST['pass1'].'"><input type="hidden" name="db" value="'.$_REQUEST['base1'].'">';
			$html.='</form>';
		}
		$html.='</td>';
		$html.='<td colspan=2 class="segunda tabela">';
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$temCreate++;
			$html.='<form name="create1'.$tab->name.'" action="index.php" method="POST" target="_blank">';
			$html.='<input type="submit" name="submete" value="Criar">&nbsp;<a class="gocreate" style="cursor:pointer;" onclick="sqlGo(1, \\\''.str_replace('\\','\\\\\\',addslashes($sqlCreateJs)).'\\\', this);" title="'.addslashes($sqlCreateJs).'">Go!</a>&nbsp;';
			$html.='<input type="hidden" name="sql" value="'.addslashes($sqlCreateJs).'">';
			$html.='<input type="hidden" name="ati" value="alter"><input type="hidden" name="type" value="'.$_REQUEST['tipo2'].'">';
			$html.='<input type="hidden" name="host" value="'.$_REQUEST['host2'].'"><input type="hidden" name="user" value="'.$_REQUEST['user2'].'">';
			$html.='<input type="hidden" name="pw" value="'.$_REQUEST['pass2'].'"><input type="hidden" name="db" value="'.$_REQUEST['base2'].'">';
			$html.='</form>';
		}
		echo 'addTr(\''.$html.'\');';
	}
}
function cospeTrCriarTabela($tab, $primeira=false, $ora, $ms){
	global $PHP_SELF, $trava;
	$sqlCreate=getSqlCreate($tab, $primeira, $ora, $ms);
	$sqlCreateJs=preg_replace('/\s/', ' ', $sqlCreate);
	if ($primeira){
?>
	<form name="create1<?=$tab->name?>" action="<?=$PHP_SELF?>" method="POST" target="_blank">
	<tr>
		<td colspan=4><b>Criar na primeira</b><br/>
		<?=nl2br($sqlCreate)?><br/>
		</td>
	</tr>
	<tr>
		<td colspan=4><?if ($trava=='1'){?>Alteração bloqueada<?}else{?>
		<input type="submit" name="submete" value="Criar">&nbsp;
		<a style="cursor:pointer;" onclick="sqlGo(0, '<?=addslashes($sqlCreateJs)?>', this);" title="<?=$sqlCreate?>">Go!</a>&nbsp;
		<?}?>
		</td>
	</tr>
		<input type="hidden" name="sql" value="<?=$sqlCreate?>">
		<input type="hidden" name="ati" value="alter">
		<input type="hidden" name="type" value="<?=$_REQUEST['tipo1']?>">
		<input type="hidden" name="host" value="<?=$_REQUEST['host1']?>">
		<input type="hidden" name="user" value="<?=$_REQUEST['user1']?>">
		<input type="hidden" name="pw" value="<?=$_REQUEST['pass1']?>">
		<input type="hidden" name="db" value="<?=$_REQUEST['base1']?>">
		<?=montaHidden()?>
	</form>
<?
	}else{
?>
	<form name="create1<?=$tab->name?>" action="<?=$PHP_SELF?>" method="POST" target="_blank">
	<tr>
		<td colspan=4><b>Criar na segunda</b><br/>
		<?=nl2br($sqlCreate)?><br/>
		</td>
	</tr>
	<tr>
		<td colspan=4><?if ($trava=='2'){?>Alteração bloqueada<?}else{?>
		<input type="submit" name="submete" value="Criar">&nbsp;
		<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sqlCreateJs)?>', this);" title="<?=$sqlCreate?>">Go!</a>&nbsp;
		<?}?>
		</td>
	</tr>
		<input type="hidden" name="sql" value="<?=$sqlCreate?>">
		<input type="hidden" name="ati" value="alter">
		<input type="hidden" name="type" value="<?=$_REQUEST['tipo2']?>">
		<input type="hidden" name="host" value="<?=$_REQUEST['host2']?>">
		<input type="hidden" name="user" value="<?=$_REQUEST['user2']?>">
		<input type="hidden" name="pw" value="<?=$_REQUEST['pass2']?>">
		<input type="hidden" name="db" value="<?=$_REQUEST['base2']?>">
		<?=montaHidden()?>
	</form>
<?
	}

}
function cospeTrCriarViewAjax($dbv, $tab, $primeira=false, $ora, $ms){
	global $PHP_SELF, $trava;
	$sqlCreate=getSqlCreateView($dbv, $tab, $ora, $ms);
	$sqlCreateJs=preg_replace('/\s/', ' ', $sqlCreate);
	if ($primeira){
		$html='<td colspan=4><b>Criar na primeira</b><br/>'.str_replace("\t",'',str_replace("\n",'',str_replace("\r",'',nl2br(addslashes($sqlCreate))))).'<br/></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<form name="create1'.$tab.'" action="'.$PHP_SELF.'" method="POST" target="_blank"><td colspan=4>';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<input type="submit" name="submete" value="Criar">&nbsp;<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sqlCreateJs).', this);" title="'.addslashes($sqlCreateJs).'">Go!</a>&nbsp;';
		}
		$html.='</td><input type="hidden" name="sql" value="'.addslashes($sqlCreateJs).'"><input type="hidden" name="ati" value="alter"><input type="hidden" name="type" value="'.$_REQUEST['tipo1'].'">';
		$html.='<input type="hidden" name="host" value="'.$_REQUEST['host1'].'"><input type="hidden" name="user" value="'.$_REQUEST['user1'].'"><input type="hidden" name="pw" value="'.$_REQUEST['pass1'].'">';
		$html.='<input type="hidden" name="db" value="'.$_REQUEST['base1'].'"></form>';
	}else{
		$html='<td colspan=4>===<b>Criar na segunda</b><br/>'.str_replace("\t",'',str_replace("\n",'',str_replace("\r",'',nl2br(addslashes($sqlCreate))))).'<br/></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<form name="create1'.$tab.'" action="'.$PHP_SELF.'" method="POST" target="_blank"><td colspan=4>';
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<input type="submit" name="submete" value="Criar">&nbsp;<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqlCreateJs).', this);" title="'.addslashes($sqlCreateJs).'">Go!</a>&nbsp;';
		}
		$html.='</td><input type="hidden" name="sql" value="'.addslashes($sqlCreateJs).'"><input type="hidden" name="ati" value="alter"><input type="hidden" name="type" value="'.$_REQUEST['tipo2'].'">';
		$html.='<input type="hidden" name="host" value="'.$_REQUEST['host2'].'"><input type="hidden" name="user" value="'.$_REQUEST['user2'].'"><input type="hidden" name="pw" value="'.$_REQUEST['pass2'].'"><input type="hidden" name="db" value="'.$_REQUEST['base2'].'"></form>';
	}
	echo 'addTr(\''.$html.'\');';
}
function cospeTrCriarView($dbv, $tab, $primeira=false, $ora, $ms){
	global $PHP_SELF, $trava;
	$sqlCreate=getSqlCreateView($dbv, $tab, $ora, $ms);
	$sqlCreateJs=preg_replace('/\s/', ' ', $sqlCreate);
	if ($primeira){
?>
	<form name="create1<?=$tab?>" action="<?=$PHP_SELF?>" method="POST" target="_blank">
	<tr>
		<td colspan=4><b>Criar na primeira</b><br/>
		<?=nl2br(str_replace("\"", "&quot;",$sqlCreate))?><br/>
		</td>
	</tr>
	<tr>
		<td colspan=4><?if ($trava=='1'){?>Alteração bloqueada<?}else{?>
		<input type="submit" name="submete" value="Criar">&nbsp;
		<a style="cursor:pointer;" onclick="sqlGo(0, '<?=str_replace(array("'", '"'), array("\'", '\x22'),$sqlCreateJs)?>', this);" title="<?=$sqlCreate?>">Go!</a>&nbsp;
		<?}?>
		</td>
	</tr>
		<input type="hidden" name="sql" value="<?=str_replace('"', '&quot;', $sqlCreate)?>">
		<input type="hidden" name="ati" value="alter">
		<input type="hidden" name="type" value="<?=$_REQUEST['tipo1']?>">
		<input type="hidden" name="host" value="<?=$_REQUEST['host1']?>">
		<input type="hidden" name="user" value="<?=$_REQUEST['user1']?>">
		<input type="hidden" name="pw" value="<?=$_REQUEST['pass1']?>">
		<input type="hidden" name="db" value="<?=$_REQUEST['base1']?>">
		<?=montaHidden()?>
	</form>
<?
	}else{
?>
	<form name="create1<?=$tab?>" action="<?=$PHP_SELF?>" method="POST" target="_blank">
	<tr>
		<td colspan=4>===<b>Criar na segunda</b><br/>
		<?=nl2br(str_replace("\"", "&quot;",$sqlCreate))?><br/>
		</td>
	</tr>
	<tr>
		<td colspan=4><?if ($trava=='2'){?>Alteração bloqueada<?}else{?>
		<input type="submit" name="submete" value="Criar">&nbsp;
		<a style="cursor:pointer;" onclick="sqlGo(1, '<?=str_replace(array("'", '"'), array("\'", '\x22'),$sqlCreateJs)?>', this);" title="<?=$sqlCreate?>">Go!</a>&nbsp;
		<?}?>
		</td>
	</tr>
		<input type="hidden" name="sql" value="<?=str_replace('"', '&quot;', $sqlCreate)?>">
		<input type="hidden" name="ati" value="alter">
		<input type="hidden" name="type" value="<?=$_REQUEST['tipo2']?>">
		<input type="hidden" name="host" value="<?=$_REQUEST['host2']?>">
		<input type="hidden" name="user" value="<?=$_REQUEST['user2']?>">
		<input type="hidden" name="pw" value="<?=$_REQUEST['pass2']?>">
		<input type="hidden" name="db" value="<?=$_REQUEST['base2']?>">
		<?=montaHidden()?>
	</form>
<?
	}

}
function getSqlCreateView($dbv, $nome, $ora, $ms){
	//echo nl2br(print_r($nome, true))."<br>";
	$schema='public';
	if (strpos($nome, '.')!==false){
		$nomeTab=substr($nome, strpos($nome, '.')+1);
		$schema=substr($nome, 0, strpos($nome, '.'));
	}
	$sql="SELECT n.nspname AS schemaname,
			c.relname AS viewname,
			pg_get_viewdef(c.oid) AS definition
		FROM pg_class c
			LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
		WHERE c.relkind = 'v' ::\"char\"
		AND n.nspname='".$schema."'
		AND c.relname='".$nomeTab."';
		";
	if (!$rs=$dbv->Execute($sql)){
		$dbv->_raiseError(__LINE__, __FILE__, $sql);
		exit;
	}
	if (!$rs->EOF){
		$def=$rs->fields(2);
		$fullDef="CREATE OR REPLACE VIEW \"".$schema."\".\"".$nomeTab."\" AS ".$def;
		return $fullDef;
		//echo nl2br(print_r($fullDef, true))."<br>";exit;
	}
	return '';
}
function getSqlDrop($tab, $ora, $ms){
	$sql="drop table ".$tab->name.';';
	return $sql;
}
function getSqlCreate($tab, $primeira=false, $ora, $ms){
	global $schema1, $schema2;
	$nomeTab=$tab->name;
	$nomeLimpo=str_replace($schema1.'.', '', $nomeTab);
	if ($schema1!=$schema2){
		if ($primeira){
			$nomeTab=str_replace($schema2.'.',$schema1.'.',$nomeTab);
			$nomeLimpo=str_replace($schema2.'.', '', $nomeTab);
		}else{
			$nomeTab=str_replace($schema1.'.',$schema2.'.',$nomeTab);
			$nomeLimpo=str_replace($schema1.'.', '', $nomeTab);
		}
	}
	$sql="create table ".$nomeTab." (";
	$sep="";
	$pks=getPks($tab);
	$camposGeom=[];
	foreach($tab->cols as $col){
		if (stripos($col->type, 'geometry')!==false){
			$camposGeom[]=$col;
			continue;
		}
		//echo $col->name.' - '.$col->type."<br/>";
		$sql.=$sep."
".$col->name." ".getFieldType($col->type, $ora, $ms).' '.(strstr(strtolower($col->type), 'char')?' ('.$col->max_length.')':' ').($col->not_null?' NOT NULL':'')."";
		$sep=",";
	}
	if (sizeof($pks)>0) $sql.=$sep."
PRIMARY KEY (".implode(',',$pks).')';
	$sql.="
);";
	foreach($camposGeom as $col){
		$tam=(empty($col->formated)==false?$col->formated:$col->max_length);
		$pg=parseiaGeom($tam);
		$gsql=getAddGeometry(($primeira?$schema1:$schema2),$nomeLimpo, $col->name,$pg['tipo'],$pg['srid'],$pg['dims']);
		$sp=($primeira?$schema1:$schema2);
		if (!empty($sp)) $sp.=',';
		$sql.='<alt>set search_path to '.$sp.'public;</alt>'.$gsql;
	}
	return $sql;
}
function cospeDifIdxAjax($nomeidx, $idx, $nomeidx2, $idx2, $tab, $tab2){
	global $debug, $trava, $cospeDebug, $temRenameIdx;
	if (false && $tab->name=='public.tb1_csct'){
		prettyObj($tab);
		echo nl2br(print_r($tab, true))." ===TAB<br/>";
		echo nl2br(print_r($nomeidx, true))." ===NOME IDX<br/>";
		echo nl2br(print_r($idx, true))."  ===IDX<br/>";
		echo nl2br(print_r($tab, true))." ===TAB2<br/>";
		echo nl2br(print_r($nomeidx2, true))." ===NOME IDX2<br/>";
		echo nl2br(print_r($idx2, true))." ===IDX2<br/>";
		die('aqui ');
	}

	if (!$nomeidx){
		if ($debug) echo("não tinha nomeidx local<br>");
		$txtdif='';
		$sqldrop="alter table ".$tab2->name." drop constraint ".$nomeidx2.";";
		$sqldrop.="<alt>drop index ".$nomeidx2.";";
		$html='<td colspan="4"><b>Índice: '.($nomeidx2).'</b></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan="4">'.addslashes($idx2).'</td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan="2" align="right" class="primeira index">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&host='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($idx2).'" title="'.nl2br(htmlspecialchars(addslashes($idx2))).'">Cria nesta??</a>';
			$html.='<a class="goRenameIdx" style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($idx2).'\\\', this);" title="'.nl2br(htmlspecialchars(addslashes($idx2))).'">Go!</a>';
		}
		$html.='</td><td align="right" class="segunda index">';
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&host='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sqldrop).'" title="'.nl2br($sqldrop).'">Dropa nesta!!</a>';
			$html.='&nbsp;<a class="goRenameIdx" style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqldrop).'\\\', this);" title="'.nl2br($sqldrop).'">Go!</a>';
		}
		$html.='</td><td class="segunda index">&lt;==&nbsp;==&gt;</td>';
		echo 'addTr(\''.$html.'\');';
		//if ($tab->name=='public.tb1_csct') die("alert('em nomeidx')");
		//die('aqui '.$sqldrop);
		return;
	}
	if (!$nomeidx2){
		if ($debug) echo(" não tinha nomeidx remoto<br>");
		$txtdif='';
		$sqldrop="alter table ".$tab->name." drop constraint ".$nomeidx.";";
		$sqldrop.="<alt>drop index ".$nomeidx.";";
		$html='<td colspan="4"><b>Índice: '.($nomeidx).'</b></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan="4">'.addslashes($idx).'</td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan="2" align="right" class="primeira index">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&host='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sqldrop).'" title="'.nl2br($sqldrop).'">Dropa nesta!!</a>';
			$html.='&nbsp;<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sqldrop).'\\\', this);" title="'.nl2br($sqldrop).'">Go!</a>';
		}
		$html.='</td><td align="right" class="segunda index">';
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&host='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($idx).'" title="'.nl2br(htmlspecialchars(addslashes($idx))).'">Cria nesta??</a>';
			$html.='&nbsp;<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.str_replace('\\','\\\\\\',addslashes($idx)).'\\\', this);" title="'.nl2br(htmlspecialchars(addslashes($idx))).'">Go!</a>';
		}
		$html.='</td><td class="segunda index">&lt;==&nbsp;==&gt;</td>';
		echo 'addTr(\''.$html.'\');';
		//if ($tab->name=='public.tb1_csct') die("alert('em nomeidx2')");
		if ($cospeDebug && $nomeidx=='tb0_lgar_lgar0_dado_jsonb_gin')die('aqui ');
		return;
	}
	// echo 'addTr(\'<td colspan="4"><b>Índice: '.($nomeidx).' - '.$nomeidx2.'</b></td>\');';
	// exit;
	if ($nomeidx==$nomeidx2){
		if ($debug) echo(nl2br(print_r($idx, true))."<br>-------------idx-------------<br>");
		if ($debug) echo(nl2br(print_r($idx2, true))."<br>-------------idx2-------------<br>");
		if ($idx==$idx2){
			$html='<td colspan="4"><b>Índice: '.$nomeidx.'</b></td>';
			echo 'addTr(\''.$html.'\');';
		}else{
			$html='<td colspan="4"><b>Índice: '.$nomeidx.'</b></td>';
			echo 'addTr(\''.$html.'\');';
			$html='<td class="primeira index">&nbsp;</td><td class="primeira index">'.$idx.'</td><td class="segunda index">'.$idx2.'</td><td class="segunda index">&lt;==&nbsp;==&gt;</td>';
			echo 'addTr(\''.$html.'\');';
		}
	}else{
		if ($idx==$idx2){
			$html='<td colspan="3"><b>Índice: '.$nomeidx.'</b></td><td><b>OK</b></td>';
			echo 'addTr(\''.$html.'\');';
		}else{
			if ($trava=='1'){
				$renomeia1='Alteração bloqueada';
			}else{
				$temRenameIdx++;
				$sqlRenomeia1='alter index '.$nomeidx.' rename to '.$nomeidx2.';';
				$renomeia1='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&host='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sqlRenomeia1).'" title="'.nl2br($sqlRenomeia1).'">Renomeia nesta</a>';
				$renomeia1.='&nbsp;<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sqlRenomeia1).'\\\', this);" title="'.nl2br($sqlRenomeia1).'">Go!</a>';
			}
			if ($trava=='2'){
				$renomeia2='Alteração bloqueada';
			}else{
				$temRenameIdx++;
				$sqlRenomeia2='alter index '.$nomeidx2.' rename to '.$nomeidx.';';
				$renomeia2='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&host='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sqlRenomeia2).'" title="'.nl2br($sqlRenomeia2).'">Renomeia nesta<a>';
				$renomeia2.='&nbsp;<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqlRenomeia2).'\\\', this);" title="'.nl2br($sqlRenomeia2).'">Go!</a>';
			}
			$html='<td><b>Índice:</b></td><td><b>'.$nomeidx.'</b></td><td><b>'.$nomeidx2.'</b></td><td>'.$renomeia1.'&lt;==&nbsp;==&gt;'.$renomeia2.'</td>';
			echo 'addTr(\''.$html.'\');';
			// $html='<td class="primeira index">&nbsp;</td><td class="primeira index">'.$idx.'</td><td class="segunda index">'.$idx2.'</td><td class="segunda index">&lt;==&nbsp;==&gt;</td>';
		}
	}
}
function cospeDifIdx($nomeidx, $idx, $nomeidx2, $idx2, $tab, $tab2){
	global $debug, $trava;
	if (!$nomeidx){
		if ($debug) echo("não tinha nomeidx local<br>");
		$txtdif='';
		$sqldrop="alter table ".$tab->name." drop constraint ".$nomeidx2.";";
		$sqldrop.="<alt>drop index ".$nomeidx2.";";
?>
	<tr>
		<td colspan="4"><b>Índice: <?=$nomeidx2?></b></td>
	</tr>
	<tr>
		<td colspan="4"><?=$idx2?></td>
	</tr>
	<tr>
		<td>&nbsp;</td>
		<td align="right"><?if ($trava=='1'){?>Alteração bloqueada<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo1']?>host=<?=$_REQUEST['host1']?>&dbuser=<?=$_REQUEST['user1']?>&pw=<?=$_REQUEST['pass1']?>&db=<?=$_REQUEST['base1']?>&sql=<?=urlencode($idx2)?>" title="<?=$idx2?>">Cria nesta</a>
			&nbsp;<a style="cursor:pointer;" onclick="sqlGo(0, '<?=addslashes($idx2)?>', this);" title="<?=$idx2?>">Go!</a>
		<?}?>
		</td>
		<td align="right"><?if ($trava=='2'){?>Alteração bloqueada<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo2']?>&dbhost=<?=$_REQUEST['host2']?>&dbuser=<?=$_REQUEST['user2']?>&pw=<?=$_REQUEST['pass2']?>&db=<?=$_REQUEST['base2']?>&sql=<?=urlencode($sqldrop)?>" title="<?=$sqldrop?>">Drop nesta</a>&nbsp;
			&nbsp;<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sqldrop)?>', this);" title="<?=$sqldrop?>">Go!</a>
		<?}?>
		</td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
		return;
	}
	if (!$nomeidx2){
		if ($debug) echo(" não tinha nomeidx remoto<br>");
		$txtdif='';
		$sqldrop="alter table ".$tab2->name." drop constraint ".$nomeidx.";";
		$sqldrop.="<alt>drop index ".$nomeidx.";";
?>
	<tr>
		<td colspan="4"><b>Índice: <?=$nomeidx?></b></td>
	</tr>
	<tr>
		<td colspan="4"><?=$idx?></td>
	</tr>
	<tr>
		<td>&nbsp;</td>
		<td align="right"><?if ($trava=='1'){?>Alteração bloqueada<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo1']?>&dbhost=<?=$_REQUEST['host1']?>&dbuser=<?=$_REQUEST['user1']?>&pw=<?=$_REQUEST['pass1']?>&db=<?=$_REQUEST['base1']?>&sql=<?=urlencode($sqldrop)?>" title="<?=$sqldrop?>">Drop nesta</a>&nbsp;
			&nbsp;<a style="cursor:pointer;" onclick="sqlGo(0, '<?=addslashes($sqldrop)?>', this);" title="<?=$sqldrop?>">Go!</a>
		<?}?>
		</td>
		<td align="right"><?if ($trava=='2'){?>Alteração bloqueada<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo2']?>&dbhost=<?=$_REQUEST['host2']?>&dbuser=<?=$_REQUEST['user2']?>&pw=<?=$_REQUEST['pass2']?>&db=<?=$_REQUEST['base2']?>&sql=<?=urlencode($idx)?>" title="<?=$idx?>">Cria nesta</a>&nbsp;
			&nbsp;<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($idx)?>', this);" title="<?=$idx?>">Go!</a>
		<?}?>
		</td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
		return;
	}
	if ($nomeidx==$nomeidx2){
		if ($debug) echo(nl2br(print_r($idx, true))."<br>-------------idx-------------<br>");
		if ($debug) echo(nl2br(print_r($idx2, true))."<br>-------------idx2-------------<br>");
		if ($idx==$idx2){
?>
	<tr>
		<td colspan="4"><b>Índice: <?=$nomeidx?></b></td>
	</tr>
<?
		}else{
?>
	<tr>
		<td colspan="4"><b>Índice: <?=$nomeidx?></b></td>
	</tr>
	<tr>
		<td>&nbsp;</td>
		<td><?=$idx?></td>
		<td><?=$idx2?></td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
		}
	}else{
		if ($idx==$idx2){
?>
	<tr>
		<td colspan="3"><b>Índice: <?=$nomeidx?></b></td>
		<td><b>OK</b></td>
	</tr>
<?
		}else{
?>
	<tr>
		<td><b>Índice:</b></td>
		<td><b><?=$nomeidx?></b></td>
		<td><b><?=$nomeidx2?></b></td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
	<tr>
		<td>&nbsp;</td>
		<td><?=$idx?></td>
		<td><?=$idx2?></td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
		}
	}
}


function cospeDifFkAjax($nome, $nomeRep, $nomefk, $fk, $nomefk2, $fk2, $mostrouTabela=true){
	global $debug, $trava, $schema1, $schema2;
	if (!$nomefk){
		if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
		if ($debug) echo("não tinha nomefk local<br>");
		$txtdif='';
		//echo nl2br(print_r($fk2, true))."<br>";exit;
		foreach($fk2 as $campofk2){
			$dados=explode("=", $campofk2);
			$tabRel=$dados[0];
			$campoLocal=$dados[1];
			$campoRel=$dados[2];
			$txtdif.=$campofk2."<br>";
		}
		$sql='ALTER TABLE '.$nome.' ADD CONSTRAINT '.$nomefk2.' FOREIGN KEY ('.$campoRel.') REFERENCES '.$schema1.'.'.$tabRel.'('.$campoLocal.');';
		$sqldrop='ALTER TABLE '.$nome.' DROP CONSTRAINT '.$nomefk2.';';
		$html='<td colspan="4"><b>FK: '.$nomefk2.'</b></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td colspan="4">'.nl2br($sql).'</td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td class="primeira fk">Tab. rel.: '.$tabRel.'</td><td align="right" class="segunda fk">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&dbhost='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sql).'" title="'.nl2br($sql).'">Criar</a>&nbsp;';
			$html.='&nbsp;<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sql).'\\\', this);" title="'.nl2br($sql).'">Go!</a>';
		}
		$html.='</td><td class="segunda fk">'.$txtdif.'</td><td class="segunda fk">&lt;==&nbsp;<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqldrop).'\\\', this);" title="'.$sqldrop.'">Go!</a></td>';
		echo 'addTr(\''.$html.'\');';
		return;
	}
	if (!$nomefk2){
		if (!$mostrouTabela) $mostrouTabela=cospeTrTabelaAjax($nome);
		if ($debug) echo(sizeof($fk)." não tinha nomefk remoto<br>");
		$txtdif='';
		reset($fk);
		foreach($fk as $campofk){
			if ($debug) echo($campofk."<br>");
			$dados=explode("=", $campofk);
			$tabRel=$dados[0];
			$campoLocal=$dados[1];
			$campoRel=$dados[2];
			$txtdif.=$campofk."<br>";
		}
		$sql='ALTER TABLE '.$nomeRep.' ADD CONSTRAINT '.$nomefk.' FOREIGN KEY ('.$campoRel.') REFERENCES '.$schema2.'.'.$tabRel.'('.$campoLocal.');';
		$html='<td colspan="4"><b>FK: '.$nomefk.'</b></td>';
		echo 'addTr(\''.$html.'\');';
		$html='<td class="primeira fk">Tab. rel.: '.$tabRel.'</td><td class="primeira fk">'.$txtdif.'</td>';
		$html.='<td align="right" class="segunda fk"><a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&dbhost='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sql).'" title="'.nl2br($sql).'">Criar</a>';
		$html.='&nbsp<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sql).'\\\', this);" title="'.nl2br($sql).'">Go!</a>&nbsp;</td><td class="segunda fk">&lt;==&nbsp;==&gt;</td>';
		echo 'addTr(\''.$html.'\');';
		return;
	}
	if ($nomefk==$nomefk2){
		if ($debug) echo(nl2br(print_r($fk, true))."<br>-------------fk-------------<br>");
		if ($debug) echo(nl2br(print_r($fk2, true))."<br>-------------fk2-------------<br>");
		$txtdif='';
		foreach($fk as $campofk){
			$dados=explode("=", $campofk);
			$tabRel=$dados[0];
			if ($debug) echo((int)array_search($campofk, $fk2)."<br>");
			$key2=array_search($campofk, $fk2);
			if ($key2===false){
				$txtdif.=$campofk."==>NÃO TEM"."<br>";
			}else{
				if ($key2>0){
					for ($i=0; $i<$key2; $i++){
						$txtdif.="NÃO TEM==>".$fk2[$i]."<br>";
						unset($fk2[$i]);
					}
				}
				unset($fk2[$key2]);
			}
		}
		foreach ($fk2 as $campofk2){
			$txtdif.="NÃO TEM==>".$campofk2."<br>";
		}
		if ($txtdif!=''){
			if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
			$sqldrop='ALTER TABLE '.$nome.' DROP CONSTRAINT '.$nomefk.';';
			$html='<td colspan="4">FK: '.$nomefk.'</td>';
			echo 'addTr(\''.$html.'\');';
			$html='<td>Tab. rel.: '.$tabRel.'</td><td colspan="2">'.$txtdif.'</td>';
			$html.='<td>&lt;==&nbsp;&nbsp<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqldrop).'\\\', this);" title="'.nl2br($sqldrop).'">Go!</a>&nbsp;</td>';
			echo 'addTr(\''.$html.'\');';
		}
	}else{

	}
	return $mostrouTabela;

}
function cospeDifFk($nome, $nomefk, $fk, $nomefk2, $fk2, $mostrouTabela=true){
	global $debug, $trava;
	if (!$nomefk){
		if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
		if ($debug) echo("não tinha nomefk local<br>");
		$txtdif='';
		//echo nl2br(print_r($fk2, true))."<br>";exit;
		foreach($fk2 as $campofk2){
			$dados=explode("=", $campofk2);
			$tabRel=$dados[0];
			$campoLocal=$dados[1];
			$campoRel=$dados[2];
			$txtdif.=$campofk2."<br>";
		}
		$sql='ALTER TABLE '.$nome.' ADD CONSTRAINT '.$nomefk2.' FOREIGN KEY ('.$campoRel.') REFERENCES '.$tabRel.'('.$campoLocal.');';
		$sqldrop='ALTER TABLE '.$nome.' DROP CONSTRAINT '.$nomefk2.';';
?>
	<tr>
		<td colspan="4"><b>FK: <?=$nomefk2?></b></td>
	</tr>
	<tr>
		<td colspan="4"><?=$sql?></td>
	</tr>
	<tr>
		<td>Tab. rel.: <?=$tabRel?></td>
		<td align="right"><?if ($trava=='1'){?>Alteração bloqueada<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo1']?>&dbhost=<?=$_REQUEST['host1']?>&dbuser=<?=$_REQUEST['user1']?>&pw=<?=$_REQUEST['pass1']?>&db=<?=$_REQUEST['base1']?>&sql=<?=urlencode($sql)?>" title="<?=$sql?>">Criar</a>&nbsp;
			<a style="cursor:pointer;" onclick="sqlGo(0, '<?=addslashes($sql)?>', this);" title="<?=$sql?>">Go!</a>
		<?}?>


		</td>
		<td><?=$txtdif?></td>
		<td>&lt;==&nbsp;<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sqldrop)?>', this);" title="<?=$sqldrop?>">Go!</a></td>
	</tr>
<?
		return;
	}
	if (!$nomefk2){
		if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
		if ($debug) echo(sizeof($fk)." não tinha nomefk remoto<br>");
		$txtdif='';
		reset($fk);
		foreach($fk as $campofk){
			if ($debug) echo($campofk."<br>");
			$dados=explode("=", $campofk);
			$tabRel=$dados[0];
			$campoLocal=$dados[1];
			$campoRel=$dados[2];
			$txtdif.=$campofk."<br>";
		}
		$sql='ALTER TABLE '.$nome.' ADD CONSTRAINT '.$nomefk.' FOREIGN KEY ('.$campoRel.') REFERENCES '.$tabRel.'('.$campoLocal.');';
?>
	<tr>
		<td colspan="4">FK: <?=$nomefk?></td>
	</tr>
	<tr>
		<td>Tab. rel.: <?=$tabRel?></td>
		<td><?=$txtdif?></td>
		<td align="right"><a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo2']?>&dbhost=<?=$_REQUEST['host2']?>&dbuser=<?=$_REQUEST['user2']?>&pw=<?=$_REQUEST['pass2']?>&db=<?=$_REQUEST['base2']?>&sql=<?=urlencode($sql)?>" title="<?=$sql?>">Criar</a>
		&nbsp<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sql)?>', this);" title="<?=$sql?>">Go!</a>&nbsp;</td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
		return;
	}
	if ($nomefk==$nomefk2){
		if ($debug) echo(nl2br(print_r($fk, true))."<br>-------------fk-------------<br>");
		if ($debug) echo(nl2br(print_r($fk2, true))."<br>-------------fk2-------------<br>");
		$txtdif='';
		foreach($fk as $campofk){
			$dados=explode("=", $campofk);
			$tabRel=$dados[0];
			if ($debug) echo((int)array_search($campofk, $fk2)."<br>");
			$key2=array_search($campofk, $fk2);
			if ($key2===false){
				$txtdif.=$campofk."==>NÃO TEM"."<br>";
			}else{
				if ($key2>0){
					for ($i=0; $i<$key2; $i++){
						$txtdif.="NÃO TEM==>".$fk2[$i]."<br>";
						unset($fk2[$i]);
					}
				}
				unset($fk2[$key2]);
			}
		}
		foreach ($fk2 as $campofk2){
			$txtdif.="NÃO TEM==>".$campofk2."<br>";
		}
		if ($txtdif!=''){
			if (!$mostrouTabela) $mostrouTabela=cospeTrTabela($nome);
			$sqldrop='ALTER TABLE '.$nome.' DROP CONSTRAINT '.$nomefk.';';
?>
	<tr>
		<td colspan="4">FK: <?=$nomefk?></td>
	</tr>
	<tr>
		<td>Tab. rel.: <?=$tabRel?></td>
		<td colspan="2"><?=$txtdif?></td>
		<td>&lt;==&nbsp;&nbsp<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sqldrop)?>', this);" title="<?=$sqldrop?>">Go!</a>&nbsp;</td>
	</tr>
<?
		}
	}else{

	}
	return $mostrouTabela;

}

function descreveFk($fk){

}
function getPks($tab){
	if (isset($tab->from) && $tab->from=='json') {
		// echo nl2br(print_r($tab, true))."<br/>";exit;
	}
	if (@!$tab->pks){
		$tab->pks=array();
		foreach($tab->cols as $col){
			if (!empty($col->primary_key)) $tab->pks[]=$col->name;
		}
	}
	return $tab->pks;
}
function getAddColumn($tab, $tab2, $col, $col2, $ora, $ms){
	global $schema1, $schema2;
	if ($ora){
		if ($col){
			if ($col->not_null){
				$sql="alter table ".$tab2->name." add ".$col->name." ".getFieldType($col->type, $ora, $ms).(strstr($col->type, 'char')?' ('.$col->max_length.')':' ').' NULL'.';';
				$sql.="<user>"."alter table ".$tab2->name." alter column ".$col->name." ".' SET NOT NULL;';
				if (!empty($col->primary_key)){
					$pks=getPks($tab);
					$sql.="<user>"."alter table ".$tab2->name." ADD PRIMARY KEY (".join(',',$pks).');';
				}
			}else{
				$sql="alter table ".$tab2->name." add ".$col->name." ".getFieldType($col->type, $ora, $ms).(strstr($col->type, 'char')?' ('.$col->max_length.')':' ').' NULL;';
			}
		}else{
			$sql="alter table ".$tab->name." add ".$col2->name." ".getFieldType($col2->type, $ora, $ms).(strstr($col2->type, 'char')?' ('.$col2->max_length.')':' ').($col2->not_null?' NOT NULL':'').(empty($col2->primary_key)?'':' PK').';';
			if ($col2->not_null){
				$sql="alter table ".$tab->name." add ".$col2->name." ".getFieldType($col2->type, $ora, $ms).(strstr($col2->type, 'char')?' ('.$col2->max_length.')':' ').' NULL'.';';
				$sql.="<user>"."alter table ".$tab->name." alter column ".$col2->name." ".' SET NOT NULL;';
				if (!empty($col2->primary_key)){
					$pks=getPks($tab2);
					$sql.="<user>"."alter table ".$tab->name." ADD PRIMARY KEY (".join(',',$pks).');';
				}
			}else{
				$sql="alter table ".$tab->name." add ".$col2->name." ".getFieldType($col2->type, $ora, $ms).(strstr($col2->type, 'char')?' ('.$col2->max_length.')':' ').' NULL;';
			}
		}
	}elseif($ms){

	}else{
		if ($col){
			if ($col->type=='geometry'){
				$tam=(empty($col->formated)==false?$col->formated:$col->max_length);
				$pg=parseiaGeom($tam);
				$sql=getAddGeometry($schema1, $tab->name, $col->name, $pg['tipo'], $pg['srid'], $pg['dims']);
				// $sql="select AddGeometryColumn('".$tab2->name."', '".$col->name."', ".$col->srid.", '".$col->type."', ".$col->dimension.");";
			}else{
				if ($col->not_null){
					$sql="alter table ".$tab2->name." add column ".$col->name." ".getFieldType($col->type, $ora, $ms).(strstr($col->type, 'char')?' ('.$col->max_length.')':' ').' NULL'.';';
					$sql.="<user>"."alter table ".$tab2->name." alter column ".$col->name." ".' SET NOT NULL;';
					if (!empty($col->primary_key)){
						$pks=getPks($tab);
						$sql.="<user>"."alter table ".$tab2->name." ADD PRIMARY KEY (".join(',',$pks).');';
					}
				}else{
					$sql="alter table ".$tab2->name." add column ".$col->name." ".getFieldType($col->type, $ora, $ms).(strstr($col->type, 'char')?' ('.$col->max_length.')':' ').' NULL;';
				}
			}
		}else{
			if ($col2->type=='geometry'){
				$tam=(empty($col2->formated)==false?$col2->formated:$col2->max_length);
				$pg=parseiaGeom($tam);
				$sql=getAddGeometry($schema2, $tab2->name, $col2->name, $pg['tipo'], $pg['srid'], $pg['dims']);
				// $sql="select AddGeometryColumn('".$tab2->name."', '".$col2->name."', ".$col2->srid.", '".$col2->type."', ".$col2->dimension.");";
			}else{
				$sql="alter table ".$tab->name." add column ".$col2->name." ".getFieldType($col2->type, $ora, $ms).(strstr($col2->type, 'char')?' ('.$col2->max_length.')':' ').($col2->not_null?' NOT NULL':'').(empty($col2->primary_key)?'':' PK').';';
				if ($col2->not_null){
					$sql="alter table ".$tab->name." add column ".$col2->name." ".getFieldType($col2->type, $ora, $ms).(strstr($col2->type, 'char')?' ('.$col2->max_length.')':' ').' NULL'.';';
					$sql.="<user>"."alter table ".$tab->name." alter column ".$col2->name." ".' SET NOT NULL;';
					if (!empty($col2->primary_key)){
						$pks=getPks($tab2);
						$sql.="<user>"."alter table ".$tab->name." ADD PRIMARY KEY (".join(',',$pks).');';
					}
				}else{
					$sql="alter table ".$tab->name." add column ".$col2->name." ".getFieldType($col2->type, $ora, $ms).(strstr($col2->type, 'char')?' ('.$col2->max_length.')':' ').' NULL;';
				}
			}
		}
	}
	return $sql;
}
function getDropColumn($tab, $tab2, $col, $col2, $ora, $ms){
	if ($ora){

	}elseif($ms){

	}else{
		if ($col){
			$sql="alter table ".$tab2->name." drop column ".$col->name.';';
		}else{
			$sql="alter table ".$tab->name." drop column ".$col2->name.';';
		}
	}
	return $sql;
}
function cospeDifColsAjax($tab, $tab2, $col, $col2, $ora1=false, $ms1=false, $ora2=false, $ms2=false){
	global $trava, $temComenta, $temColunas, $temColunasIgnore;
	if (!$col2){
		$oNome=$col->name;
		$sqlComment="COMMENT ON COLUMN ".$tab2->name.".".$col->name." IS '".addslashes($col->comment)."';";
		$sqladd=getAddColumn($tab, $tab2, $col, $col2, $ora2, $ms2);
		// $sqladd.=$sqlComment;
		$sqladd=str_replace("\r", " ", $sqladd);
		$sqladd=str_replace("\n", " ", $sqladd);
		$sqladd=str_replace("\t", " ", $sqladd);
		$sqldrop=getDropColumn($tab, $tab2, $col, $col2, $ora1, $ms1);
		$sqldrop=str_replace("\r", " ", $sqldrop);
		$sqldrop=str_replace("\n", " ", $sqldrop);
		$sqldrop=str_replace("\t", " ", $sqldrop);
		$html='<td class="primeira coluna">'.$col->name.'</td><td class="primeira coluna">'.descreveCol($col, $ora1, $ms1).'</td><td class="segunda coluna ausente">ausente</td>';
		$html.='<td class="segunda coluna">';
		if ($trava=='1'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&dbhost='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sqldrop).'" title="'.$sqldrop.'">Remove à esquerda</a>&nbsp;';
			$html.='<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sqldrop).'\\\', this);" title="'.$sqldrop.'">Go!</a>&nbsp;';
			$html.=' | <input type="checkbox" name="ignore_col[]" class="ignore_col" value="'.$tab->name.'.'.$oNome.'">&nbsp;Ignorar';
			$temColunas++;
		}
		if ($trava=='2'){
			$html.='Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&dbhost='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sqladd.($sqlComment)).'" title="'.addslashes($sqladd.$sqlComment).'">Adiciona à direita</a>&nbsp;';
			$html.='<a style="cursor:pointer;" onclick="sqlGo(1,'.addslashes("'".addslashes($sqladd.$sqlComment)."'").', this);" title="'.addslashes($sqladd.$sqlComment).'">Go!</a>';
			$html.=' | <input type="checkbox" name="ignore_col[]" class="ignore_col" value="'.$tab->name.'.'.$oNome.'">&nbsp;Ignorar';
			$temColunas++;
		}
		$html.='</td>';
		echo 'addTr(\''.$html.'\');';
		return;
	}
	if (!$col){
		$oNome=$col2->name;
		$sqladd=getAddColumn($tab, $tab2, $col, $col2, $ora2, $ms2);
		$sqldrop=getDropColumn($tab, $tab2, $col, $col2, $ora1, $ms1);
		$sqlComment="COMMENT ON COLUMN ".$tab->name.".".$col2->name." IS '".addslashes($col2->comment)."';";
		$html='<td class="primeira coluna">'.$col2->name.'</td><td class="segunda coluna ausente">ausente</td><td class="segunda coluna">'.descreveCol($col2, $ora2, $ms2).'</td>';
		$html.='<td class="segunda coluna">';
		if ($trava=='1'){
			$html.='&lt;&lt; Alteração bloqueada';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&dbhost='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sqladd.($sqlComment)).'" title="'.addslashes($sqladd.$sqlComment).'">Adiciona à esquerda</a>&nbsp;';
			$html.='<a style="cursor:pointer;" onclick="sqlGo(0,'.addslashes("'".addslashes($sqladd.$sqlComment)."'").', this);" title="'.addslashes($sqladd.$sqlComment).'">Go!</a>&nbsp;';
			$html.=' | <input type="checkbox" name="ignore_col[]" class="ignore_col" value="'.$tab->name.'.'.$oNome.'">&nbsp;Ignorar';
			$temColunas++;
		}
		$html.=' | ';
		if ($trava=='2'){
			$html.='Alteração bloqueada &gt;&gt;';
		}else{
			$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&dbhost='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sqldrop).'" title="'.$sqldrop.'">Remove à direita</a>&nbsp;';
			$html.='<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sqldrop).'\\\', this);" title="'.$sqldrop.'">Go!</a>';
			$html.=' | <input type="checkbox" name="ignore_col[]" class="ignore_col" value="'.$tab->name.'.'.$oNome.'">&nbsp;Ignorar';
			$temColunas++;
		}
		$html.='</td>';
		echo 'addTr(\''.$html.'\');';
		return;
	}else{
		$oNome=$col->name;
		$dc=descreveCols($col, $col2, $ora1, $ms1, $ora2, $ms2, $tab->name);
		if(!empty($dc)){
			$html='<td class="primeira coluna">'.$oNome.'</td>'.addslashes($dc);
			echo 'addTr(\''.$html.'\');';
		}
		if($col->comment!=$col2->comment){
			$sqlComment="COMMENT ON COLUMN ".$tab->name.".".$col->name." IS '".addslashes($col2->comment)."';";
			$sqlComment2="COMMENT ON COLUMN ".$tab2->name.".".$col2->name." IS '".addslashes($col->comment)."';";
			$html='<td class="primeira coluna">COMMENT: '.$col->name.'</td><td class="primeira coluna">'.$col->comment.'</td><td class="segunda coluna">'.$col2->comment.'</td>';
			$html.='<td class="segunda coluna">';
			/*
			if ($trava=='1'){
				$html.='&lt;&lt; Alteração bloqueada';
			}else{
				$temComenta++;
				$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&dbhost='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sqlComment).'" title="'.$sqlComment.'">Comenta à esquerda</a>&nbsp;';
				$html.='<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sqlComment).'\\\', this);" title="'.addslashes($sqlComment).'">Go!</a>&nbsp;';
			}
				*/
				$html.=' | ';
			if ($trava=='2'){
				$html.='Alteração bloqueada &gt;&gt;';
			}else{
				$temComenta++;
				$html.='<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&dbhost='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sqlComment2).'" title="'.addslashes($sqlComment2).'">Comenta à direita</a>&nbsp;';
				$html.='<a class="comenta" style="cursor:pointer;" onclick="sqlGo(1, '.addslashes("'".addslashes($sqlComment2)."'").', this);" title="'.addslashes($sqlComment2).'">Go!</a>';
			}
			$html.='</td>';
			echo 'addTr(\''.$html.'\');';
		}
	}
}
function cospeDifCols($tab, $tab2, $col, $col2, $ora1=false, $ms1=false, $ora2=false, $ms2=false){
	global $trava;
	if (!$col2){
		$sqladd=getAddColumn($tab, $tab2, $col, $col2, $ora2, $ms2);
		$sqldrop=getDropColumn($tab, $tab2, $col, $col2, $ora1, $ms1);
?>
	<tr>
		<td><?=$col->name?></td>
		<td><?=descreveCol($col, $ora1, $ms1)?></td>
		<td bgcolor="#ffff00">ausente</td>
		<td><?if ($trava=='1'){?>Alteração bloqueada<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo1']?>&dbhost=<?=$_REQUEST['host1']?>&dbuser=<?=$_REQUEST['user1']?>&pw=<?=$_REQUEST['pass1']?>&db=<?=$_REQUEST['base1']?>&sql=<?=urlencode($sqldrop)?>" title="<?=$sqldrop?>">Remove à esquerda</a>&nbsp;
			<a style="cursor:pointer;" onclick="sqlGo(0, '<?=addslashes($sqldrop)?>', this);" title="<?=$sqldrop?>">Go!</a>&nbsp;
		<?}?>
		<?if ($trava=='2'){?>Alteração bloqueada<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo2']?>&dbhost=<?=$_REQUEST['host2']?>&dbuser=<?=$_REQUEST['user2']?>&pw=<?=$_REQUEST['pass2']?>&db=<?=$_REQUEST['base2']?>&sql=<?=urlencode($sqladd)?>" title="<?=$sqladd?>">Adiciona à direita</a>&nbsp;
			<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sqladd)?>', this);" title="<?=$sqladd?>">Go!</a>
		<?}?>
		</td>
	</tr>
<?
		return;
	}
	if (!$col){
		$sqladd=getAddColumn($tab, $tab2, $col, $col2, $ora2, $ms2);
		$sqldrop=getDropColumn($tab, $tab2, $col, $col2, $ora1, $ms1);
?>
	<tr>
		<td><?=$col2->name?></td>
		<td bgcolor="#ffff00">ausente</td>
		<td><?=descreveCol($col2, $ora2, $ms2)?></td>
		<td>
		<?if ($trava=='1'){?>
			Alteração bloqueada
		<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo1']?>&dbhost=<?=$_REQUEST['host1']?>&dbuser=<?=$_REQUEST['user1']?>&pw=<?=$_REQUEST['pass1']?>&db=<?=$_REQUEST['base1']?>&sql=<?=urlencode($sqladd)?>" title="<?=$sqladd?>">Adiciona à esquerda</a>&nbsp;
			<a style="cursor:pointer;" onclick="sqlGo(0, '<?=addslashes($sqladd)?>', this);" title="<?=$sqladd?>">Go!</a>&nbsp;
		<?}?>
		<?if ($trava=='2'){?>
			Alteração bloqueada
		<?}else{?>
			<a target="_blank" href="index.php?ati=doalter&type=<?=$_REQUEST['tipo2']?>&dbhost=<?=$_REQUEST['host2']?>&dbuser=<?=$_REQUEST['user2']?>&pw=<?=$_REQUEST['pass2']?>&db=<?=$_REQUEST['base2']?>&sql=<?=urlencode($sqldrop)?>" title="<?=$sqldrop?>">Remove à direita</a>&nbsp;
			<a style="cursor:pointer;" onclick="sqlGo(1, '<?=addslashes($sqldrop)?>', this);" title="<?=$sqldrop?>">Go!</a>&nbsp;
		<?}?>
	</tr>
<?
		return;
	}
?>
	<tr>
		<td><?=$col->name?></td>
		<?=descreveCols($col, $col2, $ora1, $ms1, $ora2, $ms2, $tab->name)?>
	</tr>
<?
}
function cospeTrSequenciaAjax($nome, $faltando=false, $local=false){
	global $trava , $temSequencias;
	$temSequencias++;
	$txt="Sequência ".$nome.' <input type="checkbox" class="ignore_seq" name="ignore_seq[]" value="'.$nome.'"> marcar para ignorar';
	$bgc='silver';
	if ($faltando) {
		$txt="<b>".$txt."</b>";
		$bgc="lightgreen";
		$html='<td colspan=2'.($trava==1?' class="bloq"':'').'>'.($local?"<b>AUSENTE</b>":"<b>".$txt."</b>").'</td><td colspan=2'.($trava==2?' class="bloq"':'').'>'.($local?"<b>".$txt."</b>":"<b>AUSENTE</b>").'</td>';
	}else{
		$html='<td colspan="4"><b>'.$txt.'</b></td>';
	}
	echo 'addTr(\''.$html.'\', \''.$bgc.'\');';
	return true;
}
function cospeTrSequencia($nome, $faltando=false, $local=false){
	$txt="Sequência ".$nome;
	$bgc='silver';
	if ($faltando) {
		$txt="<b>".$txt."</b>";
		$bgc="yellow";
?>
	<tr bgcolor="<?=$bgc?>">
		<td><b><?=$txt?></b></td>
		<td><?=($local?"AUSENTE":"&nbsp;")?></td>
		<td><?=($local?"&nbsp;":"AUSENTE")?></td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
	}else{
?>
	<tr bgcolor="<?=$bgc?>">
		<td colspan="4"><b><?=$txt?></b></td>
	</tr>
<?
	}
	return true;
}
function getIgnoreTables(){
	if (file_exists('ignoreTables.json')){
		$json=json_decode(file_get_contents('ignoreTables.json'), true);
	}else{
		$json=[];
	}
	return $json;
}
function getIgnoreSeqs(){
	if (file_exists('ignoreSeqs.json')){
		$json=json_decode(file_get_contents('ignoreSeqs.json'), true);
	}else{
		$json=[];
	}
	return $json;
}
function getIgnoreCols(){
	if (file_exists('ignoreCols.json')){
		$json=json_decode(file_get_contents('ignoreCols.json'), true);
	}else{
		$json=[];
	}
	return $json;
}
function cospeTrTabelaAjax($nome, $faltando=false, $local=false){
	global $trava;
	$txt="Tabela ".$nome.' <input type="checkbox" class="ignore_tab" name="ignore_tab[]" value="'.$nome.'"> marcar para ignorar';
	$bgc='silver';
	if ($faltando) {
		$txt="<b>".$txt."</b>";
		$bgc="yellow";
	}
	if ($faltando){
		$html='<td class="primeira tabela" colspan=2'.($trava==1?' class="bloq"':'').'>'.($local?"<b>AUSENTE</b>":'<b>'.$txt.'</b>').'</td><td class="segunda tabela" colspan=2'.($trava==2?' class="bloq"':'').'>'.($local?'<b>'.$txt.'</b>':"<b>AUSENTE</b>").'</td>';
	}else{
		$html='<td colspan="4" class="tit-tabela"><b>'.$txt.'</b></td>';
	}
	echo 'addTr(\''.$html.'\',\''.$bgc.'\');#_#';
	return true;
}
function cospeTrTabela($nome, $faltando=false, $local=false){
	$txt="Tabela ".$nome;
	$bgc='silver';
	if ($faltando) {
		$txt="<b>".$txt."</b>";
		$bgc="yellow";
	}
	if ($faltando){
?>
	<tr bgcolor="<?=$bgc?>">
		<td><b><?=$txt?></b></td>
		<td><?=($local?"AUSENTE":"&nbsp;")?></td>
		<td><?=($local?"&nbsp;":"AUSENTE")?></td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
	}else{
?>
	<tr bgcolor="<?=$bgc?>">
		<td colspan="4"><b><?=$txt?></b></td>
	</tr>
<?
	}
	return true;
}
function cospeTrViewAjax($nome, $faltando=false, $local=false){
	$txt="View ".$nome;
	$bgc='silver';
	if ($faltando) {
		$txt="<b>".$txt."</b>";
		$bgc="yellow";
	}
	if ($faltando){
		$html='<tr bgcolor="'.$bgc.'"><td><b>'.$txt.'</b></td><td>'.($local?"AUSENTE":"&nbsp;").'</td><td>'.($local?"&nbsp;":"AUSENTE").'</td><td>&lt;==&nbsp;==&gt;</td></tr>';
	}else{
		$html='<tr bgcolor="'.$bgc.'"><td colspan="4"><b>'.$txt.'</b></td></tr>';
	}
	echo 'addTr(\''.$html.'\',\''.$bgc.'\');#_#';
	return true;
}
function cospeTrView($nome, $faltando=false, $local=false){
	$txt="View ".$nome;
	$bgc='silver';
	if ($faltando) {
		$txt="<b>".$txt."</b>";
		$bgc="yellow";
	}
	if ($faltando){
?>
	<tr bgcolor="<?=$bgc?>">
		<td><b><?=$txt?></b></td>
		<td><?=($local?"AUSENTE":"&nbsp;")?></td>
		<td><?=($local?"&nbsp;":"AUSENTE")?></td>
		<td>&lt;==&nbsp;==&gt;</td>
	</tr>
<?
	}else{
?>
	<tr bgcolor="<?=$bgc?>">
		<td colspan="4"><b><?=$txt?></b></td>
	</tr>
<?
	}
	return true;
}

function descreveCol($col, $ora2=false, $ms2=false){
	if (!$col) return '';
	return $col->type.' ('.$col->max_length.')'.($col->not_null?' NOT NULL':'').(empty($col->primary_key)?'':' PK');
}

function descreveCols($col, $col2, $ora1=false, $ms1=false, $ora2=false, $ms2=false, $tabname){
	//echoFuncStack();exit;
	global $trava, $schema1, $schema2;
	$tabname2=$tabname;
	if ($schema1!=$schema2){
		$tabname2=str_replace($schema1.'.',$schema2.'.',$tabname);
	}
	//echo(nl2br(print_r($col2, true))."<br>");
	$txtDebug='';
	$txt='';
	$txt2='';
	$ora=false;
	$ms=false;
	$tipoIgual=comparaTipoGeral($col, $col2, $ora1, $ms1, $ora2, $ms2);
	$tamIgual=comparaTamGeral($col, $col2, $ora1, $ms1, $ora2, $ms2);
	$defaultIgual=comparaDefaultGeral($col, $col2, $ora1, $ms1, $ora2, $ms2);
	$nullIgual=true;
	$acao='';
	$sepAcao='';
	if ($ora2){
		$tipoIgual=comparaTipoOracle($col, $col2);
		$tamIgual=($tipoIgual & comparaOracle($col, $col2));
	}else if ($ms2){
		$tipoIgual=comparaTipoMSSqlServer($col, $col2);
		$tamIgual=($tipoIgual & comparaMSSqlServer($col, $col2));
	}else{
		$tipoIgual=($col->type==$col2->type);
		$tamIgual=($tipoIgual & $col->max_length!=$col2->max_length);
		if ($col->type=='geometry'){
			$pg1=parseiaGeom($col->formated);
			$pg2=parseiaGeom($col2->formated);
			$tamIgual=($pg1['tipo']==$pg2['tipo'] && $pg1['srid']==$pg2['srid'] && $pg1['dims']==$pg2['dims']);
		}
	}
	// echo $col->type;exit;
	if ($col->type=='geometry'){
		if (!$tipoIgual){
			$pre="<b>";
			$pos="</b>";
			$txt.=$pre.$col->type.$pos;
			$txt2.=$pre.$col2->type.$pos;
		}
		if (!$tamIgual){
			$pre=" <b>";
			$pos="</b>";
		}else{
			$pre=' ';
			$pos='';
		}
		$txt.=$pre.(empty($col->formated)==false?$col->formated:$col->max_length).$pos;
		$txt2.=$pre.(empty($col2->formated)==false?$col2->formated:$col2->max_length).$pos;
	}else{
		if (!$tipoIgual){
			$pre="<b>";
			$pos="</b>";
		}else{
			$pre='';
			$pos='';
		}
		$txt.=$pre.$col->type.$pos;
		$txt2.=$pre.$col2->type.$pos;
		if (!$tamIgual){
			$pre=" <b>(";
			$pos=")</b>";
		}else{
			$pre=' (';
			$pos=')';
		}
		$txt.=$pre.(empty($col->formated)==false?$col->formated:$col->max_length).$pos;
		$txt2.=$pre.(empty($col2->formated)==false?$col2->formated:$col2->max_length).$pos;
	}

	if ($col->not_null!=$col2->not_null){
		$nullIgual=false;
		$ltxt1=($col->not_null?'NULL':'NOT NULL');
		$ltxt2=($col2->not_null?'NULL':'NOT NULL');
		$sql1="alter table ".$tabname." alter column ".$col->name."".($col->not_null?' drop NOT NULL':' set NOT NULL');
		$sql2="alter table ".$tabname." alter column ".$col->name."".($col2->not_null?' drop NOT NULL':' set NOT NULL');
		$pre1=" <b>".'<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&dbhost='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sql1).'" title="'.$sql1.'">';
		$pos1="</a></b>";
		$pre2=" <b>".'<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&dbhost='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sql2).'" title="'.$sql2.'">';
		$pos2="</a></b>";
		//$html.='<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($idx2).'\\\', this);" title="'.nl2br($idx2).'">Go!</a>';
		
		if ($trava=='1'){
			$acao1='&lt;&lt; Alteração bloqueada';
		}else{
			$acao1='<a style="cursor:pointer;" onclick="sqlGo(0, \\\''.addslashes($sql1).'\\\', this);" title="'.nl2br($sql1).'">&lt;&lt; go primeira!</a> ';
		}
		if($trava=='2'){
			$acao2='Alteração bloqueada &gt;&gt;';
		}else{
			$acao2='<a style="cursor:pointer;" onclick="sqlGo(1, \\\''.addslashes($sql2).'\\\', this);" title="'.nl2br($sql2).'">go segunda! &gt;&gt;</a>';
		}
		$acao.=$sepAcao.$acao1.' &ne; '.$acao2;
		$sepAcao=' | ';
	}else{
		$pre1=' ';
		$pos1='';
		$pre2=' ';
		$pos2='';
	}
	$txt.=$pre1.($col->not_null?'NOT NULL</a>':'NULL').$pos1;
	$txt2.=$pre2.($col2->not_null?'NOT NULL':'NULL').$pos2;
	if (empty($col->primary_key)){
		if (!empty($col2->primary_key)){
			$txt.=" <b>NÃO É PK</b>";
			$txt2.=" PK";
		}
	}else{
		if (empty($col2->primary_key)){
			$txt.=" PK";
			$txt2.=" <b>NÃO É PK</b>";
		}else{
			$txt.=" PK";
			$txt2.=" PK";
		}
	}
	if ($col->has_default!=$col2->has_default || $col->default_value!=$col2->default_value){
		if ($col->has_default){
			if (!$col2->has_default){
				$sql2="alter table ".$tabname." alter column ".$col2->name." set default ".str_replace("'","\'",$col->default_value).';';
				$acao2='<a style="cursor:pointer;" onclick="sqlGo(1, \''.addslashes($sql2).'\', this);" title="'.nl2br($sql2).'">&lt;&lt; go default 2ª!</a> ';
				$acao.=$sepAcao.$acao2;
				$sepAcao=' | ';
				$txt.=' [def: '.str_replace("'","\'",$col->default_value).']';
				$txt2.=' [sem def.]';
			}else{
				$txt.=' [def: '.str_replace("'","\'",$col->default_value).']';
				$txt2.=' [def: '.str_replace("'","\'",$col2->default_value).']';
			}
		}else{
			if ($col2->has_default){
				$txt.=' [sem def.]';
				$txt2.=' [def: '.str_replace("'","\'",$col2->default_value).']';
			}
		}
	}
	//$txtDebug.=(empty($col->formated)==false?$col->formated:$col->max_length);

	if (strpos($col->type, 'geometry')!==false){
		$tam=(empty($col->formated)==false?$col->formated:$col->max_length);
		$tam2=(empty($col2->formated)==false?$col2->formated:$col2->max_length);
		// echo $tam.' - '.$tam2.'<br/>';
		if ($tam!=$tam2){
			$pg=parseiaGeom($tam);
			$gsql=getAddGeometry($schema1,$tabname, $col->name,$pg['tipo'],$pg['srid'],$pg['dims']);
			if ($pg['dims']==3) $setTo='st_force3d('.$col->name.'_temp)'; else $setTo=$col->name.'_temp';
			if (empty($pg['srid'])==false) $setTo='st_setsrid('.$setTo.','.$pg['srid'].')';
			$sql2="alter table ".$tabname." rename column ".$col->name." to ".$col->name.'_temp;'.$gsql.'update '.$tabname.' set '.$col->name.'='.$setTo.';alter table '.$tabname.' drop column '.$col->name.'_temp;';
			$pg=parseiaGeom($tam2);
			$gsql=getAddGeometry($schema2,$tabname, $col2->name,$pg['tipo'],$pg['srid'],$pg['dims']);
			if ($pg['dims']==3) $setTo='st_force3d('.$col2->name.'_temp)'; else $setTo=$col2->name.'_temp';
			if (empty($pg['srid'])==false) $setTo='st_setsrid('.$setTo.','.$pg['srid'].')';
			$sql1="alter table ".$tabname." rename column ".$col2->name." to ".$col2->name.'_temp;'.$gsql.'update '.$tabname.' set '.$col2->name.'='.$setTo.';alter table '.$tabname.' drop column '.$col2->name.'_temp;';

			$pre1=" <b>".'<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo1'].'&dbhost='.$_REQUEST['host1'].'&dbuser='.$_REQUEST['user1'].'&pw='.$_REQUEST['pass1'].'&db='.$_REQUEST['base1'].'&sql='.urlencode($sql1).'" title="'.$sql1.'">';
			$pos1="</a></b>";
			$pre2=" <b>".'<a target="_blank" href="index.php?ati=doalter&type='.$_REQUEST['tipo2'].'&dbhost='.$_REQUEST['host2'].'&dbuser='.$_REQUEST['user2'].'&pw='.$_REQUEST['pass2'].'&db='.$_REQUEST['base2'].'&sql='.urlencode($sql2).'" title="'.$sql2.'">';
			$pos2="</a></b>";
	
			if ($trava=='1'){
				$acao1='&lt;&lt; Alteração bloqueada';
			}else{
				$acao1='<a style="cursor:pointer;" onclick="sqlGo(0, \''.addslashes($sql1).'\', this);" title="'.nl2br($sql1).'">&lt;&lt; go primeira!</a> ';
			}
			if($trava=='2'){
				$acao2='Alteração bloqueada &gt;&gt;';
			}else{
				$acao2='<a style="cursor:pointer;" onclick="sqlGo(1, \''.addslashes($sql2).'\', this);" title="'.nl2br($sql2).'">go segunda! &gt;&gt;</a>';
			}
			$acao.=$sepAcao.$acao1.' &ne; '.$acao2;
			$sepAcao=' | ';
		}else{
			return '';
		}
	}
	//if (!$nullIgual) $acao.=" NULL";
	//if ($tipoIgual==false || $tamIgual==false) $acao='&lt; == &gt;';
	$ret="<td class=\"primeira coluna\">".$txt."</td><td class=\"segunda coluna\">".$txt2.$txtDebug."</td><td class=\"segunda coluna\">".$acao."</td>";
	return $ret;
}
function getAddGeometry($schema,$table,$col,$tipo,$srid,$dims){
	$table=str_replace($schema.'.','',$table);
	if (empty($tipo)){
		if(empty($srid)){
			return 'alter table '.$schema.$table.' add column '.$col.' geometry;';
		}else{
			return 'alter table '.$schema.$table.' add column '.$col.' geometry(Geometry,'.$srid.');';
		}
	}else{
		if (empty($srid)){
			return 'alter table '.$schema.$table.' add column '.$col.' geometry('.$tipo.','.$srid.');';
		}else{
			if (empty($dims)) $dims=2;
			return 'select addgeometrycolumn(\''.$schema.'\',\''.$table.'\',\''.$col.'\','.$srid.', \''.strtoupper($tipo).'\','.$dims.');';
		}
	}
}
function parseiaGeom($g){
	$p=['tipo'=>null,'srid'=>null,'dims'=>null];
	if ($g=='geometry') return $p;
	$dims=2;
	$pg=str_replace('geometry','',$g);
	if ($g==$pg) return $p;
	$pg=str_replace(['(',')'],'',$pg);
	$ps=explode(',',$pg);
	$tipo=$ps[0];
	$srid=null;
	if (count($ps)>1 && is_numeric($ps[1])) $srid=$ps[1];
	$ll=substr($tipo,-1);
	if(strtolower($ll)=='m' || strtolower($ll)=='z') {
		$dims=3;
		$tipo=str_replace($ll,'',$tipo);
	}
	return ['tipo'=>$tipo,'srid'=>$srid,'dims'=>$dims];
}
function getArrayTabelas($db, $somenteTb=true, $ajax=false, $soPublic=true, $soSchema='public'){
	$debug=false;
	if ($db->databaseType=='json') return $db->getArrayTabelas($somenteTb, $ajax, $soPublic, $soSchema);
	include_once('gera/php/adodb/adodb-datadict.inc.php');
	include_once('gera/php/adodb/datadict/datadict-postgres.inc.php');
	$dd=new ADODB2_postgres();
	$dd->connection=&$db;
	$arComments=[];
	$sql="select
		c.table_schema,
		c.table_name,
		c.column_name,
		pgd.description
	from pg_catalog.pg_statio_all_tables as st
	inner join pg_catalog.pg_description pgd on (pgd.objoid = st.relid)
	inner join information_schema.columns c on (pgd.objsubid = c.ordinal_position and c.table_schema = st.schemaname and c.table_name = st.relname)
	";
	if (!$rs=$db->ExecuteBind($sql, [])){
		$db->_raiseError(__FILE__,__LINE__,$sqlCompleto);
	}else{
		if (!$rs->EOF){
			while(!$rs->EOF){
				$arComments[$rs->fields(0).'.'.$rs->fields(1).'.'.$rs->fields(2)]=$rs->fields(3);
				$rs->MoveNext();
			}
		}
	}

	$sql="select t.table_schema, t.table_name, pg_catalog.obj_description(pgc.oid, 'pg_class')
	FROM information_schema.tables t
	INNER JOIN pg_catalog.pg_class pgc
	ON t.table_name = pgc.relname 
	WHERE t.table_type='BASE TABLE'
	and t.table_schema<>'pg_catalog'
	and t.table_schema<>'information_schema'
	and pg_catalog.obj_description(pgc.oid, 'pg_class') is not null
	";
	if (!$rs=$db->ExecuteBind($sql, [])){
		$db->_raiseError(__FILE__,__LINE__,$sqlCompleto);
	}else{
		if (!$rs->EOF){
			while(!$rs->EOF){
				$arComments[$rs->fields(0).'.'.$rs->fields(1)]=$rs->fields(2);
				$rs->MoveNext();
			}
		}
	}



	//$arTabelas=$db->MetaTables(false, true);
	$arTabelas=$dd->MetaTables(false, true);
	//if ($db->database=='gtapp') {echo nl2br(print_r($arTabelas, true))."<br>";exit;}
	sort($arTabelas);
	reset($arTabelas);
	$cont=0;
	$ehMssql=false;
	if (!$ehOracle=isOracle($db)){
		$ehMssql=isMSSqlServer($db);
	}
	$tabs=array();
	$numTabs=count($arTabelas);
	$contTab=0;
	$limite=0;
	foreach($arTabelas as $key=>$nome){
		if (connection_aborted()) exit;
		$tab=(object)'';
		if ($debug) echo($nome."---<br>");
		$nomelow=strtolower($nome);
		$nomeTab=$nome;
		$schema=null;
		if (strpos($nomeTab, '.')!==false){
			$nomeTab=substr($nomeTab, strpos($nomeTab, '.')+1);
			$schema=substr($nome, 0, strpos($nome, '.'));
		}
		$arSysSchemas=array('information_schema');
		if ($schema && in_array($schema,$arSysSchemas)) {
			//echo $schema." - ".$nomeTab."<br>";
			continue;
		}
		if ($debug) echo $nomeTab.' - '.$schema."<br/>";
		if ($soPublic){ // só vamos comparar os schemas public
			if ($schema){ // veio schema no nome da tabela
				if ($schema!='public'){ // o schema que veio não é o public
					if ($debug) echo 'sopublic e não é schema public'."<br/>";
					continue;
				}else{
					if ($debug) echo 'sopublic e É schema public'."<br/>";
				}
			}else{
				if ($debug) echo 'sopublic e não veio schema em '.$nomeTab."<br/>";
			}
		}else{
			if ($debug) echo 'NOT sopublic'."<br/>";
			if ($soSchema){
				if ($schema != $soSchema){
					//echo $schema.'.'.$nome."<br/>";
					continue;
				}
			}
		}
		$contTab++;
		if (@$limite && $limite>0 && $contTab>$limite)break;
		if ($ajax){
			cospeMsgProgressoAjax("Carregando tabelas da base ".$db->nome.': '.$nome.' ('.$contTab.'/'.$numTabs.')');
		}else{
			cospeMsgProgresso("Carregando tabelas da base ".$db->nome.': '.$nome.' ('.$contTab.'/'.$numTabs.')');
		}
		if (($somenteTb && substr($nomeTab, 0, 2) == 'tb') || !$somenteTb){
			$cont++;
			$tab->name=$nome;
			// echo $nomeTab.' - '.$schema."<br>";
			if ($cols=$db->MetaColumns($nomeTab, true, $schema)){
				foreach($cols as $k=>$col){
					if (substr(strtolower($col->type), 0, 3)=='geo'){
						$sql="SELECT format_type(a.atttypid, a.atttypmod)
						FROM pg_class c, pg_attribute a,pg_type t , pg_namespace n
						WHERE relkind = 'r' AND lower(c.relname) = lower(?)
						AND lower(n.nspname) = lower(?)
						and lower(a.attname)=lower(?)
						AND a.attnum > 0 AND c.relnamespace=n.oid AND a.atttypid = t.oid AND a.attrelid = c.oid";
						if ($rs=$db->ExecuteBind($sql, array(array('varchar', $nomeTab), array('varchar', $schema), array('varchar', $col->name)))){
							if (!$rs->EOF){
								$cols[$k]->formated=$rs->fields(0);
							}
						}else{
							$db->_raiseError(__FILE__,__LINE__,$sql);
						}
						// echo nl2br(print_r($cols[$k], true))."<br>";exit;
					}
				}
				//if ($db->databaseType=='mssql') echo(print_r($cols, true)."<br>");
				$pks=array();
				if ($ehOracle || $ehMssql) $pks=$db->metaPrimaryKeys($nome);
				$arCols=array();
				foreach($cols as $col){
					$col->name=strtolower($col->name);
					if (isset($arComments[$schema.'.'.$nomeTab.'.'.$col->name])){
						$col->comment=$arComments[$schema.'.'.$nomeTab.'.'.$col->name];
					}else{
						$col->comment='';
					}
					$arCols[$col->name]=$col;
					if ($ehOracle || $ehMssql) {
						if ($pks && in_array(strtoupper($col->name), $pks)) $arCols[$col->name]->primary_key=true;
						$para=true;
					}
				}
				ksort($arCols);
			}else{
				$arCols=array();
			}
			if($nomeTab=='tb1_pcar--') {
				echo nl2br(print_r($arCols, true))."<br>";
				exit;
			}
			$tab->cols=$arCols;
			$tab->schema=$schema;
			if (isset($arComments[$schema.'.'.$nomeTab])){
				$tab->comment=$arComments[$schema.'.'.$nomeTab];
			}else{
				$tab->comment='';
			}
			$tabs[$tab->name]=$tab;
			//if ($cont>30) break;
			if (false && @$para==true){
				echo(nl2br(print_r($pks, true))."<br>");
				echo(nl2br(print_r($arCols, true))."<br>");
				exit;
			}
		}
	}
	//if ($db->database=='xmapnew') {echo nl2br(print_r($tabs, true))."<br>";exit;}
	if ($debug) {
		echo nl2br(print_r($tabs, true))."<br/>";
		exit;
	}
	return $tabs;
}
function getArraySeqs(&$banco){
	if ($banco->databaseType=='json') return $banco->getArraySeqs();
	$ehMssql=false;
	if (!$ehOracle=isOracle($banco)){
		$ehMssql=isMSSqlServer($banco);
	}
	$a = array();
	if ($ehOracle){
		$sql="select sequence_name from user_sequences";
	}elseif($ehMssql){

	}else{
		$sql="SELECT c.relname FROM pg_class c WHERE c.relkind = 'S';";
	}
	if (!($rs = $banco->Execute($sql))){
		$banco->_raiseError(__FILE__, __LINE__, $sql);
	}else{
		if ($rs && !$rs->EOF) {
			$arr =& $rs->GetArray();
			foreach($arr as $v) {
				$nomeseq=$v['relname'];
				$a[]=$nomeseq;
			}
		}
	}
	return $a;
}
function MetaIndexes(&$banco, $table, $tabDef){
	global $debug;
	$ehMssql=false;
	if (!$ehOracle=isOracle($banco)){
		$ehMssql=isMSSqlServer($banco);
	}
	$schema=null;
	if (strpos($table, ".")!==false){
		$schema=substr($table, 0, strpos($table, "."));
		$table=substr($table, strpos($table, ".")+1);
	}
	if ($banco->databaseType=='json') return $banco->MetaIndexes($table);
	//if ($debug) echo $table."<br>";
	$a = array();
	//select index_name , table_name , column_name from user_ind_columns order by table_name ;
	$sql="select indexname, indexdef
	from pg_indexes
	where tablename ='".strtolower($table)."'
	".($schema?"and schemaname = '".strtolower($schema)."'":"")."
	order by indexname
	";
	if (!($rs = $banco->Execute($sql))){
		$banco->_raiseError(__FILE__, __LINE__, $sql);
	}else{
		if ($debug) echo nl2br(print_r($sql,true))."<br/>";
		if ($rs && !$rs->EOF) {
			$arr =& $rs->GetArray();
			foreach($arr as $v) {
				$defidx=$v['indexdef'];
				if (stripos($defidx, 'unique')!==false){
					// get the columns of the index from definition
					$cols=array();
					if (preg_match('/using\s+\w+\s+\(([^)]+)\)/im', $defidx, $cols)) {
						# Successful match
						$cols=explode(',', $cols[1]);
						$tudoPk=true;
						foreach($cols as $col){
							// echo $col." col<br/>";
							$col=trim($col);
							if (!isset($tabDef->cols[$col]->primary_key) || !$tabDef->cols[$col]->primary_key) {
								// echo nl2br(print_r($tabDef->cols[$col], true))."<br/>=========================<br/>";
								$tudoPk=false;
							}
						}
						if ($tudoPk) continue;
						// echo nl2br(print_r($cols, true))."<br/>";
						// echo nl2br(print_r($tabDef, true))."<br/>";exit;

					} else {
						# Match attempt failed
					}
				}
				$nomeidx=$v['indexname'];
				$a[$nomeidx]=$defidx;
			}
		}
	}
	return $a;
}
function MetaForeignKeys(&$banco, $table, $owner=false, $upper=false)	{
//original não funciona - alterei pela do adodb4.70
//echo($banco->database."<br>");
	$schema=null;
	if (strpos($table, ".")!==false){
		$schema=substr($table, 0, strpos($table, "."));
		$table=substr($table, strpos($table, ".")+1);
	}
	if ($banco->databaseType=='json') return $banco->MetaForeignKeys($table, $owner, $upper);
	$via='499';
	if ($via=='old'){
	$sql = '
SELECT t.tgargs as args
   FROM pg_trigger t,
        pg_class c,
        pg_class c2,
        pg_proc f
   WHERE t.tgenabled
   AND t.tgrelid=c.oid
   AND t.tgconstrrelid=c2.oid
   AND t.tgfoid=f.oid
   AND f.proname ~ \'^RI_FKey_check_ins\'
   AND t.tgargs like \'$1\\\000'.strtolower($table).'%\'
   ORDER BY t.tgrelid';
		$rs = $banco->Execute($sql);
		if ($rs && !$rs->EOF) {
			$arr =& $rs->GetArray();
			$a = array();
			foreach($arr as $v) {
				$nomefk=$v['nome'];
				$data = explode(chr(0), $v['args']);
				if ($upper) {
					$a[$nomefk] = array(strtoupper($data[2]).'='.strtoupper($data[4].'='.$data[5]));
				} else {
					$a[$nomefk] = array($data[2] .'='. $data[4].'='.$data[5]);
				}
			}
			return $a;
		}
		else return false;
	}else if ($via==='new'){
		$sql = 'SELECT t.tgargs as args, t.tgconstrname as nome
		FROM
		pg_trigger t,pg_class c,pg_proc p
		WHERE
		t.tgenabled '.($banco->pgVersion>8.2?'= \'O\'':'').' AND
		t.tgrelid = c.oid AND
		t.tgfoid = p.oid AND
		p.proname = \'RI_FKey_check_ins\' AND
		c.relname = \''.strtolower($table).'\'
		ORDER BY
			t.tgrelid';
		$rs = $banco->Execute($sql);
		if ($rs && !$rs->EOF) {
			$arr =& $rs->GetArray();
			$a = array();
			foreach($arr as $v) {
				$nomefk=$v['nome'];
				$data = explode(chr(0), $v['args']);
				echo(print_r($data, true)."---------<br>");
				if ($upper) {
					$a[$nomefk] = array(strtoupper($data[2]).'='.strtoupper($data[4].'='.$data[5]));
				} else {
					$a[$nomefk] = array($data[2] .'='. $data[4].'='.$data[5]);
				}
			}
			return $a;
		}
		else return array();
	}else if ($via=='499'){
		$sql="
SELECT fkname as nome, fum.ftblname AS lookup_table, split_part(fum.rf, ')'::text, 1) AS lookup_field,
         fum.ltable AS dep_table, split_part(fum.lf, ')'::text, 1) AS dep_field
      FROM (
      SELECT fee.ltable, fee.ftblname, fee.consrc, split_part(fee.consrc,'('::text, 2) AS lf,
        split_part(fee.consrc, '('::text, 3) AS rf, fee.fkname
      FROM (
          SELECT foo.relname AS ltable, foo.ftblname,
              pg_get_constraintdef(foo.oid) AS consrc, foo.name as fkname
          FROM (
              SELECT c.oid, c.conname AS name, t.relname, ft.relname AS ftblname
              FROM pg_constraint c
              JOIN pg_class t ON (t.oid = c.conrelid)
              JOIN pg_class ft ON (ft.oid = c.confrelid)
              JOIN pg_namespace nft ON (nft.oid = ft.relnamespace)
              LEFT JOIN pg_description ds ON (ds.objoid = c.oid)
              JOIN pg_namespace n ON (n.oid = t.relnamespace)
              WHERE c.contype = 'f'::\"char\"
	          ORDER BY t.relname, n.nspname, c.conname, c.oid
	          ) foo
	      ) fee) fum
	  WHERE fum.ltable='".strtolower($table)."'
	  ORDER BY fum.ftblname, fum.ltable, split_part(fum.lf, ')'::text, 1)
		";
		//echo nl2br(print_r($sql,true))."<br/>";
		$rs = $banco->Execute($sql);
		if ($rs && !$rs->EOF) {
			$arr =& $rs->GetArray();
			//echo nl2br(print_r($arr,true))."<br/>";
			$a = array();
			foreach($arr as $v) {
				$nomefk=$v['nome'];
				$data = array(1=>$v['dep_table'], 2=>$v['lookup_table'], 4=>$v['lookup_field'], 5=>$v['dep_field']);
				//echo(print_r($data, true)."---------<br>");
				if ($upper) {
					$a[$nomefk] = array(strtoupper($data[2]).'='.strtoupper($data[4].'='.$data[5]));
				} else {
					$a[$nomefk] = array($data[2] .'='. $data[4].'='.$data[5]);
				}
			}
			// echo(print_r($a, true)."---------<br>");exit;
			return $a;
		} else {
			//echo "EOF em ".$table."<br>";
			return array();
		}
	}
   //echo nl2br(print_r($banco->pgVersion, true))."<br>";

}
function getFieldType($type, $ora, $ms){
	//echo $type." - ".((int) isset($types[$type]))." T<br>";
	//echo ((int)$ora)." ora<br>";
	$type=strtolower($type);
	if ($ora){
		$types=Array('int'=>'number(38)','number'=>'number(38,18)');
		if (isset($types[$type])) {
			//echo $types[$type]." S<br>";
			return $types[$type];
		}else{
			return $type;
		}
	}elseif($ms){

	}else{
		$types=Array('int4'=>'Integer','int'=>'Integer','clob'=>'text','int8'=>'bigint','number'=>'double precision','date'=>'timestamp');
		if (isset($types[$type])) return $types[$type]; else return $type;
	}
}
function comparaOracle(&$col, &$colOra){
	switch ($col->type){
		case "int4":
			if ($colOra->type=="INT" && $colOra->max_length==38) return true;
			break;
		case "varchar":
			if ($colOra->type=="VARCHAR2" && $colOra->max_length==$col->max_length) return true;
			break;
		case "timestamp":
			if ($colOra->type=="DATE" && $colOra->max_length==7) return true;
			break;
		case "text":
			if ($colOra->type=="CLOB" && $colOra->max_length==4000) return true;
			break;
		case "bool":
			if ($colOra->type=="INT" && $colOra->max_length==1) return true;
			break;
		case "float8":
			if ($colOra->type=="FLOAT" && $colOra->max_length==22) return true;
			break;
	}
	return false;
}
function comparaMSSqlServer(&$col, &$colMS){
	switch ($col->type){
		case "int4":
			if ($colMS->type=="bigint" && $colMS->max_length==8) return true;
			break;
		case "varchar":
			if ($colMS->type=="varchar" && $colMS->max_length==$col->max_length) return true;
			break;
		case "timestamp":
			if ($colMS->type=="datetime" && $colMS->max_length==8) return true;
			break;
		case "text":
			if ($colMS->type=="text" && ($colMS->max_length==-1 || $colMS->max_length==16)) return true;
			break;
		case "bool":
			if ($colMS->type=="bit" && $colMS->max_length==1) return true;
			break;
		case "float8":
			if ($colMS->type=="float" && ($colMS->max_length==53 || $colMS->max_length==8)) return true;
			break;
	}
	return false;
}
function comparaTamGeral($col, $col2, $ora1, $ms1, $ora2, $ms2){
	if ($ora1==$ora2 && $ms1==$ms2) return ($col->max_length!=$col2->max_length);
	return true;
}
function comparaTipoGeral($col, $col2, $ora1, $ms1, $ora2, $ms2){
	if ($ora1==$ora2 && $ms1==$ms2){
		return ($col->type==$col2->type);
	}else{
		return (getTipoGenerico($col, $ora1, $ms1)==getTipoGenerico($col2, $ora2, $ms2));
	}
}
function comparaDefaultGeral($col, $col2, $ora1, $ms1, $ora2, $ms2){
	if ($col->has_default!=$col2->has_default || $col->default_value!=$col2->default_value){
		return false;
	}
	return true;
}
function getTipoGenerico($col, $ora, $ms){
	$oraTypes=array('int4'=>'INT', 'varchar'=>'VARCHAR2', 'timestamp'=>'DATE', 'text'=>'CLOB', 'bool'=>'INT', 'float8'=>'FLOAT');
	$msTypes=array('int4'=>'bigint', 'varchar'=>'varchar', 'timestamp'=>'datetime', 'text'=>'text', 'bool'=>'bit', 'float8'=>'float');
	if ($ora){
		$types=$oraTypes;
		if (!$type=array_search($col->type, $types)){
			//echo nl2br(print_r($col, true));exit;
			echo("Tipo de coluna (oracle) não contemplado: ".$col->type."<br>");
			return $col->type;
		}else{
			return $type;
		}
	}elseif ($ms){
		$types=$msTypes;
		if (!$type=array_search($col->type, $types)){
			echo("Tipo de coluna (SQL Server) não contemplado: ".$col->type."<br>");
			return $col->type;
		}else{
			return $type;
		}
	}else{
		return $col->type;
	}
}
function comparaTipoOracle(&$col, &$colOra){
	switch ($col->type){
		case "int4":
			if ($colOra->type=="INT") return true;
			break;
		case "varchar":
			if ($colOra->type=="VARCHAR2") return true;
			break;
		case "timestamp":
			if ($colOra->type=="DATE") return true;
			break;
		case "text":
			if ($colOra->type=="CLOB") return true;
			break;
		case "bool":
			if ($colOra->type=="INT") return true;
			break;
		case "float8":
			if ($colOra->type=="FLOAT") return true;
			break;
	}
	return false;
}
function comparaTipoMSSqlServer(&$col, &$colMS){
	switch ($col->type){
		case "int4":
			if ($colMS->type=="bigint") return true;
			break;
		case "varchar":
			if ($colMS->type=="varchar") return true;
			break;
		case "timestamp":
			if ($colMS->type=="datetime") return true;
			break;
		case "text":
			if ($colMS->type=="text") return true;
			break;
		case "bool":
			if ($colMS->type=="bit") return true;
			break;
		case "float8":
			if ($colMS->type=="float") return true;
			break;
	}
	return false;
}
function getDbVersion($db){
	if ($db->databaseType=='postgres' || $db->databaseType=='zpostgres'){
		$sql="SELECT version()";
		if ($rs=$db->Execute($sql)){
			if (!$rs->EOF){
				return $rs->fields(0);
			}
		}
	}
}
function in_arrayi($needle, $haystack) {
	return in_array(strtolower($needle), array_map('strtolower', $haystack));
}
function array_searchi($needle, $haystack) {
	return array_search(strtolower($needle), array_map('strtolower', $haystack));
}
?>